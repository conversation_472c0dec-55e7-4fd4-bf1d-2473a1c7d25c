upstream php_app {
    # server 127.0.0.1:9001;
    server php:9000;
}

# đ<PERSON>y là file dùng cấu hình bên trong docker
server {
    listen 80;
    server_name dash.pokee.club;
    root /var/www/domains/public;

    access_log /var/log/nginx/dash.pokee.club_access.log;
    error_log /var/log/nginx/dash.pokee.club_error.log;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    # Handle CORS preflight requests for API routes
    # location ~ ^/api/ {
    #     # Handle preflight OPTIONS requests
    #     if ($request_method = OPTIONS) {
    #         add_header Access-Control-Allow-Origin $http_origin always;
    #         add_header Access-Control-Allow-Methods "GET, POST, PUT, PATCH, DELETE, OPTIONS" always;
    #         add_header Access-Control-Allow-Headers "Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-TOKEN, X-External-Token, x-external-token" always;
    #         add_header Access-Control-Allow-Credentials true always;
    #         add_header Access-Control-Max-Age 86400 always;
    #         add_header Content-Length 0;
    #         add_header Content-Type text/plain;
    #         return 204;
    #     }

    #     # Add CORS headers for actual requests
    #     add_header Access-Control-Allow-Origin $http_origin always;
    #     add_header Access-Control-Allow-Credentials true always;
    #     add_header Access-Control-Allow-Headers "Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-TOKEN, X-External-Token, x-external-token" always;

    #     try_files $uri $uri/ /index.php?$query_string;

    #     location ~ \.php$ {
    #         fastcgi_split_path_info ^(.+\.php)(/.*)$;
    #         include fastcgi_params;
    #         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    #         fastcgi_param HTTP_AUTHORIZATION $http_authorization;
    #         fastcgi_pass php:9000;
    #         fastcgi_index index.php;
    #     }
    # }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    location ~ \.php$ {

        # Handle preflight OPTIONS requests
        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, PATCH, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-TOKEN, X-External-Token, x-external-token" always;
            add_header Access-Control-Allow-Credentials true always;
            add_header Access-Control-Max-Age 86400 always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }

        # Add CORS headers for actual requests
        # add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Headers "Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-TOKEN, X-External-Token, x-external-token" always;

        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTP_AUTHORIZATION $http_authorization;
        fastcgi_pass php:9000;
        fastcgi_index index.php;
    }

    # location /templates22 {
    #    proxy_pass http://php_app;
    #    proxy_http_version 1.1;
    #    proxy_set_header Upgrade $http_upgrade;
    #    proxy_set_header Connection 'upgrade';
    #    proxy_set_header Host $host;
    #    proxy_cache_bypass $http_upgrade;
    # }
    # location /build22 {
    #    root /var/www/domains/pokee-dash/public;
    #    try_files $uri $uri/ /index.php?$query_string;
    #    gzip_static on;
    # }
    # location /images22 {
    #    root /var/www/domains/pokee-dash/public;
    #    try_files $uri $uri/ /index.php?$query_string;
    #    gzip_static on;
    # }
    # location /storage2 {
    #    proxy_pass http://php_app;
    #    proxy_http_version 1.1;
    #    proxy_set_header Upgrade $http_upgrade;
    #    proxy_set_header Connection 'upgrade';
    #    proxy_set_header Host $host;
    #    proxy_cache_bypass $http_upgrade;
    # }

    # config to api endpoint
    # location /api/v1 {
    #    proxy_pass http://php_app;
    #    proxy_http_version 1.1;
    #    proxy_set_header Upgrade $http_upgrade;
    #    proxy_set_header Connection 'upgrade';
    #    proxy_set_header Host $host;
    #    proxy_cache_bypass $http_upgrade;
    #}
}
