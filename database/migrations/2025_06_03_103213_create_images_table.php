<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('images', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('upload_id');
            $table->string('original_filename', 255);
            $table->string('mime_type', 100)->nullable();
            $table->bigInteger('size')->nullable()->comment('Size in bytes');
            $table->string('path_original', 500)->nullable();
            $table->timestamps();

            // $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('upload_id')->references('id')->on('user_uploads');
            $table->index(['user_id', 'upload_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('images');
    }
};
