<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_uploads', function (Blueprint $table) {
            $table->enum('type', ['admin', 'external'])->default('admin')->after('user_id');
            $table->json('external_user_info')->nullable()->after('type')->comment('Store external user info like game_uid, username, etc.');
        });

        Schema::table('images', function (Blueprint $table) {
            $table->enum('type', ['admin', 'external'])->default('admin')->after('user_id');
            $table->json('external_user_info')->nullable()->after('type')->comment('Store external user info like game_uid, username, etc.');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_uploads', function (Blueprint $table) {
            $table->dropColumn(['type', 'external_user_info']);
        });

        Schema::table('images', function (Blueprint $table) {
            $table->dropColumn(['type', 'external_user_info']);
        });
    }
};
