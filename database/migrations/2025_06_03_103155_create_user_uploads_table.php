<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_uploads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->datetime('upload_time')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->integer('total_files')->default(0);
            $table->bigInteger('total_size')->default(0)->comment('Total size in bytes');
            $table->text('description')->nullable();
            $table->timestamps();

            // $table->foreign('user_id')->references('id')->on('users');
            $table->index(['user_id', 'upload_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_uploads');
    }
};
