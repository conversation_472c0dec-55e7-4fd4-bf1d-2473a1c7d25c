<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('image_versions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('image_id');
            $table->string('version_type', 50)->nullable()->comment('thumbnail, medium, large');
            $table->integer('width')->nullable();
            $table->integer('height')->nullable();
            $table->bigInteger('size')->nullable()->comment('Size in bytes');
            $table->string('path', 500)->nullable();
            $table->timestamps();

            $table->foreign('image_id')->references('id')->on('images');
            $table->index(['image_id', 'version_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('image_versions');
    }
};
