<?php

use App\Http\Controllers\Dash\ActivityController;
use App\Http\Controllers\Dash\AdminController;
use App\Http\Controllers\Dash\ApplicationController;
use App\Http\Controllers\Dash\ArticleController;
use App\Http\Controllers\Dash\ChatController;
use App\Http\Controllers\Dash\DashboardController;
use App\Http\Controllers\Dash\FaqsController;
use App\Http\Controllers\Dash\MissionController;
use App\Http\Controllers\Dash\NotificationController;
use App\Http\Controllers\Dash\PartnerController;
use App\Http\Controllers\Dash\PermissionController;
use App\Http\Controllers\Dash\PlayerController;
use App\Http\Controllers\Dash\ReplayController;
use App\Http\Controllers\Dash\RoleController;
use App\Http\Controllers\Dash\SettingController;
use App\Http\Controllers\Dash\TransactionController;
use App\Http\Controllers\Dash\UserController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::group(['prefix' => '/', 'as' => 'cms.'], function (Router $router) {

    $router->group(['prefix' => 'auth'], function () {
        require __DIR__ . '/auth.php';
    });

    $router->group(['prefix' => '/', 'middleware' => 'auth'], function () {
        // $router->group(['prefix' => '/'], function () {
        Route::get('/', function () {
            return Inertia::render('Dashboard');
        });

        // Route::get('/', [IndexController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');
        Route::resource('players', PlayerController::class);
        Route::put('players/{id}/update-password', [PlayerController::class, 'updatePassword'])->name('players.updatePassword');
        Route::put('players/{id}/update-email', [PlayerController::class, 'updateEmail'])->name('players.updateEmail');
        Route::put('players/{id}/update-balance', [PlayerController::class, 'updateBalance'])->name('players.updateBalance');
        Route::get('players/{id}/friends', [PlayerController::class, 'showFriends'])->name('players.friends');
        Route::get('players/{id}/histories', [PlayerController::class, 'showGamePlayHistories'])->name('players.histories');
        Route::get('players/{id}/transactions', [PlayerController::class, 'showTransactions'])->name('players.transactions');
        Route::get('players/{id}/tournaments', [PlayerController::class, 'showTournaments'])->name('players.tournaments');
        Route::get('players/{id}/notifications', [PlayerController::class, 'showNotifications'])->name('players.notifications');
        Route::get('players/{id}/activities', [PlayerController::class, 'showActivities'])->name('players.activities');

        // API endpoints for dashboard
        Route::get('api/players/new', [PlayerController::class, 'getNewPlayers'])->name('api.players.new');
        Route::get('api/dashboard/stats', [DashboardController::class, 'dashboardStats'])->name('api.dashboard.stats');
        Route::get('api/dashboard/transactions', [DashboardController::class, 'dashboardTransactions'])->name('api.dashboard.transactions');
        Route::get('api/dashboard/top-players-win-rate', [DashboardController::class, 'getTopPlayersByWinRate'])->name('api.dashboard.top-players-win-rate');
        Route::get('api/dashboard/top-players-balance', [DashboardController::class, 'getTopPlayersByBalance'])->name('api.dashboard.top-players-balance');
        Route::get('api/dashboard/top-players-level', [DashboardController::class, 'getTopPlayersByLevel'])->name('api.dashboard.top-players-level');

        Route::resource('users', UserController::class);
        Route::delete('users/batch-delete', [UserController::class, 'destroyMultiple'])->name('users.destroy-multiple');
        Route::get('users/{id}/by-user-id', [UserController::class, 'showByUserId'])->name('users.showByUserId');

        Route::resource('partners', PartnerController::class);
        Route::match(
            array('PUT', 'PATCH'),
            'partners/{id}/change-status',
            [PartnerController::class, 'updateStatus']
        )->name('partners.change-status');

        Route::resource('activities', ActivityController::class);
        Route::resource('transactions', TransactionController::class);
        Route::resource('replays', ReplayController::class);

        Route::delete('articles/bulk-destroy', [ArticleController::class, 'bulkDestroy'])->name('articles.bulk-destroy');
        Route::resource('articles', ArticleController::class);
        Route::get('articles/{id}/duplicate', [ArticleController::class, 'duplicate'])->name('articles.duplicate');
        // Route::post('articles/destroys', [ArticleController::class, 'destroys'])->name('articles.destroys');


        Route::resource('chats', ChatController::class);
        Route::resource('notifications', NotificationController::class);
        Route::resource('apps', ApplicationController::class);

        Route::resource('faqs', FaqsController::class);
        Route::post('faqs/destroys', [FaqsController::class, 'destroys'])->name('faqs.destroys');
        Route::get('faqs/{id}/duplicate', [FaqsController::class, 'duplicate'])->name('faqs.duplicate');

        Route::resource('leaderboards', \App\Http\Controllers\Dash\LeaderboardController::class);

        Route::resource('missions', MissionController::class);
        Route::get('missions/{id}/duplicate', [MissionController::class, 'duplicate'])->name('missions.duplicate');
        Route::post('missions/destroys', [MissionController::class, 'destroys'])->name('missions.destroys');

        Route::resource('badges', \App\Http\Controllers\Dash\BadgeController::class);
        Route::post('badges/{id}/duplicate', [\App\Http\Controllers\Dash\BadgeController::class, 'duplicate'])->name('badges.duplicate');
        Route::post('badges/destroys', [\App\Http\Controllers\Dash\BadgeController::class, 'destroys'])->name('badges.destroys');

        Route::get('feedbacks/search-players', [\App\Http\Controllers\Dash\FeedbacksController::class, 'searchPlayers'])->name('feedbacks.search-players');
        Route::resource('feedbacks', \App\Http\Controllers\Dash\FeedbacksController::class)->only(['index', 'show', 'destroy']);
        Route::post('feedbacks/destroys', [\App\Http\Controllers\Dash\FeedbacksController::class, 'destroys'])->name('feedbacks.destroys');

        Route::resource('shops', \App\Http\Controllers\Dash\ShopController::class);
        Route::post('shops/destroys', [\App\Http\Controllers\Dash\ShopController::class, 'destroys'])->name('shops.destroys');
        Route::get('shops/{id}/duplicate', [\App\Http\Controllers\Dash\ShopController::class, 'duplicate'])->name('shops.duplicate');
        Route::patch('shops/{id}/status', [\App\Http\Controllers\Dash\ShopController::class, 'updateStatus'])->name('shops.updateStatus');

        Route::resource('roles', RoleController::class); // ->except(['update']);
        Route::post('roles/destroys', [RoleController::class, 'destroys'])->name('roles.destroys');
        Route::get('roles/{id}/duplicate', [RoleController::class, 'duplicate'])->name('roles.duplicate');

        Route::resource('permissions', PermissionController::class);
        Route::resource('settings', SettingController::class);

        Route::resource('admin', AdminController::class);
        Route::match(
            array('PUT', 'PATCH'),
            'admin/{id}/change-status',
            [AdminController::class, 'updateStatus']
        )->name('admin.change-status');
        Route::post('admin/destroys', [AdminController::class, 'destroys'])->name('admin.destroys');

        // Upload routes for web session authentication
        Route::group(['prefix' => 'uploads'], function () {
            Route::post('single', [\App\Http\Controllers\Api\V1\UploadController::class, 'uploadSingle'])->name('uploads.single');
            Route::post('multiple', [\App\Http\Controllers\Api\V1\UploadController::class, 'uploadMultiple'])->name('uploads.multiple');
            Route::delete('file/{imageId}', [\App\Http\Controllers\Api\V1\UploadController::class, 'deleteFile'])->name('uploads.delete-file');
            Route::delete('user/all', [\App\Http\Controllers\Api\V1\UploadController::class, 'deleteAllUserFiles'])->name('uploads.delete-all');
            Route::get('user/files', [\App\Http\Controllers\Api\V1\UploadController::class, 'getUserFiles'])->name('uploads.user-files');
        });

        /*
        Route::get('users/export', [UserController::class, 'exportDataInExcel'])->name('users.export');
        Route::match(array('PUT', 'PATCH'), 'users/{id}/addresses-update',
            [UserController::class, 'updateAddress'])->name('users.addresses-update');
        Route::match(array('PUT', 'PATCH'), 'users/{id}/products', [UserController::class, 'syncUserProduct'])
            ->name('users.syncUserProduct');


        Route::get('roles/{id}/duplicate', [RoleController::class, 'duplicate'])->name('roles.duplicate');
        Route::post('roles/destroys', [RoleController::class, 'destroys'])->name('roles.destroys');
        Route::get('roles/{id}/members', [RoleController::class, 'members'])->name('roles.members');

        Route::resource('products', ProductController::class)->except(['show']);
        Route::get('products/{id}/duplicate', [ProductController::class, 'duplicate'])->name('products.duplicate');
        Route::post('products/destroys', [ProductController::class, 'destroys'])->name('products.destroys');
        Route::get('products/export', [ProductController::class, 'exportDataInExcel'])->name('products.export');

        Route::get('orders/export', [OrderController::class, 'exportDataInExcel'])->name('orders.export');
        Route::resource('orders', OrderController::class)->except('update', 'create', 'store');
        Route::post('orders/destroys', [OrderController::class, 'destroys'])->name('orders.destroys');

        Route::resource('category', CategoryController::class);
        Route::post('category/destroys', [CategoryController::class, 'destroys'])->name('category.destroys');

        Route::resource('brands', BrandController::class);
        Route::post('brands/destroys', [BrandController::class, 'destroys'])->name('brands.destroys');

        Route::resource('pharmacies', PharmacyController::class);

        Route::resource('promotions', PromotionController::class);
        Route::match(array('PUT', 'PATCH'), 'promotions/{id}/update-status', [PromotionController::class, 'updateStatus'])
            ->name('promotions.update-status');
        Route::get('promotions/{id}/duplicate', [PromotionController::class, 'duplicate'])->name('promotions.duplicate');
        Route::post('promotions/destroys', [PromotionController::class, 'destroys'])->name('promotions.destroys');

        Route::resource('media', MediaController::class);

        Route::resource('articles', ArticleController::class);
        Route::get('articles/{id}/duplicate', [ArticleController::class, 'duplicate'])->name('articles.duplicate');
        Route::post('articles/destroys', [ArticleController::class, 'destroys'])->name('articles.destroys');

        Route::resource('article-categories', CategoryController::class)->except('show', 'update');
        Route::match(array('PUT', 'PATCH'), 'article-categories/{id}/update', [CategoryController::class, 'update'])->name('article-categories.update');
        Route::post('article-categories/destroys', [CategoryController::class, 'destroys'])->name('article-categories.destroys');

        Route::resource('product-categories', CategoryController::class)->except('show', 'update');
        Route::match(array('PUT', 'PATCH'), 'product-categories/{id}/update', [CategoryController::class, 'update'])->name('product-categories.update');
        Route::post('product-categories/destroys', [CategoryController::class, 'destroys'])->name('product-categories.destroys');

        Route::resource('tags', TagController::class);
        Route::post('tags/destroys', [TagController::class, 'destroys'])->name('tags.destroys');

        Route::resource('contact', ContactController::class)->except(['create', 'store', 'edit', 'update']);

        Route::resource('sliders', SliderController::class);

        Route::resource('faqs', FaqsController::class);
        Route::post('faqs/destroys', [FaqsController::class, 'destroys'])->name('faqs.destroys');

        // Route::resource('setting', SettingController::class)->only(['index', 'store']);
        Route::group(['prefix' => 'settings'], function () {
            Route::get('general', [SettingController::class, 'general'])->name('settings.general');
            Route::post('general', [SettingController::class, 'storeGeneral'])->name('settings.store-general');
            Route::get('emails', [SettingController::class, 'emailSetting'])->name('settings.emails');
            Route::post('emails', [SettingController::class, 'storeEmailSetting'])->name('settings.store-emails');
        });

        Route::middleware('auth')->group(function () {
            Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
            Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
            Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
        });

        Route::group(['prefix' => 'notifications'], function () {
            Route::match(array('PUT', 'PATCH'), '{id}/mark', [IndexController::class, 'markNotification'])
                ->name('notifications.markNotification');
        });*/
    });
});
