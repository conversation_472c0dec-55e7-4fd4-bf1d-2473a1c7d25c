# External User Authentication Flow

## Overview

Hệ thống hỗ trợ hai loại external authentication:

1. **API Key Authentication** - <PERSON> hệ thống server-to-server (game server calls API)
2. **External User Token** - Cho client game uploads với thông tin user thực tế

## External User Token Flow

### 1. Game Server Tạo Token cho User

Khi user login vào game, game server sẽ call API để tạo token:

```bash
curl -X POST "https://yourdomain.com/api/v1/external/tokens/create" \
  -H "X-API-Key: your_game_server_api_key" \
  -H "X-API-Secret: your_game_server_api_secret" \
  -H "Content-Type: application/json" \
  -d '{
    "game_uid": "player_12345",
    "username": "PlayerName",
    "email": "<EMAIL>",
    "level": 25,
    "avatar_url": "https://game.com/avatars/player.jpg",
    "expires_in": 3600
  }'
```

**Response:**

```json
{
    "success": true,
    "message": "External user token created successfully",
    "token": "ext_abc123def456789_1677123456",
    "expires_in": 3600,
    "expires_at": "2024-06-03T15:30:00.000Z",
    "user_info": {
        "game_uid": "player_12345",
        "username": "PlayerName",
        "external_system": "Game Server"
    }
}
```

### 2. Client Game Upload Files

Client game sử dụng token để upload files:

```bash
curl -X POST "https://yourdomain.com/api/v1/uploads/single" \
  -H "X-External-Token: ext_abc123def456789_1677123456" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@player_screenshot.jpg" \
  -F "description=Player achievement screenshot"
```

**Response:**

```json
{
    "success": true,
    "message": "File uploaded successfully",
    "upload_id": 123,
    "total_files": 1,
    "total_size": 1024000,
    "external_system": "Game Server",
    "upload_source": "external_user",
    "game_user": {
        "game_uid": "player_12345",
        "username": "PlayerName"
    },
    "images": [
        {
            "id": 456,
            "original_filename": "player_screenshot.jpg",
            "mime_type": "image/jpeg",
            "size": 1024000,
            "absolute_path": "https://domain.com/storage/uploads/2024/06/03/hash.jpg",
            "relative_path": "storage/uploads/2024/06/03/hash.jpg",
            "versions": [...]
        }
    ]
}
```

## Database Storage

### user_uploads Table

```sql
INSERT INTO user_uploads (
    user_id,                    -- Virtual user ID (generated from game_uid + system)
    type,                       -- 'external'
    external_user_info,         -- JSON: game user info
    upload_time,
    total_files,
    total_size,
    description
) VALUES (
    901234,                     -- Virtual ID: 900000 + hash(system:game_uid)
    'external',
    '{"game_uid":"player_12345","username":"PlayerName","external_system":"Game Server","email":"<EMAIL>","level":25}',
    '2024-06-03 15:00:00',
    1,
    1024000,
    'Player achievement screenshot'
);
```

### images Table

```sql
INSERT INTO images (
    user_id,                    -- Same virtual user ID
    upload_id,                  -- Link to user_uploads
    type,                       -- 'external'
    external_user_info,         -- Same JSON data
    original_filename,
    mime_type,
    size,
    path_original
) VALUES (
    901234,
    123,
    'external',
    '{"game_uid":"player_12345","username":"PlayerName","external_system":"Game Server","email":"<EMAIL>","level":25}',
    'player_screenshot.jpg',
    'image/jpeg',
    1024000,
    'storage/uploads/2024/06/03/hash.jpg'
);
```

## Token Management APIs

### Create Token

-   **POST** `/api/v1/external/tokens/create`
-   **Auth**: API Key required
-   **Purpose**: Game server tạo token cho user

### Refresh Token

-   **POST** `/api/v1/external/tokens/refresh`
-   **Auth**: API Key required
-   **Purpose**: Gia hạn token trước khi hết hạn

```bash
curl -X POST "https://yourdomain.com/api/v1/external/tokens/refresh" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_api_secret" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "ext_abc123def456789_1677123456",
    "expires_in": 3600
  }'
```

### Revoke Token

-   **POST** `/api/v1/external/tokens/revoke`
-   **Auth**: API Key required
-   **Purpose**: Thu hồi token khi user logout

```bash
curl -X POST "https://yourdomain.com/api/v1/external/tokens/revoke" \
  -H "X-API-Key: your_api_key" \
  -H "X-API-Secret: your_api_secret" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "ext_abc123def456789_1677123456"
  }'
```

### Get Token Info

-   **POST** `/api/v1/external/tokens/info`
-   **Auth**: API Key required
-   **Purpose**: Debug - kiểm tra thông tin token

## Redis Storage Structure

### User Token Storage

```
Key: external_user_token:ext_abc123def456789_1677123456
Value: {
    "game_uid": "player_12345",
    "username": "PlayerName",
    "email": "<EMAIL>",
    "avatar_url": "https://game.com/avatars/player.jpg",
    "level": 25,
    "external_system": "Game Server",
    "created_at": "2024-06-03T14:00:00.000Z",
    "expires_at": "2024-06-03T15:00:00.000Z"
}
TTL: 3600 seconds
```

### Reverse Lookup

```
Key: external_user_uid:Game Server:player_12345
Value: ext_abc123def456789_1677123456
TTL: 3600 seconds
```

## Virtual User ID Generation

```php
// Consistent virtual user ID generation
$virtualUserId = 900000 + abs(crc32($external_system . ':' . $game_uid)) % 99999;

// Examples:
// Game Server:player_12345 → 901234
// Game Server:player_67890 → 956789
// Same game_uid always gets same virtual ID
```

## Implementation Examples

### Game Server Token Management (PHP)

```php
<?php

class GameServerTokenManager
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;

    public function __construct($apiKey, $apiSecret, $baseUrl)
    {
        $this->apiKey = $apiKey;
        $this->apiSecret = $apiSecret;
        $this->baseUrl = $baseUrl;
    }

    public function createTokenForUser($gameUid, $username, $email = null, $level = 1)
    {
        $data = [
            'game_uid' => $gameUid,
            'username' => $username,
            'email' => $email,
            'level' => $level,
            'expires_in' => 3600 // 1 hour
        ];

        return $this->makeApiCall('external/tokens/create', $data);
    }

    public function revokeToken($token)
    {
        return $this->makeApiCall('external/tokens/revoke', ['token' => $token]);
    }

    private function makeApiCall($endpoint, $data)
    {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . '/api/v1/' . $endpoint,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'X-API-Key: ' . $this->apiKey,
                'X-API-Secret: ' . $this->apiSecret,
                'Content-Type: application/json',
            ],
            CURLOPT_POSTFIELDS => json_encode($data)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'success' => $httpCode === 200,
            'data' => json_decode($response, true),
            'http_code' => $httpCode
        ];
    }
}

// Usage
$tokenManager = new GameServerTokenManager($apiKey, $apiSecret, $baseUrl);

// User login → create token
$result = $tokenManager->createTokenForUser('player_12345', 'PlayerName', '<EMAIL>', 25);
$userToken = $result['data']['token'];

// Send token to client game for uploads
// Client uses X-External-Token header

// User logout → revoke token
$tokenManager->revokeToken($userToken);
```

### Client Game Upload (Unity C#)

```csharp
using UnityEngine;
using UnityEngine.Networking;
using System.Collections;

public class GameUploadManager : MonoBehaviour
{
    private string apiBaseUrl = "https://yourdomain.com/api/v1";
    private string userToken;

    public void SetUserToken(string token)
    {
        userToken = token;
    }

    public IEnumerator UploadScreenshot(Texture2D screenshot, string description)
    {
        if (string.IsNullOrEmpty(userToken))
        {
            Debug.LogError("No user token available");
            yield break;
        }

        byte[] imageBytes = screenshot.EncodeToJPG();

        WWWForm form = new WWWForm();
        form.AddBinaryData("file", imageBytes, "screenshot.jpg", "image/jpeg");
        form.AddField("description", description);

        using (UnityWebRequest www = UnityWebRequest.Post(apiBaseUrl + "/uploads/single", form))
        {
            www.SetRequestHeader("X-External-Token", userToken);

            yield return www.SendWebRequest();

            if (www.result == UnityWebRequest.Result.Success)
            {
                string responseText = www.downloadHandler.text;
                Debug.Log("Upload successful: " + responseText);

                // Parse response and handle success
                var response = JsonUtility.FromJson<UploadResponse>(responseText);
                // Process response...
            }
            else
            {
                Debug.LogError("Upload failed: " + www.error);
            }
        }
    }
}

[System.Serializable]
public class UploadResponse
{
    public bool success;
    public string message;
    public int upload_id;
    public string upload_source;
    public GameUser game_user;
}

[System.Serializable]
public class GameUser
{
    public string game_uid;
    public string username;
}
```

## Authentication Priority Order

1. **Sanctum Bearer Token** - Highest priority for web users
2. **External User Token** - For game clients with user context
3. **API Key Authentication** - For server-to-server calls

## Security Features

-   ✅ Tokens stored in Redis with automatic expiration
-   ✅ Reverse lookup for efficient token management
-   ✅ Virtual user IDs prevent collision with real users
-   ✅ External user info stored in database for audit
-   ✅ Separate rate limits for external users
-   ✅ API key required for token management
-   ✅ Proper cleanup on token revocation

## Error Handling

### Invalid/Expired Token

```json
{
    "success": false,
    "message": "Unauthorized. Please provide a valid Bearer token, external user token, or API key.",
    "errors": ["Authentication required"]
}
```

### Token Creation Errors

```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "game_uid": ["The game uid field is required."],
        "username": ["The username field is required."]
    }
}
```

## Monitoring & Analytics

Với cấu trúc database mới, có thể track:

-   Upload statistics by external system
-   User activity by game_uid
-   File usage patterns from external sources
-   Performance metrics for different user types

```sql
-- Upload stats by external system
SELECT
    JSON_EXTRACT(external_user_info, '$.external_system') as system,
    COUNT(*) as total_uploads,
    SUM(total_size) as total_bytes
FROM user_uploads
WHERE type = 'external'
GROUP BY JSON_EXTRACT(external_user_info, '$.external_system');

-- Top uploading game users
SELECT
    JSON_EXTRACT(external_user_info, '$.game_uid') as game_uid,
    JSON_EXTRACT(external_user_info, '$.username') as username,
    COUNT(*) as upload_count,
    SUM(total_size) as total_bytes
FROM user_uploads
WHERE type = 'external'
GROUP BY game_uid, username
ORDER BY upload_count DESC
LIMIT 10;
```
