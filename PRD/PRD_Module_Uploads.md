# PRD Module Uploads files & Images

## Yêu cầu

- viết 1 module upload file nằm trong Api/V1/UploadController.php với các chức năng sau:
    + API: cho phép upload 1 file
    + API: cho phép upload nhiều file
    + API: xóa file
    + sau khi upload xong trả về đường dẫn tuyệt đối và tương đối, id của files đó
    + hãy thiết kế và tạo các migrate cho các tables liên quan
    + hệ thống có thể sử dụng cơ chế authen bằng token (jwt) hoặc hỗ trở cả authen bằng 1 token cấp phát
      theo cơ chế Backend to Backend

## Cấu trúc tables

### 1. user_uploads

```sql
CREATE TABLE user_uploads
(
    id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id     BIGINT UNSIGNED NOT NULL,
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_files INT      DEFAULT 0,
    total_size  BIGINT   DEFAULT 0, -- Tổng dung lượng (bytes)
    description TEXT,               -- optional
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 2. images

```sql
CREATE TABLE images
(
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id           BIGINT UNSIGNED NOT NULL,
    upload_id         BIGINT UNSIGNED NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type         VARCHAR(100),
    size              BIGINT,       -- bytes
    path_original     VARCHAR(500), -- Đường dẫn ảnh gốc
    created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (upload_id) REFERENCES user_uploads (id)
);
```

### 3. image_versions

```sql
CREATE TABLE image_versions
(
    id           BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    image_id     BIGINT UNSIGNED NOT NULL,
    version_type VARCHAR(50),  -- ví dụ: 'thumbnail', 'medium', 'large'
    width        INT,
    height       INT,
    size         BIGINT,       -- bytes
    path         VARCHAR(500), -- đường dẫn file resize
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (image_id) REFERENCES images (id)
);
```

## Triển khai

- Tạo migrate cho các table trên
- Tạo các service, repository, controller, route cho các API
    - controller: Api/V1/UploadController.php
    - tên ảnh upload lên phải được md5 lại thành 1 chuỗi không trùng lặp
    - các tên ảnh resize cũng phải có prefix, và định nghĩa mảng lưu các kích thước size để sau có thể
      mở rộng
    - Tạo api upload 1 file và nhiều file
    - tạo API xóa file
    - tạo API xóa toàn bộ files của user
- Tạo các test cho các API
