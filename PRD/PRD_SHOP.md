# PRD SHOP
- Đọc luồng logic của chức năng: Nhiệm vụ (MissionContorller.php), chú ý kèm các luồng logic trên UI trong đường dẫn tương ứng
- Đọc luồng logic của chức năng Hỗ trợ (FaqsController.php), chú ý kèm các luồng logic trên UI trong đường dẫn tương ứng
- Hãy viết tiếp các chức năng cho phần ShopController.php với các hàm tương ứng giống 2 chức năng: Misssion và Faqs như: thêm/sửa/xóa, cập nhật status, duplicate item
- Đảm bảo style coding và logic được học hỏi từ 2 chức năng được tham khảo kia
- Đọc nội table: shop_items.sql nằm ở thư mục gốc để nắm rõ logic của table này, model tương ứng /App/Models/Shop.php

## Triển khai UI
- tôi cần bạn triển khai các giao diện UI tương ứng sử dụng vue3 và element UI trong folder: /resources/js/Pages/Shop
- Hãy tham khảo và đọc nội dung 2 chức năng /resources/js/Pages/Mission và /resources/js/Pages/Faq, để tiến hành ghép logic các chức năng API tương ứng vừa triển khai ở trên với các giao diện UI CRUD tương ứng 