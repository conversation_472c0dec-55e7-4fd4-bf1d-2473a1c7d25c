{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/apps/user-management/roles/view/update-role.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC,iCAAiC;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,6BAA6B;;AAE7B,4CAA4C;AAC5C,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/apps/user-management/roles/view/update-role.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersUpdatePermissions = function () {\r\n    // Shared variables\r\n    const element = document.getElementById('kt_modal_update_role');\r\n    const form = element.querySelector('#kt_modal_update_role_form');\r\n    const modal = new bootstrap.Modal(element);\r\n\r\n    // Init add schedule modal\r\n    var initUpdatePermissions = () => {\r\n\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        var validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'role_name': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Role name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Close button handler\r\n        const closeButton = element.querySelector('[data-kt-roles-modal-action=\"close\"]');\r\n        closeButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to close?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, close it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                }\r\n            });\r\n        });\r\n\r\n        // Cancel button handler\r\n        const cancelButton = element.querySelector('[data-kt-roles-modal-action=\"cancel\"]');\r\n        cancelButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Submit button handler\r\n        const submitButton = element.querySelector('[data-kt-roles-modal-action=\"submit\"]');\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Show loading indication\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable button to avoid multiple click \r\n                        submitButton.disabled = true;\r\n\r\n                        // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        setTimeout(function () {\r\n                            // Remove loading indication\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            // Enable button\r\n                            submitButton.disabled = false;\r\n\r\n                            // Show popup confirmation \r\n                            Swal.fire({\r\n                                text: \"Form has been successfully submitted!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        // Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Select all handler\r\n    const handleSelectAll = () => {\r\n        // Define variables\r\n        const selectAll = form.querySelector('#kt_roles_select_all');\r\n        const allCheckboxes = form.querySelectorAll('[type=\"checkbox\"]');\r\n\r\n        // Handle check state\r\n        selectAll.addEventListener('change', e => {\r\n\r\n            // Apply check state to all checkboxes\r\n            allCheckboxes.forEach(c => {\r\n                c.checked = e.target.checked;\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            initUpdatePermissions();\r\n            handleSelectAll();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersUpdatePermissions.init();\r\n});"], "sourceRoot": ""}