{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/apps/customers/list/list.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,sFAAsF;AACtF;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD,iBAAiB,+BAA+B;AAChD;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,yBAAyB;;AAEzB;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/apps/customers/list/list.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTCustomersList = function () {\r\n    // Define shared variables\r\n    var datatable;\r\n    var filterMonth;\r\n    var filterPayment;\r\n    var table\r\n\r\n    // Private functions\r\n    var initCustomerList = function () {\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[5].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 5th column in table\r\n            dateRow[5].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            'columnDefs': [\r\n                { orderable: false, targets: 0 }, // Disable ordering on column 0 (checkbox)\r\n                { orderable: false, targets: 6 }, // Disable ordering on column 6 (actions)\r\n            ]\r\n        });\r\n\r\n        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw\r\n        datatable.on('draw', function () {\r\n            initToggleToolbar();\r\n            handleDeleteRows();\r\n            toggleToolbars();\r\n        });\r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    var handleSearchDatatable = () => {\r\n        const filterSearch = document.querySelector('[data-kt-customer-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Filter Datatable\r\n    var handleFilterDatatable = () => {\r\n        // Select filter options\r\n        filterMonth = $('[data-kt-customer-table-filter=\"month\"]');\r\n        filterPayment = document.querySelectorAll('[data-kt-customer-table-filter=\"payment_type\"] [name=\"payment_type\"]');\r\n        const filterButton = document.querySelector('[data-kt-customer-table-filter=\"filter\"]');\r\n\r\n        // Filter datatable on submit\r\n        filterButton.addEventListener('click', function () {\r\n            // Get filter values\r\n            const monthValue = filterMonth.val();\r\n            let paymentValue = '';\r\n\r\n            // Get payment value\r\n            filterPayment.forEach(r => {\r\n                if (r.checked) {\r\n                    paymentValue = r.value;\r\n                }\r\n\r\n                // Reset payment value if \"All\" is selected\r\n                if (paymentValue === 'all') {\r\n                    paymentValue = '';\r\n                }\r\n            });\r\n\r\n            // Build filter string from filter options\r\n            const filterString = monthValue + ' ' + paymentValue;\r\n\r\n            // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            datatable.search(filterString).draw();\r\n        });\r\n    }\r\n\r\n    // Delete customer\r\n    var handleDeleteRows = () => {\r\n        // Select all delete buttons\r\n        const deleteButtons = table.querySelectorAll('[data-kt-customer-table-filter=\"delete_row\"]');\r\n\r\n        deleteButtons.forEach(d => {\r\n            // Delete button on click\r\n            d.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n\r\n                // Select parent row\r\n                const parent = e.target.closest('tr');\r\n\r\n                // Get customer name\r\n                const customerName = parent.querySelectorAll('td')[1].innerText;\r\n\r\n                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    text: \"Are you sure you want to delete \" + customerName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete!\",\r\n                    cancelButtonText: \"No, cancel\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-danger\",\r\n                        cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        Swal.fire({\r\n                            text: \"You have deleted \" + customerName + \"!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        }).then(function () {\r\n                            // Remove current row\r\n                            datatable.row($(parent)).remove().draw();\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: customerName + \" was not deleted.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        });\r\n    }\r\n\r\n    // Reset Filter\r\n    var handleResetForm = () => {\r\n        // Select reset button\r\n        const resetButton = document.querySelector('[data-kt-customer-table-filter=\"reset\"]');\r\n\r\n        // Reset datatable\r\n        resetButton.addEventListener('click', function () {\r\n            // Reset month\r\n            filterMonth.val(null).trigger('change');\r\n\r\n            // Reset payment type\r\n            filterPayment[0].checked = true;\r\n\r\n            // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            datatable.search('').draw();\r\n        });\r\n    }\r\n\r\n    // Init toggle toolbar\r\n    var initToggleToolbar = () => {\r\n        // Toggle selected action toolbar\r\n        // Select all checkboxes\r\n        const checkboxes = table.querySelectorAll('[type=\"checkbox\"]');\r\n\r\n        // Select elements\r\n        const deleteSelected = document.querySelector('[data-kt-customer-table-select=\"delete_selected\"]');\r\n\r\n        // Toggle delete selected toolbar\r\n        checkboxes.forEach(c => {\r\n            // Checkbox on click event\r\n            c.addEventListener('click', function () {\r\n                setTimeout(function () {\r\n                    toggleToolbars();\r\n                }, 50);\r\n            });\r\n        });\r\n\r\n        // Deleted selected rows\r\n        deleteSelected.addEventListener('click', function () {\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete selected customers?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have deleted all selected customers!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    }).then(function () {\r\n                        // Remove all selected customers\r\n                        checkboxes.forEach(c => {\r\n                            if (c.checked) {\r\n                                datatable.row($(c.closest('tbody tr'))).remove().draw();\r\n                            }\r\n                        });\r\n\r\n                        // Remove header checked box\r\n                        const headerCheckbox = table.querySelectorAll('[type=\"checkbox\"]')[0];\r\n                        headerCheckbox.checked = false;\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Selected customers was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Toggle toolbars\r\n    const toggleToolbars = () => {\r\n        // Define variables\r\n        const toolbarBase = document.querySelector('[data-kt-customer-table-toolbar=\"base\"]');\r\n        const toolbarSelected = document.querySelector('[data-kt-customer-table-toolbar=\"selected\"]');\r\n        const selectedCount = document.querySelector('[data-kt-customer-table-select=\"selected_count\"]');\r\n\r\n        // Select refreshed checkbox DOM elements \r\n        const allCheckboxes = table.querySelectorAll('tbody [type=\"checkbox\"]');\r\n\r\n        // Detect checkboxes state & count\r\n        let checkedState = false;\r\n        let count = 0;\r\n\r\n        // Count checked boxes\r\n        allCheckboxes.forEach(c => {\r\n            if (c.checked) {\r\n                checkedState = true;\r\n                count++;\r\n            }\r\n        });\r\n\r\n        // Toggle toolbars\r\n        if (checkedState) {\r\n            selectedCount.innerHTML = count;\r\n            toolbarBase.classList.add('d-none');\r\n            toolbarSelected.classList.remove('d-none');\r\n        } else {\r\n            toolbarBase.classList.remove('d-none');\r\n            toolbarSelected.classList.add('d-none');\r\n        }\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            table = document.querySelector('#kt_customers_table');\r\n            \r\n            if (!table) {\r\n                return;\r\n            }\r\n\r\n            initCustomerList();\r\n            initToggleToolbar();\r\n            handleSearchDatatable();\r\n            handleFilterDatatable();\r\n            handleDeleteRows();\r\n            handleResetForm();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTCustomersList.init();\r\n});"], "sourceRoot": ""}