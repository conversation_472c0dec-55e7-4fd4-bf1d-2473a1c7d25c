{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/apps/customers/update.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;;AAEjB,gCAAgC;AAChC,aAAa;AACb,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC,iCAAiC;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC,iCAAiC;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/apps/customers/update.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalUpdateCustomer = function () {\r\n    var element;\r\n    var submitButton;\r\n    var cancelButton;\r\n    var closeButton;\r\n    var form;\r\n    var modal;\r\n\r\n    // Init form inputs\r\n    var initForm = function () {\r\n        // Action buttons\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Show loading indication\r\n            submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n            // Simulate form submission\r\n            setTimeout(function () {\r\n                // Simulate form submission\r\n                submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                // Show popup confirmation \r\n                Swal.fire({\r\n                    text: \"Form has been successfully submitted!\",\r\n                    icon: \"success\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.isConfirmed) {\r\n                        modal.hide();\r\n                    }\r\n                });\r\n\r\n                //form.submit(); // Submit form\r\n            }, 2000);\r\n        });\r\n\r\n        cancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        closeButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            // Elements\r\n            element = document.querySelector('#kt_modal_update_customer');\r\n            modal = new bootstrap.Modal(element);\r\n\r\n            form = element.querySelector('#kt_modal_update_customer_form');\r\n            submitButton = form.querySelector('#kt_modal_update_customer_submit');\r\n            cancelButton = form.querySelector('#kt_modal_update_customer_cancel');\r\n            closeButton = element.querySelector('#kt_modal_update_customer_close');\r\n\r\n            initForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTModalUpdateCustomer.init();\r\n});"], "sourceRoot": ""}