{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/apps/customers/view/adjust-balance.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,oGAAoG,EAAE;AACtG,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B;;AAE7B,4CAA4C;AAC5C,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC,iCAAiC;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,iCAAiC;AACjC,iCAAiC;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/apps/customers/view/adjust-balance.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalAdjustBalance = function () {\r\n    var element;\r\n    var submitButton;\r\n    var cancelButton;\r\n    var closeButton;\r\n    var validator;\r\n    var maskInput;\r\n    var newBalance;\r\n    var form;\r\n    var modal;\r\n\r\n    // Init form inputs\r\n    var initForm = function () {\r\n        // Init inputmask plugin --- For more info please refer to the official documentation here: https://github.com/RobinHerbots/Inputmask\r\n        Inputmask(\"US$ 9,999,999.99\", {\r\n            \"numericInput\": true\r\n        }).mask(\"#kt_modal_inputmask\");\r\n    }\r\n\r\n    var handleBalanceCalculator = function () {\r\n        // Select elements\r\n        const currentBalance = element.querySelector('[kt-modal-adjust-balance=\"current_balance\"]');\r\n        newBalance = element.querySelector('[kt-modal-adjust-balance=\"new_balance\"]');\r\n        maskInput = document.getElementById('kt_modal_inputmask');\r\n\r\n        // Get current balance value\r\n        let currentValue = parseFloat(currentBalance.innerHTML.replace(/[^0-9.]/g, '').replace(',', ''));\r\n\r\n        // On change event for inputmask\r\n        let maskValue;\r\n        maskInput.addEventListener('focusout', function (e) {\r\n            // Get inputmask value on change\r\n            maskValue = parseFloat(e.target.value.replace(/[^0-9.]/g, '').replace(',', ''));\r\n\r\n            // Set mask value as 0 when NaN detected\r\n            if(isNaN(maskValue)){\r\n                maskValue = 0;\r\n            }\r\n\r\n            // Calculate & set new balance value\r\n            newBalance.innerHTML = 'US$ ' + (maskValue + currentValue).toFixed(2).replace(/\\d(?=(\\d{3})+\\.)/g, '$&,');\r\n        });\r\n    }\r\n\r\n    // Handle form validation and submittion\r\n    var handleForm = function () {\r\n        // Stepper custom navigation\r\n\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'adjustment': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Adjustment type is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    'amount': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Amount is required'\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Revalidate country field. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"adjustment\"]')).on('change', function () {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('adjustment');\r\n        });\r\n\r\n        // Action buttons\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Show loading indication\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable submit button whilst loading\r\n                        submitButton.disabled = true;\r\n\r\n                        // Simulate form submission\r\n                        setTimeout(function () {\r\n                            // Simulate form submission\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            // Show popup confirmation \r\n                            Swal.fire({\r\n                                text: \"Form has been successfully submitted!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n\r\n                                    // Enable submit button after loading\r\n                                    submitButton.disabled = false;\r\n\r\n                                    // Reset form for demo purposes only\r\n                                    form.reset();\r\n                                    newBalance.innerHTML = \"--\";\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        // Show popup warning \r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        cancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        closeButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n    \r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            // Elements\r\n            element = document.querySelector('#kt_modal_adjust_balance');\r\n            modal = new bootstrap.Modal(element);\r\n\r\n            form = element.querySelector('#kt_modal_adjust_balance_form');\r\n            submitButton = form.querySelector('#kt_modal_adjust_balance_submit');\r\n            cancelButton = form.querySelector('#kt_modal_adjust_balance_cancel');\r\n            closeButton = element.querySelector('#kt_modal_adjust_balance_close');\r\n\r\n            initForm();\r\n            handleBalanceCalculator();\r\n            handleForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTModalAdjustBalance.init();\r\n});"], "sourceRoot": ""}