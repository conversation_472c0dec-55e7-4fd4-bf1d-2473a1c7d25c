{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/apps/customers/view/statement.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sFAAsF;AACtF;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sFAAsF;AACtF;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sFAAsF;AACtF;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sFAAsF;AACtF;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/apps/customers/view/statement.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTCustomerViewStatements = function () {\r\n\r\n    // Private functions\r\n    // Init current year datatable\r\n    var initStatementYearCurrent = function () {\r\n        // Define table element\r\n        const id = '#kt_customer_view_statement_table_1';\r\n        var table = document.querySelector(id);\r\n\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[0].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 1st column in table\r\n            dateRow[0].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        var datatable = $(id).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"pageLength\": 10,\r\n            \"lengthChange\": false,\r\n            'columnDefs': [\r\n                { orderable: false, targets: 4 }, // Disable ordering on column 0 (download)\r\n            ]\r\n        });\r\n    }\r\n\r\n    // Init year 2020 datatable\r\n    var initStatementYear2020 = function () {\r\n        // Define table element\r\n        const id = '#kt_customer_view_statement_table_2';\r\n        var table = document.querySelector(id);\r\n\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[0].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 1st column in table\r\n            dateRow[0].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        var datatable = $(id).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"pageLength\": 10,\r\n            \"lengthChange\": false,\r\n            'columnDefs': [\r\n                { orderable: false, targets: 4 }, // Disable ordering on column 0 (download)\r\n            ]\r\n        });\r\n    }\r\n\r\n    // Init year 2019 datatable\r\n    var initStatementYear2019 = function () {\r\n        // Define table element\r\n        const id = '#kt_customer_view_statement_table_3';\r\n        var table = document.querySelector(id);\r\n\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[0].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 1st column in table\r\n            dateRow[0].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        var datatable = $(id).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"pageLength\": 10,\r\n            \"lengthChange\": false,\r\n            'columnDefs': [\r\n                { orderable: false, targets: 4 }, // Disable ordering on column 0 (download)\r\n            ]\r\n        });\r\n    }\r\n\r\n    // Init year 2018 datatable\r\n    var initStatementYear2018 = function () {\r\n        // Define table element\r\n        const id = '#kt_customer_view_statement_table_4';\r\n        var table = document.querySelector(id);\r\n\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[0].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 1st column in table\r\n            dateRow[0].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        var datatable = $(id).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"pageLength\": 10,\r\n            \"lengthChange\": false,\r\n            'columnDefs': [\r\n                { orderable: false, targets: 4 }, // Disable ordering on column 0 (download)\r\n            ]\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initStatementYearCurrent();\r\n            initStatementYear2020();\r\n            initStatementYear2019();\r\n            initStatementYear2018();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTCustomerViewStatements.init();\r\n});"], "sourceRoot": ""}