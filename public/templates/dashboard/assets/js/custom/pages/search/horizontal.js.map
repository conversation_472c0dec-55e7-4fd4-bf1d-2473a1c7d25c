{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/pages/search/horizontal.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,K;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/pages/search/horizontal.js", "sourcesContent": ["\"use strict\";\r\n \r\n// Class definition\r\nvar KTSearchHorizontal = function () {\r\n    // Private functions\r\n    var initAdvancedSearchForm = function () {\r\n       var form = document.querySelector('#kt_advanced_search_form');\r\n\r\n       // Init tags\r\n       var tags = form.querySelector('[name=\"tags\"]');\r\n       new Tagify(tags);\r\n    }\r\n\r\n    var handleAdvancedSearchToggle = function () {\r\n        var link = document.querySelector('#kt_horizontal_search_advanced_link');\r\n\r\n        link.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            \r\n            if (link.innerHTML === \"Advanced Search\") {\r\n                link.innerHTML = \"Hide Advanced Search\";\r\n            } else {\r\n                link.innerHTML = \"Advanced Search\";\r\n            }\r\n        })\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initAdvancedSearchForm();\r\n            handleAdvancedSearchToggle();\r\n        }\r\n    }     \r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTSearchHorizontal.init();\r\n});\r\n"], "sourceRoot": ""}