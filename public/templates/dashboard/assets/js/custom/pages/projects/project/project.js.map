{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/pages/projects/project/project.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,qEAAqE;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA,CAAC,E", "file": "js/custom/pages/projects/project/project.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTProjectOverview = function () {\r\n    // Colors\r\n    var primary = KTUtil.getCssVariableValue('--bs-primary');\r\n    var lightPrimary = KTUtil.getCssVariableValue('--bs-light-primary');\r\n    var success = KTUtil.getCssVariableValue('--bs-success');\r\n    var lightSuccess = KTUtil.getCssVariableValue('--bs-light-success');\r\n    var gray200 = KTUtil.getCssVariableValue('--bs-gray-200');\r\n    var gray500 = KTUtil.getCssVariableValue('--bs-gray-500');\r\n\r\n    // Private functions\r\n    var initChart = function () {        \r\n        // init chart\r\n        var element = document.getElementById(\"project_overview_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var config = {\r\n            type: 'doughnut',\r\n            data: {\r\n                datasets: [{\r\n                    data: [30, 45, 25],\r\n                    backgroundColor: ['#00A3FF', '#50CD89', '#E4E6EF']\r\n                }],\r\n                labels: ['Active', 'Completed', 'Yet to start']\r\n            },\r\n            options: {\r\n                chart: {\r\n                    fontFamily: 'inherit'\r\n                },\r\n                cutoutPercentage: 75,\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                cutout: '75%',\r\n                title: {\r\n                    display: false\r\n                },\r\n                animation: {\r\n                    animateScale: true,\r\n                    animateRotate: true\r\n                },\r\n                tooltips: {\r\n                    enabled: true,\r\n                    intersect: false,\r\n                    mode: 'nearest',\r\n                    bodySpacing: 5,\r\n                    yPadding: 10,\r\n                    xPadding: 10,\r\n                    caretPadding: 0,\r\n                    displayColors: false,\r\n                    backgroundColor: '#20D489',\r\n                    titleFontColor: '#ffffff',\r\n                    cornerRadius: 4,\r\n                    footerSpacing: 0,\r\n                    titleSpacing: 0\r\n                },\r\n                plugins: {\r\n                    legend: {\r\n                        display: false\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var ctx = element.getContext('2d');\r\n        var myDoughnut = new Chart(ctx, config);\r\n    }\r\n\r\n    var initGraph = function () {\r\n        var element = document.getElementById(\"kt_project_overview_graph\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Incomplete',\r\n                data: [70, 70, 80, 80, 75, 75, 75]\r\n            }, {\r\n                name: 'Complete',\r\n                data: [55, 55, 60, 60, 55, 55, 60]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [primary, success]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: gray500,\r\n                        fontSize: '12px'\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: primary,\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: gray500,\r\n                        fontSize: '12px',\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return val + \" tasks\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [lightPrimary, lightSuccess],\r\n            grid: {\r\n                borderColor: gray200,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                //size: 5,\r\n                colors: [lightPrimary, lightSuccess],\r\n                strokeColor: [primary, success],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var initTable = function () {\r\n        var table = document.querySelector('#kt_profile_overview_table');\r\n\r\n        if (!table) {\r\n            return;\r\n        }\r\n\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[1].innerHTML, \"MMM D, YYYY\").format();\r\n            dateRow[1].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        const datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': []\r\n        });\r\n\r\n        // Filter dropdown elements\r\n        const filterOrders = document.getElementById('kt_filter_orders');\r\n        const filterYear = document.getElementById('kt_filter_year');\r\n\r\n        // Filter by order status --- official docs reference: https://datatables.net/reference/api/search()\r\n        filterOrders.addEventListener('change', function (e) {\r\n            datatable.column(3).search(e.target.value).draw();\r\n        });\r\n\r\n        // Filter by date --- official docs reference: https://momentjs.com/docs/\r\n        var minDate;\r\n        var maxDate;\r\n\r\n        filterYear.addEventListener('change', function (e) {\r\n            const value = e.target.value;\r\n            switch (value) {\r\n                case 'thisyear': {\r\n                    minDate = moment().startOf('year').format();\r\n                    maxDate = moment().endOf('year').format();\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'thismonth': {\r\n                    minDate = moment().startOf('month').format();\r\n                    maxDate = moment().endOf('month').format();\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'lastmonth': {\r\n                    minDate = moment().subtract(1, 'months').startOf('month').format();\r\n                    maxDate = moment().subtract(1, 'months').endOf('month').format();\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'last90days': {\r\n                    minDate = moment().subtract(30, 'days').format();\r\n                    maxDate = moment().format();\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                default: {\r\n                    minDate = moment().subtract(100, 'years').startOf('month').format();\r\n                    maxDate = moment().add(1, 'months').endOf('month').format();\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n            }\r\n        });\r\n\r\n        // Date range filter --- offical docs reference: https://datatables.net/examples/plug-ins/range_filtering.html\r\n        $.fn.dataTable.ext.search.push(\r\n            function (settings, data, dataIndex) {\r\n                var min = minDate;\r\n                var max = maxDate;\r\n                var date = parseFloat(moment(data[1]).format()) || 0; // use data for the age column\r\n\r\n                if ((isNaN(min) && isNaN(max)) ||\r\n                    (isNaN(min) && date <= max) ||\r\n                    (min <= date && isNaN(max)) ||\r\n                    (min <= date && date <= max)) {\r\n                    return true;\r\n                }\r\n                return false;\r\n            }\r\n        );\r\n\r\n        // Search --- official docs reference: https://datatables.net/reference/api/search()\r\n        var filterSearch = document.getElementById('kt_filter_search');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initChart();\r\n            initGraph();\r\n            initTable();\r\n        }\r\n    }\r\n}();\r\n\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTProjectOverview.init();\r\n});"], "sourceRoot": ""}