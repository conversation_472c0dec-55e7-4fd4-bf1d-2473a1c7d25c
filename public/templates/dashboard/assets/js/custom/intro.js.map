{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/intro.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/webpack/startup"], "names": [], "mappings": ";;;;;;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;;AAEA,gDAAgD;AAChD,gDAAgD;AAChD,kDAAkD;AAClD,kDAAkD;AAClD,kDAAkD;;AAElD;AACA;AACA;AACA,6CAA6C;;AAE7C,mDAAmD,8CAA8C;;AAEjG,oDAAoD,+CAA+C;;AAEnG;AACA,a;;AAEA;AACA;AACA,6CAA6C;;AAE7C,mDAAmD,8CAA8C;;AAEjG,oDAAoD,+CAA+C;;AAEnG;AACA;;AAEA;AACA;AACA,6CAA6C;;AAE7C,mDAAmD,+CAA+C;;AAElG,oDAAoD,+CAA+C;;AAEnG;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,a;AACA,SAAS,aAAa;;AAEtB;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,a;AACA,SAAS,aAAa;;AAEtB;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,a;AACA,SAAS,aAAa;;AAEtB;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,0C;AACA;AACA,aAAa;AACb,aAAa;AACb;AACA,S;AACA;;AAEA;AACA;AACA;AACA;AACA,S;AACA;AACA,CAAC;;AAED;AACA,IAAI,IAA6B;AACjC;AACA;;AAEA;AACA;AACA;AACA,CAAC;;;;;;;UC5LD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UCtBA;UACA;UACA;UACA", "file": "js/custom/intro.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTIntro = function () {\r\n    // Private functions\r\n    var handleIntro = function(id, calback, timeout) {\r\n        var date = new Date();\r\n\r\n        var currentTime = date.getTime();\r\n\r\n        var time2days = 1000 * 60 * 60 * 24 * 2; // 2 days\r\n        var time7days = 1000 * 60 * 60 * 24 * 7; // 7 days\r\n        var time15days = 1000 * 60 * 60 * 24 * 15; // 15 days\r\n        var time21days = 1000 * 60 * 60 * 24 * 21; // 21 days\r\n        var time30days = 1000 * 60 * 60 * 24 * 30; // 30 days        \r\n\r\n        if (!KTCookie.get(id + '_counter') || parseInt(KTCookie.get(id + '_counter')) < 3) {\r\n            // Initial display\r\n            if (!KTCookie.get(id + '_counter')) {\r\n                setTimeout(calback, timeout); // Display intro in 5 seconds\r\n                \r\n                KTCookie.set(id + '_show_1', '1', {expires: new Date(date.getTime() + time2days)});\r\n\r\n                KTCookie.set(id + '_counter', '1', {expires: new Date(date.getTime() + time30days)});\r\n\r\n                return true;\r\n            } \r\n\r\n            // 2 display\r\n            if (KTCookie.get(id + '_counter') == '1' && !KTCookie.get(id + '_show_1')) {\r\n                setTimeout(calback, timeout); // Display intro in 5 seconds\r\n                \r\n                KTCookie.set(id + '_show_2', '1', {expires: new Date(date.getTime() + time7days)});\r\n                \r\n                KTCookie.set(id + '_counter', '2', {expires: new Date(date.getTime() + time21days)});\r\n\r\n                return true;\r\n            }\r\n\r\n            // 3 display\r\n            if (KTCookie.get(id + '_counter') == '2' && !KTCookie.get(id + '_show_2')) {\r\n                setTimeout(calback, timeout); // Display intro in 5 seconds\r\n                \r\n                KTCookie.set(id + '_show_3', '1', {expires: new Date(date.getTime() + time15days)});\r\n                \r\n                KTCookie.set(id + '_counter', '3', {expires: new Date(date.getTime() + time15days)});\r\n\r\n                return true;\r\n            }\r\n\r\n            return false;\r\n        }\r\n    }\r\n\r\n    var showtIntro1 = function() {\r\n        var element = document.querySelector('#kt_header_search_toggle');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            customClass: 'popover-dark',\r\n            container: 'body',\r\n            trigger: 'manual',\r\n            boundary: 'window',\r\n            placement: 'left',\r\n            dismiss: true,\r\n            html: true,\r\n            title: 'Quick Search',\r\n            content: 'Fully functional search with advance options and preferences setup'\r\n        }\r\n\r\n        // Initialize popover\r\n        var popover = KTApp.initBootstrapPopover(element, options);\r\n        popover.show();\r\n\r\n        // Auto remove\r\n        setTimeout(function() {\r\n            if (popover) {\r\n                popover.dispose();\r\n            } \r\n        }, 1000 * 10); // 10 seconds\r\n\r\n        // Hide popover on element click\r\n        element.addEventListener(\"click\", function(e) {\r\n            popover.dispose();\r\n        });\r\n    }\r\n\r\n    var showtIntro2 = function() {\r\n        var element = document.querySelector('#kt_toolbar_primary_button');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            customClass: 'popover-dark',\r\n            container: 'body',\r\n            boundary: 'window',\r\n            trigger: 'manual',\r\n            placement: 'left',\r\n            dismiss: true,\r\n            html: true,\r\n            title: 'Quick Notifications',\r\n            content: 'Seamless access to updates and notifications in various formats'\r\n        }\r\n\r\n        // Initialize popover\r\n        var popover = KTApp.initBootstrapPopover(element, options);\r\n        popover.show();\r\n\r\n        // Remove\r\n        setTimeout(function() {\r\n            if (popover) {\r\n                popover.dispose();\r\n            } \r\n        }, 1000 * 10); // 10 seconds\r\n\r\n        // Hide popover on element click\r\n        element.addEventListener(\"click\", function(e) {\r\n            popover.dispose();\r\n        });\r\n    }\r\n\r\n    var showtIntro3 = function() {\r\n        var element = document.querySelector('#kt_header_user_menu_toggle');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            customClass: 'popover-dark',\r\n            container: 'body',\r\n            boundary: 'window',\r\n            placement: 'left',\r\n            trigger: 'manual',\r\n            dismiss: true,\r\n            html: true,\r\n            title: 'Advanced User Menu',\r\n            content: 'With quick links to user profile and account settings pages'\r\n        }\r\n\r\n        // Initialize popover\r\n        var popover = KTApp.initBootstrapPopover(element, options);\r\n        popover.show();\r\n\r\n        // Remove\r\n        setTimeout(function() {\r\n            if (popover) {\r\n                popover.dispose();\r\n            } \r\n        }, 1000 * 10); // 10 seconds\r\n\r\n        // Hide popover on element click\r\n        element.addEventListener(\"click\", function(e) {\r\n            popover.dispose();\r\n        });\r\n    }\r\n\r\n    var initIntro = function(product) {\r\n        // Handle intro popovers displays\r\n        if (KTUtil.inIframe() === false) {                \r\n            if (handleIntro('kt_' + product + '_intro_1', showtIntro1, 1000 * 5)) {\r\n            } else if (handleIntro('kt_' + product + '_intro_2', showtIntro2, 1000 * 5)) {\r\n            } else if (handleIntro('kt_' + product + '_intro_3', showtIntro3, 1000 * 5)) {\r\n            }\r\n        } \r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initIntro('metronic');\r\n        }   \r\n    }\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined') {\r\n    module.exports = KTIntro;\r\n}\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTIntro.init();\r\n});\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(\"../../../themes/metronic/html/demo1/src/js/custom/intro.js\");\n"], "sourceRoot": ""}