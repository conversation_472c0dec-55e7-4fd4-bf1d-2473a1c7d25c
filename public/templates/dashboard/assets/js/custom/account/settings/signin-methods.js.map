{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/account/settings/signin-methods.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB,0BAA0B;AAC1B;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;;AAEjB,0BAA0B;AAC1B;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/account/settings/signin-methods.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountSettingsSigninMethods = function () {\r\n    // Private functions\r\n    var initSettings = function () {\r\n\r\n        // UI elements\r\n        var signInMainEl = document.getElementById('kt_signin_email');\r\n        var signInEditEl = document.getElementById('kt_signin_email_edit');\r\n        var passwordMainEl = document.getElementById('kt_signin_password');\r\n        var passwordEditEl = document.getElementById('kt_signin_password_edit');\r\n\r\n        // button elements\r\n        var signInChangeEmail = document.getElementById('kt_signin_email_button');\r\n        var signInCancelEmail = document.getElementById('kt_signin_cancel');\r\n        var passwordChange = document.getElementById('kt_signin_password_button');\r\n        var passwordCancel = document.getElementById('kt_password_cancel');\r\n\r\n        // toggle UI\r\n        signInChangeEmail.querySelector('button').addEventListener('click', function () {\r\n            toggleChangeEmail();\r\n        });\r\n\r\n        signInCancelEmail.addEventListener('click', function () {\r\n            toggleChangeEmail();\r\n        });\r\n\r\n        passwordChange.querySelector('button').addEventListener('click', function () {\r\n            toggleChangePassword();\r\n        });\r\n\r\n        passwordCancel.addEventListener('click', function () {\r\n            toggleChangePassword();\r\n        });\r\n\r\n        var toggleChangeEmail = function () {\r\n            signInMainEl.classList.toggle('d-none');\r\n            signInChangeEmail.classList.toggle('d-none');\r\n            signInEditEl.classList.toggle('d-none');\r\n        }\r\n\r\n        var toggleChangePassword = function () {\r\n            passwordMainEl.classList.toggle('d-none');\r\n            passwordChange.classList.toggle('d-none');\r\n            passwordEditEl.classList.toggle('d-none');\r\n        }\r\n    }\r\n\r\n    var handleChangeEmail = function (e) {\r\n        var validation;\r\n\r\n        // form elements\r\n        var signInForm = document.getElementById('kt_signin_change_email');\r\n\r\n        validation = FormValidation.formValidation(\r\n            signInForm,\r\n            {\r\n                fields: {\r\n                    emailaddress: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Email is required'\r\n                            },\r\n                            emailAddress: {\r\n                                message: 'The value is not a valid email address'\r\n                            }\r\n                        }\r\n                    },\r\n\r\n                    confirmemailpassword: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Password is required'\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n\r\n                plugins: { //Learn more: https://formvalidation.io/guide/plugins\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row'\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        signInForm.querySelector('#kt_signin_submit').addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            console.log('click');\r\n\r\n            validation.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n                    swal.fire({\r\n                        text: \"Sent password reset. Please check your email\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn font-weight-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n                } else {\r\n                    swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn font-weight-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    var handleChangePassword = function (e) {\r\n        var validation;\r\n\r\n        // form elements\r\n        var passwordForm = document.getElementById('kt_signin_change_password');\r\n\r\n        validation = FormValidation.formValidation(\r\n            passwordForm,\r\n            {\r\n                fields: {\r\n                    currentpassword: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Current Password is required'\r\n                            }\r\n                        }\r\n                    },\r\n\r\n                    newpassword: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'New Password is required'\r\n                            }\r\n                        }\r\n                    },\r\n\r\n                    confirmpassword: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Confirm Password is required'\r\n                            },\r\n                            identical: {\r\n                                compare: function() {\r\n                                    return passwordForm.querySelector('[name=\"newpassword\"]').value;\r\n                                },\r\n                                message: 'The password and its confirm are not the same'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n\r\n                plugins: { //Learn more: https://formvalidation.io/guide/plugins\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row'\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        passwordForm.querySelector('#kt_password_submit').addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            console.log('click');\r\n\r\n            validation.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n                    swal.fire({\r\n                        text: \"Sent password reset. Please check your email\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn font-weight-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n                } else {\r\n                    swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn font-weight-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initSettings();\r\n            handleChangeEmail();\r\n            handleChangePassword();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountSettingsSigninMethods.init();\r\n});\r\n"], "sourceRoot": ""}