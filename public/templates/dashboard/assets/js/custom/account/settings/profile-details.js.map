{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/account/settings/profile-details.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/account/settings/profile-details.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountSettingsProfileDetails = function () {\r\n    // Private variables\r\n    var form;\r\n    var submitButton;\r\n    var validation;\r\n\r\n    // Private functions\r\n    var initValidation = function () {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validation = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    fname: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'First name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    lname: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Last name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    company: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Company name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    phone: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Contact phone number is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    country: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select a country'\r\n                            }\r\n                        }\r\n                    },\r\n                    timezone: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select a timezone'\r\n                            }\r\n                        }\r\n                    },\r\n                    'communication[]': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select at least one communication method'\r\n                            }\r\n                        }\r\n                    },\r\n                    language: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select a language'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    submitButton: new FormValidation.plugins.SubmitButton(),\r\n                    //defaultSubmit: new FormValidation.plugins.DefaultSubmit(), // Uncomment this line to enable normal button submit after form validation\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Select2 validation integration\r\n        $(form.querySelector('[name=\"country\"]')).on('change', function() {\r\n            // Revalidate the color field when an option is chosen\r\n            validation.revalidateField('country');\r\n        });\r\n\r\n        $(form.querySelector('[name=\"language\"]')).on('change', function() {\r\n            // Revalidate the color field when an option is chosen\r\n            validation.revalidateField('language');\r\n        });\r\n\r\n        $(form.querySelector('[name=\"timezone\"]')).on('change', function() {\r\n            // Revalidate the color field when an option is chosen\r\n            validation.revalidateField('timezone');\r\n        });\r\n    }\r\n\r\n    var handleForm = function () {\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            validation.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n\r\n                    swal.fire({\r\n                        text: \"Thank you! You've updated your basic info\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n\r\n                } else {\r\n                    swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-light-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            form = document.getElementById('kt_account_profile_details_form');\r\n            submitButton = form.querySelector('#kt_account_profile_details_submit');\r\n\r\n            initValidation();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountSettingsProfileDetails.init();\r\n});\r\n"], "sourceRoot": ""}