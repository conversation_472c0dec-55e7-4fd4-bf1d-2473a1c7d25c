{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/modals/new-target.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;;AAER,uBAAuB;AACvB,OAAO,Q;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,kBAAkB;AAClB,kBAAkB;AAClB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI;AACJ,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/modals/new-target.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalNewTarget = function () {\r\n\tvar submitButton;\r\n\tvar cancelButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar modal;\r\n\tvar modalEl;\r\n\r\n\t// Init form inputs\r\n\tvar initForm = function() {\r\n\t\t// Tags. For more info, please visit the official plugin site: https://yaireo.github.io/tagify/\r\n\t\tvar tags = new Tagify(form.querySelector('[name=\"tags\"]'), {\r\n\t\t\twhitelist: [\"Important\", \"Urgent\", \"High\", \"Medium\", \"Low\"],\r\n\t\t\tmaxTags: 5,\r\n\t\t\tdropdown: {\r\n\t\t\t\tmaxItems: 10,           // <- mixumum allowed rendered suggestions\r\n\t\t\t\tenabled: 0,             // <- show suggestions on focus\r\n\t\t\t\tcloseOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n\t\t\t}\r\n\t\t});\r\n\t\ttags.on(\"change\", function(){\r\n\t\t\t// Revalidate the field when an option is chosen\r\n            validator.revalidateField('tags');\r\n\t\t});\r\n\r\n\t\t// Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar dueDate = $(form.querySelector('[name=\"due_date\"]'));\r\n\t\tdueDate.flatpickr({\r\n\t\t\tenableTime: true,\r\n\t\t\tdateFormat: \"d, M Y, H:i\",\r\n\t\t});\r\n\r\n\t\t// Team assign. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"team_assign\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('team_assign');\r\n        });\r\n\t}\r\n\r\n\t// Handle form validation and submittion\r\n\tvar handleForm = function() {\r\n\t\t// Stepper custom navigation\r\n\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\ttarget_title: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target title is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\ttarget_assign: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target assign is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\ttarget_due_date: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target due date is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\ttarget_tags: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target tags are required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'targets_notifications[]': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select at least one communication method'\r\n                            }\r\n                        }\r\n                    },\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Action buttons\r\n\t\tsubmitButton.addEventListener('click', function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\tsubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\t\tsubmitButton.disabled = true;\r\n\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tsubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tsubmitButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Show success message. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\t\ttext: \"Form has been successfully submitted!\",\r\n\t\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\t\tif (result.isConfirmed) {\r\n\t\t\t\t\t\t\t\t\tmodal.hide();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t//form.submit(); // Submit form\r\n\t\t\t\t\t\t}, 2000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Show error message.\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tcancelButton.addEventListener('click', function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tSwal.fire({\r\n\t\t\t\ttext: \"Are you sure you would like to cancel?\",\r\n\t\t\t\ticon: \"warning\",\r\n\t\t\t\tshowCancelButton: true,\r\n\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\tconfirmButtonText: \"Yes, cancel it!\",\r\n\t\t\t\tcancelButtonText: \"No, return\",\r\n\t\t\t\tcustomClass: {\r\n\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\tcancelButton: \"btn btn-active-light\"\r\n\t\t\t\t}\r\n\t\t\t}).then(function (result) {\r\n\t\t\t\tif (result.value) {\r\n\t\t\t\t\tform.reset(); // Reset form\t\r\n\t\t\t\t\tmodal.hide(); // Hide modal\t\t\t\t\r\n\t\t\t\t} else if (result.dismiss === 'cancel') {\r\n\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\ttext: \"Your form has not been cancelled!.\",\r\n\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\t// Elements\r\n\t\t\tmodalEl = document.querySelector('#kt_modal_new_target');\r\n\r\n\t\t\tif (!modalEl) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tmodal = new bootstrap.Modal(modalEl);\r\n\r\n\t\t\tform = document.querySelector('#kt_modal_new_target_form');\r\n\t\t\tsubmitButton = document.getElementById('kt_modal_new_target_submit');\r\n\t\t\tcancelButton = document.getElementById('kt_modal_new_target_cancel');\r\n\r\n\t\t\tinitForm();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tKTModalNewTarget.init();\r\n});"], "sourceRoot": ""}