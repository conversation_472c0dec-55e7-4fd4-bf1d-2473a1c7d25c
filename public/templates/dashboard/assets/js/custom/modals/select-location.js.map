{"version": 3, "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/custom/modals/select-location.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA,8BAA8B,EAAE,yBAAyB,EAAE,EAAE,EAAE,EAAE,EAAE;AACnE,gCAAgC;AAChC,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,oBAAoB,2DAA2D,qBAAqB;;AAE7I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,iB;AACA,aAAa;AACb;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/modals/select-location.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalSelectLocation = function () {\r\n    // Private variables\r\n    var locationSelectTarget;\r\n    var locationSelectButton;\r\n\r\n    var modal;\r\n    var selectedlocation = '';\r\n    var mapInitialized = false;\r\n\r\n    // Private functions\r\n    var initMap = function() {\r\n        // Check if Leaflet is included\r\n        if (!L) {\r\n            return;\r\n        }\r\n\r\n        // Define Map Location\r\n        var leaflet = L.map('kt_modal_select_location_map', {\r\n            center: [40.725, -73.985],\r\n            zoom: 30\r\n        });\r\n\r\n        // Init Leaflet Map. For more info check the plugin's documentation: https://leafletjs.com/\r\n        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n            attribution: '&copy; <a href=\"https://osm.org/copyright\">OpenStreetMap</a> contributors'\r\n        }).addTo(leaflet);\r\n\r\n        // Set Geocoding\r\n        var geocodeService;\r\n        if (typeof L.esri.Geocoding === 'undefined') {\r\n            geocodeService = L.esri.geocodeService();\r\n        } else {\r\n            geocodeService = L.esri.Geocoding.geocodeService();\r\n        }\r\n\r\n        // Define Marker Layer\r\n        var markerLayer = L.layerGroup().addTo(leaflet);\r\n\r\n        // Set Custom SVG icon marker\r\n        var leafletIcon = L.divIcon({\r\n            html: `<span class=\"svg-icon svg-icon-danger svg-icon-3x\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\"><g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\"><rect x=\"0\" y=\"24\" width=\"24\" height=\"0\"/><path d=\"M5,10.5 C5,6 8,3 12.5,3 C17,3 20,6.75 20,10.5 C20,12.8325623 17.8236613,16.03566 13.470984,20.1092932 C12.9154018,20.6292577 12.0585054,20.6508331 11.4774555,20.1594925 C7.15915182,16.5078313 5,13.2880005 5,10.5 Z M12.5,12 C13.8807119,12 15,10.8807119 15,9.5 C15,8.11928813 13.8807119,7 12.5,7 C11.1192881,7 10,8.11928813 10,9.5 C10,10.8807119 11.1192881,12 12.5,12 Z\" fill=\"#000000\" fill-rule=\"nonzero\"/></g></svg></span>`,\r\n            bgPos: [10, 10],\r\n            iconAnchor: [20, 37],\r\n            popupAnchor: [0, -37],\r\n            className: 'leaflet-marker'\r\n        });\r\n\r\n        // Map onClick Action\r\n        leaflet.on('click', function (e) {\r\n            geocodeService.reverse().latlng(e.latlng).run(function (error, result) {\r\n                if (error) {\r\n                    return;\r\n                }\r\n                markerLayer.clearLayers();\r\n                selectedlocation = result.address.Match_addr;\r\n                L.marker(result.latlng, { icon: leafletIcon }).addTo(markerLayer).bindPopup(result.address.Match_addr, { closeButton: false }).openPopup();\r\n\r\n                // Show popup confirmation. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    html: '<div class=\"mb-2\">Your selected - <b>\"' + selectedlocation + '\"</b>.</div>' + 'Click on the \"Apply\" button to select this location.',\r\n                    icon: \"success\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    // Confirmed\r\n                });\r\n            });\r\n        });\r\n    }\r\n\r\n    var handleSelection = function() {\r\n        locationSelectButton.addEventListener('click', function() {\r\n            if (locationSelectTarget) {\r\n                if (locationSelectTarget.value) {\r\n                    locationSelectTarget.value = selectedlocation;\r\n                } else {\r\n                    locationSelectTarget.innerHTML = selectedlocation;\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            // Elements\r\n\t\t\tmodal = document.querySelector('#kt_modal_select_location');\r\n\r\n\t\t\tif (!modal) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n            \r\n            locationSelectTarget = document.querySelector('#kt_modal_select_location_target');\r\n            locationSelectButton = document.querySelector('#kt_modal_select_location_button');\r\n\r\n            handleSelection();\r\n            \r\n            modal.addEventListener('shown.bs.modal', function () {\r\n                if (!mapInitialized) {\r\n                    initMap();\r\n                    mapInitialized = true;\r\n                }                \r\n            });\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTModalSelectLocation.init();\r\n});\r\n"], "sourceRoot": ""}