{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/vis-timeline/interaction.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,mCAAmC;AACtD,SAAS;;AAET;AACA;AACA,aAAa,yDAAyD;AACtE,aAAa,gDAAgD;AAC7D,aAAa,gDAAgD;AAC7D,aAAa,mEAAmE;AAChF,aAAa,+DAA+D;AAC5E,aAAa,gDAAgD;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/vis-timeline/interaction.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTVisTimelineInteraction = function () {\r\n    // Private functions\r\n    var exampleInteraction = function () {\r\n        // create a dataset with items\r\n        // we specify the type of the fields `start` and `end` here to be strings\r\n        // containing an ISO date. The fields will be outputted as ISO dates\r\n        // automatically getting data from the DataSet via items.get().\r\n        var items = new vis.DataSet({\r\n            type: { start: \"ISODate\", end: \"ISODate\" },\r\n        });\r\n\r\n        // add items to the DataSet\r\n        items.add([\r\n            { id: 1, content: \"item 1<br>start\", start: \"2021-01-23\" },\r\n            { id: 2, content: \"item 2\", start: \"2021-01-18\" },\r\n            { id: 3, content: \"item 3\", start: \"2021-01-21\" },\r\n            { id: 4, content: \"item 4\", start: \"2021-01-19\", end: \"2021-01-24\" },\r\n            { id: 5, content: \"item 5\", start: \"2021-01-28\", type: \"point\" },\r\n            { id: 6, content: \"item 6\", start: \"2021-01-26\" },\r\n        ]);\r\n\r\n        var container = document.getElementById(\"kt_docs_vistimeline_interaction\");\r\n        var options = {\r\n            start: \"2021-01-10\",\r\n            end: \"2021-02-10\",\r\n            editable: true,\r\n            showCurrentTime: true,\r\n        };\r\n\r\n        var timeline = new vis.Timeline(container, items, options);\r\n\r\n        document.getElementById(\"window1\").onclick = function () {\r\n            timeline.setWindow(\"2021-01-01\", \"2021-04-01\");\r\n        };\r\n        document.getElementById(\"fit\").onclick = function () {\r\n            timeline.fit();\r\n        };\r\n        document.getElementById(\"select\").onclick = function () {\r\n            timeline.setSelection([5, 6], {\r\n                focus: true,\r\n            });\r\n        };\r\n        document.getElementById(\"focus1\").onclick = function () {\r\n            timeline.focus(2);\r\n        };\r\n        document.getElementById(\"moveTo\").onclick = function () {\r\n            timeline.moveTo(\"2021-02-01\");\r\n        };\r\n\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleInteraction();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTVisTimelineInteraction.init();\r\n});\r\n"], "sourceRoot": ""}