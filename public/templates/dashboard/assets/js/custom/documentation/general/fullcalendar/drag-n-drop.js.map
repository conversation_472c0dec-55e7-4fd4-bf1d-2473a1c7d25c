{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/drag-n-drop.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/fullcalendar/drag-n-drop.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarDragDemos = function () {\r\n    // Private functions\r\n\r\n    var exampleDrag = function () {\r\n        // Initialize the external events -- for more info please visit the official site: https://fullcalendar.io/demos\r\n        var containerEl = document.getElementById('kt_docs_fullcalendar_events_list');\r\n        new FullCalendar.Draggable(containerEl, {\r\n          itemSelector: '.fc-event',\r\n          eventData: function(eventEl) {\r\n            return {\r\n              title: eventEl.innerText.trim()\r\n            }\r\n          }\r\n        });\r\n    \r\n        // initialize the calendar -- for more info please visit the official site: https://fullcalendar.io/demos\r\n        var calendarEl = document.getElementById('kt_docs_fullcalendar_drag');\r\n        var calendar = new FullCalendar.Calendar(calendarEl, {\r\n          headerToolbar: {\r\n            left: 'prev,next today',\r\n            center: 'title',\r\n            right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\r\n          },\r\n          editable: true,\r\n          droppable: true, // this allows things to be dropped onto the calendar\r\n          drop: function(arg) {\r\n            // is the \"remove after drop\" checkbox checked?\r\n            if (document.getElementById('drop-remove').checked) {\r\n              // if so, remove the element from the \"Draggable Events\" list\r\n              arg.draggedEl.parentNode.removeChild(arg.draggedEl);\r\n            }\r\n          }\r\n        });\r\n\r\n        calendar.render();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleDrag();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralFullCalendarDragDemos.init();\r\n});\r\n"], "sourceRoot": ""}