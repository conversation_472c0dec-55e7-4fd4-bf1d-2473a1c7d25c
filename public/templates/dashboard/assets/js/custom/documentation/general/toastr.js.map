{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/toastr.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;;AAEA,8DAA8D;AAC9D;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,0CAA0C,cAAc;AACxD,iBAAiB;AACjB;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/toastr.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralToastr = function() {\r\n    // Private functions\r\n    var example1 = function() {\r\n        var i = -1;\r\n        var toastCount = 0;\r\n        var $toastlast;\r\n\r\n        var getMessage = function () {\r\n            var msgs = [\r\n                'New order has been placed!',\r\n                'Are you the six fingered man?',\r\n                'Inconceivable!',\r\n                'I do not think that means what you think it means.',\r\n                'Have fun storming the castle!'\r\n            ];\r\n            i++;\r\n            if (i === msgs.length) {\r\n                i = 0;\r\n            }\r\n\r\n            return msgs[i];\r\n        };\r\n\r\n        var getMessageWithClearButton = function (msg) {\r\n            msg = msg ? msg : 'Clear itself?';\r\n            msg += '<br /><br /><button type=\"button\" class=\"btn btn-outline-light btn-sm\">Yes</button>';\r\n            return msg;\r\n        };\r\n\r\n        $('#showtoast').on('click', function () {\r\n            var shortCutFunction = $(\"#toastTypeGroup input:radio:checked\").val();\r\n            var msg = $('#message').val();\r\n            var title = $('#title').val() || '';\r\n            var $showDuration = $('#showDuration');\r\n            var $hideDuration = $('#hideDuration');\r\n            var $timeOut = $('#timeOut');\r\n            var $extendedTimeOut = $('#extendedTimeOut');\r\n            var $showEasing = $('#showEasing');\r\n            var $hideEasing = $('#hideEasing');\r\n            var $showMethod = $('#showMethod');\r\n            var $hideMethod = $('#hideMethod');\r\n            var toastIndex = toastCount++;\r\n            var addClear = $('#addClear').prop('checked');\r\n\r\n            toastr.options = {\r\n                closeButton: $('#closeButton').prop('checked'),\r\n                debug: $('#debugInfo').prop('checked'),\r\n                newestOnTop: $('#newestOnTop').prop('checked'),\r\n                progressBar: $('#progressBar').prop('checked'),\r\n                positionClass: $('#positionGroup input:radio:checked').val() || 'toast-top-right',\r\n                preventDuplicates: $('#preventDuplicates').prop('checked'),\r\n                onclick: null\r\n            };\r\n\r\n            if ($('#addBehaviorOnToastClick').prop('checked')) {\r\n                toastr.options.onclick = function () {\r\n                    alert('You can perform some custom action after a toast goes away');\r\n                };\r\n            }\r\n\r\n            if ($showDuration.val().length) {\r\n                toastr.options.showDuration = $showDuration.val();\r\n            }\r\n\r\n            if ($hideDuration.val().length) {\r\n                toastr.options.hideDuration = $hideDuration.val();\r\n            }\r\n\r\n            if ($timeOut.val().length) {\r\n                toastr.options.timeOut = addClear ? 0 : $timeOut.val();\r\n            }\r\n\r\n            if ($extendedTimeOut.val().length) {\r\n                toastr.options.extendedTimeOut = addClear ? 0 : $extendedTimeOut.val();\r\n            }\r\n\r\n            if ($showEasing.val().length) {\r\n                toastr.options.showEasing = $showEasing.val();\r\n            }\r\n\r\n            if ($hideEasing.val().length) {\r\n                toastr.options.hideEasing = $hideEasing.val();\r\n            }\r\n\r\n            if ($showMethod.val().length) {\r\n                toastr.options.showMethod = $showMethod.val();\r\n            }\r\n\r\n            if ($hideMethod.val().length) {\r\n                toastr.options.hideMethod = $hideMethod.val();\r\n            }\r\n\r\n            if (addClear) {\r\n                msg = getMessageWithClearButton(msg);\r\n                toastr.options.tapToDismiss = false;\r\n            }\r\n            if (!msg) {\r\n                msg = getMessage();\r\n            }\r\n\r\n            $('#toastrOptions').text(\r\n                    'toastr.options = '\r\n                    + JSON.stringify(toastr.options, null, 2)\r\n                    + ';'\r\n                    + '\\n\\ntoastr.'\r\n                    + shortCutFunction\r\n                    + '(\"'\r\n                    + msg\r\n                    + (title ? '\", \"' + title : '')\r\n                    + '\");'\r\n            );\r\n\r\n            var $toast = toastr[shortCutFunction](msg, title); // Wire up an event handler to a button in the toast, if it exists\r\n            $toastlast = $toast;\r\n\r\n            if(typeof $toast === 'undefined'){\r\n                return;\r\n            }\r\n\r\n            if ($toast.find('#okBtn').length) {\r\n                $toast.delegate('#okBtn', 'click', function () {\r\n                    alert('you clicked me. i was toast #' + toastIndex + '. goodbye!');\r\n                    $toast.remove();\r\n                });\r\n            }\r\n            if ($toast.find('#surpriseBtn').length) {\r\n                $toast.delegate('#surpriseBtn', 'click', function () {\r\n                    alert('Surprise! you clicked me. i was toast #' + toastIndex + '. You could perform an action here.');\r\n                });\r\n            }\r\n            if ($toast.find('.clear').length) {\r\n                $toast.delegate('.clear', 'click', function () {\r\n                    toastr.clear($toast, { force: true });\r\n                });\r\n            }\r\n        });\r\n\r\n        function getLastToast(){\r\n            return $toastlast;\r\n        }\r\n        $('#clearlasttoast').on('click', function () {\r\n            toastr.clear(getLastToast());\r\n        });\r\n        $('#cleartoasts').on('click', function () {\r\n            toastr.clear();\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            example1();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralToastr.init();\r\n});\r\n"], "sourceRoot": ""}