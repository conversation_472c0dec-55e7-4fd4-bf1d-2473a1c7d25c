{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/datatables/advanced.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,uCAAuC;AACvD,gBAAgB,wCAAwC;AACxD,gBAAgB,wCAAwC;AACxD,gBAAgB,uCAAuC;AACvD,gBAAgB,iCAAiC;AACjD,gBAAgB,qCAAqC;AACrD,gBAAgB,uCAAuC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA,iCAAiC,iBAAiB;AAClD;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,yB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/general/datatables/advanced.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesAdvanced = function () {\r\n    // Private functions\r\n\r\n    var _initExample1 = function() {\r\n        var status = {\r\n            1: {\"title\": \"Pending\", \"state\": \"primary\"},\r\n            2: {\"title\": \"Delivered\", \"state\": \"danger\"},\r\n            3: {\"title\": \"Canceled\", \"state\": \"primary\"},\r\n            4: {\"title\": \"Success\", \"state\": \"success\"},\r\n            5: {\"title\": \"Info\", \"state\": \"info\"},\r\n            6: {\"title\": \"Danger\", \"state\": \"danger\"},\r\n            7: {\"title\": \"Warning\", \"state\": \"warning\"},\r\n        };\r\n\r\n        $(\"#kt_datatable_example_1\").DataTable({\r\n            \"columnDefs\": [\r\n                {\r\n                    // The `data` parameter refers to the data for the cell (defined by the\r\n                    // `data` option, which defaults to the column being worked with, in\r\n                    // this case `data: 0`.\r\n                    \"render\": function ( data, type, row ) {\r\n                        var index = KTUtil.getRandomInt(1, 7);\r\n\r\n                        return data + '<span class=\"ms-2 badge badge-light-' + status[index]['state'] + ' fw-bold\">' + status[index]['title'] + '</span>';\r\n                    },\r\n                    \"targets\": 1\r\n                }\r\n            ]\r\n        });\r\n    }\r\n\r\n    var _initExample2 = function() {\r\n        $(\"#kt_datatable_example_2\").DataTable({\r\n            \"columnDefs\": [ {\r\n                \"visible\": false,\r\n                \"targets\": -1\r\n            }]\r\n        });\r\n    }\r\n\r\n    var _initExample3 = function() {\r\n        var groupColumn = 2;\r\n\r\n        var table = $('#kt_datatable_example_3').DataTable({\r\n            \"columnDefs\": [{\r\n                \"visible\": false,\r\n                \"targets\": groupColumn\r\n            }],\r\n            \"order\": [\r\n                [groupColumn, 'asc']\r\n            ],\r\n            \"displayLength\": 25,\r\n            \"drawCallback\": function(settings) {\r\n                var api = this.api();\r\n                var rows = api.rows({\r\n                    page: 'current'\r\n                }).nodes();\r\n                var last = null;\r\n\r\n                api.column(groupColumn, {\r\n                    page: 'current'\r\n                }).data().each(function(group, i) {\r\n                    if (last !== group) {\r\n                        $(rows).eq(i).before(\r\n                            '<tr class=\"group fs-5 fw-bolder\"><td colspan=\"5\">' + group + '</td></tr>'\r\n                        );\r\n\r\n                        last = group;\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        // Order by the grouping\r\n        $('#kt_datatable_example_3 tbody').on('click', 'tr.group', function() {\r\n            var currentOrder = table.order()[0];\r\n            if (currentOrder[0] === groupColumn && currentOrder[1] === 'asc') {\r\n                table.order([groupColumn, 'desc']).draw();\r\n            } else {\r\n                table.order([groupColumn, 'asc']).draw();\r\n            }\r\n        });\r\n    }\r\n\r\n    var _initExample4 = function() {\r\n        $(\"#kt_datatable_example_4\").DataTable({\r\n            \"footerCallback\": function ( row, data, start, end, display ) {\r\n                var api = this.api(), data;\r\n     \r\n                // Remove the formatting to get integer data for summation\r\n                var intVal = function ( i ) {\r\n                    return typeof i === \"string\" ?\r\n                        i.replace(/[\\$,]/g, \"\")*1 :\r\n                        typeof i === \"number\" ?\r\n                            i : 0;\r\n                };\r\n     \r\n                // Total over all pages\r\n                var total = api\r\n                    .column( 4 )\r\n                    .data()\r\n                    .reduce( function (a, b) {\r\n                        return intVal(a) + intVal(b);\r\n                    }, 0 );\r\n     \r\n                // Total over this page\r\n                var pageTotal = api\r\n                    .column( 4, { page: \"current\"} )\r\n                    .data()\r\n                    .reduce( function (a, b) {\r\n                        return intVal(a) + intVal(b);\r\n                    }, 0 );\r\n     \r\n                // Update footer\r\n                $( api.column( 4 ).footer() ).html(\r\n                    \"$\"+pageTotal +\" ( $\"+ total +\" total)\"\r\n                );\r\n            }\r\n        });\r\n    }\r\n\r\n    var _initExample5 = function() {\r\n        $(\"#kt_datatable_example_5\").DataTable({\r\n            \"language\": {\t\t\r\n                \"lengthMenu\": \"Show _MENU_\",\r\n            },\r\n            \"dom\": \r\n                \"<'row'\" +\r\n                \"<'col-sm-6 d-flex align-items-center justify-conten-start'l>\" +\r\n                \"<'col-sm-6 d-flex align-items-center justify-content-end'f>\" +\r\n                \">\" +\r\n                \r\n                \"<'table-responsive'tr>\" +\r\n                \r\n                \"<'row'\" + \r\n                \"<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>\" + \r\n                \"<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>\" +\r\n                \">\"\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            _initExample1();\r\n            _initExample2();\r\n            _initExample3();\r\n            _initExample4();\r\n            _initExample5();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDatatablesAdvanced.init();\r\n});"], "sourceRoot": ""}