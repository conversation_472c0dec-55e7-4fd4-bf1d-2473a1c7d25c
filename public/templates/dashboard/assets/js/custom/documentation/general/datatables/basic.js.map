{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/datatables/basic.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT,K;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/general/datatables/basic.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesBasic = function () {\r\n    // Private functions\r\n\r\n    var _initExample1 = function() {\r\n        $(\"#kt_datatable_example_1\").DataTable();\r\n    }\r\n\r\n    var _initExample2 = function() {\r\n        $(\"#kt_datatable_example_2\").DataTable({\r\n            \"scrollY\":        \"500px\",\r\n            \"scrollCollapse\": true,\r\n            \"paging\":         false,\r\n            \"dom\":   \"<'table-responsive'tr>\" \r\n        });\r\n    }\r\n\r\n    var _initExample3 = function() {\r\n        $(\"#kt_datatable_example_3\").DataTable({\r\n            \"scrollX\": true\r\n        });\r\n    }\r\n\r\n    var _initExample4 = function() {\r\n        $(\"#kt_datatable_example_4\").DataTable({\r\n            \"scrollY\": 300,\r\n            \"scrollX\": true\r\n        });\r\n    }  \r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            _initExample1();\r\n            _initExample2();\r\n            _initExample3();\r\n            _initExample4();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDatatablesBasic.init();\r\n});"], "sourceRoot": ""}