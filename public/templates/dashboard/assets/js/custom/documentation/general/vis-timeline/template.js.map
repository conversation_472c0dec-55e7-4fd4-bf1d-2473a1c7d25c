{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/vis-timeline/template.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,kBAAkB,WAAW;AAC7B;AACA,cAAc,UAAU,KAAK;AAC7B;AACA,kBAAkB,WAAW;AAC7B;AACA;AACA;AACA;AACA,6DAA6D,OAAO;AACpE;AACA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,6DAA6D,OAAO;AACpE;AACA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/vis-timeline/template.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTVisTimelineTemplate = function () {\r\n    // Template data --- handlebars is used as the template for this demo. For more info: https://handlebarsjs.com/\r\n    const data = `<table class=\"score\">\r\n        <tr>\r\n            <td colspan=\"3\" class=\"description\">\r\n                {{ description }}\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>{{ player1 }}</td>\r\n        <th>\r\n            {{ score1 }} - {{ score2 }}\r\n        </th>\r\n            <td>{{ player2 }}</td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n                <img\r\n                src=\"https://flagpedia.net/data/flags/mini/{{abbr1}}.png\"\r\n                width=\"31\"\r\n                height=\"20\"\r\n                alt=\"{{abbr1}}\"\r\n                />\r\n            </td>\r\n        <th></th>\r\n            <td>\r\n                <img\r\n                src=\"https://flagpedia.net/data/flags/mini/{{abbr2}}.png\"\r\n                width=\"31\"\r\n                height=\"20\"\r\n                alt=\"{{abbr2}}\"\r\n                />\r\n            </td>\r\n        </tr>\r\n    </table>`;\r\n\r\n    // Private functions\r\n    var exampleTemplate = function () {\r\n        // create a handlebars template --- For more info: https://handlebarsjs.com/\r\n        var template = Handlebars.compile(data);\r\n\r\n        // DOM element where the Timeline will be attached\r\n        var container = document.getElementById(\"kt_docs_vistimeline_template\");\r\n\r\n        // Create a DataSet (allows two way data-binding)\r\n        var items = new vis.DataSet([\r\n            // round of 16\r\n            {\r\n                player1: \"Brazil\",\r\n                abbr1: \"br\",\r\n                score1: \"1 (3)\",\r\n                player2: \"Chile\",\r\n                abbr2: \"cl\",\r\n                score2: \"1 (2)\",\r\n                description: \"round of 16\",\r\n                start: \"2014-06-28T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Colombia\",\r\n                abbr1: \"co\",\r\n                score1: 2,\r\n                player2: \"Uruguay\",\r\n                abbr2: \"uy\",\r\n                score2: 0,\r\n                description: \"round of 16\",\r\n                start: \"2014-06-28T17:00:00\",\r\n            },\r\n            {\r\n                player1: \"Netherlands\",\r\n                abbr1: \"nl\",\r\n                score1: 2,\r\n                player2: \"Mexico\",\r\n                abbr2: \"mx\",\r\n                score2: 1,\r\n                description: \"round of 16\",\r\n                start: \"2014-06-29T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Costa Rica\",\r\n                abbr1: \"cr\",\r\n                score1: \"1 (5)\",\r\n                player2: \"Greece\",\r\n                abbr2: \"gr\",\r\n                score2: \"1 (3)\",\r\n                description: \"round of 16\",\r\n                start: \"2014-06-29T17:00:00\",\r\n            },\r\n            {\r\n                player1: \"France\",\r\n                abbr1: \"fr\",\r\n                score1: 2,\r\n                player2: \"Nigeria\",\r\n                abbr2: \"ng\",\r\n                score2: 0,\r\n                description: \"round of 16\",\r\n                start: \"2014-06-30T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Germany\",\r\n                abbr1: \"de\",\r\n                score1: 2,\r\n                player2: \"Algeria\",\r\n                abbr2: \"dz\",\r\n                score2: 1,\r\n                description: \"round of 16\",\r\n                start: \"2014-06-30T17:00:00\",\r\n            },\r\n            {\r\n                player1: \"Argentina\",\r\n                abbr1: \"ar\",\r\n                score1: 1,\r\n                player2: \"Switzerland\",\r\n                abbr2: \"ch\",\r\n                score2: 0,\r\n                description: \"round of 16\",\r\n                start: \"2014-07-01T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Belgium\",\r\n                abbr1: \"be\",\r\n                score1: 2,\r\n                player2: \"USA\",\r\n                abbr2: \"us\",\r\n                score2: 1,\r\n                description: \"round of 16\",\r\n                start: \"2014-07-01T17:00:00\",\r\n            },\r\n\r\n            // quarter-finals\r\n            {\r\n                player1: \"France\",\r\n                abbr1: \"fr\",\r\n                score1: 0,\r\n                player2: \"Germany\",\r\n                abbr2: \"de\",\r\n                score2: 1,\r\n                description: \"quarter-finals\",\r\n                start: \"2014-07-04T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Brazil\",\r\n                abbr1: \"br\",\r\n                score1: 2,\r\n                player2: \"Colombia\",\r\n                abbr2: \"co\",\r\n                score2: 1,\r\n                description: \"quarter-finals\",\r\n                start: \"2014-07-04T17:00:00\",\r\n            },\r\n            {\r\n                player1: \"Argentina\",\r\n                abbr1: \"ar\",\r\n                score1: 1,\r\n                player2: \"Belgium\",\r\n                abbr2: \"be\",\r\n                score2: 0,\r\n                description: \"quarter-finals\",\r\n                start: \"2014-07-05T13:00:00\",\r\n            },\r\n            {\r\n                player1: \"Netherlands\",\r\n                abbr1: \"nl\",\r\n                score1: \"0 (4)\",\r\n                player2: \"Costa Rica\",\r\n                abbr2: \"cr\",\r\n                score2: \"0 (3)\",\r\n                description: \"quarter-finals\",\r\n                start: \"2014-07-05T17:00:00\",\r\n            },\r\n\r\n            // semi-finals\r\n            {\r\n                player1: \"Brazil\",\r\n                abbr1: \"br\",\r\n                score1: 1,\r\n                player2: \"Germany\",\r\n                abbr2: \"de\",\r\n                score2: 7,\r\n                description: \"semi-finals\",\r\n                start: \"2014-07-08T17:00:00\",\r\n            },\r\n            {\r\n                player1: \"Netherlands\",\r\n                abbr1: \"nl\",\r\n                score1: \"0 (2)\",\r\n                player2: \"Argentina\",\r\n                abbr2: \"ar\",\r\n                score2: \"0 (4)\",\r\n                description: \"semi-finals\",\r\n                start: \"2014-07-09T17:00:00\",\r\n            },\r\n\r\n            // final\r\n            {\r\n                player1: \"Germany\",\r\n                score1: 1,\r\n                abbr1: \"de\",\r\n                player2: \"Argentina\",\r\n                abbr2: \"ar\",\r\n                score2: 0,\r\n                description: \"final\",\r\n                start: \"2014-07-13T16:00:00\",\r\n            },\r\n        ]);\r\n\r\n        // Configuration for the Timeline\r\n        var options = {\r\n            // specify a template for the items\r\n            template: template,\r\n        };\r\n\r\n        // Create a Timeline\r\n        var timeline = new vis.Timeline(container, items, options);\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleTemplate();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTVisTimelineTemplate.init();\r\n});\r\n"], "sourceRoot": ""}