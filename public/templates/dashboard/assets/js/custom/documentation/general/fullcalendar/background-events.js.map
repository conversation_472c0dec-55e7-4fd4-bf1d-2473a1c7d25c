{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/background-events.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/general/fullcalendar/background-events.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarEventsDemos = function() {\r\n\t// Private functions\r\n\r\n\tvar exampleBackgroundEvents = function() {\r\n\t\t// Define colors\r\n\t\tvar green = KTUtil.getCssVariableValue('--bs-active-success');\r\n\t\tvar red = KTUtil.getCssVariableValue('--bs-active-danger');\r\n\r\n\t\t// Initialize Fullcalendar -- for more info please visit the official site: https://fullcalendar.io/demos\r\n\t\tvar calendarEl = document.getElementById('kt_docs_fullcalendar_background_events');\r\n\r\n\t\tvar calendar = new FullCalendar.Calendar(calendarEl, {\r\n\t\t\theaderToolbar: {\r\n\t\t\t\tleft: 'prev,next today',\r\n\t\t\t\tcenter: 'title',\r\n\t\t\t\tright: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'\r\n\t\t\t},\r\n\t\t\tinitialDate: '2020-09-12',\r\n\t\t\tnavLinks: true, // can click day/week names to navigate views\r\n\t\t\tbusinessHours: true, // display business hours\r\n\t\t\teditable: true,\r\n\t\t\tselectable: true,\r\n\t\t\tevents: [{\r\n\t\t\t\t\ttitle: 'Business Lunch',\r\n\t\t\t\t\tstart: '2020-09-03T13:00:00',\r\n\t\t\t\t\tconstraint: 'businessHours'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\ttitle: 'Meeting',\r\n\t\t\t\t\tstart: '2020-09-13T11:00:00',\r\n\t\t\t\t\tconstraint: 'availableForMeeting', // defined below\r\n\t\t\t\t\tcolor: green\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\ttitle: 'Conference',\r\n\t\t\t\t\tstart: '2020-09-18',\r\n\t\t\t\t\tend: '2020-09-20'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\ttitle: 'Party',\r\n\t\t\t\t\tstart: '2020-09-29T20:00:00'\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// areas where \"Meeting\" must be dropped\r\n\t\t\t\t{\r\n\t\t\t\t\tgroupId: 'availableForMeeting',\r\n\t\t\t\t\tstart: '2020-09-11',\r\n\t\t\t\t\tend: '2020-09-11',\r\n\t\t\t\t\tdisplay: 'background',\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tgroupId: 'availableForMeeting',\r\n\t\t\t\t\tstart: '2020-09-13',\r\n\t\t\t\t\tend: '2020-09-13',\r\n\t\t\t\t\tdisplay: 'background',\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// red areas where no events can be dropped\r\n\t\t\t\t{\r\n\t\t\t\t\tstart: '2020-09-24',\r\n\t\t\t\t\tend: '2020-09-28',\r\n\t\t\t\t\toverlap: false,\r\n\t\t\t\t\tdisplay: 'background',\r\n\t\t\t\t\tcolor: red\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tstart: '2020-09-06',\r\n\t\t\t\t\tend: '2020-09-08',\r\n\t\t\t\t\toverlap: false,\r\n\t\t\t\t\tdisplay: 'background',\r\n\t\t\t\t\tcolor: red\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t});\r\n\r\n\t\tcalendar.render();\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public Functions\r\n\t\tinit: function() {\r\n\t\t\texampleBackgroundEvents();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n\tKTGeneralFullCalendarEventsDemos.init();\r\n});"], "sourceRoot": ""}