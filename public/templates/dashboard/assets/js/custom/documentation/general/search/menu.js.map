{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/search/menu.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,a;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,K;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wC;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/general/search/menu.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralSearchMenuDemos = function() {\r\n    // Private variables\r\n    var element;\r\n    var formElement;\r\n    var mainElement;\r\n    var resultsElement;\r\n    var wrapperElement;\r\n    var emptyElement;\r\n\r\n    var preferencesElement;\r\n    var preferencesShowElement;\r\n    var preferencesDismissElement;\r\n    \r\n    var advancedOptionsFormElement;\r\n    var advancedOptionsFormShowElement;\r\n    var advancedOptionsFormCancelElement;\r\n    var advancedOptionsFormSearchElement;\r\n    \r\n    var searchObject;\r\n\r\n    // Private functions\r\n    var processs = function(search) {\r\n        var timeout = setTimeout(function() {\r\n            var number = KTUtil.getRandomInt(1, 3);\r\n\r\n            // Hide recently viewed\r\n            mainElement.classList.add('d-none');\r\n\r\n            if (number === 3) {\r\n                // Hide results\r\n                resultsElement.classList.add('d-none');\r\n                // Show empty message \r\n                emptyElement.classList.remove('d-none');\r\n            } else {\r\n                // Show results\r\n                resultsElement.classList.remove('d-none');\r\n                // Hide empty message \r\n                emptyElement.classList.add('d-none');\r\n            }                  \r\n\r\n            // Complete search\r\n            search.complete();\r\n        }, 1500);\r\n    }\r\n\r\n    var clear = function(search) {\r\n        // Show recently viewed\r\n        mainElement.classList.remove('d-none');\r\n        // Hide results\r\n        resultsElement.classList.add('d-none');\r\n        // Hide empty message \r\n        emptyElement.classList.add('d-none');\r\n    }    \r\n\r\n    var handlePreferences = function() {\r\n        // Preference show handler\r\n        preferencesShowElement.addEventListener('click', function() {\r\n            wrapperElement.classList.add('d-none');\r\n            preferencesElement.classList.remove('d-none');\r\n        });\r\n\r\n        // Preference dismiss handler\r\n        preferencesDismissElement.addEventListener('click', function() {\r\n            wrapperElement.classList.remove('d-none');\r\n            preferencesElement.classList.add('d-none');\r\n        });\r\n    }\r\n\r\n    var handleAdvancedOptionsForm = function() {\r\n        // Show\r\n        advancedOptionsFormShowElement.addEventListener('click', function() {\r\n            wrapperElement.classList.add('d-none');\r\n            advancedOptionsFormElement.classList.remove('d-none');\r\n        });\r\n\r\n        // Cancel\r\n        advancedOptionsFormCancelElement.addEventListener('click', function() {\r\n            wrapperElement.classList.remove('d-none');\r\n            advancedOptionsFormElement.classList.add('d-none');\r\n        });\r\n\r\n        // Search\r\n        advancedOptionsFormSearchElement.addEventListener('click', function() {\r\n            \r\n        });\r\n    }\r\n\r\n    // Public methods\r\n\treturn {\r\n\t\tinit: function() {\r\n            // Elements\r\n            element = document.querySelector('#kt_docs_search_handler_menu');\r\n\r\n            if (!element) {\r\n                return;\r\n            }\r\n\r\n            wrapperElement = element.querySelector('[data-kt-search-element=\"wrapper\"]');\r\n            formElement = element.querySelector('[data-kt-search-element=\"form\"]');\r\n            mainElement = element.querySelector('[data-kt-search-element=\"main\"]');\r\n            resultsElement = element.querySelector('[data-kt-search-element=\"results\"]');\r\n            emptyElement = element.querySelector('[data-kt-search-element=\"empty\"]');\r\n\r\n            preferencesElement = element.querySelector('[data-kt-search-element=\"preferences\"]');\r\n            preferencesShowElement = element.querySelector('[data-kt-search-element=\"preferences-show\"]');\r\n            preferencesDismissElement = element.querySelector('[data-kt-search-element=\"preferences-dismiss\"]');\r\n\r\n            advancedOptionsFormElement = element.querySelector('[data-kt-search-element=\"advanced-options-form\"]');\r\n            advancedOptionsFormShowElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-show\"]');\r\n            advancedOptionsFormCancelElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-cancel\"]');\r\n            advancedOptionsFormSearchElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-search\"]');\r\n            \r\n            // Initialize search handler\r\n            searchObject = new KTSearch(element);\r\n\r\n            // Search handler\r\n            searchObject.on('kt.search.process', processs);\r\n\r\n            // Clear handler\r\n            searchObject.on('kt.search.clear', clear);\r\n\r\n            // Custom handlers\r\n            handlePreferences();\r\n            handleAdvancedOptionsForm();            \r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralSearchMenuDemos.init();\r\n});"], "sourceRoot": ""}