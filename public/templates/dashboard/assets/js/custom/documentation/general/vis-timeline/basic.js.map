{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/vis-timeline/basic.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,gDAAgD;AAC7D,aAAa,gDAAgD;AAC7D,aAAa,gDAAgD;AAC7D,aAAa,mEAAmE;AAChF,aAAa,gDAAgD;AAC7D,aAAa,+DAA+D;AAC5E;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/vis-timeline/basic.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTVisTimelineBasic = function () {\r\n    // Private functions\r\n    var exampleBasic = function () {\r\n        // DOM element where the Timeline will be attached\r\n        var container = document.getElementById(\"kt_docs_vistimeline_basic\");\r\n\r\n        // Create a DataSet (allows two way data-binding)\r\n        var items = new vis.DataSet([\r\n            { id: 1, content: \"item 1\", start: \"2021-04-20\" },\r\n            { id: 2, content: \"item 2\", start: \"2021-04-14\" },\r\n            { id: 3, content: \"item 3\", start: \"2021-04-18\" },\r\n            { id: 4, content: \"item 4\", start: \"2021-04-16\", end: \"2021-04-19\" },\r\n            { id: 5, content: \"item 5\", start: \"2021-04-25\" },\r\n            { id: 6, content: \"item 6\", start: \"2021-04-27\", type: \"point\" },\r\n        ]);\r\n\r\n        // Configuration for the Timeline\r\n        var options = {};\r\n\r\n        // Create a Timeline\r\n        var timeline = new vis.Timeline(container, items, options);\r\n\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTVisTimelineBasic.init();\r\n});\r\n"], "sourceRoot": ""}