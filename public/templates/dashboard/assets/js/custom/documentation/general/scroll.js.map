{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/scroll.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/scroll.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralScrollDemos = function() {\r\n    // Private functions\r\n    var _exampleBasic = function() {\r\n        \r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            _exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralScrollDemos.init();\r\n});\r\n"], "sourceRoot": ""}