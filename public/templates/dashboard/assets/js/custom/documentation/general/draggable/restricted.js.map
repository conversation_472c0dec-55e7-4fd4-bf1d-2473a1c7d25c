{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/draggable/restricted.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/draggable/restricted.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDraggableRestricted = function () {\r\n    // Private functions\r\n    var exampleRestricted = function () {\r\n        var containers = document.querySelectorAll('.draggable-zone');\r\n        const restrcitedWrapper = document.querySelector('[data-kt-draggable-level=\"restricted\"]');\r\n\r\n        if (containers.length === 0) {\r\n            return false;\r\n        }\r\n\r\n        var droppable = new Droppable.default(containers, {\r\n            draggable: '.draggable',\r\n            dropzone: '.draggable-zone',\r\n            handle: '.draggable .draggable-handle',\r\n            mirror: {\r\n                //appendTo: selector,\r\n                appendTo: 'body',\r\n                constrainDimensions: true\r\n            }\r\n        });\r\n\r\n        // Define draggable element variable for permissions level\r\n        let droppableOrigin;\r\n\r\n        // Handle drag start event -- more info: https://shopify.github.io/draggable/docs/class/src/Draggable/DragEvent/DragEvent.js~DragEvent.html\r\n        droppable.on('drag:start', (e) => {\r\n            droppableOrigin = e.originalSource.getAttribute('data-kt-draggable-level');\r\n        });\r\n\r\n        // Handle drag over event -- more info: https://shopify.github.io/draggable/docs/class/src/Draggable/DragEvent/DragEvent.js~DragOverEvent.html\r\n        droppable.on('drag:over', (e) => {\r\n            const isRestricted = e.overContainer.closest('[data-kt-draggable-level=\"restricted\"]');\r\n            if (isRestricted) {\r\n                if (droppableOrigin !== 'admin') {\r\n                    restrcitedWrapper.classList.add('bg-light-danger');\r\n                } else {\r\n                    restrcitedWrapper.classList.remove('bg-light-danger');\r\n                }\r\n            } else {\r\n                restrcitedWrapper.classList.remove('bg-light-danger');\r\n            }\r\n        });\r\n\r\n        // Handle drag stop event -- more info: https://shopify.github.io/draggable/docs/class/src/Draggable/DragEvent/DragEvent.js~DragStopEvent.html\r\n        droppable.on('drag:stop', (e) => {\r\n            // remove all draggable occupied limit\r\n            containers.forEach(c => {\r\n                c.classList.remove('draggable-dropzone--occupied');\r\n            });\r\n\r\n            // Remove restricted alert\r\n            restrcitedWrapper.classList.remove('bg-light-danger');\r\n        });\r\n\r\n        // Handle drop event -- https://shopify.github.io/draggable/docs/class/src/Droppable/DroppableEvent/DroppableEvent.js~DroppableDroppedEvent.html\r\n        droppable.on('droppable:dropped', (e) => {\r\n            const isRestricted = e.dropzone.closest('[data-kt-draggable-level=\"restricted\"]');\r\n            // Detect if drop container is restricted\r\n            if (isRestricted) {\r\n                // Check if dragged element has permission level\r\n                if (droppableOrigin !== 'admin') {\r\n                    restrcitedWrapper.classList.add('bg-light-danger');\r\n                    e.cancel();\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleRestricted();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTDraggableRestricted.init();\r\n});\r\n"], "sourceRoot": ""}