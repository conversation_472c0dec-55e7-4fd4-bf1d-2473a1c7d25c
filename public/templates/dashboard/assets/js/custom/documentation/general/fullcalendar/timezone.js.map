{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/timezone.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,4DAA4D;AAC1F;AACA,SAAS;;AAET;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iB;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;;AAEb;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/fullcalendar/timezone.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarTimezoneDemos = function () {\r\n    // Private functions\r\n\r\n    var exampleTimezone = function () {\r\n        // Define variables\r\n        var initialTimeZone = 'local';\r\n        var timeZoneSelectorEl = document.getElementById('kt_docs_fullcalendar_timezone_selector');\r\n        var calendarEl = document.getElementById('kt_docs_fullcalendar_timezone');\r\n        var todayDate = moment().startOf('day');\r\n        var YM = todayDate.format('YYYY-MM');\r\n        var YESTERDAY = todayDate.clone().subtract(1, 'day').format('YYYY-MM-DD');\r\n        var TODAY = todayDate.format('YYYY-MM-DD');\r\n        var TOMORROW = todayDate.clone().add(1, 'day').format('YYYY-MM-DD');\r\n        var eventsArray = [\r\n            {\r\n                title: 'All Day Event',\r\n                start: YM + '-01',\r\n                description: 'Toto lorem ipsum dolor sit incid idunt ut',\r\n                className: \"fc-event-danger fc-event-solid-warning\"\r\n            },\r\n            {\r\n                title: 'Reporting',\r\n                start: YM + '-14T13:30:00',\r\n                description: 'Lorem ipsum dolor incid idunt ut labore',\r\n                end: YM + '-14',\r\n                className: \"fc-event-success\"\r\n            },\r\n            {\r\n                title: 'Company Trip',\r\n                start: YM + '-02',\r\n                description: 'Lorem ipsum dolor sit tempor incid',\r\n                end: YM + '-03',\r\n                className: \"fc-event-primary\"\r\n            },\r\n            {\r\n                title: 'ICT Expo 2017 - Product Release',\r\n                start: YM + '-03',\r\n                description: 'Lorem ipsum dolor sit tempor inci',\r\n                end: YM + '-05',\r\n                className: \"fc-event-light fc-event-solid-primary\"\r\n            },\r\n            {\r\n                title: 'Dinner',\r\n                start: YM + '-12',\r\n                description: 'Lorem ipsum dolor sit amet, conse ctetur',\r\n                end: YM + '-10'\r\n            },\r\n            {\r\n                id: 999,\r\n                title: 'Repeating Event',\r\n                start: YM + '-09T16:00:00',\r\n                description: 'Lorem ipsum dolor sit ncididunt ut labore',\r\n                className: \"fc-event-danger\"\r\n            },\r\n            {\r\n                id: 1000,\r\n                title: 'Repeating Event',\r\n                description: 'Lorem ipsum dolor sit amet, labore',\r\n                start: YM + '-16T16:00:00'\r\n            },\r\n            {\r\n                title: 'Conference',\r\n                start: YESTERDAY,\r\n                end: TOMORROW,\r\n                description: 'Lorem ipsum dolor eius mod tempor labore',\r\n                className: \"fc-event-primary\"\r\n            },\r\n            {\r\n                title: 'Meeting',\r\n                start: TODAY + 'T10:30:00',\r\n                end: TODAY + 'T12:30:00',\r\n                description: 'Lorem ipsum dolor eiu idunt ut labore'\r\n            },\r\n            {\r\n                title: 'Lunch',\r\n                start: TODAY + 'T12:00:00',\r\n                className: \"fc-event-info\",\r\n                description: 'Lorem ipsum dolor sit amet, ut labore'\r\n            },\r\n            {\r\n                title: 'Meeting',\r\n                start: TODAY + 'T14:30:00',\r\n                className: \"fc-event-warning\",\r\n                description: 'Lorem ipsum conse ctetur adipi scing'\r\n            },\r\n            {\r\n                title: 'Happy Hour',\r\n                start: TODAY + 'T17:30:00',\r\n                className: \"fc-event-info\",\r\n                description: 'Lorem ipsum dolor sit amet, conse ctetur'\r\n            },\r\n            {\r\n                title: 'Dinner',\r\n                start: TOMORROW + 'T05:00:00',\r\n                className: \"fc-event-solid-danger fc-event-light\",\r\n                description: 'Lorem ipsum dolor sit ctetur adipi scing'\r\n            },\r\n            {\r\n                title: 'Birthday Party',\r\n                start: TOMORROW + 'T07:00:00',\r\n                className: \"fc-event-primary\",\r\n                description: 'Lorem ipsum dolor sit amet, scing'\r\n            },\r\n            {\r\n                title: 'Click for Google',\r\n                url: 'http://google.com/',\r\n                start: YM + '-28',\r\n                className: \"fc-event-solid-info fc-event-light\",\r\n                description: 'Lorem ipsum dolor sit amet, labore'\r\n            }\r\n        ];\r\n\r\n        // Initialize the external events -- for more info please visit the official site: https://fullcalendar.io/demos\r\n        var calendar = new FullCalendar.Calendar(calendarEl, {\r\n            timeZone: initialTimeZone,\r\n            headerToolbar: {\r\n                left: 'prev,next today',\r\n                center: 'title',\r\n                right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\r\n            },\r\n            initialDate: TODAY,\r\n            navLinks: true, // can click day/week names to navigate views\r\n            editable: true,\r\n            selectable: true,\r\n            dayMaxEvents: true, // allow \"more\" link when too many events\r\n\r\n            eventTimeFormat: { hour: 'numeric', minute: '2-digit', timeZoneName: 'short' },\r\n            events: eventsArray,\r\n        });\r\n\r\n        calendar.render();\r\n\r\n        // when the timezone selector changes, dynamically change the calendar option -- more info on Select2 on Change event: https://select2.org/programmatic-control/events\r\n        $(timeZoneSelectorEl).on('change', function () {\r\n            calendar.setOption('timeZone', 'UTC');\r\n\r\n            // Remove all events\r\n            const removeEvents = calendar.getEvents();\r\n            removeEvents.forEach(event => {\r\n                event.remove();\r\n            });\r\n\r\n            // Add events with new timezone offset\r\n            const newEvents = eventsArray;\r\n            newEvents.forEach(event => {\r\n                var start;\r\n                var end;\r\n\r\n                if(this.value < 0){\r\n                    start = moment(event.start).subtract(this.value.replace(/\\D/g,''), 'seconds').format(getFormat(event.start));\r\n                    end = event.end ? moment(event.end).subtract(this.value.replace(/\\D/g,''), 'seconds').format(getFormat(event.end)) : start;\r\n                } else {\r\n                    start = moment(event.start).add(this.value, 'seconds').format(getFormat(event.start));\r\n                    end = event.end ? moment(event.end).add(this.value, 'seconds').format(getFormat(event.end)) : start;\r\n                }               \r\n\r\n                calendar.addEvent({\r\n                    title: event.title,\r\n                    start: start,\r\n                    end: end\r\n                });\r\n            });\r\n\r\n            calendar.render();\r\n        });\r\n\r\n        // Dynamic date format generator\r\n        const getFormat = (d) => {\r\n            if(d.includes('T')){\r\n                return 'YYYY-MM-DDTHH:mm:ss';\r\n            } else {\r\n                return 'YYYY-MM-DD';\r\n            }\r\n        }\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleTimezone();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralFullCalendarTimezoneDemos.init();\r\n});\r\n"], "sourceRoot": ""}