{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jstree/customicons.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;;AAET;AACA;AACA;AACA,8EAA8E;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/jstree/customicons.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJSTreeCustomIcons = function() {\r\n    // Private functions\r\n    var exampleCustomIcons = function() {\r\n        $('#kt_docs_jstree_customicons').jstree({\r\n            \"core\" : {\r\n                \"themes\" : {\r\n                    \"responsive\": false\r\n                }\r\n            },\r\n            \"types\" : {\r\n                \"default\" : {\r\n                    \"icon\" : \"fa fa-folder text-warning\"\r\n                },\r\n                \"file\" : {\r\n                    \"icon\" : \"fa fa-file  text-warning\"\r\n                }\r\n            },\r\n            \"plugins\": [\"types\"]\r\n        });\r\n\r\n        // handle link clicks in tree nodes(support target=\"_blank\" as well)\r\n        $('#kt_docs_jstree_customicons').on('select_node.jstree', function(e,data) {\r\n            var link = $('#' + data.selected).find('a');\r\n            if (link.attr(\"href\") != \"#\" && link.attr(\"href\") != \"javascript:;\" && link.attr(\"href\") != \"\") {\r\n                if (link.attr(\"target\") == \"_blank\") {\r\n                    link.attr(\"href\").target = \"_blank\";\r\n                }\r\n                document.location.href = link.attr(\"href\");\r\n                return false;\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleCustomIcons();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJSTreeCustomIcons.init();\r\n});\r\n"], "sourceRoot": ""}