{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/cropper.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,6CAA6C,0BAA0B;;AAEvE;AACA;AACA,6CAA6C,yBAAyB;;AAEtE;AACA;AACA,6CAA6C,wBAAwB;;AAErE;AACA;AACA,6CAA6C,wBAAwB;AACrE,IAAI;AACJ;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,iDAAiD,YAAY,2BAA2B;AACxF,IAAI;AACJ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,IAAI;AACJ,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/general/cropper.js", "sourcesContent": ["'use strict';\r\n\r\n// Class definition\r\nvar KTCropperDemo = function () {\r\n\r\n\t// Private functions\r\n\tvar initCropperDemo = function () {\r\n\t\tvar image = document.getElementById('image');\r\n\r\n\t\tvar options = {\r\n\t\t\tcrop: function (event) {\r\n\t\t\t\tdocument.getElementById('dataX').value = Math.round(event.detail.x);\r\n\t\t\t\tdocument.getElementById('dataY').value = Math.round(event.detail.y);\r\n\t\t\t\tdocument.getElementById('dataWidth').value = Math.round(event.detail.width);\r\n\t\t\t\tdocument.getElementById('dataHeight').value = Math.round(event.detail.height);\r\n\t\t\t\tdocument.getElementById('dataRotate').value = event.detail.rotate;\r\n\t\t\t\tdocument.getElementById('dataScaleX').value = event.detail.scaleX;\r\n\t\t\t\tdocument.getElementById('dataScaleY').value = event.detail.scaleY;\r\n\r\n\t\t\t\tvar lg = document.getElementById('cropper-preview-lg');\r\n\t\t\t\tlg.innerHTML = '';\r\n\t\t\t\tlg.appendChild(cropper.getCroppedCanvas({ width: 256, height: 160 }));\r\n\r\n\t\t\t\tvar md = document.getElementById('cropper-preview-md');\r\n\t\t\t\tmd.innerHTML = '';\r\n\t\t\t\tmd.appendChild(cropper.getCroppedCanvas({ width: 128, height: 80 }));\r\n\r\n\t\t\t\tvar sm = document.getElementById('cropper-preview-sm');\r\n\t\t\t\tsm.innerHTML = '';\r\n\t\t\t\tsm.appendChild(cropper.getCroppedCanvas({ width: 64, height: 40 }));\r\n\r\n\t\t\t\tvar xs = document.getElementById('cropper-preview-xs');\r\n\t\t\t\txs.innerHTML = '';\r\n\t\t\t\txs.appendChild(cropper.getCroppedCanvas({ width: 32, height: 20 }));\r\n\t\t\t},\r\n\t\t};\r\n\r\n\t\tvar cropper = new Cropper(image, options);\r\n\r\n\t\tvar buttons = document.getElementById('cropper-buttons');\r\n\t\tvar methods = buttons.querySelectorAll('[data-method]');\r\n\t\tmethods.forEach(function (button) {\r\n\t\t\tbutton.addEventListener('click', function (e) {\r\n\t\t\t\tvar method = button.getAttribute('data-method');\r\n\t\t\t\tvar option = button.getAttribute('data-option');\r\n\t\t\t\tvar option2 = button.getAttribute('data-second-option');\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\toption = JSON.parse(option);\r\n\t\t\t\t}\r\n\t\t\t\tcatch (e) {\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar result;\r\n\t\t\t\tif (!option2) {\r\n\t\t\t\t\tresult = cropper[method](option, option2);\r\n\t\t\t\t}\r\n\t\t\t\telse if (option) {\r\n\t\t\t\t\tresult = cropper[method](option);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tresult = cropper[method]();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (method === 'getCroppedCanvas') {\r\n\t\t\t\t\tvar modal = document.getElementById('getCroppedCanvasModal');\r\n\t\t\t\t\tvar modalBody = modal.querySelector('.modal-body');\r\n\t\t\t\t\tmodalBody.innerHTML = '';\r\n\t\t\t\t\tmodalBody.appendChild(result);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar input = document.querySelector('#putData');\r\n\t\t\t\ttry {\r\n\t\t\t\t\tinput.value = JSON.stringify(result);\r\n\t\t\t\t}\r\n\t\t\t\tcatch (e) {\r\n\t\t\t\t\tif (!result) {\r\n\t\t\t\t\t\tinput.value = result;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\t// set aspect ratio option buttons\r\n\t\tvar radioOptions = document.getElementById('setAspectRatio').querySelectorAll('[name=\"aspectRatio\"]');\r\n\t\tradioOptions.forEach(function (button) {\r\n\t\t\tbutton.addEventListener('click', function (e) {\r\n\t\t\t\tcropper.setAspectRatio(e.target.value);\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\t// set view mode\r\n\t\tvar viewModeOptions = document.getElementById('viewMode').querySelectorAll('[name=\"viewMode\"]');\r\n\t\tviewModeOptions.forEach(function (button) {\r\n\t\t\tbutton.addEventListener('click', function (e) {\r\n\t\t\t\tcropper.destroy();\r\n\t\t\t\tcropper = new Cropper(image, Object.assign({}, options, { viewMode: e.target.value }));\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tvar toggleoptions = document.getElementById('toggleOptionButtons').querySelectorAll('[type=\"checkbox\"]');\r\n\t\ttoggleoptions.forEach(function (checkbox) {\r\n\t\t\tcheckbox.addEventListener('click', function (e) {\r\n\t\t\t\tvar appendOption = {};\r\n\t\t\t\tappendOption[e.target.getAttribute('name')] = e.target.checked;\r\n\t\t\t\toptions = Object.assign({}, options, appendOption);\r\n\t\t\t\tcropper.destroy();\r\n\t\t\t\tcropper = new Cropper(image, options);\r\n\t\t\t})\r\n\t\t});\r\n\t};\r\n\r\n\treturn {\r\n\t\t// public functions\r\n\t\tinit: function () {\r\n\t\t\tinitCropperDemo();\r\n\t\t},\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tKTCropperDemo.init();\r\n});\r\n"], "sourceRoot": ""}