{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/datatables/server-side.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA,0C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,iBAAiB,iBAAiB;AAClC,iBAAiB,aAAa;AAC9B,iBAAiB,cAAc;AAC/B,iBAAiB,gBAAgB;AACjC,iBAAiB,yBAAyB;AAC1C,iBAAiB,iBAAiB;AAClC,iBAAiB,WAAW;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yFAAyF,KAAK;AAC9F;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,uHAAuH,mBAAmB,iCAAiC,mBAAmB;AAC9L;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA,SAAS;;AAET;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/general/datatables/server-side.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesServerSide = function () {  \r\n    // Shared variables\r\n    var table;\r\n    var dt;\r\n\r\n    // Private functions\r\n    var initDatatable = function () {\r\n        dt = $(\"#kt_datatable_example_1\").DataTable({\r\n            responsive: true,\r\n            searchDelay: 500,\r\n            processing: true,\r\n            serverSide: true,\r\n            order: [[5, 'desc']],\r\n            stateSave: true,\r\n            select: {\r\n                style: 'os',\r\n                selector: 'td:first-child',\r\n                className: 'row-selected'\r\n            },\r\n            ajax: {\r\n                url: \"https://preview.keenthemes.com/api/datatables.php\",\r\n            },\r\n            columns: [\r\n                {data: 'RecordID'},\r\n                {data: 'Name'},\r\n                {data: 'Email'},\r\n                {data: 'Company'},\r\n                {data: 'CreditCardNumber'},\r\n                {data: 'Datetime'},\r\n                {data: null},\r\n            ],\r\n            columnDefs: [\r\n                {\r\n                    targets: 0,\r\n                    orderable: false,\r\n                    render: function (data) {\r\n                        return `\r\n                            <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                                <input class=\"form-check-input\" type=\"checkbox\" value=\"${data}\" />\r\n                            </div>`;\r\n                    }\r\n                },\r\n                {\r\n                    targets: 4,\r\n                    render: function (data, type, row) {\r\n                        return `<img src=\"https://preview.keenthemes.com/metronic8/demo1/assets/media/svg/card-logos/${row.CreditCardType}.svg\" class=\"w-35px me-3\" alt=\"${row.CreditCardType}\">` + data;\r\n                    }\r\n                },\r\n                {\r\n                    targets: -1,\r\n                    data: null,\r\n                    orderable: false,\r\n                    className: 'text-end',\r\n                    render: function (data, type, row) {\r\n                        return `\r\n                            <a href=\"#\" class=\"btn btn-light btn-active-light-primary btn-sm\" data-kt-menu-trigger=\"click\" data-kt-menu-placement=\"bottom-end\" data-kt-menu-flip=\"top-end\">\r\n                                Actions\r\n                                <span class=\"svg-icon svg-icon-5 m-0\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\">\r\n                                        <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\r\n                                            <polygon points=\"0 0 24 0 24 24 0 24\"></polygon>\r\n                                            <path d=\"M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z\" fill=\"#000000\" fill-rule=\"nonzero\" transform=\"translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)\"></path>\r\n                                        </g>\r\n                                    </svg>\r\n                                </span>\r\n                            </a>\r\n                            <!--begin::Menu-->\r\n                            <div class=\"menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4\" data-kt-menu=\"true\">\r\n                                <!--begin::Menu item-->\r\n                                <div class=\"menu-item px-3\">\r\n                                    <a href=\"#\" class=\"menu-link px-3\" data-kt-users-table-filter=\"edit_row\">\r\n                                        Edit\r\n                                    </a>\r\n                                </div>\r\n                                <!--end::Menu item-->\r\n                                \r\n                                <!--begin::Menu item-->\r\n                                <div class=\"menu-item px-3\">\r\n                                    <a href=\"#\" class=\"menu-link px-3\" data-kt-users-table-filter=\"delete_row\">\r\n                                        Delete\r\n                                    </a>\r\n                                </div>\r\n                                <!--end::Menu item-->\r\n                            </div>\r\n                            <!--end::Menu-->\r\n                        `;\r\n                    },\r\n                },\r\n            ]\r\n        });\r\n\r\n        table = dt.$;\r\n\r\n        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw\r\n        dt.on('draw', function () {\r\n            initToggleToolbar();\r\n            toggleToolbars();\r\n            KTMenu.createInstances();\r\n        });\r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    var handleSearchDatatable = function () {\r\n        const filterSearch = document.querySelector('[data-kt-docs-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            dt.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Init toggle toolbar\r\n    var initToggleToolbar = function () {\r\n        // Toggle selected action toolbar\r\n        // Select all checkboxes\r\n        const container = document.querySelector('#kt_datatable_example_1');\r\n        const checkboxes = container.querySelectorAll('[type=\"checkbox\"]');\r\n\r\n        // Toggle delete selected toolbar\r\n        checkboxes.forEach(c => {\r\n            // Checkbox on click event\r\n            c.addEventListener('click', function () {\r\n                setTimeout(function () {\r\n                    toggleToolbars();\r\n                }, 50);\r\n            });\r\n        });\r\n    }\r\n\r\n    // Toggle toolbars\r\n    var toggleToolbars = function () {\r\n        // Define variables\r\n        const container = document.querySelector('#kt_datatable_example_1');\r\n        const toolbarBase = document.querySelector('[data-kt-docs-table-toolbar=\"base\"]');\r\n        const toolbarSelected = document.querySelector('[data-kt-docs-table-toolbar=\"selected\"]');\r\n        const selectedCount = document.querySelector('[data-kt-docs-table-select=\"selected_count\"]');\r\n\r\n        // Select refreshed checkbox DOM elements \r\n        const allCheckboxes = container.querySelectorAll('tbody [type=\"checkbox\"]');\r\n\r\n        // Detect checkboxes state & count\r\n        let checkedState = false;\r\n        let count = 0;\r\n\r\n        // Count checked boxes\r\n        allCheckboxes.forEach(c => {\r\n            if (c.checked) {\r\n                checkedState = true;\r\n                count++;\r\n            }\r\n        });\r\n\r\n        // Toggle toolbars\r\n        if (checkedState) {\r\n            selectedCount.innerHTML = count;\r\n            toolbarBase.classList.add('d-none');\r\n            toolbarSelected.classList.remove('d-none');\r\n        } else {\r\n            toolbarBase.classList.remove('d-none');\r\n            toolbarSelected.classList.add('d-none');\r\n        }\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initDatatable();\r\n            handleSearchDatatable();\r\n            initToggleToolbar();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTDatatablesServerSide.init();\r\n});"], "sourceRoot": ""}