{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/tinymce/hidden.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/editors/tinymce/hidden.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsTinyMCEHidden = function() {\r\n    // Private functions\r\n    var exampleHidden = function() {\r\n        tinymce.init({\r\n            selector: '#kt_docs_tinymce_hidden',\r\n            menubar: false,\r\n            toolbar: ['styleselect fontselect fontsizeselect',\r\n                'undo redo | cut copy paste | bold italic | link image | alignleft aligncenter alignright alignjustify',\r\n                'bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | lists charmap | print preview |  code'],\r\n            plugins : 'advlist autolink link image lists charmap print preview code'\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleHidden();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsTinyMCEHidden.init();\r\n});\r\n"], "sourceRoot": ""}