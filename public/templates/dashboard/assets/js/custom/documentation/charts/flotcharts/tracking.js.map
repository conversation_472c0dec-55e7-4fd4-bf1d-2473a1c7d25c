{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/tracking.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,cAAc,oBAAoB;AAClC;;AAEA;AACA,eAAe,wBAAwB;AACvC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/charts/flotcharts/tracking.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoTracking = function () {\r\n    // Private functions\r\n    var exampleTracking = function () {\r\n        var sin = [],\r\n\t\t\tcos = [];\r\n\t\tfor (var i = 0; i < 14; i += 0.1) {\r\n\t\t\tsin.push([i, Math.sin(i)]);\r\n\t\t\tcos.push([i, Math.cos(i)]);\r\n\t\t}\r\n\r\n\t\tvar plot = $.plot($(\"#kt_docs_flot_tracking\"), [{\r\n\t\t\tdata: sin,\r\n\t\t\tlabel: \"sin(x) = -0.00\",\r\n\t\t\tlines: {\r\n\t\t\t\tlineWidth: 1,\r\n\t\t\t},\r\n\t\t\tshadowSize: 0\r\n\t\t}, {\r\n\t\t\tdata: cos,\r\n\t\t\tlabel: \"cos(x) = -0.00\",\r\n\t\t\tlines: {\r\n\t\t\t\tlineWidth: 1,\r\n\t\t\t},\r\n\t\t\tshadowSize: 0\r\n\t\t}], {\r\n\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-primary'), KTUtil.getCssVariableValue('--bs-active-warning')],\r\n\t\t\tseries: {\r\n\t\t\t\tlines: {\r\n\t\t\t\t\tshow: true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcrosshair: {\r\n\t\t\t\tmode: \"x\"\r\n\t\t\t},\r\n\t\t\tgrid: {\r\n\t\t\t\thoverable: true,\r\n\t\t\t\tautoHighlight: false,\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderWidth: 1\r\n\t\t\t},\r\n\t\t\tyaxis: {\r\n\t\t\t\tmin: -1.2,\r\n\t\t\t\tmax: 1.2\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tvar legends = $(\"#kt_docs_flot_tracking .legendLabel\");\r\n\t\tlegends.each(function() {\r\n\t\t\t// fix the widths so they don't jump around\r\n\t\t\t$(this).css('width', $(this).width());\r\n\t\t});\r\n\r\n\t\tvar updateLegendTimeout = null;\r\n\t\tvar latestPosition = null;\r\n\r\n\t\tfunction updateLegend() {\r\n\t\t\tupdateLegendTimeout = null;\r\n\r\n\t\t\tvar pos = latestPosition;\r\n\r\n\t\t\tvar axes = plot.getAxes();\r\n\t\t\tif (pos.x < axes.xaxis.min || pos.x > axes.xaxis.max || pos.y < axes.yaxis.min || pos.y > axes.yaxis.max) return;\r\n\r\n\t\t\tvar i, j, dataset = plot.getData();\r\n\t\t\tfor (i = 0; i < dataset.length; ++i) {\r\n\t\t\t\tvar series = dataset[i];\r\n\r\n\t\t\t\t// find the nearest points, x-wise\r\n\t\t\t\tfor (j = 0; j < series.data.length; ++j)\r\n\t\t\t\t\tif (series.data[j][0] > pos.x) break;\r\n\r\n\t\t\t\t// now interpolate\r\n\t\t\t\tvar y, p1 = series.data[j - 1],\r\n\t\t\t\t\tp2 = series.data[j];\r\n\r\n\t\t\t\tif (p1 == null) y = p2[1];\r\n\t\t\t\telse if (p2 == null) y = p1[1];\r\n\t\t\t\telse y = p1[1] + (p2[1] - p1[1]) * (pos.x - p1[0]) / (p2[0] - p1[0]);\r\n\r\n\t\t\t\tlegends.eq(i).text(series.label.replace(/=.*/, \"= \" + y.toFixed(2)));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t$(\"#kt_docs_flot_tracking\").bind(\"plothover\", function(event, pos, item) {\r\n\t\t\tlatestPosition = pos;\r\n\t\t\tif (!updateLegendTimeout) updateLegendTimeout = setTimeout(updateLegend, 50);\r\n\t\t});\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleTracking();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoTracking.init();\r\n});\r\n"], "sourceRoot": ""}