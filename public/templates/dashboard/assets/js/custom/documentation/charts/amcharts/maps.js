"use strict";var KTGeneralAmChartsMaps=function(){var e,t=function(){am4core.ready((function(){am4core.useTheme(am4themes_animated),(e=am4core.create("kt_amcharts_1",am4maps.MapChart)).geodata=am4geodata_worldLow,e.projection=new am4maps.projections.Miller;var t=e.series.push(new am4maps.MapPolygonSeries);t.exclude=["AQ"],t.useGeodata=!0;var a=t.mapPolygons.template;a.tooltipText="{name}",a.polygon.fillOpacity=.6,a.states.create("hover").properties.fill=e.colors.getIndex(0);var o=e.series.push(new am4maps.MapImageSeries);o.mapImages.template.propertyFields.longitude="longitude",o.mapImages.template.propertyFields.latitude="latitude",o.mapImages.template.tooltipText="{title}",o.mapImages.template.propertyFields.url="url";var i=o.mapImages.template.createChild(am4core.Circle);i.radius=3,i.propertyFields.fill="color",i.nonScaling=!0;var r=o.mapImages.template.createChild(am4core.Circle);function n(t){t.animate([{property:"scale",from:1/e.zoomLevel,to:5/e.zoomLevel},{property:"opacity",from:1,to:0}],1e3,am4core.ease.circleOut).events.on("animationended",(function(e){n(e.target.object)}))}r.radius=3,r.propertyFields.fill="color",r.events.on("inited",(function(e){n(e.target)}));var l=new am4core.ColorSet;o.data=[{title:"Brussels",latitude:50.8371,longitude:4.3676,color:l.next()},{title:"Copenhagen",latitude:55.6763,longitude:12.5681,color:l.next()},{title:"Paris",latitude:48.8567,longitude:2.351,color:l.next()},{title:"Reykjavik",latitude:64.1353,longitude:-21.8952,color:l.next()},{title:"Moscow",latitude:55.7558,longitude:37.6176,color:l.next()},{title:"Madrid",latitude:40.4167,longitude:-3.7033,color:l.next()},{title:"London",latitude:51.5002,longitude:-.1262,url:"http://www.google.co.uk",color:l.next()},{title:"Peking",latitude:39.9056,longitude:116.3958,color:l.next()},{title:"New Delhi",latitude:28.6353,longitude:77.225,color:l.next()},{title:"Tokyo",latitude:35.6785,longitude:139.6823,url:"http://www.google.co.jp",color:l.next()},{title:"Ankara",latitude:39.9439,longitude:32.856,color:l.next()},{title:"Buenos Aires",latitude:-34.6118,longitude:-58.4173,color:l.next()},{title:"Brasilia",latitude:-15.7801,longitude:-47.9292,color:l.next()},{title:"Ottawa",latitude:45.4235,longitude:-75.6979,color:l.next()},{title:"Washington",latitude:38.8921,longitude:-77.0241,color:l.next()},{title:"Kinshasa",latitude:-4.3369,longitude:15.3271,color:l.next()},{title:"Cairo",latitude:30.0571,longitude:31.2272,color:l.next()},{title:"Pretoria",latitude:-25.7463,longitude:28.1876,color:l.next()}]}))};return{init:function(){t(),am4core.ready((function(){am4core.useTheme(am4themes_animated),(e=am4core.create("kt_amcharts_2",am4maps.MapChart)).geodata=am4geodata_worldLow,e.projection=new am4maps.projections.Miller,e.homeZoomLevel=2.5,e.homeGeoPoint={latitude:38,longitude:-60};var t=e.series.push(new am4maps.MapPolygonSeries);t.useGeodata=!0,t.mapPolygons.template.fill=e.colors.getIndex(0).lighten(.5),t.mapPolygons.template.nonScalingStroke=!0,t.exclude=["AQ"];var a=e.series.push(new am4maps.MapImageSeries);a.mapImages.template.nonScaling=!0;var o=a.mapImages.template.createChild(am4core.Circle);function i(e,t){var o=a.mapImages.create();return o.latitude=e.latitude,o.longitude=e.longitude,o.tooltipText=t,o}o.radius=6,o.fill=e.colors.getIndex(0).brighten(-.2),o.strokeWidth=2,o.stroke=am4core.color("#fff");var r=i({latitude:48.8567,longitude:2.351},"Paris"),n=i({latitude:43.8163,longitude:-79.4287},"Toronto"),l=i({latitude:34.3,longitude:-118.15},"Los Angeles"),s=i({latitude:23,longitude:-82},"Havana"),m=e.series.push(new am4maps.MapArcSeries);m.mapLines.template.line.strokeWidth=2,m.mapLines.template.line.strokeOpacity=.5,m.mapLines.template.line.stroke=o.fill,m.mapLines.template.line.nonScalingStroke=!0,m.mapLines.template.line.strokeDasharray="1,1",m.zIndex=10;var p=e.series.push(new am4maps.MapLineSeries);function c(e,t){var a=m.mapLines.create();return a.imagesToConnect=[e,t],a.line.controlPointDistance=-.3,p.mapLines.create().imagesToConnect=[e,t],a}p.mapLines.template.line.strokeOpacity=0,p.mapLines.template.line.nonScalingStroke=!0,p.mapLines.template.shortestDistance=!1,p.zIndex=5,c(r,n),c(n,l),c(l,s);var d=m.mapLines.getIndex(0).lineObjects.create();d.position=0,d.width=48,d.height=48,d.adapter.add("scale",(function(e,t){return.5*(1-Math.abs(.5-t.position))}));var g=d.createChild(am4core.Sprite);g.scale=.08,g.horizontalCenter="middle",g.verticalCenter="middle",g.path="m2,106h28l24,30h72l-44,-133h35l80,132h98c21,0 21,34 0,34l-98,0 -80,134h-35l43,-133h-71l-24,30h-28l15,-47",g.fill=e.colors.getIndex(2).brighten(-.2),g.strokeOpacity=0;var u=p.mapLines.getIndex(0).lineObjects.create();u.position=0,u.width=48,u.height=48;var h=u.createChild(am4core.Sprite);h.scale=.05,h.horizontalCenter="middle",h.verticalCenter="middle",h.path="m2,106h28l24,30h72l-44,-133h35l80,132h98c21,0 21,34 0,34l-98,0 -80,134h-35l43,-133h-71l-24,30h-28l15,-47",h.fill=am4core.color("#000"),h.strokeOpacity=0,u.adapter.add("scale",(function(e,t){return t.opacity=.6-Math.abs(.5-t.position),.5-.3*(1-Math.abs(.5-t.position))}));var y=0,f=1;!function e(){var t,a;d.mapLine=m.mapLines.getIndex(y),d.parent=m,u.mapLine=p.mapLines.getIndex(y),u.parent=p,h.rotation=g.rotation;var o=m.mapLines.length;if(1==f){if(t=0,a=1,0!=g.rotation)return void g.animate({to:0,property:"rotation"},1e3).events.on("animationended",e)}else if(t=1,a=0,180!=g.rotation)return void g.animate({to:180,property:"rotation"},1e3).events.on("animationended",e);d.animate({from:t,to:a,property:"position"},5e3,am4core.ease.sinInOut).events.on("animationended",e),u.animate({from:t,to:a,property:"position"},5e3,am4core.ease.sinInOut),(y+=f)<0?(y=0,f=1):y+1>o&&(y=o-1,f=-1)}()})),am4core.ready((function(){am4core.useTheme(am4themes_animated),e=am4core.create("kt_amcharts_3",am4maps.MapChart),new am4core.InterfaceColorSet;try{e.geodata=am4geodata_worldLow}catch(t){e.raiseCriticalError(new Error('Map geodata could not be loaded. Please download the latest <a href="https://www.amcharts.com/download/download-v4/">amcharts geodata</a> and extract its contents into the same directory as your amCharts files.'))}var t=e.createChild(am4core.Label);t.text="12 months (3/7/2019 data) rolling measles\nincidence per 1'000'000 total population. \n Bullet size uses logarithmic scale.",t.fontSize=12,t.align="left",t.valign="bottom",t.fill=am4core.color("#927459"),t.background=new am4core.RoundedRectangle,t.background.cornerRadius(10,10,10,10),t.padding(10,10,10,10),t.marginLeft=30,t.marginBottom=30,t.background.strokeOpacity=.3,t.background.stroke=am4core.color("#927459"),t.background.fill=am4core.color("#f9e3ce"),t.background.fillOpacity=.6;var a=e.createChild(am4core.TextLink);a.text="Data source: WHO",a.fontSize=12,a.align="left",a.valign="top",a.url="https://www.who.int/immunization/monitoring_surveillance/burden/vpd/surveillance_type/active/measles_monthlydata/en/",a.urlTarget="_blank",a.fill=am4core.color("#927459"),a.padding(10,10,10,10),a.marginLeft=30,a.marginTop=30,e.projection=new am4maps.projections.Orthographic,e.panBehavior="rotateLongLat",e.padding(20,20,20,20),e.zoomControl=new am4maps.ZoomControl;var o=new am4core.Button;o.events.on("hit",(function(){e.goHome()})),o.icon=new am4core.Sprite,o.padding(7,5,7,5),o.width=30,o.icon.path="M16,8 L14,8 L14,16 L10,16 L10,10 L6,10 L6,16 L2,16 L2,8 L0,8 L8,0 L16,8 Z M16,8",o.marginBottom=10,o.parent=e.zoomControl,o.insertBefore(e.zoomControl.plusButton),e.backgroundSeries.mapPolygons.template.polygon.fill=am4core.color("#bfa58d"),e.backgroundSeries.mapPolygons.template.polygon.fillOpacity=1,e.deltaLongitude=20,e.deltaLatitude=-20,e.adapter.add("deltaLatitude",(function(e){return am4core.math.fitToRange(e,-90,90)}));var i=e.series.push(new am4maps.MapPolygonSeries);i.geodata=am4geodata_continentsLow;try{i.geodata=am4geodata_continentsLow}catch(e){i.raiseCriticalError(new Error('Map geodata could not be loaded. Please download the latest <a href="https://www.amcharts.com/download/download-v4/">amcharts geodata</a> and extract its contents into the same directory as your amCharts files.'))}i.useGeodata=!0,i.dx=2,i.dy=2,i.mapPolygons.template.fill=am4core.color("#000"),i.mapPolygons.template.fillOpacity=.2,i.mapPolygons.template.strokeOpacity=0,i.fillOpacity=.1,i.fill=am4core.color("#000");var r=e.series.push(new am4maps.MapPolygonSeries);r.useGeodata=!0,r.calculateVisualCenter=!0,r.tooltip.background.fillOpacity=.2,r.tooltip.background.cornerRadius=20;var n=r.mapPolygons.template;n.nonScalingStroke=!0,n.fill=am4core.color("#f9e3ce"),n.stroke=am4core.color("#e2c9b0"),r.calculateVisualCenter=!0,n.propertyFields.id="id",n.tooltipPosition="fixed",n.fillOpacity=1,n.events.on("over",(function(e){e.target.dummyData&&(e.target.dummyData.isHover=!0)})),n.events.on("out",(function(e){e.target.dummyData&&(e.target.dummyData.isHover=!1)}));var l=r.mapPolygons.template.states.create("hover");l.properties.fillOpacity=1,l.properties.fill=am4core.color("#deb7ad");var s=e.series.push(new am4maps.GraticuleSeries);s.mapLines.template.stroke=am4core.color("#fff"),s.fitExtent=!1,s.mapLines.template.strokeOpacity=.2,s.mapLines.template.stroke=am4core.color("#fff");var m=e.series.push(new am4maps.MapPolygonSeries);m.tooltip.background.fillOpacity=0,m.tooltip.background.cornerRadius=20,m.tooltip.autoTextColor=!1,m.tooltip.label.fill=am4core.color("#000"),m.tooltip.dy=-5;var p=m.mapPolygons.template;p.fill=am4core.color("#bf7569"),p.strokeOpacity=0,p.fillOpacity=.75,p.tooltipPosition="fixed";var c=m.mapPolygons.template.states.create("hover");c.properties.fillOpacity=1,c.properties.fill=am4core.color("#86240c"),r.events.on("inited",(function(){r.mapPolygons.each((function(e){var t=d[e.id];if(t>0){var a=m.mapPolygons.create();a.multiPolygon=am4maps.getCircle(e.visualLongitude,e.visualLatitude,Math.max(.2,Math.log(t)*Math.LN10/10)),a.tooltipText=e.dataItem.dataContext.name+": "+t,e.dummyData=a,a.events.on("over",(function(){e.isHover=!0})),a.events.on("out",(function(){e.isHover=!1}))}else e.tooltipText=e.dataItem.dataContext.name+": no data",e.fillOpacity=.9}))}));var d={AL:504.38,AM:6.5,AO:2.98,AR:.32,AT:10.9,AU:5.02,AZ:17.38,BA:24.45,BD:13.4,BE:12.06,BF:93.37,BG:1.68,BI:.95,BJ:93.36,BR:49.42,BT:10.03,BY:26.16,CA:.96,CD:69.71,CF:4.57,CG:19.7,CH:6.19,CI:14.1,CL:1.4,CM:41.26,CN:2.6,CO:4.48,CY:7.69,CZ:23.09,DK:1.58,EE:9.91,EG:.63,ES:4.96,FI:3.27,FR:43.26,GA:3.03,GB:14.3,GE:809.09,GH:39.78,GM:2.45,GN:45.98,GQ:23.74,GR:154.42,HR:5.46,HU:1.44,ID:16.87,IE:17.56,IL:412.24,IN:47.85,IQ:12.96,IR:1.13,IT:44.29,JP:3.27,KE:16.8,KG:253.37,KH:.44,KM:1.26,KZ:116.3,LA:1.33,LK:.53,LR:692.27,LS:5.9,LT:14.44,LU:6.95,LV:6.09,MA:.2,MD:83.75,ME:319.75,MG:2386.35,MK:28.83,ML:48.68,MM:40.31,MN:.66,MR:14.65,MT:11.65,MV:9.35,MX:.04,MY:86.41,MZ:13.49,NA:12.9,NE:80.88,NG:31.44,NL:1.47,NO:2.47,NP:10.8,NZ:9.23,PE:1.29,PK:159.14,PL:8.24,PT:16.68,RO:63.05,RS:473.46,RU:14.24,RW:5.45,SE:2.64,SG:8.18,SI:3.37,SK:112.78,SN:3.37,SO:8.03,SS:19.3,TD:75.63,TG:34.84,TH:81.02,TL:9.46,TN:7.8,TR:7.08,UA:1439.02,UG:62.55,US:1.32,UZ:.99,VE:179.55,ZA:3.09,ZM:9.82,ZW:.06}})),am4core.ready((function(){am4core.useTheme(am4themes_frozen),am4core.useTheme(am4themes_animated),(e=am4core.create("kt_amcharts_4",am4maps.MapChart)).geodata=am4geodata_worldTimeZoneAreasHigh,e.projection=new am4maps.projections.Miller,e.panBehavior="move",e.colors.getIndex(0),e.colors.getIndex(7),e.colors.getIndex(14);var t=new am4core.InterfaceColorSet,a=e.series.push(new am4maps.MapPolygonSeries);a.useGeodata=!0,a.calculateVisualCenter=!0;var o=a.mapPolygons.template;o.fillOpacity=.6,o.nonScalingStroke=!0,o.strokeWidth=1,o.stroke=t.getFor("background"),o.strokeOpacity=1,o.adapter.add("fill",(function(t,a){return a.dataItem.index>0?e.colors.getIndex(a.dataItem.index):t}));var i=e.series.push(new am4maps.MapPolygonSeries);i.useGeodata=!0,i.geodata=am4geodata_worldLow;var r=i.mapPolygons.template;r.fillOpacity=0,r.nonScalingStroke=!0,r.strokeWidth=1,r.stroke=t.getFor("background"),r.strokeOpacity=.3;var n=e.series.push(new am4maps.MapPolygonSeries);n.geodata=am4geodata_worldTimeZonesHigh,n.useGeodata=!0,n.mapPolygons.template.fill=am4core.color(t.getFor("alternativeBackground")),n.mapPolygons.template.fillOpacity=.07,n.mapPolygons.template.nonScalingStroke=!0,n.mapPolygons.template.strokeWidth=.5,n.mapPolygons.template.strokeOpacity=1,n.mapPolygons.template.stroke=t.getFor("background"),n.tooltipText="{id}",o.states.create("hover").properties.fillOpacity=1,n.mapPolygons.template.states.create("hover").properties.fillOpacity=.3,a.mapPolygons.template.events.on("over",(function(e){var t=n.getPolygonById(e.target.dataItem.dataContext.id);t&&(t.isHover=!0)})),a.mapPolygons.template.events.on("out",(function(e){var t=n.getPolygonById(e.target.dataItem.dataContext.id);t&&(t.isHover=!1)}));var l=e.series.push(new am4maps.MapImageSeries),s=l.mapImages.template.createChild(am4core.Label);s.text="{id}",s.strokeOpacity=0,s.fill=am4core.color("#000000"),s.horizontalCenter="middle",s.fontSize=9,s.nonScaling=!0,l.mapImages.template.adapter.add("longitude",((e,t)=>{t.zIndex=1e5;var o=a.getPolygonById(t.dataItem.dataContext.id);return o?o.visualLongitude:e})),l.mapImages.template.adapter.add("latitude",((e,t)=>{var o=a.getPolygonById(t.dataItem.dataContext.id);return o?o.visualLatitude:e}));var m=e.createChild(am4core.SwitchButton);m.align="right",m.marginTop=40,m.marginRight=40,m.valign="top",m.leftLabel.text="Map",m.rightLabel.text="Globe",m.events.on("toggled",(function(){e.deltaLatitude=0,e.deltaLongitude=0,e.deltaGamma=0,m.isActive?(e.projection=new am4maps.projections.Orthographic,e.panBehavior="rotateLongLat"):(e.projection=new am4maps.projections.Miller,e.panBehavior="move")})),a.events.on("datavalidated",(function(){l.data=a.data}))}))}}}();KTUtil.onDOMContentLoaded((function(){KTGeneralAmChartsMaps.init()}));