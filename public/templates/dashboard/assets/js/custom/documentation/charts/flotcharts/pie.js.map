{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/pie.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA,aAAa,mFAAmF;AAChG,aAAa,qFAAqF;AAClG,aAAa,kFAAkF;AAC/F,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/charts/flotcharts/pie.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoPie = function () {\r\n    // Private functions\r\n    var examplePie = function () {\r\n        var data = [\r\n            { label: \"CSS\", data: 10, color: KTUtil.getCssVariableValue('--bs-active-primary') },\r\n            { label: \"HTML5\", data: 40, color: KTUtil.getCssVariableValue('--bs-active-success') },\r\n            { label: \"PHP\", data: 30, color: KTUtil.getCssVariableValue('--bs-active-danger') },\r\n            { label: \"Angular\", data: 20, color: KTUtil.getCssVariableValue('--bs-active-warning') }\r\n        ];\r\n\r\n        $.plot($(\"#kt_docs_flot_pie\"), data, {\r\n            series: {\r\n                pie: {\r\n                    show: true\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            examplePie();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoPie.init();\r\n});\r\n"], "sourceRoot": ""}