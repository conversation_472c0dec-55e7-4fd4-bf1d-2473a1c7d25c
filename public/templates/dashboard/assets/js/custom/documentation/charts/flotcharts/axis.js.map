{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/axis.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA,GAAG;AACH;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/charts/flotcharts/axis.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoAxis = function () {\r\n    // Private functions\r\n    var exampleAxis = function () {\r\n        function randValue() {\r\n\t\t\treturn (Math.floor(Math.random() * (1 + 40 - 20))) + 20;\r\n\t\t}\r\n\t\tvar pageviews = [\r\n\t\t\t[1, randValue()],\r\n\t\t\t[2, randValue()],\r\n\t\t\t[3, 2 + randValue()],\r\n\t\t\t[4, 3 + randValue()],\r\n\t\t\t[5, 5 + randValue()],\r\n\t\t\t[6, 10 + randValue()],\r\n\t\t\t[7, 15 + randValue()],\r\n\t\t\t[8, 20 + randValue()],\r\n\t\t\t[9, 25 + randValue()],\r\n\t\t\t[10, 30 + randValue()],\r\n\t\t\t[11, 35 + randValue()],\r\n\t\t\t[12, 25 + randValue()],\r\n\t\t\t[13, 15 + randValue()],\r\n\t\t\t[14, 20 + randValue()],\r\n\t\t\t[15, 45 + randValue()],\r\n\t\t\t[16, 50 + randValue()],\r\n\t\t\t[17, 65 + randValue()],\r\n\t\t\t[18, 70 + randValue()],\r\n\t\t\t[19, 85 + randValue()],\r\n\t\t\t[20, 80 + randValue()],\r\n\t\t\t[21, 75 + randValue()],\r\n\t\t\t[22, 80 + randValue()],\r\n\t\t\t[23, 75 + randValue()],\r\n\t\t\t[24, 70 + randValue()],\r\n\t\t\t[25, 65 + randValue()],\r\n\t\t\t[26, 75 + randValue()],\r\n\t\t\t[27, 80 + randValue()],\r\n\t\t\t[28, 85 + randValue()],\r\n\t\t\t[29, 90 + randValue()],\r\n\t\t\t[30, 95 + randValue()]\r\n\t\t];\r\n\t\tvar visitors = [\r\n\t\t\t[1, randValue() - 5],\r\n\t\t\t[2, randValue() - 5],\r\n\t\t\t[3, randValue() - 5],\r\n\t\t\t[4, 6 + randValue()],\r\n\t\t\t[5, 5 + randValue()],\r\n\t\t\t[6, 20 + randValue()],\r\n\t\t\t[7, 25 + randValue()],\r\n\t\t\t[8, 36 + randValue()],\r\n\t\t\t[9, 26 + randValue()],\r\n\t\t\t[10, 38 + randValue()],\r\n\t\t\t[11, 39 + randValue()],\r\n\t\t\t[12, 50 + randValue()],\r\n\t\t\t[13, 51 + randValue()],\r\n\t\t\t[14, 12 + randValue()],\r\n\t\t\t[15, 13 + randValue()],\r\n\t\t\t[16, 14 + randValue()],\r\n\t\t\t[17, 15 + randValue()],\r\n\t\t\t[18, 15 + randValue()],\r\n\t\t\t[19, 16 + randValue()],\r\n\t\t\t[20, 17 + randValue()],\r\n\t\t\t[21, 18 + randValue()],\r\n\t\t\t[22, 19 + randValue()],\r\n\t\t\t[23, 20 + randValue()],\r\n\t\t\t[24, 21 + randValue()],\r\n\t\t\t[25, 14 + randValue()],\r\n\t\t\t[26, 24 + randValue()],\r\n\t\t\t[27, 25 + randValue()],\r\n\t\t\t[28, 26 + randValue()],\r\n\t\t\t[29, 27 + randValue()],\r\n\t\t\t[30, 31 + randValue()]\r\n\t\t];\r\n\r\n\t\tvar plot = $.plot($(\"#kt_docs_flot_axis\"), [{\r\n\t\t\tdata: pageviews,\r\n\t\t\tlabel: \"Unique Visits\",\r\n\t\t\tlines: {\r\n\t\t\t\tlineWidth: 1,\r\n\t\t\t},\r\n\t\t\tshadowSize: 0\r\n\r\n\t\t}, {\r\n\t\t\tdata: visitors,\r\n\t\t\tlabel: \"Page Views\",\r\n\t\t\tlines: {\r\n\t\t\t\tlineWidth: 1,\r\n\t\t\t},\r\n\t\t\tshadowSize: 0\r\n\t\t}], {\r\n\t\t\tseries: {\r\n\t\t\t\tlines: {\r\n\t\t\t\t\tshow: true,\r\n\t\t\t\t\tlineWidth: 2,\r\n\t\t\t\t\tfill: true,\r\n\t\t\t\t\tfillColor: {\r\n\t\t\t\t\t\tcolors: [{\r\n\t\t\t\t\t\t\topacity: 0.05\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\topacity: 0.01\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tpoints: {\r\n\t\t\t\t\tshow: true,\r\n\t\t\t\t\tradius: 3,\r\n\t\t\t\t\tlineWidth: 1\r\n\t\t\t\t},\r\n\t\t\t\tshadowSize: 2\r\n\t\t\t},\r\n\t\t\tgrid: {\r\n\t\t\t\thoverable: true,\r\n\t\t\t\tclickable: true,\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderWidth: 1\r\n\t\t\t},\r\n\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-primary'), KTUtil.getCssVariableValue('--bs-active-danger')],\r\n\t\t\txaxis: {\r\n\t\t\t\tticks: 11,\r\n\t\t\t\ttickDecimals: 0,\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-active-dark'),\r\n\t\t\t},\r\n\t\t\tyaxis: {\r\n\t\t\t\tticks: 11,\r\n\t\t\t\ttickDecimals: 0,\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-active-dark'),\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tfunction showTooltip(x, y, contents) {\r\n\t\t\t$('<div id=\"tooltip\">' + contents + '</div>').css({\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tdisplay: 'none',\r\n\t\t\t\ttop: y + 5,\r\n\t\t\t\tleft: x + 15,\r\n\t\t\t\tborder: '1px solid ' + KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tpadding: '4px',\r\n\t\t\t\tcolor:  + KTUtil.getCssVariableValue('--bs-active-dark'),\r\n\t\t\t\t'border-radius': '3px',\r\n\t\t\t\t'background-color':  + KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\topacity: 0.80\r\n\t\t\t}).appendTo(\"body\").fadeIn(200);\r\n\t\t}\r\n\r\n\t\tvar previousPoint = null;\r\n\t\t$(\"#chart_2\").bind(\"plothover\", function(event, pos, item) {\r\n\t\t\t$(\"#x\").text(pos.x.toFixed(2));\r\n\t\t\t$(\"#y\").text(pos.y.toFixed(2));\r\n\r\n\t\t\tif (item) {\r\n\t\t\t\tif (previousPoint != item.dataIndex) {\r\n\t\t\t\t\tpreviousPoint = item.dataIndex;\r\n\r\n\t\t\t\t\t$(\"#tooltip\").remove();\r\n\t\t\t\t\tvar x = item.datapoint[0].toFixed(2),\r\n\t\t\t\t\t\ty = item.datapoint[1].toFixed(2);\r\n\r\n\t\t\t\t\tshowTooltip(item.pageX, item.pageY, item.series.label + \" of \" + x + \" = \" + y);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t$(\"#tooltip\").remove();\r\n\t\t\t\tpreviousPoint = null;\r\n\t\t\t}\r\n\t\t});\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleAxis();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoAxis.init();\r\n});\r\n"], "sourceRoot": ""}