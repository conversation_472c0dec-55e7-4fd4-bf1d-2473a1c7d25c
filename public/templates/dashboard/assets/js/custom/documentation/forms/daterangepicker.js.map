{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/daterangepicker.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA,K;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/forms/daterangepicker.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsDaterangepickerDemos = function() {\r\n    // Private functions\r\n    var example1 = function(element) {\r\n        $(\"#kt_daterangepicker_1\").daterangepicker();\r\n    }\r\n\r\n    var example2 = function(element) {\r\n        $(\"#kt_daterangepicker_2\").daterangepicker({\r\n            timePicker: true,\r\n            startDate: moment().startOf(\"hour\"),\r\n            endDate: moment().startOf(\"hour\").add(32, \"hour\"),\r\n            locale: {\r\n                format: \"M/DD hh:mm A\"\r\n            }\r\n        });\r\n    }\r\n\r\n    var example3 = function(element) {\r\n        $(\"#kt_daterangepicker_3\").daterangepicker({\r\n                singleDatePicker: true,\r\n                showDropdowns: true,\r\n                minYear: 1901,\r\n                maxYear: parseInt(moment().format(\"YYYY\"),10)\r\n            }, function(start, end, label) {\r\n                var years = moment().diff(start, \"years\");\r\n                alert(\"You are \" + years + \" years old!\");\r\n            }\r\n        );\r\n    }\r\n\r\n    var example4 = function(element) {\r\n        var start = moment().subtract(29, \"days\");\r\n        var end = moment();\r\n\r\n        function cb(start, end) {\r\n            $(\"#kt_daterangepicker_4\").html(start.format(\"MMMM D, YYYY\") + \" - \" + end.format(\"MMMM D, YYYY\"));\r\n        }\r\n\r\n        $(\"#kt_daterangepicker_4\").daterangepicker({\r\n            startDate: start,\r\n            endDate: end,\r\n            ranges: {\r\n            \"Today\": [moment(), moment()],\r\n            \"Yesterday\": [moment().subtract(1, \"days\"), moment().subtract(1, \"days\")],\r\n            \"Last 7 Days\": [moment().subtract(6, \"days\"), moment()],\r\n            \"Last 30 Days\": [moment().subtract(29, \"days\"), moment()],\r\n            \"This Month\": [moment().startOf(\"month\"), moment().endOf(\"month\")],\r\n            \"Last Month\": [moment().subtract(1, \"month\").startOf(\"month\"), moment().subtract(1, \"month\").endOf(\"month\")]\r\n            }\r\n        }, cb);\r\n\r\n        cb(start, end);\r\n    }\r\n\r\n    var example5 = function(element) {\r\n        $(\"#kt_daterangepicker_5\").daterangepicker();\r\n    }    \r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsDaterangepickerDemos.init();\r\n});\r\n"], "sourceRoot": ""}