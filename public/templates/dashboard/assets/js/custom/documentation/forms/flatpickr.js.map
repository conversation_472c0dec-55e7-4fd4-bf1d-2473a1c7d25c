{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/flatpickr.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E", "file": "js/custom/documentation/forms/flatpickr.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsFlatpickrDemos = function() {\r\n    // Private functions\r\n    var example1 = function(element) {\r\n        $(\"#kt_datepicker_1\").flatpickr();\r\n\r\n        $(\"#kt_datepicker_2\").flatpickr();\r\n    }\r\n\r\n    var example2 = function(element) {\r\n        $(\"#kt_datepicker_3\").flatpickr({\r\n            enableTime: true,\r\n            dateFormat: \"Y-m-d H:i\",\r\n        });\r\n    }\r\n\r\n    var example3 = function(element) {\r\n        $(\"#kt_datepicker_4\").flatpickr({\r\n            onReady: function () {\r\n                this.jumpToDate(\"2025-01\")\r\n            },\r\n            disable: [\"2025-01-10\", \"22025-01-11\", \"2025-01-12\", \"2025-01-13\", \"2025-01-14\", \"2025-01-15\", \"2025-01-16\", \"2025-01-17\"],\r\n            dateFormat: \"Y-m-d\",\r\n        });\r\n\r\n        $(\"#kt_datepicker_5\").flatpickr({\r\n            onReady: function () {\r\n                this.jumpToDate(\"2025-01\")\r\n            },\r\n            dateFormat: \"Y-m-d\",\r\n            disable: [\r\n                {\r\n                    from: \"2025-01-05\",\r\n                    to: \"2025-01-25\"\r\n                },\r\n                {\r\n                    from: \"2025-02-03\",\r\n                    to: \"2025-02-15\"\r\n                }\r\n            ]\r\n        });\r\n    }\r\n\r\n    var example4 = function(element) {\r\n        $(\"#kt_datepicker_6\").flatpickr({\r\n            onReady: function () {\r\n                this.jumpToDate(\"2025-01\")\r\n            },\r\n            mode: \"multiple\",\r\n            dateFormat: \"Y-m-d\",\r\n            defaultDate: [\"2025-01-05\", \"2025-01-10\"]\r\n        });\r\n    }\r\n\r\n    var example5 = function(element) {\r\n        $(\"#kt_datepicker_7\").flatpickr({\r\n            altInput: true,\r\n            altFormat: \"F j, Y\",\r\n            dateFormat: \"Y-m-d\",\r\n            mode: \"range\"\r\n        });\r\n    }\r\n\r\n    var example6 = function(element) {\r\n        $(\"#kt_datepicker_8\").flatpickr({\r\n            enableTime: true,\r\n            noCalendar: true,\r\n            dateFormat: \"H:i\",\r\n        });\r\n    }\r\n\r\n    var example7 = function(element) {\r\n        $(\"#kt_datepicker_9\").flatpickr({\r\n            weekNumbers: true\r\n        });\r\n    }\r\n\r\n    var example8 = function(element) {\r\n        $(\"#kt_datepicker_10\").flatpickr();\r\n    }\r\n    \r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n            example6();\r\n            example7();\r\n            example8();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsFlatpickrDemos.init();\r\n});"], "sourceRoot": ""}