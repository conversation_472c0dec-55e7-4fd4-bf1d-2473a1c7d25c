{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/clipboard.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/forms/clipboard.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsClipboard = function () {\r\n    // Shared variables\r\n    var clipboard;\r\n\r\n    // Private functions\r\n    var example1 = function () {\r\n        // Select elements\r\n        const target = document.getElementById('kt_clipboard_1');\r\n        const button = target.nextElementSibling;\r\n\r\n        // Init clipboard -- for more info, please read the offical documentation: https://clipboardjs.com/\r\n        clipboard = new ClipboardJS(button, {\r\n            target: target,\r\n            text: function () {\r\n                return target.value;\r\n            }\r\n        });\r\n\r\n        // Success action handler\r\n        clipboard.on('success', function (e) {\r\n            const currentLabel = button.innerHTML;\r\n\r\n            // Exit label update when already in progress\r\n            if (button.innerHTML === 'Copied!') {\r\n                return;\r\n            }\r\n\r\n            // Update button label\r\n            button.innerHTML = \"Copied!\";\r\n\r\n            // Revert button label after 3 seconds\r\n            setTimeout(function () {\r\n                button.innerHTML = currentLabel;\r\n            }, 3000)\r\n        });\r\n    }\r\n\r\n    var example2 = function () {\r\n        // Select elements\r\n        const target = document.getElementById('kt_clipboard_2');\r\n        const button = target.nextElementSibling;\r\n\r\n        // Init clipboard -- for more info, please read the offical documentation: https://clipboardjs.com/\r\n        clipboard = new ClipboardJS(button, {\r\n            target: target,\r\n            text: function () {\r\n                return target.innerText;\r\n            }\r\n        });\r\n\r\n        // Success action handler\r\n        clipboard.on('success', function (e) {\r\n            const currentLabel = button.innerHTML;\r\n\r\n            // Exit label update when already in progress\r\n            if (button.innerHTML === 'Copied!') {\r\n                return;\r\n            }\r\n\r\n            // Update button label\r\n            button.innerHTML = \"Copied!\";\r\n\r\n            // Revert button label after 3 seconds\r\n            setTimeout(function () {\r\n                button.innerHTML = currentLabel;\r\n            }, 3000)\r\n        });\r\n    }\r\n\r\n    var example3 = function () {\r\n        // Select element\r\n        const target = document.getElementById('kt_clipboard_3');\r\n\r\n        // Init clipboard -- for more info, please read the offical documentation: https://clipboardjs.com/\r\n        clipboard = new ClipboardJS(target);\r\n\r\n        // Success action handler\r\n        clipboard.on('success', function (e) {\r\n            const currentLabel = target.innerHTML;\r\n\r\n            // Exit label update when already in progress\r\n            if (target.innerHTML === 'Copied!') {\r\n                return;\r\n            }\r\n\r\n            // Update button label\r\n            target.innerHTML = \"Copied!\";\r\n\r\n            // Revert button label after 3 seconds\r\n            setTimeout(function () {\r\n                target.innerHTML = currentLabel;\r\n            }, 3000)\r\n        });\r\n    }\r\n\r\n    var example4 = function () {\r\n        // Select elements\r\n        const target = document.getElementById('kt_clipboard_4');\r\n        const button = target.nextElementSibling;\r\n\r\n        // Init clipboard -- for more info, please read the offical documentation: https://clipboardjs.com/\r\n        clipboard = new ClipboardJS(button, {\r\n            target: target,\r\n            text: function () {\r\n                return target.innerHTML;\r\n            }\r\n        });\r\n\r\n        // Success action handler\r\n        clipboard.on('success', function (e) {\r\n            var checkIcon = button.querySelector('.bi.bi-check');\r\n            var svgIcon = button.querySelector('.svg-icon');\r\n\r\n            // Exit check icon when already showing\r\n            if (checkIcon) {\r\n                return;\r\n            }\r\n\r\n            // Create check icon\r\n            checkIcon = document.createElement('i');\r\n            checkIcon.classList.add('bi');\r\n            checkIcon.classList.add('bi-check');\r\n            checkIcon.classList.add('fs-2x');\r\n\r\n            // Append check icon\r\n            button.appendChild(checkIcon);\r\n\r\n            // Highlight target\r\n            const classes = ['text-success', 'fw-boldest'];\r\n            target.classList.add(...classes);\r\n\r\n            // Highlight button\r\n            button.classList.add('btn-success');\r\n\r\n            // Hide copy icon\r\n            svgIcon.classList.add('d-none');\r\n\r\n            // Revert button label after 3 seconds\r\n            setTimeout(function () {\r\n                // Remove check icon\r\n                svgIcon.classList.remove('d-none');\r\n\r\n                // Revert icon\r\n                button.removeChild(checkIcon);\r\n\r\n                // Remove target highlight\r\n                target.classList.remove(...classes);\r\n\r\n                // Remove button highlight\r\n                button.classList.remove('btn-success');\r\n            }, 3000)\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsClipboard.init();\r\n});\r\n"], "sourceRoot": ""}