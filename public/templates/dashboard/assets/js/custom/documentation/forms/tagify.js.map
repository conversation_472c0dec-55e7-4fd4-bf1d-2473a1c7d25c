{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/tagify.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/forms/tagify.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsTagifyDemos = function() {\r\n    // Private functions\r\n    var example1 = function(element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_1\");\r\n        var input2 = document.querySelector(\"#kt_tagify_2\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1, {\r\n            placeholder: \"Type something\"\r\n        });\r\n        new Tagify(input2, {\r\n            placeholder: \"Type something\"\r\n        });\r\n    }\r\n\r\n    var example2 = function(element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_3\");\r\n        var input2 = document.querySelector(\"#kt_tagify_4\");\r\n        var input3 = document.querySelector(\"#kt_tagify_5\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1);\r\n        new Tagify(input2);\r\n        new Tagify(input3);\r\n    }\r\n\r\n    var example3 = function(element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_6\");\r\n        var input2 = document.querySelector(\"#kt_tagify_7\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1, {\r\n            whitelist: [\"A# .NET\", \"A# (Axiom)\", \"A-0 System\", \"A+\", \"A++\", \"ABAP\", \"ABC\", \"ABC ALGOL\", \"ABSET\", \"ABSYS\", \"ACC\", \"Accent\", \"Ace DASL\", \"ACL2\", \"Avicsoft\", \"ACT-III\", \"Action!\", \"ActionScript\", \"Ada\", \"Adenine\", \"Agda\", \"Agilent VEE\", \"Agora\", \"AIMMS\", \"Alef\", \"ALF\", \"ALGOL 58\", \"ALGOL 60\", \"ALGOL 68\", \"ALGOL W\", \"Alice\", \"Alma-0\", \"AmbientTalk\", \"Amiga E\", \"AMOS\", \"AMPL\", \"Apex (Salesforce.com)\", \"APL\", \"AppleScript\", \"Arc\", \"ARexx\", \"Argus\", \"AspectJ\", \"Assembly language\", \"ATS\", \"Ateji PX\", \"AutoHotkey\", \"Autocoder\", \"AutoIt\", \"AutoLISP / Visual LISP\", \"Averest\", \"AWK\", \"Axum\", \"Active Server Pages\", \"ASP.NET\", \"B\", \"Babbage\", \"Bash\", \"BASIC\", \"bc\", \"BCPL\", \"BeanShell\", \"Batch (Windows/Dos)\", \"Bertrand\", \"BETA\", \"Bigwig\", \"Bistro\", \"BitC\", \"BLISS\", \"Blockly\", \"BlooP\", \"Blue\", \"Boo\", \"Boomerang\", \"Bourne shell (including bash and ksh)\", \"BREW\", \"BPEL\", \"B\", \"C--\", \"C++ – ISO/IEC 14882\", \"C# – ISO/IEC 23270\", \"C/AL\", \"Caché ObjectScript\", \"C Shell\", \"Caml\", \"Cayenne\", \"CDuce\", \"Cecil\", \"Cesil\", \"Céu\", \"Ceylon\", \"CFEngine\", \"CFML\", \"Cg\", \"Ch\", \"Chapel\", \"Charity\", \"Charm\", \"Chef\", \"CHILL\", \"CHIP-8\", \"chomski\", \"ChucK\", \"CICS\", \"Cilk\", \"Citrine (programming language)\", \"CL (IBM)\", \"Claire\", \"Clarion\", \"Clean\", \"Clipper\", \"CLIPS\", \"CLIST\", \"Clojure\", \"CLU\", \"CMS-2\", \"COBOL – ISO/IEC 1989\", \"CobolScript – COBOL Scripting language\", \"Cobra\", \"CODE\", \"CoffeeScript\", \"ColdFusion\", \"COMAL\", \"Combined Programming Language (CPL)\", \"COMIT\", \"Common Intermediate Language (CIL)\", \"Common Lisp (also known as CL)\", \"COMPASS\", \"Component Pascal\", \"Constraint Handling Rules (CHR)\", \"COMTRAN\", \"Converge\", \"Cool\", \"Coq\", \"Coral 66\", \"Corn\", \"CorVision\", \"COWSEL\", \"CPL\", \"CPL\", \"Cryptol\", \"csh\", \"Csound\", \"CSP\", \"CUDA\", \"Curl\", \"Curry\", \"Cybil\", \"Cyclone\", \"Cython\", \"Java\", \"Javascript\", \"M2001\", \"M4\", \"M#\", \"Machine code\", \"MAD (Michigan Algorithm Decoder)\", \"MAD/I\", \"Magik\", \"Magma\", \"make\", \"Maple\", \"MAPPER now part of BIS\", \"MARK-IV now VISION:BUILDER\", \"Mary\", \"MASM Microsoft Assembly x86\", \"MATH-MATIC\", \"Mathematica\", \"MATLAB\", \"Maxima (see also Macsyma)\", \"Max (Max Msp – Graphical Programming Environment)\", \"Maya (MEL)\", \"MDL\", \"Mercury\", \"Mesa\", \"Metafont\", \"Microcode\", \"MicroScript\", \"MIIS\", \"Milk (programming language)\", \"MIMIC\", \"Mirah\", \"Miranda\", \"MIVA Script\", \"ML\", \"Model 204\", \"Modelica\", \"Modula\", \"Modula-2\", \"Modula-3\", \"Mohol\", \"MOO\", \"Mortran\", \"Mouse\", \"MPD\", \"Mathcad\", \"MSIL – deprecated name for CIL\", \"MSL\", \"MUMPS\", \"Mystic Programming L\"],\r\n            maxTags: 10,\r\n            dropdown: {\r\n                maxItems: 20,           // <- mixumum allowed rendered suggestions\r\n                classname: \"tagify__inline__suggestions\", // <- custom classname for this dropdown, so it could be targeted\r\n                enabled: 0,             // <- show suggestions on focus\r\n                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n            }\r\n        });\r\n\r\n        new Tagify(input2, {\r\n            whitelist: [\"A# .NET\", \"A# (Axiom)\", \"A-0 System\", \"A+\", \"A++\", \"ABAP\", \"ABC\", \"ABC ALGOL\", \"ABSET\", \"ABSYS\", \"ACC\", \"Accent\", \"Ace DASL\", \"ACL2\", \"Avicsoft\", \"ACT-III\", \"Action!\", \"ActionScript\", \"Ada\", \"Adenine\", \"Agda\", \"Agilent VEE\", \"Agora\", \"AIMMS\", \"Alef\", \"ALF\", \"ALGOL 58\", \"ALGOL 60\", \"ALGOL 68\", \"ALGOL W\", \"Alice\", \"Alma-0\", \"AmbientTalk\", \"Amiga E\", \"AMOS\", \"AMPL\", \"Apex (Salesforce.com)\", \"APL\", \"AppleScript\", \"Arc\", \"ARexx\", \"Argus\", \"AspectJ\", \"Assembly language\", \"ATS\", \"Ateji PX\", \"AutoHotkey\", \"Autocoder\", \"AutoIt\", \"AutoLISP / Visual LISP\", \"Averest\", \"AWK\", \"Axum\", \"Active Server Pages\", \"ASP.NET\", \"B\", \"Babbage\", \"Bash\", \"BASIC\", \"bc\", \"BCPL\", \"BeanShell\", \"Batch (Windows/Dos)\", \"Bertrand\", \"BETA\", \"Bigwig\", \"Bistro\", \"BitC\", \"BLISS\", \"Blockly\", \"BlooP\", \"Blue\", \"Boo\", \"Boomerang\", \"Bourne shell (including bash and ksh)\", \"BREW\", \"BPEL\", \"B\", \"C--\", \"C++ – ISO/IEC 14882\", \"C# – ISO/IEC 23270\", \"C/AL\", \"Caché ObjectScript\", \"C Shell\", \"Caml\", \"Cayenne\", \"CDuce\", \"Cecil\", \"Cesil\", \"Céu\", \"Ceylon\", \"CFEngine\", \"CFML\", \"Cg\", \"Ch\", \"Chapel\", \"Charity\", \"Charm\", \"Chef\", \"CHILL\", \"CHIP-8\", \"chomski\", \"ChucK\", \"CICS\", \"Cilk\", \"Citrine (programming language)\", \"CL (IBM)\", \"Claire\", \"Clarion\", \"Clean\", \"Clipper\", \"CLIPS\", \"CLIST\", \"Clojure\", \"CLU\", \"CMS-2\", \"COBOL – ISO/IEC 1989\", \"CobolScript – COBOL Scripting language\", \"Cobra\", \"CODE\", \"CoffeeScript\", \"ColdFusion\", \"COMAL\", \"Combined Programming Language (CPL)\", \"COMIT\", \"Common Intermediate Language (CIL)\", \"Common Lisp (also known as CL)\", \"COMPASS\", \"Component Pascal\", \"Constraint Handling Rules (CHR)\", \"COMTRAN\", \"Converge\", \"Cool\", \"Coq\", \"Coral 66\", \"Corn\", \"CorVision\", \"COWSEL\", \"CPL\", \"CPL\", \"Cryptol\", \"csh\", \"Csound\", \"CSP\", \"CUDA\", \"Curl\", \"Curry\", \"Cybil\", \"Cyclone\", \"Cython\", \"Java\", \"Javascript\", \"M2001\", \"M4\", \"M#\", \"Machine code\", \"MAD (Michigan Algorithm Decoder)\", \"MAD/I\", \"Magik\", \"Magma\", \"make\", \"Maple\", \"MAPPER now part of BIS\", \"MARK-IV now VISION:BUILDER\", \"Mary\", \"MASM Microsoft Assembly x86\", \"MATH-MATIC\", \"Mathematica\", \"MATLAB\", \"Maxima (see also Macsyma)\", \"Max (Max Msp – Graphical Programming Environment)\", \"Maya (MEL)\", \"MDL\", \"Mercury\", \"Mesa\", \"Metafont\", \"Microcode\", \"MicroScript\", \"MIIS\", \"Milk (programming language)\", \"MIMIC\", \"Mirah\", \"Miranda\", \"MIVA Script\", \"ML\", \"Model 204\", \"Modelica\", \"Modula\", \"Modula-2\", \"Modula-3\", \"Mohol\", \"MOO\", \"Mortran\", \"Mouse\", \"MPD\", \"Mathcad\", \"MSIL – deprecated name for CIL\", \"MSL\", \"MUMPS\", \"Mystic Programming L\"],\r\n            maxTags: 10,\r\n            dropdown: {\r\n                maxItems: 20,           // <- mixumum allowed rendered suggestions\r\n                classname: \"\", // <- custom classname for this dropdown, so it could be targeted\r\n                enabled: 0,             // <- show suggestions on focus\r\n                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n            }\r\n        });\r\n    }\r\n\r\n    var example4 = function(element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_8\");\r\n \r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1);\r\n    }\r\n    \r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsTagifyDemos.init();\r\n});\r\n"], "sourceRoot": ""}