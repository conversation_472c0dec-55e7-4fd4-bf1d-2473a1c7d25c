{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/image-input.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/forms/image-input.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralImageInputDemos = function() {\r\n    // Private functions\r\n    var _exampleBasic = function() {\r\n        \r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            _exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralImageInputDemos.init();\r\n});\r\n"], "sourceRoot": ""}