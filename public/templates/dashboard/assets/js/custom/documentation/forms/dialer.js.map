{"version": 3, "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/dialer.js"], "names": [], "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC", "file": "js/custom/documentation/forms/dialer.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsDialerDemos = function() {\r\n    // Private functions\r\n    var example1 = function(element) {\r\n        // Dialer container element\r\n        var dialerElement = document.querySelector(\"#kt_dialer_example_1\");\r\n\r\n        // Create dialer object and initialize a new instance\r\n        var dialerObject = new KTDialer(dialerElement, {\r\n            min: 1000,\r\n            max: 50000,\r\n            step: 1000,\r\n            prefix: \"$\",\r\n            decimals: 2\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            example1();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsDialerDemos.init();\r\n});\r\n"], "sourceRoot": ""}