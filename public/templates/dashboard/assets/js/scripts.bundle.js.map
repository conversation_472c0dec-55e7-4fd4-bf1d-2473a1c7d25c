{"version": 3, "sources": ["webpack://keenthemes/../src/js/components/blockui.js", "webpack://keenthemes/../src/js/components/cookie.js", "webpack://keenthemes/../src/js/components/dialer.js", "webpack://keenthemes/../src/js/components/drawer.js", "webpack://keenthemes/../src/js/components/event-handler.js", "webpack://keenthemes/../src/js/components/feedback.js", "webpack://keenthemes/../src/js/components/image-input.js", "webpack://keenthemes/../src/js/components/menu.js", "webpack://keenthemes/../src/js/components/password-meter.js", "webpack://keenthemes/../src/js/components/scroll.js", "webpack://keenthemes/../src/js/components/scrolltop.js", "webpack://keenthemes/../src/js/components/search.js", "webpack://keenthemes/../src/js/components/stepper.js", "webpack://keenthemes/../src/js/components/sticky.js", "webpack://keenthemes/../src/js/components/swapper.js", "webpack://keenthemes/../src/js/components/toggle.js", "webpack://keenthemes/../src/js/components/util.js", "webpack://keenthemes/../src/js/layout/app.js", "webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/layout/aside.js", "webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/layout/explore.js", "webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/layout/search.js", "webpack://keenthemes/../../../themes/metronic/html/demo1/src/js/layout/toolbar.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/../../../themes/metronic/html/tools/webpack/scripts.demo1.js"], "names": [], "mappings": ";;;;;;;;;;AAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,0E;AACA;AACA;AACA;;AAEA,2D;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,S;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;ACxKa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,8BAA8B,qCAAqC;AAC1F;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC;AACtC;AACA,aAAa;;AAEb;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,mCAAmC;AACnC;;AAEA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,CAAC;;AAED;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC7Da;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C;;AAE1C;AACA;AACA;AACA;AACA,oE;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8E;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,8D;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,8E;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,uG;AACA;;AAEA;AACA;AACA;AACA;AACA,6B;;AAEA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;ACpPa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,S;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8FAA8F;;AAE9F;;AAEA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,S;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,S;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;ACtWa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC5Fa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC3Ja;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACvMa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,+BAA+B;;AAE/B;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB;AACxB;;AAEA;AACA,mDAAmD,SAAS;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gCAAgC;AAChC,SAAS;AACT;AACA;;AAEA;AACA,6DAA6D;AAC7D;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,iC;AACA;AACA;AACA;;AAEA;;AAEA;AACA,yGAAyG;AACzG,8C;AACA,6C;AACA,4C;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA,+CAA+C,SAAS;AACxD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,S;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,a;AACA;AACA;;AAEA;AACA,gD;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,a;;AAEA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA,K;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,a;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mC;;AAEA;AACA;;AAEA;AACA;;AAEA,iDAAiD;AACjD,yDAAyD;;AAEzD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,uC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA,S;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;;AAEA,yF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,uCAAuC;;AAEvC;;AAEA;AACA;AACA;AACA;;AAEA;AACA,SAAS,E;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,8CAA8C;;AAE9C;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA,qDAAqD,SAAS;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,K;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,2CAA2C,SAAS;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,2CAA2C,SAAS;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gDAAgD,SAAS;AACzD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sDAAsD,SAAS;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACv+Ba;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;;AAEA;AACA;AACA;AACA;AACA,yG;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA,K;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,sEAAsE;AACtE;;AAEA;AACA,oDAAoD;AACpD;;AAEA;AACA,oDAAoD;AACpD;;AAEA;AACA,oDAAoD;AACpD;;AAEA;AACA,uCAAuC,IAAI,0CAA0C;AACrF,K;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA,a;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA,S;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;ACzPa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;;AAE1C;AACA,8B;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,S;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,uDAAuD,SAAS;AAChE;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uDAAuD,SAAS;AAChE;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACvTa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;;AAET;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA,wCAAwC;;AAExC;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACrKa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA,8B;AACA,oD;AACA,8C;AACA;AACA;AACA;AACA,kD;AACA;AACA,oD;;AAEA;AACA,0D;AACA,gD;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qB;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,a;;AAEA;AACA;AACA,aAAa;AACb,S;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA,S;AACA;;AAEA;AACA,4B;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,2D;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,K;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC/aa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,uDAAuD,SAAS;AAChE;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,kDAAkD,SAAS;AAC3D;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACrUa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS,OAAO;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACtSa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;;AAE1C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA,iB;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACzKa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,S;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,S;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;AChNa;;AAEb;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qDAAqD;AACxE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA,SAAS;AACT,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,YAAY;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAA2B;AACtD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,2BAA2B,2BAA2B;AACtD;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;AACA;;AAEA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B;AACA,qBAAqB;AACrB;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA,a;;AAEA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA,aAAa;;AAEb;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gDAAgD,yBAAyB;AACzE;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,2BAA2B,SAAS;AACpC;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;;AAET,0CAA0C;AAC1C;AACA;;AAEA,2BAA2B,sBAAsB;AACjD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;;AAET,+BAA+B;AAC/B;AACA;;AAEA,2BAA2B,sBAAsB;AACjD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,mBAAmB,OAAO;AAC1B,mBAAmB,OAAO;AAC1B,qBAAqB;AACrB;AACA;AACA;AACA;AACA;;AAEA;;AAEA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;;AAEA;AACA,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA;AACA,aAAa;AACb,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;;AAEA;AACA,+BAA+B,uBAAuB;AACtD;AACA;AACA,aAAa;AACb,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,0DAA0D;;AAE1D,uCAAuC;AACvC;AACA;AACA,aAAa;AACb;AACA;AACA,uCAAuC;AACvC;AACA;AACA,SAAS;;AAET;AACA;AACA,iBAAiB,cAAc;AAC/B;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA,aAAa;AACb;;AAEA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,uDAAuD,oBAAoB,gBAAgB;;AAE3F;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,4BAA4B;AAC7C;AACA;AACA;AACA,qBAAqB;;AAErB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8BAA8B;AAC9B,mDAAmD,kBAAkB;;AAErE;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;;;AAGjB,aAAa,0BAA0B;AACvC,mDAAmD,kBAAkB;;AAErE;AACA,8EAA8E;AAC9E;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,wDAAwD,OAAO;AAC/D;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;;AAEA;AACA,uBAAuB,gBAAgB;AACvC;AACA;AACA,0BAA0B;AAC1B;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,2BAA2B,oBAAoB;AAC/C;AACA;AACA,SAAS;;AAET;AACA;AACA,2BAA2B,oBAAoB;AAC/C;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,EAAE;AACf,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,gCAAgC,EAAE;AAClC;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA,oDAAoD,2BAA2B;AAC/E,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,wEAAwE,eAAe;AACvF;AACA;;AAEA;AACA;;AAEA,+BAA+B,uCAAuC,EAAE,uCAAuC,EAAE,uCAAuC;AACxJ,SAAS;;AAET;AACA;AACA;AACA;AACA,wEAAwE,eAAe;;AAEvF;AACA;;AAEA;AACA;;AAEA,+BAA+B,4CAA4C,EAAE,4CAA4C,EAAE,4CAA4C;AACvK,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV,SAAS;;AAET;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA,iBAAiB,WAAW;AAC5B;;AAEA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;;AAET,yC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;AC7hDa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,S;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,qDAAqD;AACrD,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,qDAAqD;AACrD,SAAS;AACT;;AAEA;AACA;;AAEA;AACA,6D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;;AAEjB;AACA,aAAa;AACb,SAAS;AACT,K;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iB;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA,a;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,E;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA,qBAAqB;;AAErB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb,wCAAwC;AACxC;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,aAAa;AACb,S;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,KAA6B;AACjC;AACA,C;;;;;;;;;;;AC3aa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ,QAAQ;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E;;;;;;;;;;;ACzCY;;AAEb;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,a;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E;;;;;;;;;;;ACzBY;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,a;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,K;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wC;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E;;;;;;;;;;;ACtIY;;AAEb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;;AAEA;;AAEA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC,E;;;;;;UCzED;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;ACtBA;AACA,gBAAgB,mBAAO,CAAC,iEAA6B;AACrD,mBAAmB,mBAAO,CAAC,uEAAgC;AAC3D,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,wBAAwB,mBAAO,CAAC,mFAAsC;AACtE,oBAAoB,mBAAO,CAAC,yEAAiC;AAC7D,sBAAsB,mBAAO,CAAC,+EAAoC;AAClE,gBAAgB,mBAAO,CAAC,iEAA6B;AACrD,yBAAyB,mBAAO,CAAC,qFAAuC;AACxE,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,qBAAqB,mBAAO,CAAC,2EAAkC;AAC/D,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,mBAAmB,mBAAO,CAAC,uEAAgC;AAC3D,kBAAkB,mBAAO,CAAC,qEAA+B;AACzD,mBAAmB,mBAAO,CAAC,uEAAgC;AAC3D,kBAAkB,mBAAO,CAAC,qEAA+B;;AAEzD;AACA,eAAe,mBAAO,CAAC,uDAAwB;AAC/C,uBAAuB,mBAAO,CAAC,4FAA0B;AACzD,yBAAyB,mBAAO,CAAC,gGAA4B;AAC7D,wBAAwB,mBAAO,CAAC,8FAA2B;AAC3D,yBAAyB,mBAAO,CAAC,gGAA4B", "file": "js/scripts.bundle.js", "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTBlockUI = function(element, options) {\r\n    //////////////////////////////\r\n    // ** Private variables  ** //\r\n    //////////////////////////////\r\n    var the = this;\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        zIndex: false,\r\n        overlayClass: '',\r\n        overflow: 'hidden',\r\n        message: '<span class=\"spinner-border text-primary\"></span>'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('blockui') ) {\r\n            the = KTUtil.data(element).get('blockui');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.element = element;\r\n        the.overlayElement = null;\r\n        the.blocked = false;\r\n        the.positionChanged = false;\r\n        the.overflowChanged = false;\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('blockui', the);\r\n    }\r\n\r\n    var _block = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.blockui.block', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        var isPage = (the.element.tagName === 'BODY');\r\n       \r\n        var position = KTUtil.css(the.element, 'position');\r\n        var overflow = KTUtil.css(the.element, 'overflow');\r\n        var zIndex = isPage ? 10000 : 1;\r\n\r\n        if (the.options.zIndex > 0) {\r\n            zIndex = the.options.zIndex;\r\n        } else {\r\n            if (KTUtil.css(the.element, 'z-index') != 'auto') {\r\n                zIndex = KTUtil.css(the.element, 'z-index');\r\n            }\r\n        }\r\n\r\n        the.element.classList.add('blockui');\r\n\r\n        if (position === \"absolute\" || position === \"relative\" || position === \"fixed\") {\r\n            KTUtil.css(the.element, 'position', 'relative');\r\n            the.positionChanged = true;\r\n        }\r\n\r\n        if (the.options.overflow === 'hidden' && overflow === 'visible') {           \r\n            KTUtil.css(the.element, 'overflow', 'hidden');\r\n            the.overflowChanged = true;\r\n        }\r\n\r\n        the.overlayElement = document.createElement('DIV');    \r\n        the.overlayElement.setAttribute('class', 'blockui-overlay ' + the.options.overlayClass);\r\n        \r\n        the.overlayElement.innerHTML = the.options.message;\r\n\r\n        KTUtil.css(the.overlayElement, 'z-index', zIndex);\r\n\r\n        the.element.append(the.overlayElement);\r\n        the.blocked = true;\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.blockui.after.blocked', the) === false\r\n    }\r\n\r\n    var _release = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.blockui.release', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        the.element.classList.add('blockui');\r\n        \r\n        if (the.positionChanged) {\r\n            KTUtil.css(the.element, 'position', '');\r\n        }\r\n\r\n        if (the.overflowChanged) {\r\n            KTUtil.css(the.element, 'overflow', '');\r\n        }\r\n\r\n        if (the.overlayElement) {\r\n            KTUtil.remove(the.overlayElement);\r\n        }        \r\n\r\n        the.blocked = false;\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.blockui.released', the);\r\n    }\r\n\r\n    var _isBlocked = function() {\r\n        return the.blocked;\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.block = function() {\r\n        _block();\r\n    }\r\n\r\n    the.release = function() {\r\n        _release();\r\n    }\r\n\r\n    the.isBlocked = function() {\r\n        return _isBlocked();\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTBlockUI.getInstance = function(element) {\r\n    if (element !== null && KTUtil.data(element).has('blockui')) {\r\n        return KTUtil.data(element).get('blockui');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTBlockUI;\r\n}", "\"use strict\";\r\n// DOCS: https://javascript.info/cookie\r\n\r\n// Class definition\r\nvar KTCookie = function() {\r\n    return {\r\n        // returns the cookie with the given name,\r\n        // or undefined if not found\r\n        get: function(name) {\r\n            var matches = document.cookie.match(new RegExp(\r\n                \"(?:^|; )\" + name.replace(/([\\.$?*|{}\\(\\)\\[\\]\\\\\\/\\+^])/g, '\\\\$1') + \"=([^;]*)\"\r\n            ));\r\n\r\n            return matches ? decodeURIComponent(matches[1]) : null;\r\n        },\r\n\r\n        // Please note that a cookie value is encoded,\r\n        // so get<PERSON><PERSON><PERSON> uses a built-in decodeURIComponent function to decode it.\r\n        set: function(name, value, options) {\r\n            if ( typeof options === \"undefined\" || options === null ) {\r\n                options = {};\r\n            }\r\n\r\n            options = Object.assign({}, {\r\n                path: '/'\r\n            }, options);\r\n\r\n            if ( options.expires instanceof Date ) {\r\n                options.expires = options.expires.toUTCString();\r\n            }\r\n\r\n            var updatedCookie = encodeURIComponent(name) + \"=\" + encodeURIComponent(value);\r\n\r\n            for ( var optionKey in options ) {\r\n                if ( options.hasOwnProperty(optionKey) === false ) {\r\n                    continue;\r\n                }\r\n\r\n                updatedCookie += \"; \" + optionKey;\r\n                var optionValue = options[optionKey];\r\n\r\n                if ( optionValue !== true ) {\r\n                    updatedCookie += \"=\" + optionValue;\r\n                }\r\n            }\r\n\r\n            document.cookie = updatedCookie;\r\n        },\r\n\r\n        // To remove a cookie, we can call it with a negative expiration date:\r\n        remove: function(name) {\r\n            this.set(name, \"\", {\r\n                'max-age': -1\r\n            });\r\n        }\r\n    }\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTCookie;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTDialer = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if (!element) {\r\n        return;\r\n    }\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        min: null,\r\n        max: null,\r\n        step: 1,\r\n        decimals: 0,\r\n        prefix: \"\",\r\n        suffix: \"\"\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    // Constructor\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('dialer') === true ) {\r\n            the = KTUtil.data(element).get('dialer');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    // Initialize\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n\r\n        // Elements\r\n        the.element = element;\r\n        the.incElement = the.element.querySelector('[data-kt-dialer-control=\"increase\"]');\r\n        the.decElement = the.element.querySelector('[data-kt-dialer-control=\"decrease\"]');\r\n        the.inputElement = the.element.querySelector('input[type]'); \r\n        \r\n        // Set Values\r\n        if (_getOption('decimals')) {\r\n            the.options.decimals = parseInt(_getOption('decimals'));\r\n        }\r\n        \r\n        if (_getOption('prefix')) {\r\n            the.options.prefix = _getOption('prefix');\r\n        }\r\n        \r\n        if (_getOption('suffix')) {\r\n            the.options.suffix = _getOption('suffix');\r\n        }\r\n        \r\n        if (_getOption('step')) {\r\n            the.options.step = parseFloat(_getOption('step'));\r\n        }\r\n\r\n        if (_getOption('min')) {\r\n            the.options.min = parseFloat(_getOption('min'));\r\n        }\r\n\r\n        if (_getOption('max')) {\r\n            the.options.max = parseFloat(_getOption('max'));\r\n        }\r\n\r\n        the.value = parseFloat(the.inputElement.value.replace(/[^\\d.]/g, ''));  \r\n\r\n        _setValue();\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('dialer', the);\r\n    }\r\n\r\n    // Handlers\r\n    var _handlers = function() {\r\n        KTUtil.addEvent(the.incElement, 'click', function(e) {\r\n            e.preventDefault();\r\n        \r\n            _increase();\r\n        });\r\n\r\n        KTUtil.addEvent(the.decElement, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            _decrease();\r\n        });\r\n\r\n        KTUtil.addEvent(the.inputElement, 'change', function(e) {\r\n            e.preventDefault();\r\n\r\n            _setValue();\r\n        });\r\n    }\r\n\r\n    // Event handlers\r\n    var _increase = function() {\r\n        // Trigger \"after.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.increase', the);\r\n\r\n        the.inputElement.value = the.value + the.options.step;\r\n        _setValue();\r\n\r\n        // Trigger \"before.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.increased', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _decrease = function() {\r\n        // Trigger \"after.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.decrease', the);\r\n\r\n        the.inputElement.value = the.value - the.options.step;        \r\n        _setValue();\r\n\r\n        // Trigger \"before.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.decreased', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    // Set Input Value\r\n    var _setValue = function() {\r\n        // Trigger \"after.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.change', the);\r\n\r\n        the.value = parseFloat(the.inputElement.value.replace(/[^\\d.]/g, '')); \r\n        \r\n        if (the.value < the.options.min) {\r\n            the.value = the.options.min;\r\n        }\r\n\r\n        if (the.value > the.options.max) {\r\n            the.value = the.options.max;\r\n        }\r\n\r\n        the.inputElement.value = _format(the.value);\r\n\r\n        // Trigger \"after.dialer\" event\r\n        KTEventHandler.trigger(the.element, 'kt.dialer.changed', the);\r\n    }\r\n\r\n    // Format\r\n    var _format = function(val){\r\n        return the.options.prefix + parseFloat(val).toFixed(the.options.decimals) + the.options.suffix;              \r\n    }\r\n\r\n    // Get option\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-dialer-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-dialer-' + name);\r\n            var value = attr;            \r\n\r\n            return value;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.increase = function() {\r\n        return _increase();\r\n    }\r\n\r\n    the.decrease = function() {\r\n        return _decrease();\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTDialer.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('dialer') ) {\r\n        return KTUtil.data(element).get('dialer');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTDialer.createInstances = function(selector = '[data-kt-dialer=\"true\"]') {\r\n    // Get instances\r\n    var elements = document.body.querySelectorAll(selector);\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            // Initialize instances\r\n            new KTDialer(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTDialer.init = function() {\r\n    KTDialer.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTDialer.init);\r\n} else {\r\n    KTDialer.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTDialer;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTDrawer = function(element, options) {\r\n    //////////////////////////////\r\n    // ** Private variables  ** //\r\n    //////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        overlay: true,\r\n        direction: 'end',\r\n        baseClass: 'drawer',\r\n        overlayClass: 'drawer-overlay'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('drawer') ) {\r\n            the = KTUtil.data(element).get('drawer');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('drawer');\r\n        the.element = element;\r\n        the.overlayElement = null;\r\n        the.name = the.element.getAttribute('data-kt-drawer-name');\r\n        the.shown = false;\r\n        the.lastWidth;\r\n        the.toggleElement = null;\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-drawer', 'true');\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Update Instance\r\n        _update();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('drawer', the);\r\n    }\r\n\r\n    var _handlers = function() {\r\n        var togglers = _getOption('toggle');\r\n        var closers = _getOption('close');\r\n\r\n        if ( togglers !== null && togglers.length > 0 ) {\r\n            KTUtil.on(body, togglers, 'click', function(e) {\r\n                e.preventDefault();\r\n\r\n                the.toggleElement = this;\r\n                _toggle();\r\n            });\r\n        }\r\n\r\n        if ( closers !== null && closers.length > 0 ) {\r\n            KTUtil.on(body, closers, 'click', function(e) {\r\n                e.preventDefault();\r\n\r\n                the.closeElement = this;\r\n                _hide();\r\n            });\r\n        }\r\n    }\r\n\r\n    var _toggle = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.drawer.toggle', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        if ( the.shown === true ) {\r\n            _hide();\r\n        } else {\r\n            _show();\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.drawer.toggled', the);\r\n    }\r\n\r\n    var _hide = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.drawer.hide', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        the.shown = false;\r\n\r\n        _deleteOverlay();\r\n\r\n        body.removeAttribute('data-kt-drawer-' + the.name, 'on');\r\n        body.removeAttribute('data-kt-drawer');\r\n\r\n        KTUtil.removeClass(the.element, the.options.baseClass + '-on');\r\n\r\n        if ( the.toggleElement !== null ) {\r\n            KTUtil.removeClass(the.toggleElement, 'active');\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.drawer.after.hidden', the) === false\r\n    }\r\n\r\n    var _show = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.drawer.show', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        the.shown = true;\r\n\r\n        _createOverlay();\r\n        body.setAttribute('data-kt-drawer-' + the.name, 'on');\r\n        body.setAttribute('data-kt-drawer', 'on');\r\n\r\n        KTUtil.addClass(the.element, the.options.baseClass + '-on');\r\n\r\n        if ( the.toggleElement !== null ) {\r\n            KTUtil.addClass(the.toggleElement, 'active');\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.drawer.shown', the);\r\n    }\r\n\r\n    var _update = function() {\r\n        var width = _getWidth();\r\n        var direction = _getOption('direction');\r\n\r\n        // Reset state\r\n        if ( KTUtil.hasClass(the.element, the.options.baseClass + '-on') === true && String(body.getAttribute('data-kt-drawer-' + the.name + '-')) === 'on' ) {\r\n            the.shown = true;\r\n        } else {\r\n            the.shown = false;\r\n        }       \r\n\r\n        // Activate/deactivate\r\n        if ( _getOption('activate') === true ) {\r\n            KTUtil.addClass(the.element, the.options.baseClass);\r\n            KTUtil.addClass(the.element, the.options.baseClass + '-' + direction);\r\n            KTUtil.css(the.element, 'width', width, true);\r\n\r\n            the.lastWidth = width;\r\n        } else {\r\n            KTUtil.css(the.element, 'width', '');\r\n\r\n            KTUtil.removeClass(the.element, the.options.baseClass);\r\n            KTUtil.removeClass(the.element, the.options.baseClass + '-' + direction);\r\n\r\n            _hide();\r\n        }\r\n    }\r\n\r\n    var _createOverlay = function() {\r\n        if ( _getOption('overlay') === true ) {\r\n            the.overlayElement = document.createElement('DIV');\r\n\r\n            KTUtil.css(the.overlayElement, 'z-index', KTUtil.css(the.element, 'z-index') - 1); // update\r\n\r\n            body.append(the.overlayElement);\r\n\r\n            KTUtil.addClass(the.overlayElement, _getOption('overlay-class'));\r\n\r\n            KTUtil.addEvent(the.overlayElement, 'click', function(e) {\r\n                e.preventDefault();\r\n                _hide();\r\n            });\r\n        }\r\n    }\r\n\r\n    var _deleteOverlay = function() {\r\n        if ( the.overlayElement !== null ) {\r\n            KTUtil.remove(the.overlayElement);\r\n        }\r\n    }\r\n\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-drawer-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-drawer-' + name);\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    var _getWidth = function() {\r\n        var width = _getOption('width');\r\n\r\n        if ( width === 'auto') {\r\n            width = KTUtil.css(the.element, 'width');\r\n        }\r\n\r\n        return width;\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.toggle = function() {\r\n        return _toggle();\r\n    }\r\n\r\n    the.show = function() {\r\n        return _show();\r\n    }\r\n\r\n    the.hide = function() {\r\n        return _hide();\r\n    }\r\n\r\n    the.isShown = function() {\r\n        return the.shown;\r\n    }\r\n\r\n    the.update = function() {\r\n        _update();\r\n    }\r\n\r\n    the.goElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTDrawer.getInstance = function(element) {\r\n    if (element !== null && KTUtil.data(element).has('drawer')) {\r\n        return KTUtil.data(element).get('drawer');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTDrawer.createInstances = function(selector = '[data-kt-drawer=\"true\"]') {\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Initialize Menus\r\n    var elements = body.querySelectorAll(selector);\r\n    var drawer;\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            drawer = new KTDrawer(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Toggle instances\r\nKTDrawer.handleShow = function() {\r\n    // External drawer toggle handler\r\n    KTUtil.on(document.body,  '[data-kt-drawer-show=\"true\"][data-kt-drawer-target]', 'click', function(e) {\r\n        var element = document.querySelector(this.getAttribute('data-kt-drawer-target'));\r\n\r\n        if (element) {\r\n            KTDrawer.getInstance(element).show();\r\n        } \r\n    });\r\n}\r\n\r\n// Dismiss instances\r\nKTDrawer.handleDismiss = function() {\r\n    // External drawer toggle handler\r\n    KTUtil.on(document.body,  '[data-kt-drawer-dismiss=\"true\"]', 'click', function(e) {\r\n        var element = this.closest('[data-kt-drawer=\"true\"]');\r\n\r\n        if (element) {\r\n            var drawer = KTDrawer.getInstance(element);\r\n            if (drawer.isShown()) {\r\n                drawer.hide();\r\n            }\r\n        } \r\n    });\r\n}\r\n\r\n// Window resize Handling\r\nwindow.addEventListener('resize', function() {\r\n    var timer;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    KTUtil.throttle(timer, function() {\r\n        // Locate and update drawer instances on window resize\r\n        var elements = body.querySelectorAll('[data-kt-drawer=\"true\"]');\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for (var i = 0, len = elements.length; i < len; i++) {\r\n                var drawer = KTDrawer.getInstance(elements[i]);\r\n                if (drawer) {\r\n                    drawer.update();\r\n                }\r\n            }\r\n        }\r\n    }, 200);\r\n});\r\n\r\n// Global initialization\r\nKTDrawer.init = function() {\r\n    KTDrawer.createInstances();\r\n    KTDrawer.handleShow();\r\n    KTDrawer.handleDismiss();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTDrawer.init);\r\n} else {\r\n    KTDrawer.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTDrawer;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTEventHandler = function() {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var _handlers = {};\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n    var _triggerEvent = function(element, name, target, e) {\r\n        if ( KTUtil.data(element).has(name) === true ) {\r\n            var handlerId = KTUtil.data(element).get(name);\r\n\r\n            if ( _handlers[name] && _handlers[name][handlerId] ) {\r\n                var handler = _handlers[name][handlerId];\r\n\r\n                if ( handler.name === name ) {\r\n                    if ( handler.one == true ) {\r\n                        if ( handler.fired == false ) {\r\n                            _handlers[name][handlerId].fired = true;\r\n\r\n                            return handler.callback.call(this, target, e);\r\n                        }\r\n                    } else {\r\n                        return handler.callback.call(this, target, e);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    var _addEvent = function(element, name, callback, one) {\r\n        var handlerId = KTUtil.getUniqueId('event');\r\n\r\n        KTUtil.data(element).set(name, handlerId);\r\n\r\n        if ( !_handlers[name] ) {\r\n            _handlers[name] = {};\r\n        }\r\n\r\n        _handlers[name][handlerId] = {\r\n            name: name,\r\n            callback: callback,\r\n            one: one,\r\n            fired: false\r\n        };\r\n    }\r\n\r\n    var _removeEvent = function(element, name) {\r\n        var handlerId = KTUtil.data(element).get(name);\r\n\r\n        if (_handlers[name] && _handlers[name][handlerId]) {\r\n            delete _handlers[name][handlerId];\r\n        }\r\n    }\r\n\r\n    ////////////////////////////\r\n    // ** Public Methods  ** //\r\n    ////////////////////////////\r\n    return {\r\n        trigger: function(element, name, target, e) {\r\n            return _triggerEvent(element, name, target, e);\r\n        },\r\n\r\n        on: function(element, name, handler) {\r\n            return _addEvent(element, name, handler);\r\n        },\r\n\r\n        one: function(element, name, handler) {\r\n            return _addEvent(element, name, handler, true);\r\n        },\r\n\r\n        off: function(element, name) {\r\n            return _removeEvent(element, name);\r\n        },\r\n\r\n        debug: function() {\r\n            for (var b in _handlers) {\r\n                if ( _handlers.hasOwnProperty(b) ) console.log(b);\r\n            }\r\n        }\r\n    }\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTEventHandler;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTFeedback = function(options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        'width' : 100,\r\n        'placement' : 'top-center',\r\n        'content' : '',\r\n        'type': 'popup'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        _init();\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('feedback');\r\n        the.element;\r\n        the.shown = false;\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('feedback', the);\r\n    }\r\n\r\n    var _handlers = function() {\r\n        KTUtil.addEvent(the.element, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            _go();\r\n        });\r\n    }\r\n\r\n    var _show = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.feedback.show', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        if ( the.options.type === 'popup') {\r\n            _showPopup();\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.feedback.shown', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _hide = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.feedback.hide', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        if ( the.options.type === 'popup') {\r\n            _hidePopup();\r\n        }\r\n\r\n        the.shown = false;\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.feedback.hidden', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _showPopup = function() {\r\n        the.element = document.createElement(\"DIV\");\r\n\r\n        KTUtil.addClass(the.element, 'feedback feedback-popup');\r\n        KTUtil.setHTML(the.element, the.options.content);\r\n\r\n        if (the.options.placement == 'top-center') {\r\n            _setPopupTopCenterPosition();\r\n        }\r\n\r\n        body.appendChild(the.element);\r\n\r\n        KTUtil.addClass(the.element, 'feedback-shown');\r\n\r\n        the.shown = true;\r\n    }\r\n\r\n    var _setPopupTopCenterPosition = function() {\r\n        var width = KTUtil.getResponsiveValue(the.options.width);\r\n        var height = KTUtil.css(the.element, 'height');\r\n\r\n        KTUtil.addClass(the.element, 'feedback-top-center');\r\n\r\n        KTUtil.css(the.element, 'width', width);\r\n        KTUtil.css(the.element, 'left', '50%');\r\n        KTUtil.css(the.element, 'top', '-' + height);\r\n    }\r\n\r\n    var _hidePopup = function() {\r\n        the.element.remove();\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.show = function() {\r\n        return _show();\r\n    }\r\n\r\n    the.hide = function() {\r\n        return _hide();\r\n    }\r\n\r\n    the.isShown = function() {\r\n        return the.shown;\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTFeedback;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTImageInput = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        \r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('image-input') === true ) {\r\n            the = KTUtil.data(element).get('image-input');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('image-input');\r\n\r\n        // Elements\r\n        the.element = element;\r\n        the.inputElement = KTUtil.find(element, 'input[type=\"file\"]');\r\n        the.wrapperElement = KTUtil.find(element, '.image-input-wrapper');\r\n        the.cancelElement = KTUtil.find(element, '[data-kt-image-input-action=\"cancel\"]');\r\n        the.removeElement = KTUtil.find(element, '[data-kt-image-input-action=\"remove\"]');\r\n        the.hiddenElement = KTUtil.find(element, 'input[type=\"hidden\"]');\r\n        the.src = KTUtil.css(the.wrapperElement, 'backgroundImage');\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-image-input', 'true');\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('image-input', the);\r\n    }\r\n\r\n    // Init Event Handlers\r\n    var _handlers = function() {\r\n        KTUtil.addEvent(the.inputElement, 'change', _change);\r\n        KTUtil.addEvent(the.cancelElement, 'click', _cancel);\r\n        KTUtil.addEvent(the.removeElement, 'click', _remove);\r\n    }\r\n\r\n    // Event Handlers\r\n    var _change = function(e) {\r\n        e.preventDefault();\r\n\r\n        if ( the.inputElement !== null && the.inputElement.files && the.inputElement.files[0] ) {\r\n            // Fire change event\r\n            if ( KTEventHandler.trigger(the.element, 'kt.imageinput.change', the) === false ) {\r\n                return;\r\n            }\r\n\r\n            var reader = new FileReader();\r\n\r\n            reader.onload = function(e) {\r\n                KTUtil.css(the.wrapperElement, 'background-image', 'url('+ e.target.result +')');\r\n            }\r\n\r\n            reader.readAsDataURL(the.inputElement.files[0]);\r\n\r\n            KTUtil.addClass(the.element, 'image-input-changed');\r\n            KTUtil.removeClass(the.element, 'image-input-empty');\r\n\r\n            // Fire removed event\r\n            KTEventHandler.trigger(the.element, 'kt.imageinput.changed', the);\r\n        }\r\n    }\r\n\r\n    var _cancel = function(e) {\r\n        e.preventDefault();\r\n\r\n        // Fire cancel event\r\n        if ( KTEventHandler.trigger(the.element, 'kt.imageinput.cancel', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        KTUtil.removeClass(the.element, 'image-input-changed');\r\n        KTUtil.removeClass(the.element, 'image-input-empty');\r\n        KTUtil.css(the.wrapperElement, 'background-image', the.src);\r\n        the.inputElement.value = \"\";\r\n\r\n        if ( the.hiddenElement !== null ) {\r\n            the.hiddenElement.value = \"0\";\r\n        }\r\n\r\n        // Fire canceled event\r\n        KTEventHandler.trigger(the.element, 'kt.imageinput.canceled', the);\r\n    }\r\n\r\n    var _remove = function(e) {\r\n        e.preventDefault();\r\n\r\n        // Fire remove event\r\n        if ( KTEventHandler.trigger(the.element, 'kt.imageinput.remove', the) === false ) {\r\n            return;\r\n        }\r\n\r\n        KTUtil.removeClass(the.element, 'image-input-changed');\r\n        KTUtil.addClass(the.element, 'image-input-empty');\r\n        KTUtil.css(the.wrapperElement, 'background-image', \"none\");\r\n        the.inputElement.value = \"\";\r\n\r\n        if ( the.hiddenElement !== null ) {\r\n            the.hiddenElement.value = \"1\";\r\n        }\r\n\r\n        // Fire removed event\r\n        KTEventHandler.trigger(the.element, 'kt.imageinput.removed', the);\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.getInputElement = function() {\r\n        return the.inputElement;\r\n    }\r\n\r\n    the.goElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTImageInput.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('image-input') ) {\r\n        return KTUtil.data(element).get('image-input');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTImageInput.createInstances = function(selector = '[data-kt-image-input]') {\r\n    // Initialize Menus\r\n    var elements = document.querySelectorAll(selector);\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            new KTImageInput(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTImageInput.init = function() {\r\n    KTImageInput.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTImageInput.init);\r\n} else {\r\n    KTImageInput.init();\r\n}\r\n\r\n// Webpack Support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTImageInput;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTMenu = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        dropdown: {\r\n            hoverTimeout: 200,\r\n            zindex: 105\r\n        },\r\n\r\n        accordion: {\r\n            slideSpeed: 250,\r\n            expand: false\r\n        }\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('menu') === true ) {\r\n            the = KTUtil.data(element).get('menu');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('menu');\r\n        the.element = element;\r\n        the.triggerElement;\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-menu', 'true');\r\n\r\n        _setTriggerElement();\r\n        _update();\r\n\r\n        KTUtil.data(the.element).set('menu', the);\r\n    }\r\n\r\n    var _destroy = function() {  // todo\r\n\r\n    }\r\n\r\n    // Event Handlers\r\n    // Toggle handler\r\n    var _click = function(element, e) {\r\n        e.preventDefault();\r\n\r\n        var item = _getItemElement(element);\r\n\r\n        if ( _getItemOption(item, 'trigger') !== 'click' ) {\r\n            return;\r\n        }\r\n\r\n        if ( _getItemOption(item, 'toggle') === false ) {\r\n            _show(item);\r\n        } else {\r\n            _toggle(item);\r\n        }\r\n    }\r\n\r\n    // Link handler\r\n    var _link = function(element, e) {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.menu.link.click', the) === false )  {\r\n            return;\r\n        }\r\n\r\n        // Dismiss all shown dropdowns\r\n        KTMenu.hideDropdowns();\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.menu.link.clicked', the);\r\n    }\r\n\r\n    // Dismiss handler\r\n    var _dismiss = function(element, e) {\r\n        var item = _getItemElement(element);\r\n        var items = _getItemChildElements(item);\r\n\r\n        if ( item !== null && _getItemSubType(item) === 'dropdown') {\r\n            _hide(item); // hide items dropdown\r\n            // Hide all child elements as well\r\n            \r\n            if ( items.length > 0 ) {\r\n                for (var i = 0, len = items.length; i < len; i++) {\r\n                    if ( items[i] !== null &&  _getItemSubType(items[i]) === 'dropdown') {\r\n                        _hide(tems[i]);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Mouseover handle\r\n    var _mouseover = function(element, e) {\r\n        var item = _getItemElement(element);\r\n\r\n        if ( item === null ) {\r\n            return;\r\n        }\r\n\r\n        if ( _getItemOption(item, 'trigger') !== 'hover' ) {\r\n            return;\r\n        }\r\n\r\n        if ( KTUtil.data(item).get('hover') === '1' ) {\r\n            clearTimeout(KTUtil.data(item).get('timeout'));\r\n            KTUtil.data(item).remove('hover');\r\n            KTUtil.data(item).remove('timeout');\r\n        }\r\n\r\n        _show(item);\r\n    }\r\n\r\n    // Mouseout handle\r\n    var _mouseout = function(element, e) {\r\n        var item = _getItemElement(element);\r\n\r\n        if ( item === null ) {\r\n            return;\r\n        }\r\n\r\n        if ( _getItemOption(item, 'trigger') !== 'hover' ) {\r\n            return;\r\n        }\r\n\r\n        var timeout = setTimeout(function() {\r\n            if ( KTUtil.data(item).get('hover') === '1' ) {\r\n                _hide(item);\r\n            }\r\n        }, the.options.dropdown.hoverTimeout);\r\n\r\n        KTUtil.data(item).set('hover', '1');\r\n        KTUtil.data(item).set('timeout', timeout);\r\n    }\r\n\r\n    // Toggle item sub\r\n    var _toggle = function(item) {\r\n        if ( !item ) {\r\n            item = the.triggerElement;\r\n        }\r\n\r\n        if ( _isItemSubShown(item) === true ) {\r\n            _hide(item);\r\n        } else {\r\n            _show(item);\r\n        }\r\n    }\r\n\r\n    // Show item sub\r\n    var _show = function(item) {\r\n        if ( !item ) {\r\n            item = the.triggerElement;\r\n        }\r\n\r\n        if ( _isItemSubShown(item) === true ) {\r\n            return;\r\n        }\r\n\r\n        if ( _getItemSubType(item) === 'dropdown' ) {\r\n            _showDropdown(item); // // show current dropdown\r\n        } else if ( _getItemSubType(item) === 'accordion' ) {\r\n            _showAccordion(item);\r\n        }\r\n\r\n        // Remember last submenu type\r\n        KTUtil.data(item).set('type', _getItemSubType(item));  // updated\r\n    }\r\n\r\n    // Hide item sub\r\n    var _hide = function(item) {\r\n        if ( !item ) {\r\n            item = the.triggerElement;\r\n        }\r\n\r\n        if ( _isItemSubShown(item) === false ) {\r\n            return;\r\n        }\r\n        \r\n        if ( _getItemSubType(item) === 'dropdown' ) {\r\n            _hideDropdown(item);\r\n        } else if ( _getItemSubType(item) === 'accordion' ) {\r\n            _hideAccordion(item);\r\n        }\r\n    }\r\n\r\n    // Reset item state classes if item sub type changed\r\n    var _reset = function(item) {        \r\n        if ( _hasItemSub(item) === false ) {\r\n            return;\r\n        }\r\n\r\n        var sub = _getItemSubElement(item);\r\n\r\n        // Reset sub state if sub type is changed during the window resize\r\n        if ( KTUtil.data(item).has('type') && KTUtil.data(item).get('type') !== _getItemSubType(item) ) {  // updated\r\n            KTUtil.removeClass(item, 'hover'); \r\n            KTUtil.removeClass(item, 'show'); \r\n            KTUtil.removeClass(sub, 'show'); \r\n        }  // updated\r\n    }\r\n\r\n    // Update all item state classes if item sub type changed\r\n    var _update = function() {\r\n        var items = the.element.querySelectorAll('.menu-item[data-kt-menu-trigger]');\r\n\r\n        if ( items && items.length > 0 ) {\r\n            for (var i = 0, len = items.length; i < len; i++) {\r\n                _reset(items[i]);\r\n            }\r\n        }\r\n    }\r\n\r\n    // Set external trigger element\r\n    var _setTriggerElement = function() {\r\n        var target = document.querySelector('[data-kt-menu-target=\"# ' + the.element.getAttribute('id')  + '\"]');\r\n\r\n        if ( target !== null ) {\r\n            the.triggerElement = target;\r\n        } else if ( the.element.closest('[data-kt-menu-trigger]') ) {\r\n            the.triggerElement = the.element.closest('[data-kt-menu-trigger]');\r\n        } else if ( the.element.parentNode && KTUtil.child(the.element.parentNode, '[data-kt-menu-trigger]')) {\r\n            the.triggerElement = KTUtil.child(the.element.parentNode, '[data-kt-menu-trigger]');\r\n        }\r\n\r\n        if ( the.triggerElement ) {\r\n            KTUtil.data(the.triggerElement).set('menu', the);\r\n        }\r\n    }\r\n\r\n    // Test if menu has external trigger element\r\n    var _isTriggerElement = function(item) {\r\n        return ( the.triggerElement === item ) ? true : false;\r\n    }\r\n\r\n    // Test if item's sub is shown\r\n    var _isItemSubShown = function(item) {\r\n        var sub = _getItemSubElement(item);\r\n\r\n        if ( sub !== null ) {\r\n            if ( _getItemSubType(item) === 'dropdown' ) {\r\n                if ( KTUtil.hasClass(sub, 'show') === true && sub.hasAttribute('data-popper-placement') === true ) {\r\n                    return true;\r\n                } else {\r\n                    return false;\r\n                }\r\n            } else {\r\n                return KTUtil.hasClass(item, 'show');\r\n            }\r\n        } else {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    // Test if item dropdown is permanent\r\n    var _isItemDropdownPermanent = function(item) {\r\n        return _getItemOption(item, 'permanent') === true ? true : false;\r\n    }\r\n\r\n    // Test if item's parent is shown\r\n    var _isItemParentShown = function(item) {\r\n        return KTUtil.parents(item, '.menu-item.show').length > 0;\r\n    }\r\n\r\n    // Test of it is item sub element\r\n    var _isItemSubElement = function(item) {\r\n        return KTUtil.hasClass(item, 'menu-sub');\r\n    }\r\n\r\n    // Test if item has sub\r\n    var _hasItemSub = function(item) {\r\n        return (KTUtil.hasClass(item, 'menu-item') && item.hasAttribute('data-kt-menu-trigger'));\r\n    }\r\n\r\n    // Get link element\r\n    var _getItemLinkElement = function(item) {\r\n        return KTUtil.child(item, '.menu-link');\r\n    }\r\n\r\n    // Get toggle element\r\n    var _getItemToggleElement = function(item) {\r\n        if ( the.triggerElement ) {\r\n            return the.triggerElement;\r\n        } else {\r\n            return _getItemLinkElement(item);\r\n        }\r\n    }\r\n\r\n    // Get item sub element\r\n    var _getItemSubElement = function(item) {\r\n        if ( _isTriggerElement(item) === true ) {\r\n            return the.element;\r\n        } if ( item.classList.contains('menu-sub') === true ) {\r\n            return item;\r\n        } else if ( KTUtil.data(item).has('sub') ) {\r\n            return KTUtil.data(item).get('sub');\r\n        } else {\r\n            return KTUtil.child(item, '.menu-sub');\r\n        }\r\n    }\r\n\r\n    // Get item sub type\r\n    var _getItemSubType = function(element) {\r\n        var sub = _getItemSubElement(element);\r\n\r\n        if ( sub && parseInt(KTUtil.css(sub, 'z-index')) > 0 ) {\r\n            return \"dropdown\";\r\n        } else {\r\n            return \"accordion\";\r\n        }\r\n    }\r\n\r\n    // Get item element\r\n    var _getItemElement = function(element) {\r\n        var item, sub;\r\n\r\n        // Element is the external trigger element\r\n        if (_isTriggerElement(element) ) {\r\n            return element;\r\n        }   \r\n\r\n        // Element has item toggler attribute\r\n        if ( element.hasAttribute('data-kt-menu-trigger') ) {\r\n            return element;\r\n        }\r\n\r\n        // Element has item DOM reference in it's data storage\r\n        if ( KTUtil.data(element).has('item') ) {\r\n            return KTUtil.data(element).get('item');\r\n        }\r\n\r\n        // Item is parent of element\r\n        if ( (item = element.closest('.menu-item[data-kt-menu-trigger]')) ) {\r\n            return item;\r\n        }\r\n\r\n        // Element's parent has item DOM reference in it's data storage\r\n        if ( (sub = element.closest('.menu-sub')) ) {\r\n            if ( KTUtil.data(sub).has('item') === true ) {\r\n                return KTUtil.data(sub).get('item')\r\n            } \r\n        }\r\n    }\r\n\r\n    // Get item parent element\r\n    var _getItemParentElement = function(item) {  \r\n        var sub = item.closest('.menu-sub');\r\n        var parentItem;\r\n\r\n        if ( KTUtil.data(sub).has('item') ) {\r\n            return KTUtil.data(sub).get('item');\r\n        }\r\n\r\n        if ( sub && (parentItem = sub.closest('.menu-item[data-kt-menu-trigger]')) ) {\r\n            return parentItem;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    // Get item parent elements\r\n    var _getItemParentElements = function(item) {\r\n        var parents = [];\r\n        var parent;\r\n        var i = 0;\r\n\r\n        do {\r\n            parent = _getItemParentElement(item);\r\n            \r\n            if ( parent ) {\r\n                parents.push(parent);\r\n                item = parent;\r\n            }           \r\n\r\n            i++;\r\n        } while (parent !== null && i < 20);\r\n\r\n        if ( the.triggerElement ) {\r\n            parents.unshift(the.triggerElement);\r\n        }\r\n\r\n        return parents;\r\n    }\r\n\r\n    // Get item child element\r\n    var _getItemChildElement = function(item) {\r\n        var selector = item;\r\n        var element;\r\n\r\n        if ( KTUtil.data(item).get('sub') ) {\r\n            selector = KTUtil.data(item).get('sub');\r\n        }\r\n\r\n        if ( selector !== null ) {\r\n            //element = selector.querySelector('.show.menu-item[data-kt-menu-trigger]');\r\n            element = selector.querySelector('.menu-item[data-kt-menu-trigger]');\r\n\r\n            if ( element ) {\r\n                return element;\r\n            } else {\r\n                return null;\r\n            }\r\n        } else {\r\n            return null;\r\n        }\r\n    }   \r\n    \r\n    // Get item child elements\r\n    var _getItemChildElements = function(item) {\r\n        var children = [];\r\n        var child;\r\n        var i = 0;\r\n\r\n        do {\r\n            child = _getItemChildElement(item);\r\n            \r\n            if ( child ) {\r\n                children.push(child);\r\n                item = child;\r\n            }           \r\n\r\n            i++;\r\n        } while (child !== null && i < 20);\r\n\r\n        return children;\r\n    }\r\n\r\n    // Show item dropdown\r\n    var _showDropdown = function(item) {\r\n        // Handle dropdown show event\r\n        if ( KTEventHandler.trigger(the.element, 'kt.menu.dropdown.show', item) === false )  {\r\n            return;\r\n        }\r\n\r\n        // Hide all currently shown dropdowns except current one\r\n        KTMenu.hideDropdowns(item); \r\n\r\n        var toggle = _isTriggerElement(item) ? item : _getItemLinkElement(item);\r\n        var sub = _getItemSubElement(item);\r\n\r\n        var width = _getItemOption(item, 'width');\r\n        var height = _getItemOption(item, 'height');\r\n\r\n        var zindex = the.options.dropdown.zindex; // update\r\n        var parentZindex = KTUtil.getHighestZindex(item); // update\r\n\r\n        // Apply a new z-index if dropdown's toggle element or it's parent has greater z-index // update\r\n        if ( parentZindex !== null && parentZindex >= zindex ) {\r\n            zindex = parentZindex + 1;\r\n        }\r\n\r\n        if ( zindex > 0 ) {\r\n            KTUtil.css(sub, 'z-index', zindex);\r\n        }\r\n\r\n        if ( width !== null ) {\r\n            KTUtil.css(sub, 'width', width);\r\n        }\r\n\r\n        if ( height !== null ) {\r\n            KTUtil.css(sub, 'height', height);\r\n        }\r\n\r\n        KTUtil.css(sub, 'display', '');\r\n        KTUtil.css(sub, 'overflow', '');\r\n\r\n        // Init popper(new)\r\n        _initDropdownPopper(item, sub); \r\n\r\n        KTUtil.addClass(item, 'show');\r\n        KTUtil.addClass(item, 'menu-dropdown');\r\n        KTUtil.addClass(sub, 'show');\r\n\r\n        // Append the sub the the root of the menu\r\n        if ( _getItemOption(item, 'overflow') === true ) {\r\n            document.body.appendChild(sub);\r\n            KTUtil.data(item).set('sub', sub);\r\n            KTUtil.data(sub).set('item', item);\r\n            KTUtil.data(sub).set('menu', the);\r\n        } else {\r\n            KTUtil.data(sub).set('item', item);\r\n        }\r\n\r\n        // Handle dropdown shown event\r\n        KTEventHandler.trigger(the.element, 'kt.menu.dropdown.shown', item);\r\n    }\r\n\r\n    // Hide item dropdown\r\n    var _hideDropdown = function(item) {\r\n        // Handle dropdown hide event\r\n        if ( KTEventHandler.trigger(the.element, 'kt.menu.dropdown.hide', item) === false )  {\r\n            return;\r\n        }\r\n\r\n        var sub = _getItemSubElement(item);\r\n\r\n        KTUtil.css(sub, 'z-index', '');\r\n        KTUtil.css(sub, 'width', '');\r\n        KTUtil.css(sub, 'height', '');\r\n\r\n        KTUtil.removeClass(item, 'show');\r\n        KTUtil.removeClass(item, 'menu-dropdown');\r\n        KTUtil.removeClass(sub, 'show');\r\n\r\n        // Append the sub back to it's parent\r\n        if ( _getItemOption(item, 'overflow') === true ) {\r\n            if (item.classList.contains('menu-item')) {\r\n                item.appendChild(sub);\r\n            } else {\r\n                KTUtil.insertAfter(the.element, item);\r\n            }\r\n            \r\n            KTUtil.data(item).remove('sub');\r\n            KTUtil.data(sub).remove('item');\r\n            KTUtil.data(sub).remove('menu');\r\n        } \r\n\r\n        // Destroy popper(new)\r\n        _destroyDropdownPopper(item);\r\n        \r\n        // Handle dropdown hidden event \r\n        KTEventHandler.trigger(the.element, 'kt.menu.dropdown.hidden', item);\r\n    }\r\n\r\n    // Init dropdown popper(new)\r\n    var _initDropdownPopper = function(item, sub) {\r\n        // Setup popper instance\r\n        var reference;\r\n        var attach = _getItemOption(item, 'attach');\r\n\r\n        if ( attach ) {\r\n            if ( attach === 'parent') {\r\n                reference = item.parentNode;\r\n            } else {\r\n                reference = document.querySelector(attach);\r\n            }\r\n        } else {\r\n            reference = item;\r\n        }\r\n\r\n        var popper = Popper.createPopper(reference, sub, _getDropdownPopperConfig(item)); \r\n        KTUtil.data(item).set('popper', popper);\r\n    }\r\n\r\n    // Destroy dropdown popper(new)\r\n    var _destroyDropdownPopper = function(item) {\r\n        if ( KTUtil.data(item).has('popper') === true ) {\r\n            KTUtil.data(item).get('popper').destroy();\r\n            KTUtil.data(item).remove('popper');\r\n        }\r\n    }\r\n\r\n    // Prepare popper config for dropdown(see: https://popper.js.org/docs/v2/)\r\n    var _getDropdownPopperConfig = function(item) {\r\n        // Placement\r\n        var placement = _getItemOption(item, 'placement');\r\n        if (!placement) {\r\n            placement = 'right';\r\n        }\r\n\r\n        // Flip\r\n        var flipValue = _getItemOption(item, 'flip');\r\n        var flip = flipValue ? flipValue.split(\",\") : [];\r\n\r\n        // Offset\r\n        var offsetValue = _getItemOption(item, 'offset');\r\n        var offset = offsetValue ? offsetValue.split(\",\") : [];\r\n\r\n        // Strategy\r\n        var strategy = _getItemOption(item, 'overflow') === true ? 'absolute' : 'fixed';\r\n\r\n        var popperConfig = {\r\n            placement: placement,\r\n            strategy: strategy,\r\n            modifiers: [{\r\n                name: 'offset',\r\n                options: {\r\n                    offset: offset\r\n                }\r\n            }, {\r\n                name: 'preventOverflow',\r\n                options: {\r\n                    //altBoundary: true,\r\n                    //altAxis: true,\r\n                    rootBoundary: 'clippingParents'\r\n                }\r\n            }, {\r\n                name: 'flip', \r\n                options: {\r\n                    altBoundary: true,\r\n                    fallbackPlacements: flip\r\n                }\r\n            }]\r\n        };\r\n\r\n        return popperConfig;\r\n    }\r\n\r\n    // Show item accordion\r\n    var _showAccordion = function(item) {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.menu.accordion.show', item) === false )  {\r\n            return;\r\n        }\r\n\r\n        if ( the.options.accordion.expand === false ) {\r\n            _hideAccordions(item);\r\n        }\r\n\r\n        var sub = _getItemSubElement(item);\r\n\r\n        if ( KTUtil.data(item).has('popper') === true ) {\r\n            _hideDropdown(item);\r\n        }\r\n\r\n        KTUtil.addClass(item, 'hover'); // updateWW\r\n\r\n        KTUtil.addClass(item, 'showing');\r\n\r\n        KTUtil.slideDown(sub, the.options.accordion.slideSpeed, function() {\r\n            KTUtil.removeClass(item, 'showing');\r\n            KTUtil.addClass(item, 'show');\r\n            KTUtil.addClass(sub, 'show');\r\n\r\n            KTEventHandler.trigger(the.element, 'kt.menu.accordion.shown', item);\r\n        });        \r\n    }\r\n\r\n    // Hide item accordion\r\n    var _hideAccordion = function(item) {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.menu.accordion.hide', item) === false )  {\r\n            return;\r\n        }\r\n        \r\n        var sub = _getItemSubElement(item);\r\n\r\n        KTUtil.addClass(item, 'hiding');\r\n\r\n        KTUtil.slideUp(sub, the.options.accordion.slideSpeed, function() {\r\n            KTUtil.removeClass(item, 'hiding');\r\n            KTUtil.removeClass(item, 'show');\r\n            KTUtil.removeClass(sub, 'show');\r\n\r\n            KTUtil.removeClass(item, 'hover'); // update\r\n\r\n            KTEventHandler.trigger(the.element, 'kt.menu.accordion.hidden', item);\r\n        });\r\n    }\r\n\r\n    // Hide all shown accordions of item\r\n    var _hideAccordions = function(item) {\r\n        var itemsToHide = KTUtil.findAll(the.element, '.show[data-kt-menu-trigger]');\r\n        var itemToHide;\r\n\r\n        if (itemsToHide && itemsToHide.length > 0) {\r\n            for (var i = 0, len = itemsToHide.length; i < len; i++) {\r\n                itemToHide = itemsToHide[i];\r\n\r\n                if ( _getItemSubType(itemToHide) === 'accordion' && itemToHide !== item && item.contains(itemToHide) === false && itemToHide.contains(item) === false ) {\r\n                    _hideAccordion(itemToHide);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Get item option(through html attributes)\r\n    var _getItemOption = function(item, name) {\r\n        var attr;\r\n        var value = null;\r\n\r\n        if ( item && item.hasAttribute('data-kt-menu-' + name) ) {\r\n            attr = item.getAttribute('data-kt-menu-' + name);\r\n            value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n        }\r\n\r\n        return value;\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Event Handlers\r\n    the.click = function(element, e) {\r\n        return _click(element, e);\r\n    }\r\n\r\n    the.link = function(element, e) {\r\n        return _link(element, e);\r\n    }\r\n\r\n    the.dismiss = function(element, e) {\r\n        return _dismiss(element, e);\r\n    }\r\n\r\n    the.mouseover = function(element, e) {\r\n        return _mouseover(element, e);\r\n    }\r\n\r\n    the.mouseout = function(element, e) {\r\n        return _mouseout(element, e);\r\n    }\r\n\r\n    // General Methods\r\n    the.getItemTriggerType = function(item) {\r\n        return _getItemOption(item, 'trigger');\r\n    }\r\n\r\n    the.getItemSubType = function(element) {\r\n       return _getItemSubType(element);\r\n    }\r\n\r\n    the.show = function(item) {\r\n        return _show(item);\r\n    }\r\n\r\n    the.hide = function(item) {\r\n        return _hide(item);\r\n    }\r\n\r\n    the.reset = function(item) {\r\n        return _reset(item);\r\n    }\r\n\r\n    the.update = function() {\r\n        return _update();\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    the.getItemLinkElement = function(item) {\r\n        return _getItemLinkElement(item);\r\n    }\r\n\r\n    the.getItemToggleElement = function(item) {\r\n        return _getItemToggleElement(item);\r\n    }\r\n\r\n    the.getItemSubElement = function(item) {\r\n        return _getItemSubElement(item);\r\n    }\r\n\r\n    the.getItemParentElements = function(item) {\r\n        return _getItemParentElements(item);\r\n    }\r\n\r\n    the.isItemSubShown = function(item) {\r\n        return _isItemSubShown(item);\r\n    }\r\n\r\n    the.isItemParentShown = function(item) {\r\n        return _isItemParentShown(item);\r\n    }\r\n\r\n    the.getTriggerElement = function() {\r\n        return the.triggerElement;\r\n    }\r\n\r\n    the.isItemDropdownPermanent = function(item) {\r\n        return _isItemDropdownPermanent(item);\r\n    }\r\n\r\n    // Accordion Mode Methods\r\n    the.hideAccordions = function(item) {\r\n        return _hideAccordions(item);\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n};\r\n\r\n// Get KTMenu instance by element\r\nKTMenu.getInstance = function(element) {\r\n    var menu;\r\n    var item;\r\n\r\n    // Element has menu DOM reference in it's DATA storage\r\n    if ( KTUtil.data(element).has('menu') ) {\r\n        return KTUtil.data(element).get('menu');\r\n    }\r\n\r\n    // Element has .menu parent \r\n    if ( menu = element.closest('.menu') ) {\r\n        if ( KTUtil.data(menu).has('menu') ) {\r\n            return KTUtil.data(menu).get('menu');\r\n        }\r\n    }\r\n    \r\n    // Element has a parent with DOM reference to .menu in it's DATA storage\r\n    if ( KTUtil.hasClass(element, 'menu-link') ) {\r\n        var sub = element.closest('.menu-sub');\r\n\r\n        if ( KTUtil.data(sub).has('menu') ) {\r\n            return KTUtil.data(sub).get('menu');\r\n        }\r\n    } \r\n\r\n    return null;\r\n}\r\n\r\n// Hide all dropdowns and skip one if provided\r\nKTMenu.hideDropdowns = function(skip) {\r\n    var items = document.querySelectorAll('.show.menu-dropdown[data-kt-menu-trigger]');\r\n\r\n    if (items && items.length > 0) {\r\n        for (var i = 0, len = items.length; i < len; i++) {\r\n            var item = items[i];\r\n            var menu = KTMenu.getInstance(item);\r\n\r\n            if ( menu && menu.getItemSubType(item) === 'dropdown' ) {\r\n                if ( skip ) {\r\n                    if ( menu.getItemSubElement(item).contains(skip) === false && item.contains(skip) === false &&  item !== skip ) {\r\n                        menu.hide(item);\r\n                    }\r\n                } else {\r\n                    menu.hide(item);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Update all dropdowns popover instances\r\nKTMenu.updateDropdowns = function() {\r\n    var items = document.querySelectorAll('.show.menu-dropdown[data-kt-menu-trigger]');\r\n\r\n    if (items && items.length > 0) {\r\n        for (var i = 0, len = items.length; i < len; i++) {\r\n            var item = items[i];\r\n\r\n            if ( KTUtil.data(item).has('popper') ) {\r\n                KTUtil.data(item).get('popper').forceUpdate();\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Global handlers\r\nKTMenu.initGlobalHandlers = function() {\r\n    // Dropdown handler\r\n    document.addEventListener(\"click\", function(e) {\r\n        var items = document.querySelectorAll('.show.menu-dropdown[data-kt-menu-trigger]');\r\n        var menu;\r\n        var item;\r\n        var sub;\r\n        var menuObj;\r\n\r\n        if ( items && items.length > 0 ) {\r\n            for ( var i = 0, len = items.length; i < len; i++ ) {\r\n                item = items[i];\r\n                menuObj = KTMenu.getInstance(item);\r\n\r\n                if (menuObj && menuObj.getItemSubType(item) === 'dropdown') {\r\n                    menu = menuObj.getElement();\r\n                    sub = menuObj.getItemSubElement(item);\r\n\r\n                    if ( item === e.target || item.contains(e.target) ) {\r\n                        continue;\r\n                    }\r\n                    \r\n                    if ( sub === e.target || sub.contains(e.target) ) {\r\n                        continue;\r\n                    }\r\n                        \r\n                    menuObj.hide(item);\r\n                }\r\n            }\r\n        }\r\n    });\r\n\r\n    // Sub toggle handler(updated)\r\n    KTUtil.on(document.body,  '.menu-item[data-kt-menu-trigger] > .menu-link, [data-kt-menu-trigger]:not(.menu-item):not([data-kt-menu-trigger=\"auto\"])', 'click', function(e) {\r\n        var menu = KTMenu.getInstance(this);\r\n\r\n        if ( menu !== null ) {\r\n            return menu.click(this, e);\r\n        }\r\n    });\r\n\r\n    // Link handler\r\n    KTUtil.on(document.body,  '.menu-item:not([data-kt-menu-trigger]) > .menu-link', 'click', function(e) {\r\n        var menu = KTMenu.getInstance(this);\r\n\r\n        if ( menu !== null ) {\r\n            return menu.link(this, e);\r\n        }\r\n    });\r\n\r\n    // Dismiss handler\r\n    KTUtil.on(document.body,  '[data-kt-menu-dismiss=\"true\"]', 'click', function(e) {\r\n        var menu = KTMenu.getInstance(this);\r\n\r\n        if ( menu !== null ) {\r\n            return menu.dismiss(this, e);\r\n        }\r\n    });\r\n\r\n    // Mouseover handler\r\n    KTUtil.on(document.body,  '[data-kt-menu-trigger], .menu-sub', 'mouseover', function(e) {\r\n        var menu = KTMenu.getInstance(this);\r\n\r\n        if ( menu !== null && menu.getItemSubType(this) === 'dropdown' ) {\r\n            return menu.mouseover(this, e);\r\n        }\r\n    });\r\n\r\n    // Mouseout handler\r\n    KTUtil.on(document.body,  '[data-kt-menu-trigger], .menu-sub', 'mouseout', function(e) {\r\n        var menu = KTMenu.getInstance(this);\r\n\r\n        if ( menu !== null && menu.getItemSubType(this) === 'dropdown' ) {\r\n            return menu.mouseout(this, e);\r\n        }\r\n    });\r\n\r\n    // Resize handler\r\n    window.addEventListener('resize', function() {\r\n        var menu;\r\n        var timer;\r\n\r\n        KTUtil.throttle(timer, function() {\r\n            // Locate and update Offcanvas instances on window resize\r\n            var elements = document.querySelectorAll('[data-kt-menu=\"true\"]');\r\n\r\n            if ( elements && elements.length > 0 ) {\r\n                for (var i = 0, len = elements.length; i < len; i++) {\r\n                    menu = KTMenu.getInstance(elements[i]);\r\n                    if (menu) {\r\n                        menu.update();\r\n                    }\r\n                }\r\n            }\r\n        }, 200);\r\n    });\r\n}\r\n\r\n// Global instances\r\nKTMenu.createInstances = function(selector = '[data-kt-menu=\"true\"]') {\r\n    // Initialize menus\r\n    var elements = document.querySelectorAll(selector);\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            new KTMenu(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTMenu.init = function() {\r\n    // Global Event Handlers\r\n    KTMenu.initGlobalHandlers();\r\n\r\n    // Lazy Initialization\r\n    KTMenu.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTMenu.init);\r\n} else {\r\n   KTMenu.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTMenu;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTPasswordMeter = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if (!element) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        minLength: 8,\r\n        checkUppercase: true,        \r\n        checkLowercase: true,\r\n        checkDigit: true,\r\n        checkChar: true,\r\n        scoreHighlightClass: 'active'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    // Constructor\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('password-meter') === true ) {\r\n            the = KTUtil.data(element).get('password-meter');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    // Initialize\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.score = 0;\r\n        the.checkSteps = 5;\r\n\r\n        // Elements\r\n        the.element = element;\r\n        the.inputElement = the.element.querySelector('input[type]');\r\n        the.visibilityElement = the.element.querySelector('[data-kt-password-meter-control=\"visibility\"]');\r\n        the.highlightElement = the.element.querySelector('[data-kt-password-meter-control=\"highlight\"]'); \r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-password-meter', 'true');\r\n        \r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('password-meter', the);\r\n    }\r\n\r\n    // Handlers\r\n    var _handlers = function() {\r\n        the.inputElement.addEventListener('input', function() {\r\n            _check();\r\n        });\r\n\r\n        if (the.visibilityElement) {\r\n            the.visibilityElement.addEventListener('click', function() {\r\n                _visibility();\r\n            });\r\n        }\r\n    }   \r\n\r\n    // Event handlers\r\n    var _check = function() {\r\n        var score = 0;\r\n        var checkScore = _getCheckScore();\r\n        \r\n        if (_checkLength() === true) {\r\n            score = score + checkScore;\r\n        }\r\n\r\n        if (the.options.checkUppercase === true && _checkLowercase() === true) {\r\n            score = score + checkScore;\r\n        }\r\n\r\n        if (the.options.checkLowercase === true && _checkUppercase() === true ) {\r\n            score = score + checkScore;\r\n        }\r\n\r\n        if (the.options.checkDigit === true && _checkDigit() === true ) {\r\n            score = score + checkScore;\r\n        }\r\n\r\n        if (the.options.checkChar === true && _checkChar() === true ) {\r\n            score = score + checkScore;\r\n        }\r\n\r\n        the.score = score;\r\n\r\n        _highlight();\r\n    }\r\n\r\n    var _checkLength = function() {\r\n        return the.inputElement.value.length >= the.options.minLength;  // 20 score\r\n    }\r\n\r\n    var _checkLowercase = function() {\r\n        return /[a-z]/.test(the.inputElement.value);  // 20 score\r\n    }\r\n\r\n    var _checkUppercase = function() {\r\n        return /[A-Z]/.test(the.inputElement.value);  // 20 score\r\n    }\r\n\r\n    var _checkDigit = function() {\r\n        return /[0-9]/.test(the.inputElement.value);  // 20 score\r\n    }\r\n\r\n    var _checkChar = function() {\r\n        return /[~`!#$%\\^&*+=\\-\\[\\]\\\\';,/{}|\\\\\":<>\\?]/g.test(the.inputElement.value);  // 20 score\r\n    }    \r\n\r\n    var _getCheckScore = function() {\r\n        var count = 1;\r\n        \r\n        if (the.options.checkUppercase === true) {\r\n            count++;\r\n        }\r\n\r\n        if (the.options.checkLowercase === true) {\r\n            count++;\r\n        }\r\n\r\n        if (the.options.checkDigit === true) {\r\n            count++;\r\n        }\r\n\r\n        if (the.options.checkChar === true) {\r\n            count++;\r\n        }\r\n\r\n        the.checkSteps = count;\r\n\r\n        return 100 / the.checkSteps;\r\n    }\r\n    \r\n    var _highlight = function() {\r\n        var items = [].slice.call(the.highlightElement.querySelectorAll('div'));\r\n        var total = items.length;\r\n        var index = 0;\r\n        var checkScore = _getCheckScore();\r\n        var score = _getScore();\r\n\r\n        items.map(function (item) {\r\n            index++;\r\n\r\n            if ( (checkScore * index * (the.checkSteps / total)) <= score ) {\r\n                item.classList.add('active');\r\n            } else {\r\n                item.classList.remove('active');\r\n            }            \r\n        });\r\n    }\r\n\r\n    var _visibility = function() {\r\n        var visibleIcon = the.visibilityElement.querySelector('i:not(.d-none), .svg-icon:not(.d-none)');\r\n        var hiddenIcon = the.visibilityElement.querySelector('i.d-none, .svg-icon.d-none');\r\n        \r\n        if (the.inputElement.getAttribute('type').toLowerCase() === 'password' ) {\r\n            the.inputElement.setAttribute('type', 'text');\r\n        }  else {\r\n            the.inputElement.setAttribute('type', 'password');\r\n        }        \r\n\r\n        visibleIcon.classList.add('d-none');\r\n        hiddenIcon.classList.remove('d-none');\r\n\r\n        the.inputElement.focus();\r\n    }\r\n\r\n    var _reset = function() {\r\n        the.score = 0;\r\n\r\n        _highlight();\r\n    }\r\n\r\n    // Gets current password score\r\n    var _getScore = function() {\r\n       return the.score;\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.check = function() {\r\n        return _check();\r\n    }\r\n\r\n    the.getScore = function() {\r\n        return _getScore();\r\n    }\r\n\r\n    the.reset = function() {\r\n        return _reset();\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTPasswordMeter.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('password-meter') ) {\r\n        return KTUtil.data(element).get('password-meter');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTPasswordMeter.createInstances = function(selector = '[data-kt-password-meter]') {\r\n    // Get instances\r\n    var elements = document.body.querySelectorAll(selector);\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            // Initialize instances\r\n            new KTPasswordMeter(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTPasswordMeter.init = function() {\r\n    KTPasswordMeter.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTPasswordMeter.init);\r\n} else {\r\n    KTPasswordMeter.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTPasswordMeter;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTScroll = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if (!element) {\r\n        return;\r\n    }\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        saveState: true\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('scroll') ) {\r\n            the = KTUtil.data(element).get('scroll');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n\r\n        // Elements\r\n        the.element = element;        \r\n        the.id = the.element.getAttribute('id');\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-scroll', 'true');\r\n\r\n        // Update\r\n        _update();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('scroll', the);\r\n    }\r\n\r\n    var _setupHeight = function() {\r\n        var heightType = _getHeightType();\r\n        var height = _getHeight();\r\n\r\n        // Set height\r\n        if ( height !== null && height.length > 0 ) {\r\n            KTUtil.css(the.element, heightType, height);\r\n        } else {\r\n            KTUtil.css(the.element, heightType, '');\r\n        }\r\n    }\r\n\r\n    var _setupState = function () {\r\n        if ( _getOption('save-state') === true && typeof KTCookie !== 'undefined' && the.id ) {\r\n            if ( KTCookie.get(the.id + 'st') ) {\r\n                var pos = parseInt(KTCookie.get(the.id + 'st'));\r\n\r\n                if ( pos > 0 ) {\r\n                    the.element.scrollTop = pos;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    var _setupScrollHandler = function() {\r\n        if ( _getOption('save-state') === true && typeof KTCookie !== 'undefined' && the.id ) {\r\n            the.element.addEventListener('scroll', _scrollHandler);\r\n        } else {\r\n            the.element.removeEventListener('scroll', _scrollHandler);\r\n        }\r\n    }\r\n\r\n    var _destroyScrollHandler = function() {\r\n        the.element.removeEventListener('scroll', _scrollHandler);\r\n    }\r\n\r\n    var _resetHeight = function() {\r\n        KTUtil.css(the.element, _getHeightType(), '');\r\n    }\r\n\r\n    var _scrollHandler = function () {\r\n        KTCookie.set(the.id + 'st', the.element.scrollTop);\r\n    }\r\n\r\n    var _update = function() {\r\n        // Activate/deactivate\r\n        if ( _getOption('activate') === true || the.element.hasAttribute('data-kt-scroll-activate') === false ) {\r\n            _setupHeight();\r\n            _setupScrollHandler();\r\n            _setupState();\r\n        } else {\r\n            _resetHeight()\r\n            _destroyScrollHandler();\r\n        }        \r\n    }\r\n\r\n    var _getHeight = function() {\r\n        var height = _getOption(_getHeightType());\r\n\r\n        if ( height instanceof Function ) {\r\n            return height.call();\r\n        } else if ( height !== null && typeof height === 'string' && height.toLowerCase() === 'auto' ) {\r\n            return _getAutoHeight();\r\n        } else {\r\n            return height;\r\n        }\r\n    }\r\n\r\n    var _getAutoHeight = function() {\r\n        var height = KTUtil.getViewPort().height;\r\n\r\n        var dependencies = _getOption('dependencies');\r\n        var wrappers = _getOption('wrappers');\r\n        var offset = _getOption('offset');\r\n\r\n        // Height dependencies\r\n        if ( dependencies !== null ) {\r\n            var elements = document.querySelectorAll(dependencies);\r\n\r\n            if ( elements && elements.length > 0 ) {\r\n                for ( var i = 0, len = elements.length; i < len; i++ ) {\r\n                    var element = elements[i];\r\n\r\n                    if ( KTUtil.visible(element) === false ) {\r\n                        continue;\r\n                    }\r\n\r\n                    height = height - parseInt(KTUtil.css(element, 'height'));\r\n                    height = height - parseInt(KTUtil.css(element, 'margin-top'));\r\n                    height = height - parseInt(KTUtil.css(element, 'margin-bottom'));\r\n\r\n                    if (KTUtil.css(element, 'border-top')) {\r\n                        height = height - parseInt(KTUtil.css(element, 'border-top'));\r\n                    }\r\n\r\n                    if (KTUtil.css(element, 'border-bottom')) {\r\n                        height = height - parseInt(KTUtil.css(element, 'border-bottom'));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Wrappers\r\n        if ( wrappers !== null ) {\r\n            var elements = document.querySelectorAll(wrappers);\r\n            if ( elements && elements.length > 0 ) {\r\n                for ( var i = 0, len = elements.length; i < len; i++ ) {\r\n                    var element = elements[i];\r\n\r\n                    if ( KTUtil.visible(element) === false ) {\r\n                        continue;\r\n                    }\r\n\r\n                    height = height - parseInt(KTUtil.css(element, 'margin-top'));\r\n                    height = height - parseInt(KTUtil.css(element, 'margin-bottom'));\r\n                    height = height - parseInt(KTUtil.css(element, 'padding-top'));\r\n                    height = height - parseInt(KTUtil.css(element, 'padding-bottom'));\r\n\r\n                    if (KTUtil.css(element, 'border-top')) {\r\n                        height = height - parseInt(KTUtil.css(element, 'border-top'));\r\n                    }\r\n\r\n                    if (KTUtil.css(element, 'border-bottom')) {\r\n                        height = height - parseInt(KTUtil.css(element, 'border-bottom'));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Custom offset\r\n        if ( offset !== null ) {\r\n            height = height - parseInt(offset);\r\n        }\r\n\r\n        height = height - parseInt(KTUtil.css(the.element, 'margin-top'));\r\n        height = height - parseInt(KTUtil.css(the.element, 'margin-bottom'));\r\n        \r\n        if (KTUtil.css(element, 'border-top')) {\r\n            height = height - parseInt(KTUtil.css(element, 'border-top'));\r\n        }\r\n\r\n        if (KTUtil.css(element, 'border-bottom')) {\r\n            height = height - parseInt(KTUtil.css(element, 'border-bottom'));\r\n        }\r\n\r\n        height = String(height) + 'px';\r\n\r\n        return height;\r\n    }\r\n\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-scroll-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-scroll-' + name);\r\n\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    var _getHeightType = function() {\r\n        if (_getOption('height')) {\r\n            return 'height';\r\n        } if (_getOption('min-height')) {\r\n            return 'min-height';\r\n        } if (_getOption('max-height')) {\r\n            return 'max-height';\r\n        }\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    the.update = function() {\r\n        return _update();\r\n    }\r\n\r\n    the.getHeight = function() {\r\n        return _getHeight();\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTScroll.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('scroll') ) {\r\n        return KTUtil.data(element).get('scroll');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTScroll.createInstances = function(selector = '[data-kt-scroll=\"true\"]') {\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Initialize Menus\r\n    var elements = body.querySelectorAll(selector);\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            new KTScroll(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Window resize handling\r\nwindow.addEventListener('resize', function() {\r\n    var timer;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    KTUtil.throttle(timer, function() {\r\n        // Locate and update Offcanvas instances on window resize\r\n        var elements = body.querySelectorAll('[data-kt-scroll=\"true\"]');\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for (var i = 0, len = elements.length; i < len; i++) {\r\n                var scroll = KTScroll.getInstance(elements[i]);\r\n                if (scroll) {\r\n                    scroll.update();\r\n                }\r\n            }\r\n        }\r\n    }, 200);\r\n});\r\n\r\n// Global initialization\r\nKTScroll.init = function() {\r\n    KTScroll.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTScroll.init);\r\n} else {\r\n    KTScroll.init();\r\n}\r\n\r\n// Webpack Support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTScroll;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTScrolltop = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default options\r\n    var defaultOptions = {\r\n        offset: 300,\r\n        speed: 600\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if (KTUtil.data(element).has('scrolltop')) {\r\n            the = KTUtil.data(element).get('scrolltop');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('scrolltop');\r\n        the.element = element;\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-scrolltop', 'true');\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('scrolltop', the);\r\n    }\r\n\r\n    var _handlers = function() {\r\n        var timer;\r\n\r\n        window.addEventListener('scroll', function() {\r\n            KTUtil.throttle(timer, function() {\r\n                _scroll();\r\n            }, 200);\r\n        });\r\n\r\n        KTUtil.addEvent(the.element, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            _go();\r\n        });\r\n    }\r\n\r\n    var _scroll = function() {\r\n        var offset = parseInt(_getOption('offset'));\r\n\r\n        var pos = KTUtil.getScrollTop(); // current vertical position\r\n\r\n        if ( pos > offset ) {\r\n            if ( body.hasAttribute('data-kt-scrolltop') === false ) {\r\n                body.setAttribute('data-kt-scrolltop', 'on');\r\n            }\r\n        } else {\r\n            if ( body.hasAttribute('data-kt-scrolltop') === true ) {\r\n                body.removeAttribute('data-kt-scrolltop');\r\n            }\r\n        }\r\n    }\r\n\r\n    var _go = function() {\r\n        var speed = parseInt(_getOption('speed'));\r\n\r\n        KTUtil.scrollTop(0, speed);\r\n    }\r\n\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-scrolltop-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-scrolltop-' + name);\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.go = function() {\r\n        return _go();\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTScrolltop.getInstance = function(element) {\r\n    if (element && KTUtil.data(element).has('scrolltop')) {\r\n        return KTUtil.data(element).get('scrolltop');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTScrolltop.createInstances = function(selector = '[data-kt-scrolltop=\"true\"]') {\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Initialize Menus\r\n    var elements = body.querySelectorAll(selector);\r\n    var scrolltop;\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            scrolltop = new KTScrolltop(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTScrolltop.init = function() {\r\n    KTScrolltop.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTScrolltop.init);\r\n} else {\r\n    KTScrolltop.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTScrolltop;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTSearch = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if (!element) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        minLength: 2,  // Miniam text lenght to query search\r\n        keypress: true,  // Enable search on keypress \r\n        enter: true,  // Enable search on enter key press\r\n        layout: 'menu',  // Use 'menu' or 'inline' layout options to display search results\r\n        responsive: null, // Pass integer value or bootstrap compatible breakpoint key(sm,md,lg,xl,xxl) to enable reponsive form mode for device width below the breakpoint value\r\n        showOnFocus: true // Always show menu on input focus\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    // Construct\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('search') === true ) {\r\n            the = KTUtil.data(element).get('search');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    // Init\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.processing = false;\r\n\r\n        // Elements\r\n        the.element = element;               \r\n        the.contentElement = _getElement('content');     \r\n        the.formElement = _getElement('form');         \r\n        the.inputElement = _getElement('input');\r\n        the.spinnerElement = _getElement('spinner');\r\n        the.clearElement = _getElement('clear');\r\n        the.toggleElement = _getElement('toggle');   \r\n        the.submitElement = _getElement('submit');\r\n        the.toolbarElement = _getElement('toolbar');   \r\n\r\n        the.resultsElement = _getElement('results');\r\n        the.suggestionElement = _getElement('suggestion'); \r\n        the.emptyElement = _getElement('empty'); \r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-search', 'true');\r\n        \r\n        // Layout\r\n        the.layout = _getOption('layout');\r\n        \r\n        // Menu\r\n        if ( the.layout === 'menu' ) {\r\n            the.menuObject = new KTMenu(the.contentElement);\r\n        } else {\r\n            the.menuObject = null;\r\n        }\r\n\r\n        // Update\r\n        _update();\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('search', the);\r\n    }\r\n\r\n    // Handlera\r\n    var _handlers = function() {\r\n        // Focus\r\n        the.inputElement.addEventListener('focus', _focus);\r\n\r\n        // Blur\r\n        the.inputElement.addEventListener('blur', _blur);\r\n\r\n        // Keypress\r\n        if ( _getOption('keypress') === true ) {\r\n            the.inputElement.addEventListener('input', _input);\r\n        }\r\n\r\n        // Submit\r\n        if ( the.submitElement ) {\r\n            the.submitElement.addEventListener('click', _search);\r\n        }\r\n\r\n        // Enter\r\n        if ( _getOption('enter') === true ) {\r\n            the.inputElement.addEventListener('keypress', _enter);\r\n        }\r\n\r\n        // Clear \r\n        if ( the.clearElement ) {\r\n            the.clearElement.addEventListener('click', _clear);\r\n        }\r\n\r\n        // Menu\r\n        if ( the.menuObject ) {\r\n            // Toggle menu\r\n            if ( the.toggleElement ) {\r\n                the.toggleElement.addEventListener('click', _show);\r\n\r\n                the.menuObject.on('kt.menu.dropdown.show', function(item) {\r\n                    if (KTUtil.visible(the.toggleElement)) {\r\n                        the.toggleElement.classList.add('active');\r\n                        the.toggleElement.classList.add('show');\r\n                    } \r\n                });\r\n    \r\n                the.menuObject.on('kt.menu.dropdown.hide', function(item) {\r\n                    if (KTUtil.visible(the.toggleElement)) {\r\n                        the.toggleElement.classList.remove('active');\r\n                        the.toggleElement.classList.remove('show');\r\n                    }\r\n                });\r\n            }            \r\n\r\n            the.menuObject.on('kt.menu.dropdown.shown', function() {\r\n                the.inputElement.focus();\r\n            });\r\n        } \r\n\r\n        // Window resize handling\r\n        window.addEventListener('resize', function() {\r\n            var timer;\r\n\r\n            KTUtil.throttle(timer, function() {\r\n                _update();\r\n            }, 200);\r\n        });\r\n    }\r\n\r\n    // Focus\r\n    var _focus = function() {\r\n        the.element.classList.add('focus');\r\n\r\n        if ( _getOption('show-on-focus') === true || the.inputElement.value.length >= minLength ) {\r\n            _show();\r\n        }        \r\n    }\r\n\r\n    // Blur\r\n    var _blur = function() {        \r\n        the.element.classList.remove('focus');\r\n    }\r\n\r\n    // Enter \r\n    var _enter = function(e) {\r\n        var key = e.charCode || e.keyCode || 0;\r\n\r\n        if (key == 13) {\r\n            e.preventDefault();\r\n\r\n            _search();\r\n        }\r\n    }\r\n\r\n    // Input\r\n    var _input = function() {\r\n        if ( _getOption('min-length') )  {\r\n            var minLength = parseInt(_getOption('min-length'));\r\n\r\n            if ( the.inputElement.value.length >= minLength ) {\r\n                _search();\r\n            } else if ( the.inputElement.value.length === 0 ) {\r\n                _clear();\r\n            }\r\n        }\r\n    }\r\n\r\n    // Search\r\n    var _search = function() {\r\n        if (the.processing === false) {\r\n            // Show search spinner\r\n            if (the.spinnerElement) {\r\n                the.spinnerElement.classList.remove(\"d-none\");\r\n            }\r\n            \r\n            // Hide search clear button\r\n            if (the.clearElement) {\r\n                the.clearElement.classList.add(\"d-none\");\r\n            }\r\n\r\n            // Hide search toolbar\r\n            if (the.toolbarElement) {\r\n                the.toolbarElement.classList.add(\"d-none\");\r\n            }\r\n\r\n            // Focus input\r\n            the.inputElement.focus();\r\n\r\n            the.processing = true;\r\n            KTEventHandler.trigger(the.element, 'kt.search.process', the);\r\n        }\r\n    }\r\n\r\n    // Complete\r\n    var _complete = function() {\r\n        if (the.spinnerElement) {\r\n            the.spinnerElement.classList.add(\"d-none\");\r\n        }\r\n\r\n        // Show search toolbar\r\n        if (the.clearElement) {\r\n            the.clearElement.classList.remove(\"d-none\");\r\n        }\r\n\r\n        if ( the.inputElement.value.length === 0 ) {\r\n            _clear();\r\n        }\r\n\r\n        // Focus input\r\n        the.inputElement.focus();\r\n\r\n        _show();\r\n\r\n        the.processing = false;\r\n    }\r\n\r\n    // Clear\r\n    var _clear = function() {\r\n        if ( KTEventHandler.trigger(the.element, 'kt.search.clear', the) === false )  {\r\n            return;\r\n        }\r\n\r\n        // Clear and focus input\r\n        the.inputElement.value = \"\";\r\n        the.inputElement.focus();\r\n\r\n        // Hide clear icon\r\n        if (the.clearElement) {\r\n            the.clearElement.classList.add(\"d-none\");\r\n        }\r\n\r\n        // Show search toolbar\r\n        if (the.toolbarElement) {\r\n            the.toolbarElement.classList.remove(\"d-none\");\r\n        }\r\n\r\n        // Hide menu\r\n        if ( _getOption('show-on-focus') === false ) {\r\n            _hide();\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.search.cleared', the);\r\n    }\r\n\r\n    // Update\r\n    var _update = function() {\r\n        // Handle responsive form\r\n        if (the.layout === 'menu') {\r\n            var responsiveFormMode = _getResponsiveFormMode();\r\n\r\n            if ( responsiveFormMode === 'on' && the.contentElement.contains(the.formElement) === false ) {\r\n                the.contentElement.prepend(the.formElement);\r\n                the.formElement.classList.remove('d-none');                \r\n            } else if ( responsiveFormMode === 'off' && the.contentElement.contains(the.formElement) === true ) {\r\n                the.element.prepend(the.formElement);\r\n                the.formElement.classList.add('d-none');\r\n            }\r\n        }\r\n    }\r\n\r\n    // Show menu\r\n    var _show = function() {\r\n        if ( the.menuObject ) {\r\n            _update();\r\n\r\n            the.menuObject.show(the.element);\r\n        }\r\n    }\r\n\r\n    // Hide menu\r\n    var _hide = function() {\r\n        if ( the.menuObject ) {\r\n            _update();\r\n\r\n            the.menuObject.hide(the.element);\r\n        }\r\n    }\r\n\r\n    // Get option\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-search-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-search-' + name);\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Get element\r\n    var _getElement = function(name) {\r\n        return the.element.querySelector('[data-kt-search-element=\"' + name + '\"]');\r\n    }\r\n\r\n    // Check if responsive form mode is enabled\r\n    var _getResponsiveFormMode = function() {\r\n        var responsive = _getOption('responsive');\r\n        var width = KTUtil.getViewPort().width;\r\n\r\n        if (!responsive) {\r\n            return null;\r\n        }\r\n\r\n        var breakpoint = KTUtil.getBreakpoint(responsive);\r\n\r\n        if (!breakpoint ) {\r\n            breakpoint = parseInt(responsive);\r\n        }\r\n\r\n        if (width < breakpoint) {\r\n            return \"on\";\r\n        } else {\r\n            return \"off\";\r\n        }\r\n    }\r\n    \r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.show = function() {\r\n        return _show();\r\n    }\r\n\r\n    the.hide = function() {\r\n        return _hide();\r\n    }\r\n\r\n    the.update = function() {\r\n        return _update();\r\n    }\r\n\r\n    the.search = function() {\r\n        return _search();\r\n    }\r\n\r\n    the.complete = function() {\r\n        return _complete();\r\n    }\r\n\r\n    the.clear = function() {\r\n        return _clear();\r\n    }\r\n\r\n    the.isProcessing = function() {\r\n        return the.processing;\r\n    }\r\n\r\n    the.getQuery = function() {\r\n        return the.inputElement.value();\r\n    }    \r\n\r\n    the.getMenu = function() {\r\n        return the.menuObject;\r\n    }\r\n\r\n    the.getFormElement = function() {\r\n        return the.formElement;\r\n    }\r\n\r\n    the.getInputElement = function() {\r\n        return the.inputElement;\r\n    }\r\n\r\n    the.getContentElement = function() {\r\n        return the.contentElement;\r\n    }\r\n\r\n    the.getElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTSearch.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('search') ) {\r\n        return KTUtil.data(element).get('search');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTSearch;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTStepper = function(element, options) {\r\n    //////////////////////////////\r\n    // ** Private variables  ** //\r\n    //////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        startIndex: 1,\r\n        animation: false,\r\n        animationSpeed: '0.3s',\r\n        animationNextClass: 'animate__animated animate__slideInRight animate__fast',\r\n        animationPreviousClass: 'animate__animated animate__slideInLeft animate__fast'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('stepper') === true ) {\r\n            the = KTUtil.data(element).get('stepper');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('stepper');\r\n\r\n        the.element = element;\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-stepper', 'true');\r\n\r\n        // Elements\r\n        the.steps = KTUtil.findAll(the.element, '[data-kt-stepper-element=\"nav\"]');\r\n        the.btnNext = KTUtil.find(the.element, '[data-kt-stepper-action=\"next\"]');\r\n        the.btnPrevious = KTUtil.find(the.element, '[data-kt-stepper-action=\"previous\"]');\r\n        the.btnSubmit = KTUtil.find(the.element, '[data-kt-stepper-action=\"submit\"]');\r\n\r\n        // Variables\r\n        the.totalStepsNumber = the.steps.length;\r\n        the.passedStepIndex = 0;\r\n        the.currentStepIndex = 1;\r\n        the.clickedStepIndex = 0;\r\n\r\n        // Set Current Step\r\n        if ( the.options.startIndex > 1 ) {\r\n            _goTo(the.options.startIndex);\r\n        }\r\n\r\n        // Event Handlers\r\n        KTUtil.addEvent(the.btnNext, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            KTEventHandler.trigger(the.element, 'kt.stepper.next', the);\r\n        });\r\n\r\n        KTUtil.addEvent(the.btnPrevious, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            KTEventHandler.trigger(the.element, 'kt.stepper.previous', the);\r\n        });\r\n\r\n        KTUtil.on(the.element, '[data-kt-stepper-action=\"step\"]', 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            if ( the.steps && the.steps.length > 0 ) {\r\n                for (var i = 0, len = the.steps.length; i < len; i++) {\r\n                    if ( the.steps[i] === this ) {\r\n                        the.clickedStepIndex = i + 1;\r\n\r\n                        KTEventHandler.trigger(the.element, 'kt.stepper.click', the);\r\n\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('stepper', the);\r\n    }\r\n\r\n    var _goTo = function(index) {\r\n        // Trigger \"change\" event\r\n        KTEventHandler.trigger(the.element, 'kt.stepper.change', the);\r\n\r\n        // Skip if this step is already shown\r\n        if ( index === the.currentStepIndex || index > the.totalStepsNumber || index < 0 ) {\r\n            return;\r\n        }\r\n\r\n        // Validate step number\r\n        index = parseInt(index);\r\n\r\n        // Set current step\r\n        the.passedStepIndex = the.currentStepIndex;\r\n        the.currentStepIndex = index;\r\n\r\n        // Refresh elements\r\n        _refreshUI();\r\n\r\n        // Trigger \"changed\" event\r\n        KTEventHandler.trigger(the.element, 'kt.stepper.changed', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _goNext = function() {\r\n        return _goTo( _getNextStepIndex() );\r\n    }\r\n\r\n    var _goPrevious = function() {\r\n        return _goTo( _getPreviousStepIndex() );\r\n    }\r\n\r\n    var _goLast = function() {\r\n        return _goTo( _getLastStepIndex() );\r\n    }\r\n\r\n    var _goFirst = function() {\r\n        return _goTo( _getFirstStepIndex() );\r\n    }\r\n\r\n    var _refreshUI = function() {\r\n        var state = '';\r\n\r\n        if ( _isLastStep() ) {\r\n            state = 'last';\r\n        } else if ( _isFirstStep() ) {\r\n            state = 'first';\r\n        } else {\r\n            state = 'between';\r\n        }\r\n\r\n        // Set state class\r\n        KTUtil.removeClass(the.element, 'last');\r\n        KTUtil.removeClass(the.element, 'first');\r\n        KTUtil.removeClass(the.element, 'between');\r\n\r\n        KTUtil.addClass(the.element, state);\r\n\r\n        // Step Items\r\n        var elements = KTUtil.findAll(the.element, '[data-kt-stepper-element=\"nav\"], [data-kt-stepper-element=\"content\"], [data-kt-stepper-element=\"info\"]');\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for (var i = 0, len = elements.length; i < len; i++) {\r\n                var element = elements[i];\r\n                var index = KTUtil.index(element) + 1;\r\n\r\n                KTUtil.removeClass(element, 'current');\r\n                KTUtil.removeClass(element, 'completed');\r\n                KTUtil.removeClass(element, 'pending');\r\n\r\n                if ( index == the.currentStepIndex ) {\r\n                    KTUtil.addClass(element, 'current');\r\n\r\n                    if ( the.options.animation !== false && element.getAttribute('data-kt-stepper-element') == 'content' ) {\r\n                        KTUtil.css(element, 'animationDuration', the.options.animationSpeed);\r\n\r\n                        var animation = _getStepDirection(the.passedStepIndex) === 'previous' ?  the.options.animationPreviousClass : the.options.animationNextClass;\r\n                        KTUtil.animateClass(element, animation);\r\n                    }\r\n                } else {\r\n                    if ( index < the.currentStepIndex ) {\r\n                        KTUtil.addClass(element, 'completed');\r\n                    } else {\r\n                        KTUtil.addClass(element, 'pending');\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    var _isLastStep = function() {\r\n        return the.currentStepIndex === the.totalStepsNumber;\r\n    }\r\n\r\n    var _isFirstStep = function() {\r\n        return the.currentStepIndex === 1;\r\n    }\r\n\r\n    var _isBetweenStep = function() {\r\n        return _isLastStep() === false && _isFirstStep() === false;\r\n    }\r\n\r\n    var _getNextStepIndex = function() {\r\n        if ( the.totalStepsNumber >= ( the.currentStepIndex + 1 ) ) {\r\n            return the.currentStepIndex + 1;\r\n        } else {\r\n            return the.totalStepsNumber;\r\n        }\r\n    }\r\n\r\n    var _getPreviousStepIndex = function() {\r\n        if ( ( the.currentStepIndex - 1 ) > 1 ) {\r\n            return the.currentStepIndex - 1;\r\n        } else {\r\n            return 1;\r\n        }\r\n    }\r\n\r\n    var _getFirstStepIndex = function(){\r\n        return 1;\r\n    }\r\n\r\n    var _getLastStepIndex = function() {\r\n        return the.totalStepsNumber;\r\n    }\r\n\r\n    var _getTotalStepsNumber = function() {\r\n        return the.totalStepsNumber;\r\n    }\r\n\r\n    var _getStepDirection = function(index) {\r\n        if ( index > the.currentStepIndex ) {\r\n            return 'next';\r\n        } else {\r\n            return 'previous';\r\n        }\r\n    }\r\n\r\n    var _getStepContent = function(index) {\r\n        var content = KTUtil.findAll(the.element, '[data-kt-stepper-element=\"content\"]');\r\n\r\n        if ( content[index-1] ) {\r\n            return content[index-1];\r\n        } else {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.getElement = function(index) {\r\n        return the.element;\r\n    }\r\n\r\n    the.goTo = function(index) {\r\n        return _goTo(index);\r\n    }\r\n\r\n    the.goPrevious = function() {\r\n        return _goPrevious();\r\n    }\r\n\r\n    the.goNext = function() {\r\n        return _goNext();\r\n    }\r\n\r\n    the.goFirst = function() {\r\n        return _goFirst();\r\n    }\r\n\r\n    the.goLast = function() {\r\n        return _goLast();\r\n    }\r\n\r\n    the.getCurrentStepIndex = function() {\r\n        return the.currentStepIndex;\r\n    }\r\n\r\n    the.getNextStepIndex = function() {\r\n        return the.nextStepIndex;\r\n    }\r\n\r\n    the.getPassedStepIndex = function() {\r\n        return the.passedStepIndex;\r\n    }\r\n\r\n    the.getClickedStepIndex = function() {\r\n        return the.clickedStepIndex;\r\n    }\r\n\r\n    the.getPreviousStepIndex = function() {\r\n        return the.PreviousStepIndex;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTStepper.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('stepper') ) {\r\n        return KTUtil.data(element).get('stepper');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTStepper;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTSticky = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        offset: 200,\r\n        reverse: false,\r\n        animation: true,\r\n        animationSpeed: '0.3s',\r\n        animationClass: 'animation-slide-in-down'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('sticky') === true ) {\r\n            the = KTUtil.data(element).get('sticky');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        the.element = element;\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('sticky');\r\n        the.name = the.element.getAttribute('data-kt-sticky-name');\r\n        the.attributeName = 'data-kt-sticky-' + the.name;\r\n        the.eventTriggerState = true;\r\n        the.lastScrollTop = 0;\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-sticky', 'true');\r\n\r\n        // Event Handlers\r\n        window.addEventListener('scroll', _scroll);\r\n\r\n        // Initial Launch\r\n        _scroll();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('sticky', the);\r\n    }\r\n\r\n    var _scroll = function(e) {\r\n        var offset = _getOption('offset');\r\n        var reverse = _getOption('reverse');\r\n        var st;\r\n        var attrName;\r\n\r\n        // Exit if false\r\n        if ( offset === false ) {\r\n            return;\r\n        }\r\n\r\n        offset = parseInt(offset);\r\n        st = KTUtil.getScrollTop();\r\n\r\n        if ( reverse === true ) {  // Release on reverse scroll mode\r\n            if ( st > offset && the.lastScrollTop < st ) {\r\n                if ( body.hasAttribute(the.attributeName) === false ) {\r\n                    _enable();\r\n                    body.setAttribute(the.attributeName, 'on');\r\n                }\r\n\r\n                if ( the.eventTriggerState === true ) {\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.on', the);\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.change', the);\r\n\r\n                    the.eventTriggerState = false;\r\n                }\r\n            } else { // Back scroll mode\r\n                if ( body.hasAttribute(the.attributeName) === true ) {\r\n                    _disable();\r\n                    body.removeAttribute(the.attributeName);\r\n                }\r\n\r\n                if ( the.eventTriggerState === false ) {\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.off', the);\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.change', the);\r\n                    the.eventTriggerState = true;\r\n                }\r\n            }\r\n\r\n            the.lastScrollTop = st;\r\n        } else { // Classic scroll mode\r\n            if ( st > offset ) {\r\n                if ( body.hasAttribute(the.attributeName) === false ) {\r\n                    _enable();\r\n                    body.setAttribute(the.attributeName, 'on');\r\n                }\r\n\r\n                if ( the.eventTriggerState === true ) {\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.on', the);\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.change', the);\r\n                    the.eventTriggerState = false;\r\n                }\r\n            } else { // back scroll mode\r\n                if ( body.hasAttribute(the.attributeName) === true ) {\r\n                    _disable();\r\n                    body.removeAttribute(the.attributeName);\r\n                }\r\n\r\n                if ( the.eventTriggerState === false ) {\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.off', the);\r\n                    KTEventHandler.trigger(the.element, 'kt.sticky.change', the);\r\n                    the.eventTriggerState = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    var _enable = function(update) {\r\n        var top = _getOption('top');\r\n        var left = _getOption('left');\r\n        var right = _getOption('right');\r\n        var width = _getOption('width');\r\n        var zindex = _getOption('zindex');\r\n\r\n        if ( update !== true && _getOption('animation') === true ) {\r\n            KTUtil.css(the.element, 'animationDuration', _getOption('animationSpeed'));\r\n            KTUtil.animateClass(the.element, 'animation ' + _getOption('animationClass'));\r\n        }\r\n\r\n        if ( zindex !== null ) {\r\n            KTUtil.css(the.element, 'z-index', zindex);\r\n            KTUtil.css(the.element, 'position', 'fixed');\r\n        }\r\n\r\n        if ( top !== null ) {\r\n            KTUtil.css(the.element, 'top', top);\r\n        }\r\n\r\n        if ( width !== null ) {\r\n            if (width['target']) {\r\n                var targetElement = document.querySelector(width['target']);\r\n                if (targetElement) {\r\n                    width = KTUtil.css(targetElement, 'width');\r\n                }\r\n            }\r\n\r\n            KTUtil.css(the.element, 'width', width);\r\n        }\r\n\r\n        if ( left !== null ) {\r\n            if ( String(left).toLowerCase() === 'auto' ) {\r\n                var offsetLeft = KTUtil.offset(the.element).left;\r\n\r\n                if ( offsetLeft > 0 ) {\r\n                    KTUtil.css(the.element, 'left', String(offsetLeft) + 'px');\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    var _disable = function() {\r\n        KTUtil.css(the.element, 'top', '');\r\n        KTUtil.css(the.element, 'width', '');\r\n        KTUtil.css(the.element, 'left', '');\r\n        KTUtil.css(the.element, 'right', '');\r\n        KTUtil.css(the.element, 'z-index', '');\r\n        KTUtil.css(the.element, 'position', '');\r\n    }\r\n\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-sticky-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-sticky-' + name);\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Methods\r\n    the.update = function() {\r\n        if ( body.hasAttribute(the.attributeName) === true ) {\r\n            _disable();\r\n            body.removeAttribute(the.attributeName);\r\n            _enable(true);\r\n            body.setAttribute(the.attributeName, 'on');\r\n        }\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTSticky.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('sticky') ) {\r\n        return KTUtil.data(element).get('sticky');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTSticky.createInstances = function(selector = '[data-kt-sticky=\"true\"]') {\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Initialize Menus\r\n    var elements = body.querySelectorAll(selector);\r\n    var sticky;\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            sticky = new KTSticky(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Window resize handler\r\nwindow.addEventListener('resize', function() {\r\n    var timer;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    KTUtil.throttle(timer, function() {\r\n        // Locate and update Offcanvas instances on window resize\r\n        var elements = body.querySelectorAll('[data-kt-sticky=\"true\"]');\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for (var i = 0, len = elements.length; i < len; i++) {\r\n                var sticky = KTSticky.getInstance(elements[i]);\r\n                if (sticky) {\r\n                    sticky.update();\r\n                }\r\n            }\r\n        }\r\n    }, 200);\r\n});\r\n\r\n// Global initialization\r\nKTSticky.init = function() {\r\n    KTSticky.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTSticky.init);\r\n} else {\r\n    KTSticky.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTSticky;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTSwapper = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private Variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n\r\n    if ( typeof element === \"undefined\" || element === null ) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        mode: 'append'\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private Methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('swapper') === true ) {\r\n            the = KTUtil.data(element).get('swapper');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        the.element = element;\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n\r\n        // Set initialized\r\n        the.element.setAttribute('data-kt-swapper', 'true');\r\n\r\n        // Initial update\r\n        _update();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('swapper', the);\r\n    }\r\n\r\n    var _update = function(e) {\r\n        var parentSelector = _getOption('parent');\r\n\r\n        var mode = _getOption('mode');\r\n        var parentElement = parentSelector ? document.querySelector(parentSelector) : null;\r\n       \r\n\r\n        if (parentElement && element.parentNode !== parentElement) {\r\n            if (mode === 'prepend') {\r\n                parentElement.prepend(element);\r\n            } else if (mode === 'append') {\r\n                parentElement.append(element);\r\n            }\r\n        }\r\n    }\r\n\r\n    var _getOption = function(name) {\r\n        if ( the.element.hasAttribute('data-kt-swapper-' + name) === true ) {\r\n            var attr = the.element.getAttribute('data-kt-swapper-' + name);\r\n            var value = KTUtil.getResponsiveValue(attr);\r\n\r\n            if ( value !== null && String(value) === 'true' ) {\r\n                value = true;\r\n            } else if ( value !== null && String(value) === 'false' ) {\r\n                value = false;\r\n            }\r\n\r\n            return value;\r\n        } else {\r\n            var optionName = KTUtil.snakeToCamel(name);\r\n\r\n            if ( the.options[optionName] ) {\r\n                return KTUtil.getResponsiveValue(the.options[optionName]);\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Construct Class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Methods\r\n    the.update = function() {\r\n        _update();\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTSwapper.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('swapper') ) {\r\n        return KTUtil.data(element).get('swapper');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTSwapper.createInstances = function(selector = '[data-kt-swapper=\"true\"]') {\r\n    // Initialize Menus\r\n    var elements = document.querySelectorAll(selector);\r\n    var swapper;\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            swapper = new KTSwapper(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Window resize handler\r\nwindow.addEventListener('resize', function() {\r\n    var timer;\r\n\r\n    KTUtil.throttle(timer, function() {\r\n        // Locate and update Offcanvas instances on window resize\r\n        var elements = document.querySelectorAll('[data-kt-swapper=\"true\"]');\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for (var i = 0, len = elements.length; i < len; i++) {\r\n                var swapper = KTSwapper.getInstance(elements[i]);\r\n                if (swapper) {\r\n                    swapper.update();\r\n                }                \r\n            }\r\n        }\r\n    }, 200);\r\n});\r\n\r\n// Global initialization\r\nKTSwapper.init = function() {\r\n    KTSwapper.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTSwapper.init);\r\n} else {\r\n    KTSwapper.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTSwapper;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTToggle = function(element, options) {\r\n    ////////////////////////////\r\n    // ** Private variables  ** //\r\n    ////////////////////////////\r\n    var the = this;\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    if (!element) {\r\n        return;\r\n    }\r\n\r\n    // Default Options\r\n    var defaultOptions = {\r\n        saveState: true\r\n    };\r\n\r\n    ////////////////////////////\r\n    // ** Private methods  ** //\r\n    ////////////////////////////\r\n\r\n    var _construct = function() {\r\n        if ( KTUtil.data(element).has('toggle') === true ) {\r\n            the = KTUtil.data(element).get('toggle');\r\n        } else {\r\n            _init();\r\n        }\r\n    }\r\n\r\n    var _init = function() {\r\n        // Variables\r\n        the.options = KTUtil.deepExtend({}, defaultOptions, options);\r\n        the.uid = KTUtil.getUniqueId('toggle');\r\n\r\n        // Elements\r\n        the.element = element;\r\n\r\n        the.target = document.querySelector(the.element.getAttribute('data-kt-toggle-target')) ? document.querySelector(the.element.getAttribute('data-kt-toggle-target')) : the.element;\r\n        the.state = the.element.hasAttribute('data-kt-toggle-state') ? the.element.getAttribute('data-kt-toggle-state') : '';\r\n        the.attribute = 'data-kt-' + the.element.getAttribute('data-kt-toggle-name');\r\n\r\n        // Event Handlers\r\n        _handlers();\r\n\r\n        // Bind Instance\r\n        KTUtil.data(the.element).set('toggle', the);\r\n    }\r\n\r\n    var _handlers = function() {\r\n        KTUtil.addEvent(the.element, 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            _toggle();\r\n        });\r\n    }\r\n\r\n    // Event handlers\r\n    var _toggle = function() {\r\n        // Trigger \"after.toggle\" event\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.change', the);\r\n\r\n        if ( _isEnabled() ) {\r\n            _disable();\r\n        } else {\r\n            _enable();\r\n        }\r\n\r\n        // Trigger \"before.toggle\" event\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.changed', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _enable = function() {\r\n        if ( _isEnabled() === true ) {\r\n            return;\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.enable', the);\r\n\r\n        the.target.setAttribute(the.attribute, 'on');\r\n\r\n        if (the.state.length > 0) {\r\n            the.element.classList.add(the.state);\r\n        }        \r\n\r\n        if ( typeof KTCookie !== 'undefined' && the.options.saveState === true ) {\r\n            KTCookie.set(the.attribute, 'on');\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.enabled', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _disable = function() {\r\n        if ( _isEnabled() === false ) {\r\n            return;\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.disable', the);\r\n\r\n        the.target.removeAttribute(the.attribute);\r\n\r\n        if (the.state.length > 0) {\r\n            the.element.classList.remove(the.state);\r\n        } \r\n\r\n        if ( typeof KTCookie !== 'undefined' && the.options.saveState === true ) {\r\n            KTCookie.remove(the.attribute);\r\n        }\r\n\r\n        KTEventHandler.trigger(the.element, 'kt.toggle.disabled', the);\r\n\r\n        return the;\r\n    }\r\n\r\n    var _isEnabled = function() {\r\n        return (String(the.target.getAttribute(the.attribute)).toLowerCase() === 'on');\r\n    }\r\n\r\n    // Construct class\r\n    _construct();\r\n\r\n    ///////////////////////\r\n    // ** Public API  ** //\r\n    ///////////////////////\r\n\r\n    // Plugin API\r\n    the.toggle = function() {\r\n        return _toggle();\r\n    }\r\n\r\n    the.enable = function() {\r\n        return _enable();\r\n    }\r\n\r\n    the.disable = function() {\r\n        return _disable();\r\n    }\r\n\r\n    the.isEnabled = function() {\r\n        return _isEnabled();\r\n    }\r\n\r\n    the.goElement = function() {\r\n        return the.element;\r\n    }\r\n\r\n    // Event API\r\n    the.on = function(name, handler) {\r\n        return KTEventHandler.on(the.element, name, handler);\r\n    }\r\n\r\n    the.one = function(name, handler) {\r\n        return KTEventHandler.one(the.element, name, handler);\r\n    }\r\n\r\n    the.off = function(name) {\r\n        return KTEventHandler.off(the.element, name);\r\n    }\r\n\r\n    the.trigger = function(name, event) {\r\n        return KTEventHandler.trigger(the.element, name, event, the, event);\r\n    }\r\n};\r\n\r\n// Static methods\r\nKTToggle.getInstance = function(element) {\r\n    if ( element !== null && KTUtil.data(element).has('toggle') ) {\r\n        return KTUtil.data(element).get('toggle');\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\n// Create instances\r\nKTToggle.createInstances = function(selector = '[data-kt-toggle]') {\r\n    var body = document.getElementsByTagName(\"BODY\")[0];\r\n\r\n    // Get instances\r\n    var elements = body.querySelectorAll(selector);\r\n\r\n    if ( elements && elements.length > 0 ) {\r\n        for (var i = 0, len = elements.length; i < len; i++) {\r\n            // Initialize instances\r\n            new KTToggle(elements[i]);\r\n        }\r\n    }\r\n}\r\n\r\n// Global initialization\r\nKTToggle.init = function() {\r\n    KTToggle.createInstances();\r\n};\r\n\r\n// On document ready\r\nif (document.readyState === 'loading') {\r\n   document.addEventListener('DOMContentLoaded', KTToggle.init);\r\n} else {\r\n    KTToggle.init();\r\n}\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTToggle;\r\n}", "\"use strict\";\r\n\r\n/**\r\n * @class KTUtil  base utilize class that privides helper functions\r\n */\r\n\r\n// Polyfills\r\n\r\n// Element.matches() polyfill\r\nif (!Element.prototype.matches) {\r\n    Element.prototype.matches = function(s) {\r\n        var matches = (this.document || this.ownerDocument).querySelectorAll(s),\r\n            i = matches.length;\r\n        while (--i >= 0 && matches.item(i) !== this) {}\r\n        return i > -1;\r\n    };\r\n}\r\n\r\n/**\r\n * Element.closest() polyfill\r\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/closest#Polyfill\r\n */\r\nif (!Element.prototype.closest) {\r\n\tElement.prototype.closest = function (s) {\r\n\t\tvar el = this;\r\n\t\tvar ancestor = this;\r\n\t\tif (!document.documentElement.contains(el)) return null;\r\n\t\tdo {\r\n\t\t\tif (ancestor.matches(s)) return ancestor;\r\n\t\t\tancestor = ancestor.parentElement;\r\n\t\t} while (ancestor !== null);\r\n\t\treturn null;\r\n\t};\r\n}\r\n\r\n/**\r\n * ChildNode.remove() polyfill\r\n * https://gomakethings.com/removing-an-element-from-the-dom-the-es6-way/\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n(function (elem) {\r\n\tfor (var i = 0; i < elem.length; i++) {\r\n\t\tif (!window[elem[i]] || 'remove' in window[elem[i]].prototype) continue;\r\n\t\twindow[elem[i]].prototype.remove = function () {\r\n\t\t\tthis.parentNode.removeChild(this);\r\n\t\t};\r\n\t}\r\n})(['Element', 'CharacterData', 'DocumentType']);\r\n\r\n\r\n//\r\n// requestAnimationFrame polyfill by Erik Möller.\r\n//  With fixes from Paul Irish and Tino Zijdel\r\n//\r\n//  http://paulirish.com/2011/requestanimationframe-for-smart-animating/\r\n//  http://my.opera.com/emoller/blog/2011/12/20/requestanimationframe-for-smart-er-animating\r\n//\r\n//  MIT license\r\n//\r\n(function() {\r\n    var lastTime = 0;\r\n    var vendors = ['webkit', 'moz'];\r\n    for (var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {\r\n        window.requestAnimationFrame = window[vendors[x] + 'RequestAnimationFrame'];\r\n        window.cancelAnimationFrame =\r\n            window[vendors[x] + 'CancelAnimationFrame'] || window[vendors[x] + 'CancelRequestAnimationFrame'];\r\n    }\r\n\r\n    if (!window.requestAnimationFrame)\r\n        window.requestAnimationFrame = function(callback) {\r\n            var currTime = new Date().getTime();\r\n            var timeToCall = Math.max(0, 16 - (currTime - lastTime));\r\n            var id = window.setTimeout(function() {\r\n                callback(currTime + timeToCall);\r\n            }, timeToCall);\r\n            lastTime = currTime + timeToCall;\r\n            return id;\r\n        };\r\n\r\n    if (!window.cancelAnimationFrame)\r\n        window.cancelAnimationFrame = function(id) {\r\n            clearTimeout(id);\r\n        };\r\n}());\r\n\r\n// Source: https://github.com/jserz/js_piece/blob/master/DOM/ParentNode/prepend()/prepend().md\r\n(function(arr) {\r\n    arr.forEach(function(item) {\r\n        if (item.hasOwnProperty('prepend')) {\r\n            return;\r\n        }\r\n        Object.defineProperty(item, 'prepend', {\r\n            configurable: true,\r\n            enumerable: true,\r\n            writable: true,\r\n            value: function prepend() {\r\n                var argArr = Array.prototype.slice.call(arguments),\r\n                    docFrag = document.createDocumentFragment();\r\n\r\n                argArr.forEach(function(argItem) {\r\n                    var isNode = argItem instanceof Node;\r\n                    docFrag.appendChild(isNode ? argItem : document.createTextNode(String(argItem)));\r\n                });\r\n\r\n                this.insertBefore(docFrag, this.firstChild);\r\n            }\r\n        });\r\n    });\r\n})([Element.prototype, Document.prototype, DocumentFragment.prototype]);\r\n\r\n// getAttributeNames\r\nif (Element.prototype.getAttributeNames == undefined) {\r\n  Element.prototype.getAttributeNames = function () {\r\n    var attributes = this.attributes;\r\n    var length = attributes.length;\r\n    var result = new Array(length);\r\n    for (var i = 0; i < length; i++) {\r\n      result[i] = attributes[i].name;\r\n    }\r\n    return result;\r\n  };\r\n}\r\n\r\n// Global variables\r\nwindow.KTUtilElementDataStore = {};\r\nwindow.KTUtilElementDataStoreID = 0;\r\nwindow.KTUtilDelegatedEventHandlers = {};\r\n\r\nvar KTUtil = function() {\r\n    var resizeHandlers = [];\r\n\r\n    /**\r\n     * Handle window resize event with some\r\n     * delay to attach event handlers upon resize complete\r\n     */\r\n    var _windowResizeHandler = function() {\r\n        var _runResizeHandlers = function() {\r\n            // reinitialize other subscribed elements\r\n            for (var i = 0; i < resizeHandlers.length; i++) {\r\n                var each = resizeHandlers[i];\r\n                each.call();\r\n            }\r\n        };\r\n\r\n        var timer;\r\n\r\n        window.addEventListener('resize', function() {\r\n            KTUtil.throttle(timer, function() {\r\n                _runResizeHandlers();\r\n            }, 200);\r\n        });\r\n    };\r\n\r\n    return {\r\n        /**\r\n         * Class main initializer.\r\n         * @param {object} settings.\r\n         * @returns null\r\n         */\r\n        //main function to initiate the theme\r\n        init: function(settings) {\r\n            _windowResizeHandler();\r\n        },\r\n\r\n        /**\r\n         * Adds window resize event handler.\r\n         * @param {function} callback function.\r\n         */\r\n        addResizeHandler: function(callback) {\r\n            resizeHandlers.push(callback);\r\n        },\r\n\r\n        /**\r\n         * Removes window resize event handler.\r\n         * @param {function} callback function.\r\n         */\r\n        removeResizeHandler: function(callback) {\r\n            for (var i = 0; i < resizeHandlers.length; i++) {\r\n                if (callback === resizeHandlers[i]) {\r\n                    delete resizeHandlers[i];\r\n                }\r\n            }\r\n        },\r\n\r\n        /**\r\n         * Trigger window resize handlers.\r\n         */\r\n        runResizeHandlers: function() {\r\n            _runResizeHandlers();\r\n        },\r\n\r\n        resize: function() {\r\n            if (typeof(Event) === 'function') {\r\n                // modern browsers\r\n                window.dispatchEvent(new Event('resize'));\r\n            } else {\r\n                // for IE and other old browsers\r\n                // causes deprecation warning on modern browsers\r\n                var evt = window.document.createEvent('UIEvents');\r\n                evt.initUIEvent('resize', true, false, window, 0);\r\n                window.dispatchEvent(evt);\r\n            }\r\n        },\r\n\r\n        /**\r\n         * Get GET parameter value from URL.\r\n         * @param {string} paramName Parameter name.\r\n         * @returns {string}\r\n         */\r\n        getURLParam: function(paramName) {\r\n            var searchString = window.location.search.substring(1),\r\n                i, val, params = searchString.split(\"&\");\r\n\r\n            for (i = 0; i < params.length; i++) {\r\n                val = params[i].split(\"=\");\r\n                if (val[0] == paramName) {\r\n                    return unescape(val[1]);\r\n                }\r\n            }\r\n\r\n            return null;\r\n        },\r\n\r\n        /**\r\n         * Checks whether current device is mobile touch.\r\n         * @returns {boolean}\r\n         */\r\n        isMobileDevice: function() {\r\n            var test = (this.getViewPort().width < this.getBreakpoint('lg') ? true : false);\r\n\r\n            if (test === false) {\r\n                // For use within normal web clients\r\n                test = navigator.userAgent.match(/iPad/i) != null;\r\n            }\r\n\r\n            return test;\r\n        },\r\n\r\n        /**\r\n         * Checks whether current device is desktop.\r\n         * @returns {boolean}\r\n         */\r\n        isDesktopDevice: function() {\r\n            return KTUtil.isMobileDevice() ? false : true;\r\n        },\r\n\r\n        /**\r\n         * Gets browser window viewport size. Ref:\r\n         * http://andylangton.co.uk/articles/javascript/get-viewport-size-javascript/\r\n         * @returns {object}\r\n         */\r\n        getViewPort: function() {\r\n            var e = window,\r\n                a = 'inner';\r\n            if (!('innerWidth' in window)) {\r\n                a = 'client';\r\n                e = document.documentElement || document.body;\r\n            }\r\n\r\n            return {\r\n                width: e[a + 'Width'],\r\n                height: e[a + 'Height']\r\n            };\r\n        },\r\n\r\n\t\t/**\r\n         * Checks whether given device mode is currently activated.\r\n         * @param {string} mode Responsive mode name(e.g: desktop,\r\n         *     desktop-and-tablet, tablet, tablet-and-mobile, mobile)\r\n         * @returns {boolean}\r\n         */\r\n        isBreakpointUp: function(mode) {\r\n            var width = this.getViewPort().width;\r\n\t\t\tvar breakpoint = this.getBreakpoint(mode);\r\n\r\n\t\t\treturn (width >= breakpoint);\r\n        },\r\n\r\n\t\tisBreakpointDown: function(mode) {\r\n            var width = this.getViewPort().width;\r\n\t\t\tvar breakpoint = this.getBreakpoint(mode);\r\n\r\n\t\t\treturn (width < breakpoint);\r\n        },\r\n\r\n        getViewportWidth: function() {\r\n            return this.getViewPort().width;\r\n        },\r\n\r\n        /**\r\n         * Generates unique ID for give prefix.\r\n         * @param {string} prefix Prefix for generated ID\r\n         * @returns {boolean}\r\n         */\r\n        getUniqueId: function(prefix) {\r\n            return prefix + Math.floor(Math.random() * (new Date()).getTime());\r\n        },\r\n\r\n        /**\r\n         * Gets window width for give breakpoint mode.\r\n         * @param {string} mode Responsive mode name(e.g: xl, lg, md, sm)\r\n         * @returns {number}\r\n         */\r\n        getBreakpoint: function(breakpoint) {\r\n            var value = this.getCssVariableValue('--bs-' + breakpoint);\r\n\r\n            if ( value ) {\r\n                value = parseInt(value.trim());\r\n            } \r\n\r\n            return value;\r\n        },\r\n\r\n        /**\r\n         * Checks whether object has property matchs given key path.\r\n         * @param {object} obj Object contains values paired with given key path\r\n         * @param {string} keys Keys path seperated with dots\r\n         * @returns {object}\r\n         */\r\n        isset: function(obj, keys) {\r\n            var stone;\r\n\r\n            keys = keys || '';\r\n\r\n            if (keys.indexOf('[') !== -1) {\r\n                throw new Error('Unsupported object path notation.');\r\n            }\r\n\r\n            keys = keys.split('.');\r\n\r\n            do {\r\n                if (obj === undefined) {\r\n                    return false;\r\n                }\r\n\r\n                stone = keys.shift();\r\n\r\n                if (!obj.hasOwnProperty(stone)) {\r\n                    return false;\r\n                }\r\n\r\n                obj = obj[stone];\r\n\r\n            } while (keys.length);\r\n\r\n            return true;\r\n        },\r\n\r\n        /**\r\n         * Gets highest z-index of the given element parents\r\n         * @param {object} el jQuery element object\r\n         * @returns {number}\r\n         */\r\n        getHighestZindex: function(el) {\r\n            var position, value;\r\n\r\n            while (el && el !== document) {\r\n                // Ignore z-index if position is set to a value where z-index is ignored by the browser\r\n                // This makes behavior of this function consistent across browsers\r\n                // WebKit always returns auto if the element is positioned\r\n                position = KTUtil.css(el, 'position');\r\n\r\n                if (position === \"absolute\" || position === \"relative\" || position === \"fixed\") {\r\n                    // IE returns 0 when zIndex is not specified\r\n                    // other browsers return a string\r\n                    // we ignore the case of nested elements with an explicit value of 0\r\n                    // <div style=\"z-index: -10;\"><div style=\"z-index: 0;\"></div></div>\r\n                    value = parseInt(KTUtil.css(el, 'z-index'));\r\n\r\n                    if (!isNaN(value) && value !== 0) {\r\n                        return value;\r\n                    }\r\n                }\r\n\r\n                el = el.parentNode;\r\n            }\r\n\r\n            return 1;\r\n        },\r\n\r\n        /**\r\n         * Checks whether the element has any parent with fixed positionfreg\r\n         * @param {object} el jQuery element object\r\n         * @returns {boolean}\r\n         */\r\n        hasFixedPositionedParent: function(el) {\r\n            var position;\r\n\r\n            while (el && el !== document) {\r\n                position = KTUtil.css(el, 'position');\r\n\r\n                if (position === \"fixed\") {\r\n                    return true;\r\n                }\r\n\r\n                el = el.parentNode;\r\n            }\r\n\r\n            return false;\r\n        },\r\n\r\n        /**\r\n         * Simulates delay\r\n         */\r\n        sleep: function(milliseconds) {\r\n            var start = new Date().getTime();\r\n            for (var i = 0; i < 1e7; i++) {\r\n                if ((new Date().getTime() - start) > milliseconds) {\r\n                    break;\r\n                }\r\n            }\r\n        },\r\n\r\n        /**\r\n         * Gets randomly generated integer value within given min and max range\r\n         * @param {number} min Range start value\r\n         * @param {number} max Range end value\r\n         * @returns {number}\r\n         */\r\n        getRandomInt: function(min, max) {\r\n            return Math.floor(Math.random() * (max - min + 1)) + min;\r\n        },\r\n\r\n        /**\r\n         * Checks whether Angular library is included\r\n         * @returns {boolean}\r\n         */\r\n        isAngularVersion: function() {\r\n            return window.Zone !== undefined ? true : false;\r\n        },\r\n\r\n        // Deep extend:  $.extend(true, {}, objA, objB);\r\n        deepExtend: function(out) {\r\n            out = out || {};\r\n\r\n            for (var i = 1; i < arguments.length; i++) {\r\n                var obj = arguments[i];\r\n                if (!obj) continue;\r\n\r\n                for (var key in obj) {\r\n                    if (!obj.hasOwnProperty(key)) {\r\n                        continue;\r\n                    }\r\n\r\n                    // based on https://javascriptweblog.wordpress.com/2011/08/08/fixing-the-javascript-typeof-operator/\r\n                    if ( Object.prototype.toString.call(obj[key]) === '[object Object]' ) {\r\n                        out[key] = KTUtil.deepExtend(out[key], obj[key]);\r\n                        continue;\r\n                    }\r\n\r\n                    out[key] = obj[key];\r\n                }\r\n            }\r\n\r\n            return out;\r\n        },\r\n\r\n        // extend:  $.extend({}, objA, objB);\r\n        extend: function(out) {\r\n            out = out || {};\r\n\r\n            for (var i = 1; i < arguments.length; i++) {\r\n                if (!arguments[i])\r\n                    continue;\r\n\r\n                for (var key in arguments[i]) {\r\n                    if (arguments[i].hasOwnProperty(key))\r\n                        out[key] = arguments[i][key];\r\n                }\r\n            }\r\n\r\n            return out;\r\n        },\r\n\r\n        getBody: function() {\r\n            return document.getElementsByTagName('body')[0];\r\n        },\r\n\r\n        /**\r\n         * Checks whether the element has given classes\r\n         * @param {object} el jQuery element object\r\n         * @param {string} Classes string\r\n         * @returns {boolean}\r\n         */\r\n        hasClasses: function(el, classes) {\r\n            if (!el) {\r\n                return;\r\n            }\r\n\r\n            var classesArr = classes.split(\" \");\r\n\r\n            for (var i = 0; i < classesArr.length; i++) {\r\n                if (KTUtil.hasClass(el, KTUtil.trim(classesArr[i])) == false) {\r\n                    return false;\r\n                }\r\n            }\r\n\r\n            return true;\r\n        },\r\n\r\n        hasClass: function(el, className) {\r\n            if (!el) {\r\n                return;\r\n            }\r\n\r\n            return el.classList ? el.classList.contains(className) : new RegExp('\\\\b' + className + '\\\\b').test(el.className);\r\n        },\r\n\r\n        addClass: function(el, className) {\r\n            if (!el || typeof className === 'undefined') {\r\n                return;\r\n            }\r\n\r\n            var classNames = className.split(' ');\r\n\r\n            if (el.classList) {\r\n                for (var i = 0; i < classNames.length; i++) {\r\n                    if (classNames[i] && classNames[i].length > 0) {\r\n                        el.classList.add(KTUtil.trim(classNames[i]));\r\n                    }\r\n                }\r\n            } else if (!KTUtil.hasClass(el, className)) {\r\n                for (var x = 0; x < classNames.length; x++) {\r\n                    el.className += ' ' + KTUtil.trim(classNames[x]);\r\n                }\r\n            }\r\n        },\r\n\r\n        removeClass: function(el, className) {\r\n          if (!el || typeof className === 'undefined') {\r\n                return;\r\n            }\r\n\r\n            var classNames = className.split(' ');\r\n\r\n            if (el.classList) {\r\n                for (var i = 0; i < classNames.length; i++) {\r\n                    el.classList.remove(KTUtil.trim(classNames[i]));\r\n                }\r\n            } else if (KTUtil.hasClass(el, className)) {\r\n                for (var x = 0; x < classNames.length; x++) {\r\n                    el.className = el.className.replace(new RegExp('\\\\b' + KTUtil.trim(classNames[x]) + '\\\\b', 'g'), '');\r\n                }\r\n            }\r\n        },\r\n\r\n        triggerCustomEvent: function(el, eventName, data) {\r\n            var event;\r\n            if (window.CustomEvent) {\r\n                event = new CustomEvent(eventName, {\r\n                    detail: data\r\n                });\r\n            } else {\r\n                event = document.createEvent('CustomEvent');\r\n                event.initCustomEvent(eventName, true, true, data);\r\n            }\r\n\r\n            el.dispatchEvent(event);\r\n        },\r\n\r\n        triggerEvent: function(node, eventName) {\r\n            // Make sure we use the ownerDocument from the provided node to avoid cross-window problems\r\n            var doc;\r\n\r\n            if (node.ownerDocument) {\r\n                doc = node.ownerDocument;\r\n            } else if (node.nodeType == 9) {\r\n                // the node may be the document itself, nodeType 9 = DOCUMENT_NODE\r\n                doc = node;\r\n            } else {\r\n                throw new Error(\"Invalid node passed to fireEvent: \" + node.id);\r\n            }\r\n\r\n            if (node.dispatchEvent) {\r\n                // Gecko-style approach (now the standard) takes more work\r\n                var eventClass = \"\";\r\n\r\n                // Different events have different event classes.\r\n                // If this switch statement can't map an eventName to an eventClass,\r\n                // the event firing is going to fail.\r\n                switch (eventName) {\r\n                case \"click\": // Dispatching of 'click' appears to not work correctly in Safari. Use 'mousedown' or 'mouseup' instead.\r\n                case \"mouseenter\":\r\n                case \"mouseleave\":\r\n                case \"mousedown\":\r\n                case \"mouseup\":\r\n                    eventClass = \"MouseEvents\";\r\n                    break;\r\n\r\n                case \"focus\":\r\n                case \"change\":\r\n                case \"blur\":\r\n                case \"select\":\r\n                    eventClass = \"HTMLEvents\";\r\n                    break;\r\n\r\n                default:\r\n                    throw \"fireEvent: Couldn't find an event class for event '\" + eventName + \"'.\";\r\n                    break;\r\n                }\r\n                var event = doc.createEvent(eventClass);\r\n\r\n                var bubbles = eventName == \"change\" ? false : true;\r\n                event.initEvent(eventName, bubbles, true); // All events created as bubbling and cancelable.\r\n\r\n                event.synthetic = true; // allow detection of synthetic events\r\n                // The second parameter says go ahead with the default action\r\n                node.dispatchEvent(event, true);\r\n            } else if (node.fireEvent) {\r\n                // IE-old school style\r\n                var event = doc.createEventObject();\r\n                event.synthetic = true; // allow detection of synthetic events\r\n                node.fireEvent(\"on\" + eventName, event);\r\n            }\r\n        },\r\n\r\n        index: function( el ){\r\n            var c = el.parentNode.children, i = 0;\r\n            for(; i < c.length; i++ )\r\n                if( c[i] == el ) return i;\r\n        },\r\n\r\n        trim: function(string) {\r\n            return string.trim();\r\n        },\r\n\r\n        eventTriggered: function(e) {\r\n            if (e.currentTarget.dataset.triggered) {\r\n                return true;\r\n            } else {\r\n                e.currentTarget.dataset.triggered = true;\r\n\r\n                return false;\r\n            }\r\n        },\r\n\r\n        remove: function(el) {\r\n            if (el && el.parentNode) {\r\n                el.parentNode.removeChild(el);\r\n            }\r\n        },\r\n\r\n        find: function(parent, query) {\r\n            if ( parent !== null) {\r\n                return parent.querySelector(query);\r\n            } else {\r\n                return null;\r\n            }\r\n        },\r\n\r\n        findAll: function(parent, query) {\r\n            if ( parent !== null ) {\r\n                return parent.querySelectorAll(query);\r\n            } else {\r\n                return null;\r\n            }\r\n        },\r\n\r\n        insertAfter: function(el, referenceNode) {\r\n            return referenceNode.parentNode.insertBefore(el, referenceNode.nextSibling);\r\n        },\r\n\r\n        parents: function(elem, selector) {\r\n            // Set up a parent array\r\n            var parents = [];\r\n\r\n            // Push each parent element to the array\r\n            for ( ; elem && elem !== document; elem = elem.parentNode ) {\r\n                if (selector) {\r\n                    if (elem.matches(selector)) {\r\n                        parents.push(elem);\r\n                    }\r\n                    continue;\r\n                }\r\n                parents.push(elem);\r\n            }\r\n\r\n            // Return our parent array\r\n            return parents;\r\n        },\r\n\r\n        children: function(el, selector, log) {\r\n            if (!el || !el.childNodes) {\r\n                return null;\r\n            }\r\n\r\n            var result = [],\r\n                i = 0,\r\n                l = el.childNodes.length;\r\n\r\n            for (var i; i < l; ++i) {\r\n                if (el.childNodes[i].nodeType == 1 && KTUtil.matches(el.childNodes[i], selector, log)) {\r\n                    result.push(el.childNodes[i]);\r\n                }\r\n            }\r\n\r\n            return result;\r\n        },\r\n\r\n        child: function(el, selector, log) {\r\n            var children = KTUtil.children(el, selector, log);\r\n\r\n            return children ? children[0] : null;\r\n        },\r\n\r\n        matches: function(el, selector, log) {\r\n            var p = Element.prototype;\r\n            var f = p.matches || p.webkitMatchesSelector || p.mozMatchesSelector || p.msMatchesSelector || function(s) {\r\n                return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\r\n            };\r\n\r\n            if (el && el.tagName) {\r\n                return f.call(el, selector);\r\n            } else {\r\n                return false;\r\n            }\r\n        },\r\n\r\n        data: function(el) {\r\n            return {\r\n                set: function(name, data) {\r\n                    if (!el) {\r\n                        return;\r\n                    }\r\n\r\n                    if (el.customDataTag === undefined) {\r\n                        window.KTUtilElementDataStoreID++;\r\n                        el.customDataTag = window.KTUtilElementDataStoreID;\r\n                    }\r\n\r\n                    if (window.KTUtilElementDataStore[el.customDataTag] === undefined) {\r\n                        window.KTUtilElementDataStore[el.customDataTag] = {};\r\n                    }\r\n\r\n                    window.KTUtilElementDataStore[el.customDataTag][name] = data;\r\n                },\r\n\r\n                get: function(name) {\r\n                    if (!el) {\r\n                        return;\r\n                    }\r\n\r\n                    if (el.customDataTag === undefined) {\r\n                        return null;\r\n                    }\r\n\r\n                    return this.has(name) ? window.KTUtilElementDataStore[el.customDataTag][name] : null;\r\n                },\r\n\r\n                has: function(name) {\r\n                    if (!el) {\r\n                        return false;\r\n                    }\r\n\r\n                    if (el.customDataTag === undefined) {\r\n                        return false;\r\n                    }\r\n\r\n                    return (window.KTUtilElementDataStore[el.customDataTag] && window.KTUtilElementDataStore[el.customDataTag][name]) ? true : false;\r\n                },\r\n\r\n                remove: function(name) {\r\n                    if (el && this.has(name)) {\r\n                        delete window.KTUtilElementDataStore[el.customDataTag][name];\r\n                    }\r\n                }\r\n            };\r\n        },\r\n\r\n        outerWidth: function(el, margin) {\r\n            var width;\r\n\r\n            if (margin === true) {\r\n                width = parseFloat(el.offsetWidth);\r\n                width += parseFloat(KTUtil.css(el, 'margin-left')) + parseFloat(KTUtil.css(el, 'margin-right'));\r\n\r\n                return parseFloat(width);\r\n            } else {\r\n                width = parseFloat(el.offsetWidth);\r\n\r\n                return width;\r\n            }\r\n        },\r\n\r\n        offset: function(el) {\r\n            var rect, win;\r\n\r\n            if ( !el ) {\r\n                return;\r\n            }\r\n\r\n            // Return zeros for disconnected and hidden (display: none) elements (gh-2310)\r\n            // Support: IE <=11 only\r\n            // Running getBoundingClientRect on a\r\n            // disconnected node in IE throws an error\r\n\r\n            if ( !el.getClientRects().length ) {\r\n                return { top: 0, left: 0 };\r\n            }\r\n\r\n            // Get document-relative position by adding viewport scroll to viewport-relative gBCR\r\n            rect = el.getBoundingClientRect();\r\n            win = el.ownerDocument.defaultView;\r\n\r\n            return {\r\n                top: rect.top + win.pageYOffset,\r\n                left: rect.left + win.pageXOffset,\r\n                right: window.innerWidth - (el.offsetLeft + el.offsetWidth)\r\n            };\r\n        },\r\n\r\n        height: function(el) {\r\n            return KTUtil.css(el, 'height');\r\n        },\r\n\r\n        outerHeight: function(el, withMargin) {\r\n            var height = el.offsetHeight;\r\n            var style;\r\n\r\n            if (typeof withMargin !== 'undefined' && withMargin === true) {\r\n                style = getComputedStyle(el);\r\n                height += parseInt(style.marginTop) + parseInt(style.marginBottom);\r\n\r\n                return height;\r\n            } else {\r\n                return height;\r\n            }\r\n        },\r\n\r\n        visible: function(el) {\r\n            return !(el.offsetWidth === 0 && el.offsetHeight === 0);\r\n        },\r\n\r\n        attr: function(el, name, value) {\r\n            if (el == undefined) {\r\n                return;\r\n            }\r\n\r\n            if (value !== undefined) {\r\n                el.setAttribute(name, value);\r\n            } else {\r\n                return el.getAttribute(name);\r\n            }\r\n        },\r\n\r\n        hasAttr: function(el, name) {\r\n            if (el == undefined) {\r\n                return;\r\n            }\r\n\r\n            return el.getAttribute(name) ? true : false;\r\n        },\r\n\r\n        removeAttr: function(el, name) {\r\n            if (el == undefined) {\r\n                return;\r\n            }\r\n\r\n            el.removeAttribute(name);\r\n        },\r\n\r\n        animate: function(from, to, duration, update, easing, done) {\r\n            /**\r\n             * TinyAnimate.easings\r\n             *  Adapted from jQuery Easing\r\n             */\r\n            var easings = {};\r\n            var easing;\r\n\r\n            easings.linear = function(t, b, c, d) {\r\n                return c * t / d + b;\r\n            };\r\n\r\n            easing = easings.linear;\r\n\r\n            // Early bail out if called incorrectly\r\n            if (typeof from !== 'number' ||\r\n                typeof to !== 'number' ||\r\n                typeof duration !== 'number' ||\r\n                typeof update !== 'function') {\r\n                return;\r\n            }\r\n\r\n            // Create mock done() function if necessary\r\n            if (typeof done !== 'function') {\r\n                done = function() {};\r\n            }\r\n\r\n            // Pick implementation (requestAnimationFrame | setTimeout)\r\n            var rAF = window.requestAnimationFrame || function(callback) {\r\n                window.setTimeout(callback, 1000 / 50);\r\n            };\r\n\r\n            // Animation loop\r\n            var canceled = false;\r\n            var change = to - from;\r\n\r\n            function loop(timestamp) {\r\n                var time = (timestamp || +new Date()) - start;\r\n\r\n                if (time >= 0) {\r\n                    update(easing(time, from, change, duration));\r\n                }\r\n                if (time >= 0 && time >= duration) {\r\n                    update(to);\r\n                    done();\r\n                } else {\r\n                    rAF(loop);\r\n                }\r\n            }\r\n\r\n            update(from);\r\n\r\n            // Start animation loop\r\n            var start = window.performance && window.performance.now ? window.performance.now() : +new Date();\r\n\r\n            rAF(loop);\r\n        },\r\n\r\n        actualCss: function(el, prop, cache) {\r\n            var css = '';\r\n\r\n            if (el instanceof HTMLElement === false) {\r\n                return;\r\n            }\r\n\r\n            if (!el.getAttribute('kt-hidden-' + prop) || cache === false) {\r\n                var value;\r\n\r\n                // the element is hidden so:\r\n                // making the el block so we can meassure its height but still be hidden\r\n                css = el.style.cssText;\r\n                el.style.cssText = 'position: absolute; visibility: hidden; display: block;';\r\n\r\n                if (prop == 'width') {\r\n                    value = el.offsetWidth;\r\n                } else if (prop == 'height') {\r\n                    value = el.offsetHeight;\r\n                }\r\n\r\n                el.style.cssText = css;\r\n\r\n                // store it in cache\r\n                el.setAttribute('kt-hidden-' + prop, value);\r\n\r\n                return parseFloat(value);\r\n            } else {\r\n                // store it in cache\r\n                return parseFloat(el.getAttribute('kt-hidden-' + prop));\r\n            }\r\n        },\r\n\r\n        actualHeight: function(el, cache) {\r\n            return KTUtil.actualCss(el, 'height', cache);\r\n        },\r\n\r\n        actualWidth: function(el, cache) {\r\n            return KTUtil.actualCss(el, 'width', cache);\r\n        },\r\n\r\n        getScroll: function(element, method) {\r\n            // The passed in `method` value should be 'Top' or 'Left'\r\n            method = 'scroll' + method;\r\n            return (element == window || element == document) ? (\r\n                self[(method == 'scrollTop') ? 'pageYOffset' : 'pageXOffset'] ||\r\n                (browserSupportsBoxModel && document.documentElement[method]) ||\r\n                document.body[method]\r\n            ) : element[method];\r\n        },\r\n\r\n        css: function(el, styleProp, value, important) {\r\n            if (!el) {\r\n                return;\r\n            }\r\n\r\n            if (value !== undefined) {\r\n                if ( important === true ) {\r\n                    el.style.setProperty(styleProp, value, 'important');\r\n                } else {\r\n                    el.style[styleProp] = value;\r\n                }\r\n            } else {\r\n                var defaultView = (el.ownerDocument || document).defaultView;\r\n\r\n                // W3C standard way:\r\n                if (defaultView && defaultView.getComputedStyle) {\r\n                    // sanitize property name to css notation\r\n                    // (hyphen separated words eg. font-Size)\r\n                    styleProp = styleProp.replace(/([A-Z])/g, \"-$1\").toLowerCase();\r\n\r\n                    return defaultView.getComputedStyle(el, null).getPropertyValue(styleProp);\r\n                } else if (el.currentStyle) { // IE\r\n                    // sanitize property name to camelCase\r\n                    styleProp = styleProp.replace(/\\-(\\w)/g, function(str, letter) {\r\n                        return letter.toUpperCase();\r\n                    });\r\n\r\n                    value = el.currentStyle[styleProp];\r\n\r\n                    // convert other units to pixels on IE\r\n                    if (/^\\d+(em|pt|%|ex)?$/i.test(value)) {\r\n                        return (function(value) {\r\n                            var oldLeft = el.style.left, oldRsLeft = el.runtimeStyle.left;\r\n\r\n                            el.runtimeStyle.left = el.currentStyle.left;\r\n                            el.style.left = value || 0;\r\n                            value = el.style.pixelLeft + \"px\";\r\n                            el.style.left = oldLeft;\r\n                            el.runtimeStyle.left = oldRsLeft;\r\n\r\n                            return value;\r\n                        })(value);\r\n                    }\r\n\r\n                    return value;\r\n                }\r\n            }\r\n        },\r\n\r\n        slide: function(el, dir, speed, callback, recalcMaxHeight) {\r\n            if (!el || (dir == 'up' && KTUtil.visible(el) === false) || (dir == 'down' && KTUtil.visible(el) === true)) {\r\n                return;\r\n            }\r\n\r\n            speed = (speed ? speed : 600);\r\n            var calcHeight = KTUtil.actualHeight(el);\r\n            var calcPaddingTop = false;\r\n            var calcPaddingBottom = false;\r\n\r\n            if (KTUtil.css(el, 'padding-top') && KTUtil.data(el).has('slide-padding-top') !== true) {\r\n                KTUtil.data(el).set('slide-padding-top', KTUtil.css(el, 'padding-top'));\r\n            }\r\n\r\n            if (KTUtil.css(el, 'padding-bottom') && KTUtil.data(el).has('slide-padding-bottom') !== true) {\r\n                KTUtil.data(el).set('slide-padding-bottom', KTUtil.css(el, 'padding-bottom'));\r\n            }\r\n\r\n            if (KTUtil.data(el).has('slide-padding-top')) {\r\n                calcPaddingTop = parseInt(KTUtil.data(el).get('slide-padding-top'));\r\n            }\r\n\r\n            if (KTUtil.data(el).has('slide-padding-bottom')) {\r\n                calcPaddingBottom = parseInt(KTUtil.data(el).get('slide-padding-bottom'));\r\n            }\r\n\r\n            if (dir == 'up') { // up\r\n                el.style.cssText = 'display: block; overflow: hidden;';\r\n\r\n                if (calcPaddingTop) {\r\n                    KTUtil.animate(0, calcPaddingTop, speed, function(value) {\r\n                        el.style.paddingTop = (calcPaddingTop - value) + 'px';\r\n                    }, 'linear');\r\n                }\r\n\r\n                if (calcPaddingBottom) {\r\n                    KTUtil.animate(0, calcPaddingBottom, speed, function(value) {\r\n                        el.style.paddingBottom = (calcPaddingBottom - value) + 'px';\r\n                    }, 'linear');\r\n                }\r\n\r\n                KTUtil.animate(0, calcHeight, speed, function(value) {\r\n                    el.style.height = (calcHeight - value) + 'px';\r\n                }, 'linear', function() {\r\n                    el.style.height = '';\r\n                    el.style.display = 'none';\r\n\r\n                    if (typeof callback === 'function') {\r\n                        callback();\r\n                    }\r\n                });\r\n\r\n\r\n            } else if (dir == 'down') { // down\r\n                el.style.cssText = 'display: block; overflow: hidden;';\r\n\r\n                if (calcPaddingTop) {\r\n                    KTUtil.animate(0, calcPaddingTop, speed, function(value) {//\r\n                        el.style.paddingTop = value + 'px';\r\n                    }, 'linear', function() {\r\n                        el.style.paddingTop = '';\r\n                    });\r\n                }\r\n\r\n                if (calcPaddingBottom) {\r\n                    KTUtil.animate(0, calcPaddingBottom, speed, function(value) {\r\n                        el.style.paddingBottom = value + 'px';\r\n                    }, 'linear', function() {\r\n                        el.style.paddingBottom = '';\r\n                    });\r\n                }\r\n\r\n                KTUtil.animate(0, calcHeight, speed, function(value) {\r\n                    el.style.height = value + 'px';\r\n                }, 'linear', function() {\r\n                    el.style.height = '';\r\n                    el.style.display = '';\r\n                    el.style.overflow = '';\r\n\r\n                    if (typeof callback === 'function') {\r\n                        callback();\r\n                    }\r\n                });\r\n            }\r\n        },\r\n\r\n        slideUp: function(el, speed, callback) {\r\n            KTUtil.slide(el, 'up', speed, callback);\r\n        },\r\n\r\n        slideDown: function(el, speed, callback) {\r\n            KTUtil.slide(el, 'down', speed, callback);\r\n        },\r\n\r\n        show: function(el, display) {\r\n            if (typeof el !== 'undefined') {\r\n                el.style.display = (display ? display : 'block');\r\n            }\r\n        },\r\n\r\n        hide: function(el) {\r\n            if (typeof el !== 'undefined') {\r\n                el.style.display = 'none';\r\n            }\r\n        },\r\n\r\n        addEvent: function(el, type, handler, one) {\r\n            if (typeof el !== 'undefined' && el !== null) {\r\n                el.addEventListener(type, handler);\r\n            }\r\n        },\r\n\r\n        removeEvent: function(el, type, handler) {\r\n            if (el !== null) {\r\n                el.removeEventListener(type, handler);\r\n            }\r\n        },\r\n\r\n        on: function(element, selector, event, handler) {\r\n            if ( element === null ) {\r\n                return;\r\n            }\r\n\r\n            var eventId = KTUtil.getUniqueId('event');\r\n\r\n            window.KTUtilDelegatedEventHandlers[eventId] = function(e) {\r\n                var targets = element.querySelectorAll(selector);\r\n                var target = e.target;\r\n\r\n                while ( target && target !== element ) {\r\n                    for ( var i = 0, j = targets.length; i < j; i++ ) {\r\n                        if ( target === targets[i] ) {\r\n                            handler.call(target, e);\r\n                        }\r\n                    }\r\n\r\n                    target = target.parentNode;\r\n                }\r\n            }\r\n\r\n            KTUtil.addEvent(element, event, window.KTUtilDelegatedEventHandlers[eventId]);\r\n\r\n            return eventId;\r\n        },\r\n\r\n        off: function(element, event, eventId) {\r\n            if (!element || !window.KTUtilDelegatedEventHandlers[eventId]) {\r\n                return;\r\n            }\r\n\r\n            KTUtil.removeEvent(element, event, window.KTUtilDelegatedEventHandlers[eventId]);\r\n\r\n            delete window.KTUtilDelegatedEventHandlers[eventId];\r\n        },\r\n\r\n        one: function onetime(el, type, callback) {\r\n            el.addEventListener(type, function callee(e) {\r\n                // remove event\r\n                if (e.target && e.target.removeEventListener) {\r\n                    e.target.removeEventListener(e.type, callee);\r\n                }\r\n\r\n                // need to verify from https://themeforest.net/author_dashboard#comment_23615588\r\n                if (el && el.removeEventListener) {\r\n\t\t\t\t    e.currentTarget.removeEventListener(e.type, callee);\r\n\t\t\t    }\r\n\r\n                // call handler\r\n                return callback(e);\r\n            });\r\n        },\r\n\r\n        hash: function(str) {\r\n            var hash = 0,\r\n                i, chr;\r\n\r\n            if (str.length === 0) return hash;\r\n            for (i = 0; i < str.length; i++) {\r\n                chr = str.charCodeAt(i);\r\n                hash = ((hash << 5) - hash) + chr;\r\n                hash |= 0; // Convert to 32bit integer\r\n            }\r\n\r\n            return hash;\r\n        },\r\n\r\n        animateClass: function(el, animationName, callback) {\r\n            var animation;\r\n            var animations = {\r\n                animation: 'animationend',\r\n                OAnimation: 'oAnimationEnd',\r\n                MozAnimation: 'mozAnimationEnd',\r\n                WebkitAnimation: 'webkitAnimationEnd',\r\n                msAnimation: 'msAnimationEnd',\r\n            };\r\n\r\n            for (var t in animations) {\r\n                if (el.style[t] !== undefined) {\r\n                    animation = animations[t];\r\n                }\r\n            }\r\n            \r\n            KTUtil.addClass(el, animationName);\r\n\r\n            KTUtil.one(el, animation, function() {\r\n                KTUtil.removeClass(el, animationName);\r\n            });\r\n\r\n            if (callback) {\r\n                KTUtil.one(el, animation, callback);\r\n            }\r\n        },\r\n\r\n        transitionEnd: function(el, callback) {\r\n            var transition;\r\n            var transitions = {\r\n                transition: 'transitionend',\r\n                OTransition: 'oTransitionEnd',\r\n                MozTransition: 'mozTransitionEnd',\r\n                WebkitTransition: 'webkitTransitionEnd',\r\n                msTransition: 'msTransitionEnd'\r\n            };\r\n\r\n            for (var t in transitions) {\r\n                if (el.style[t] !== undefined) {\r\n                    transition = transitions[t];\r\n                }\r\n            }\r\n\r\n            KTUtil.one(el, transition, callback);\r\n        },\r\n\r\n        animationEnd: function(el, callback) {\r\n            var animation;\r\n            var animations = {\r\n                animation: 'animationend',\r\n                OAnimation: 'oAnimationEnd',\r\n                MozAnimation: 'mozAnimationEnd',\r\n                WebkitAnimation: 'webkitAnimationEnd',\r\n                msAnimation: 'msAnimationEnd'\r\n            };\r\n\r\n            for (var t in animations) {\r\n                if (el.style[t] !== undefined) {\r\n                    animation = animations[t];\r\n                }\r\n            }\r\n\r\n            KTUtil.one(el, animation, callback);\r\n        },\r\n\r\n        animateDelay: function(el, value) {\r\n            var vendors = ['webkit-', 'moz-', 'ms-', 'o-', ''];\r\n            for (var i = 0; i < vendors.length; i++) {\r\n                KTUtil.css(el, vendors[i] + 'animation-delay', value);\r\n            }\r\n        },\r\n\r\n        animateDuration: function(el, value) {\r\n            var vendors = ['webkit-', 'moz-', 'ms-', 'o-', ''];\r\n            for (var i = 0; i < vendors.length; i++) {\r\n                KTUtil.css(el, vendors[i] + 'animation-duration', value);\r\n            }\r\n        },\r\n\r\n        scrollTo: function(target, offset, duration) {\r\n            var duration = duration ? duration : 500;\r\n            var targetPos = target ? KTUtil.offset(target).top : 0;\r\n            var scrollPos = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n            var from, to;\r\n\r\n            if (offset) {\r\n                targetPos = targetPos - offset;\r\n            }\r\n\r\n            from = scrollPos;\r\n            to = targetPos;\r\n\r\n            KTUtil.animate(from, to, duration, function(value) {\r\n                document.documentElement.scrollTop = value;\r\n                document.body.parentNode.scrollTop = value;\r\n                document.body.scrollTop = value;\r\n            }); //, easing, done\r\n        },\r\n\r\n        scrollTop: function(offset, duration) {\r\n            KTUtil.scrollTo(null, offset, duration);\r\n        },\r\n\r\n        isArray: function(obj) {\r\n            return obj && Array.isArray(obj);\r\n        },\r\n\r\n        isEmpty: function(obj) {\r\n            for (var prop in obj) {\r\n                if (obj.hasOwnProperty(prop)) {\r\n                    return false;\r\n                }\r\n            }\r\n\r\n            return true;\r\n        },\r\n\r\n        numberString: function(nStr) {\r\n            nStr += '';\r\n            var x = nStr.split('.');\r\n            var x1 = x[0];\r\n            var x2 = x.length > 1 ? '.' + x[1] : '';\r\n            var rgx = /(\\d+)(\\d{3})/;\r\n            while (rgx.test(x1)) {\r\n                x1 = x1.replace(rgx, '$1' + ',' + '$2');\r\n            }\r\n            return x1 + x2;\r\n        },\r\n\r\n        isRTL: function() {\r\n            return (document.querySelector('html').getAttribute(\"direction\") === 'rtl');\r\n        },\r\n\r\n        snakeToCamel: function(s){\r\n            return s.replace(/(\\-\\w)/g, function(m){return m[1].toUpperCase();});\r\n        },\r\n\r\n        filterBoolean: function(val) {\r\n            // Convert string boolean\r\n\t\t\tif (val === true || val === 'true') {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\r\n\t\t\tif (val === false || val === 'false') {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n            return val;\r\n        },\r\n\r\n        setHTML: function(el, html) {\r\n            el.innerHTML = html;\r\n        },\r\n\r\n        getHTML: function(el) {\r\n            if (el) {\r\n                return el.innerHTML;\r\n            }\r\n        },\r\n\r\n        getDocumentHeight: function() {\r\n            var body = document.body;\r\n            var html = document.documentElement;\r\n\r\n            return Math.max( body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight );\r\n        },\r\n\r\n        getScrollTop: function() {\r\n            return  (document.scrollingElement || document.documentElement).scrollTop;\r\n        },\r\n\r\n        colorLighten: function(color, amount) {\r\n            const addLight = function(color, amount){\r\n                let cc = parseInt(color,16) + amount;\r\n                let c = (cc > 255) ? 255 : (cc);\r\n                c = (c.toString(16).length > 1 ) ? c.toString(16) : `0${c.toString(16)}`;\r\n                return c;\r\n            }\r\n\r\n            color = (color.indexOf(\"#\")>=0) ? color.substring(1,color.length) : color;\r\n            amount = parseInt((255*amount)/100);\r\n            \r\n            return color = `#${addLight(color.substring(0,2), amount)}${addLight(color.substring(2,4), amount)}${addLight(color.substring(4,6), amount)}`;\r\n        },\r\n\r\n        colorDarken: function(color, amount) {\r\n            const subtractLight = function(color, amount){\r\n                let cc = parseInt(color,16) - amount;\r\n                let c = (cc < 0) ? 0 : (cc);\r\n                c = (c.toString(16).length > 1 ) ? c.toString(16) : `0${c.toString(16)}`;\r\n\r\n                return c;\r\n            }\r\n              \r\n            color = (color.indexOf(\"#\")>=0) ? color.substring(1,color.length) : color;\r\n            amount = parseInt((255*amount)/100);\r\n\r\n            return color = `#${subtractLight(color.substring(0,2), amount)}${subtractLight(color.substring(2,4), amount)}${subtractLight(color.substring(4,6), amount)}`;\r\n        },\r\n\r\n        // Throttle function: Input as function which needs to be throttled and delay is the time interval in milliseconds\r\n        throttle:  function (timer, func, delay) {\r\n        \t// If setTimeout is already scheduled, no need to do anything\r\n        \tif (timer) {\r\n        \t\treturn;\r\n        \t}\r\n\r\n        \t// Schedule a setTimeout after delay seconds\r\n        \ttimer  =  setTimeout(function () {\r\n        \t\tfunc();\r\n\r\n        \t\t// Once setTimeout function execution is finished, timerId = undefined so that in <br>\r\n        \t\t// the next scroll event function execution can be scheduled by the setTimeout\r\n        \t\ttimer  =  undefined;\r\n        \t}, delay);\r\n        },\r\n\r\n        // Debounce function: Input as function which needs to be debounced and delay is the debounced time in milliseconds\r\n        debounce: function (timer, func, delay) {\r\n        \t// Cancels the setTimeout method execution\r\n        \tclearTimeout(timer)\r\n\r\n        \t// Executes the func after delay time.\r\n        \ttimer  =  setTimeout(func, delay);\r\n        },\r\n\r\n        parseJson: function(value) {\r\n            if (typeof value === 'string') {\r\n                value = value.replace(/'/g, \"\\\"\");\r\n\r\n                var jsonStr = value.replace(/(\\w+:)|(\\w+ :)/g, function(matched) {\r\n                    return '\"' + matched.substring(0, matched.length - 1) + '\":';\r\n                });\r\n\r\n                try {\r\n                    value = JSON.parse(jsonStr);\r\n                } catch(e) { }\r\n            }\r\n\r\n            return value;\r\n        },\r\n\r\n        getResponsiveValue: function(value, defaultValue) {\r\n            var width = this.getViewPort().width;\r\n            var result;\r\n\r\n            value = KTUtil.parseJson(value);\r\n\r\n            if (typeof value === 'object') {\r\n                var resultKey;\r\n                var resultBreakpoint = -1;\r\n                var breakpoint;\r\n\r\n                for (var key in value) {\r\n                    if (key === 'default') {\r\n                        breakpoint = 0;\r\n                    } else {\r\n                        breakpoint = this.getBreakpoint(key) ? this.getBreakpoint(key) : parseInt(key);\r\n                    }\r\n\r\n                    if (breakpoint <= width && breakpoint > resultBreakpoint) {\r\n                        resultKey = key;\r\n                        resultBreakpoint = breakpoint;\r\n                    }\r\n                }\r\n\r\n                if (resultKey) {\r\n                    result = value[resultKey];\r\n                } else {\r\n                    result = value;\r\n                }\r\n            } else {\r\n                result = value;\r\n            }\r\n\r\n            return result;\r\n        },\r\n\r\n        each: function(array, callback) {\r\n            return [].slice.call(array).map(callback);\r\n        },\r\n\r\n        getSelectorMatchValue: function(value) {\r\n            var result = null;\r\n            value = KTUtil.parseJson(value);\r\n\r\n            if ( typeof value === 'object' ) {\r\n                // Match condition\r\n                if ( value['match'] !== undefined ) {\r\n                    var selector = Object.keys(value['match'])[0];\r\n                    value = Object.values(value['match'])[0];\r\n\r\n                    if ( document.querySelector(selector) !== null ) {\r\n                        result = value;\r\n                    }\r\n                }\r\n            } else {\r\n                result = value;\r\n            }\r\n\r\n            return result;\r\n        },\r\n\r\n        getConditionalValue: function(value) {\r\n            var value = KTUtil.parseJson(value);\r\n            var result = KTUtil.getResponsiveValue(value);\r\n\r\n            if ( result !== null && result['match'] !== undefined ) {\r\n                result = KTUtil.getSelectorMatchValue(result);\r\n            }\r\n\r\n            if ( result === null && value !== null && value['default'] !== undefined ) {\r\n                result = value['default'];\r\n            }\r\n\r\n            return result;\r\n        },\r\n\r\n        getCssVariableValue: function(variableName) {\r\n            var hex = getComputedStyle(document.documentElement).getPropertyValue(variableName);\r\n            if ( hex && hex.length > 0 ) {\r\n                hex = hex.trim();\r\n            }\r\n\r\n            return hex;\r\n        },\r\n\r\n        isInViewport: function(element) {        \r\n            var rect = element.getBoundingClientRect();\r\n\r\n            return (\r\n                rect.top >= 0 &&\r\n                rect.left >= 0 &&\r\n                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\r\n                rect.right <= (window.innerWidth || document.documentElement.clientWidth)\r\n            );\r\n        },\r\n\r\n        onDOMContentLoaded: function(callback) {\r\n            if (document.readyState === 'loading') {\r\n                document.addEventListener('DOMContentLoaded', callback);\r\n            } else {\r\n                callback();\r\n            }\r\n        },\r\n\r\n        inIframe: function() {\r\n            try {\r\n                return window.self !== window.top;\r\n            } catch (e) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTUtil;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTApp = function() {\r\n    var initPageLoader =  function() {\r\n        // CSS3 Transitions only after page load(.page-loading class added to body tag and remove with JS on page load)\r\n        KTUtil.removeClass(document.body, 'page-loading');\r\n    }\r\n\r\n    var initBootstrapTooltip = function(el, options) {\r\n        var delay = {};\r\n\r\n        // Handle delay options\r\n        if (el.hasAttribute('data-bs-delay-hide')) {\r\n            delay['hide'] = el.getAttribute('data-bs-delay-hide');\r\n        }\r\n\r\n        if (el.hasAttribute('data-bs-delay-show')) {\r\n            delay['show'] = el.getAttribute('data-bs-delay-show');\r\n        }\r\n\r\n        if (delay) {\r\n            options['delay'] = delay;\r\n        }\r\n\r\n        // Check dismiss options\r\n        if (el.hasAttribute('data-bs-dismiss') && el.getAttribute('data-bs-dismiss') == 'click') {\r\n            options['dismiss'] = 'click';\r\n        }            \r\n\r\n        // Initialize popover\r\n        var tp = new bootstrap.Tooltip(el, options);\r\n\r\n        // Handle dismiss\r\n        if (options['dismiss'] && options['dismiss'] === 'click') {\r\n            // Hide popover on element click\r\n            el.addEventListener(\"click\", function(e) {\r\n                tp.hide();\r\n            });\r\n        }\r\n\r\n        return tp;\r\n    }\r\n\r\n    var initBootstrapTooltips = function(el, options) {\r\n        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));\r\n\r\n        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {\r\n            initBootstrapTooltip(tooltipTriggerEl, {});\r\n        });\r\n    }\r\n\r\n    var initBootstrapPopover = function(el, options) {\r\n        var delay = {};\r\n\r\n        // Handle delay options\r\n        if (el.hasAttribute('data-bs-delay-hide')) {\r\n            delay['hide'] = el.getAttribute('data-bs-delay-hide');\r\n        }\r\n\r\n        if (el.hasAttribute('data-bs-delay-show')) {\r\n            delay['show'] = el.getAttribute('data-bs-delay-show');\r\n        }\r\n\r\n        if (delay) {\r\n            options['delay'] = delay;\r\n        }\r\n\r\n        // Handle dismiss option\r\n        if (el.getAttribute('data-bs-dismiss') == 'true') {\r\n            options['dismiss'] = true;\r\n        }\r\n\r\n        if (options['dismiss'] === true) {\r\n            options['template'] = '<div class=\"popover\" role=\"tooltip\"><div class=\"popover-arrow\"></div><span class=\"popover-dismiss btn btn-icon\"><i class=\"bi bi-x fs-2\"></i></span><h3 class=\"popover-header\"></h3><div class=\"popover-body\"></div></div>'\r\n        }\r\n\r\n        // Initialize popover\r\n        var popover = new bootstrap.Popover(el, options);\r\n\r\n        // Handle dismiss click\r\n        if (options['dismiss'] === true) {\r\n            var dismissHandler = function (e) {\r\n                popover.hide();\r\n            }\r\n\r\n            el.addEventListener('shown.bs.popover', function() {\r\n                var dismissEl = document.getElementById(el.getAttribute('aria-describedby'));\r\n                dismissEl.addEventListener('click', dismissHandler);\r\n            });\r\n\r\n            el.addEventListener('hide.bs.popover', function() {\r\n                var dismissEl = document.getElementById(el.getAttribute('aria-describedby'));\r\n                dismissEl.removeEventListener('click', dismissHandler);\r\n            });\r\n        }\r\n\r\n        return popover;\r\n    }\r\n\r\n    var initBootstrapPopovers = function() {\r\n        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\r\n\r\n        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {\r\n            initBootstrapPopover(popoverTriggerEl, {});\r\n        });\r\n    }\r\n\r\n    var initScrollSpy = function() {\r\n        var elements = [].slice.call(document.querySelectorAll('[data-bs-spy=\"scroll\"]'));\r\n\r\n        elements.map(function (element) {\r\n            var sel = element.getAttribute('data-bs-target');            \r\n            var scrollContent = document.querySelector(element.getAttribute('data-bs-target'));\r\n            var scrollSpy = bootstrap.ScrollSpy.getInstance(scrollContent);\r\n            if (scrollSpy) {\r\n                scrollSpy.refresh();\r\n            }\r\n        });\r\n    }\r\n\r\n    var initButtons = function() {\r\n        var buttonsGroup = [].slice.call(document.querySelectorAll('[data-kt-buttons=\"true\"]'));\r\n\r\n        buttonsGroup.map(function (group) {\r\n            var selector = group.hasAttribute('data-kt-buttons-target') ? group.getAttribute('data-kt-buttons-target') : '.btn';\r\n\r\n            // Toggle Handler\r\n            KTUtil.on(group, selector, 'click', function(e) {\r\n                var buttons = [].slice.call(group.querySelectorAll(selector + '.active'));\r\n\r\n                buttons.map(function (button) {\r\n                    button.classList.remove('active');\r\n                });\r\n\r\n                this.classList.add('active');\r\n            });\r\n        });\r\n    }   \r\n\r\n    var initCheck = function() {\r\n        // Toggle Handler\r\n        KTUtil.on(document.body,  '[data-kt-check=\"true\"]', 'change', function(e) {\r\n            var check = this;\r\n            var targets = document.querySelectorAll(check.getAttribute('data-kt-check-target'));\r\n\r\n            KTUtil.each(targets, function (target) {\r\n                if (target.type == 'checkbox') {\r\n                    target.checked = check.checked;\r\n                } else {\r\n                    target.classList.toggle('active');\r\n                }                \r\n            });\r\n        });\r\n    }\r\n\r\n    var initSelect2 = function() {\r\n        var elements = [].slice.call(document.querySelectorAll('[data-control=\"select2\"], [data-kt-select2=\"true\"]'));\r\n       \r\n        elements.map(function (element) {\r\n            var options = {\r\n                dir: document.body.getAttribute('direction')\r\n            };\r\n\r\n            if ( element.getAttribute('data-hide-search') == 'true') {\r\n                options.minimumResultsForSearch = Infinity;\r\n            }\r\n            \r\n            $(element).select2(options);\r\n        });\r\n    }\r\n\r\n    var initAutosize = function() {\r\n        var inputs = [].slice.call(document.querySelectorAll('[data-kt-autosize=\"true\"]'));\r\n       \r\n        inputs.map(function (input) {\r\n            autosize(input);\r\n        });\r\n    }\r\n\r\n    var initCountUp = function() {\r\n        var elements = [].slice.call(document.querySelectorAll('[data-kt-countup=\"true\"]:not(.counted)'));\r\n\r\n        elements.map(function (element) {\r\n            if (KTUtil.isInViewport(element) && KTUtil.visible(element) ) {\r\n                var options = {};\r\n\r\n                var value = element.getAttribute('data-kt-countup-value');\r\n                value = parseFloat(value.replace(/,/,''));\r\n\r\n                if (element.hasAttribute('data-kt-countup-start-val')) {\r\n                    options.startVal = parseFloat(element.getAttribute('data-kt-countup-start-val'));\r\n                }\r\n\r\n                if (element.hasAttribute('data-kt-countup-duration')) {\r\n                    options.duration = parseInt(element.getAttribute('data-kt-countup-duration'));\r\n                }\r\n\r\n                if (element.hasAttribute('data-kt-countup-decimal-places')) {\r\n                    options.decimalPlaces = parseInt(element.getAttribute('data-kt-countup-decimal-places'));\r\n                }\r\n\r\n                if (element.hasAttribute('data-kt-countup-prefix')) {\r\n                    options.prefix = element.getAttribute('data-kt-countup-prefix');\r\n                }\r\n\r\n                if (element.hasAttribute('data-kt-countup-suffix')) {\r\n                    options.suffix = element.getAttribute('data-kt-countup-suffix');\r\n                }\r\n\r\n                var count = new countUp.CountUp(element, value, options);\r\n\r\n                count.start();\r\n                \r\n                element.classList.add('counted');\r\n            }                \r\n        });\r\n    }\r\n\r\n    var initCountUpTabs = function() {\r\n        // Initial call\r\n        initCountUp();\r\n\r\n        // Window scroll event handler\r\n        window.addEventListener('scroll', initCountUp);\r\n\r\n        // Tabs shown event handler\r\n        var tabs = [].slice.call(document.querySelectorAll('[data-kt-countup-tabs=\"true\"][data-bs-toggle=\"tab\"]'));\r\n        tabs.map(function (tab) {\r\n            tab.addEventListener('shown.bs.tab', initCountUp);\r\n        });        \r\n    }\r\n\r\n    var initTinySliders = function() {\r\n        // Init Slider\r\n        var initSlider = function(el) {\r\n            if (!el) {\r\n                return;\r\n            }\r\n\r\n            const tnsOptions = {};\r\n\r\n            // Convert string boolean\r\n            const checkBool = function(val) {\r\n                if (val === 'true') {\r\n                    return true;\r\n                }\r\n                if (val === 'false') {\r\n                    return false;\r\n                }\r\n                return val;\r\n            };\r\n\r\n            // get extra options via data attributes\r\n            el.getAttributeNames().forEach(function(attrName) {\r\n                // more options; https://github.com/ganlanyuan/tiny-slider#options\r\n                if ((/^data-tns-.*/g).test(attrName)) {\r\n                    let optionName = attrName.replace('data-tns-', '').toLowerCase().replace(/(?:[\\s-])\\w/g, function(match) {\r\n                        return match.replace('-', '').toUpperCase();\r\n                    });\r\n                    \r\n                    if (attrName === 'data-tns-responsive') {\r\n                        // fix string with a valid json\r\n                        const jsonStr = el.getAttribute(attrName).replace(/(\\w+:)|(\\w+ :)/g, function(matched) {\r\n                            return '\"' + matched.substring(0, matched.length - 1) + '\":';\r\n                        });\r\n                        try {\r\n                            // convert json string to object\r\n                            tnsOptions[optionName] = JSON.parse(jsonStr);\r\n                        }\r\n                        catch (e) {\r\n                        }\r\n                    }\r\n                    else {\r\n                        tnsOptions[optionName] = checkBool(el.getAttribute(attrName));\r\n                    }\r\n                }\r\n            });\r\n\r\n            const opt = Object.assign({}, {\r\n                container: el,\r\n                slideBy: 'page',\r\n                autoplay: true,\r\n                autoplayButtonOutput: false,\r\n            }, tnsOptions);\r\n\r\n            if (el.closest('.tns')) {\r\n                KTUtil.addClass(el.closest('.tns'), 'tns-initiazlied');\r\n            }\r\n\r\n            return tns(opt);\r\n        }\r\n\r\n        // Sliders\r\n        const elements = Array.prototype.slice.call(document.querySelectorAll('[data-tns=\"true\"]'), 0);\r\n\r\n        if (!elements && elements.length === 0) {\r\n            return;\r\n        }\r\n\r\n        elements.forEach(function(el) {\r\n            initSlider(el);\r\n        });\r\n    }\r\n\r\n    var initSmoothScroll = function() {\r\n        if (SmoothScroll) {\r\n            new SmoothScroll('a[data-kt-scroll-toggle][href*=\"#\"]', {\r\n                offset: function (anchor, toggle) {\r\n                    // Integer or Function returning an integer. How far to offset the scrolling anchor location in pixels\r\n                    // This example is a function, but you could do something as simple as `offset: 25`\r\n\r\n                    // An example returning different values based on whether the clicked link was in the header nav or not\r\n                    if (anchor.hasAttribute('data-kt-scroll-offset')) {\r\n                        var val = KTUtil.getResponsiveValue(anchor.getAttribute('data-kt-scroll-offset'));\r\n\r\n                        return val;\r\n                    } else {\r\n                        return 0;\r\n                    }\r\n                }\r\n            });\r\n        }        \r\n    }\r\n\r\n    return {\r\n        init: function() {\r\n            this.initPageLoader();\r\n\r\n            this.initBootstrapTooltips();\r\n            \r\n            this.initBootstrapPopovers();\r\n            \r\n            this.initScrollSpy();\r\n            \r\n            this.initButtons();\r\n            \r\n            this.initCheck();\r\n            \r\n            this.initSelect2();\r\n            \r\n            this.initCountUp();\r\n\r\n            this.initCountUpTabs();\r\n\r\n            this.initAutosize();\r\n\r\n            this.initTinySliders();\r\n\r\n            this.initSmoothScroll();\r\n        },\r\n\r\n        initPageLoader: function() {\r\n            initPageLoader();\r\n        },\r\n\r\n        initBootstrapTooltip: function(el, options) {\r\n            return initBootstrapTooltip(el, options);\r\n        },\r\n\r\n        initBootstrapTooltips: function() {\r\n            initBootstrapTooltips();\r\n        },\r\n\r\n        initBootstrapPopovers: function() {\r\n            initBootstrapPopovers();\r\n        },\r\n\r\n        initBootstrapPopover: function(el, options) {\r\n            return initBootstrapPopover(el, options);\r\n        },\r\n\r\n        initScrollSpy: function() {\r\n            initScrollSpy();\r\n        },\r\n\r\n        initButtons: function() {\r\n            initButtons();\r\n        },\r\n\r\n        initCheck: function() {\r\n            initCheck();\r\n        },\r\n\r\n        initSelect2: function() {\r\n            initSelect2();\r\n        },\r\n\r\n        initCountUp: function() {\r\n            initCountUp();\r\n        },\r\n\r\n        initCountUpTabs: function() {\r\n            initCountUpTabs();\r\n        },\r\n\r\n        initAutosize: function() {\r\n            initAutosize();\r\n        },\r\n\r\n        initTinySliders: function() {\r\n            initTinySliders();\r\n        },\r\n\r\n        initSmoothScroll: function() {\r\n            initSmoothScroll();\r\n        },\r\n\r\n        isDarkMode: function() {\r\n            return document.body.classList.contains('dark-mode');\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTApp.init();\r\n});\r\n\r\n// On window load\r\nwindow.addEventListener(\"load\", function() {\r\n\tKTApp.initPageLoader();\r\n});\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTApp;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTLayoutAside = function () {\r\n    // Private variables\r\n    var toggle;\r\n    var aside;\r\n\r\n    // Private functions\r\n    var handleToggle = function () {\r\n       var toggleObj = KTToggle.getInstance(toggle);\r\n\r\n       // Add a class to prevent aside hover effect after toggle click\r\n       toggleObj.on('kt.toggle.change', function() {\r\n           aside.classList.add('animating');\r\n\r\n           setTimeout(function() {\r\n                aside.classList.remove('animating');\r\n           }, 300);\r\n       })\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            // Elements\r\n            aside = document.querySelector('#kt_aside');\r\n            toggle = document.querySelector('#kt_aside_toggle');\r\n\r\n            if (!aside || !toggle) {\r\n                return;\r\n            }\r\n\r\n            handleToggle();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTLayoutAside.init();\r\n});", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTLayoutExplore = function() {\r\n    // Private variables    \r\n    var explore;\r\n\r\n    // Private functions\r\n\r\n    // Public methods\r\n\treturn {\r\n\t\tinit: function() {\r\n            // Elements\r\n            explore = document.querySelector('#kt_explore');\r\n\r\n            if (!explore) {\r\n                return;\r\n            }      \r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTLayoutExplore.init();\r\n});", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTLayoutSearch = function() {\r\n    // Private variables\r\n    var element;\r\n    var formElement;\r\n    var mainElement;\r\n    var resultsElement;\r\n    var wrapperElement;\r\n    var emptyElement;\r\n\r\n    var preferencesElement;\r\n    var preferencesShowElement;\r\n    var preferencesDismissElement;\r\n    \r\n    var advancedOptionsFormElement;\r\n    var advancedOptionsFormShowElement;\r\n    var advancedOptionsFormCancelElement;\r\n    var advancedOptionsFormSearchElement;\r\n    \r\n    var searchObject;\r\n\r\n    // Private functions\r\n    var processs = function(search) {\r\n        var timeout = setTimeout(function() {\r\n            var number = KTUtil.getRandomInt(1, 3);\r\n\r\n            // Hide recently viewed\r\n            mainElement.classList.add('d-none');\r\n\r\n            if (number === 3) {\r\n                // Hide results\r\n                resultsElement.classList.add('d-none');\r\n                // Show empty message \r\n                emptyElement.classList.remove('d-none');\r\n            } else {\r\n                // Show results\r\n                resultsElement.classList.remove('d-none');\r\n                // Hide empty message \r\n                emptyElement.classList.add('d-none');\r\n            }                  \r\n\r\n            // Complete search\r\n            search.complete();\r\n        }, 1500);\r\n    }\r\n\r\n    var clear = function(search) {\r\n        // Show recently viewed\r\n        mainElement.classList.remove('d-none');\r\n        // Hide results\r\n        resultsElement.classList.add('d-none');\r\n        // Hide empty message \r\n        emptyElement.classList.add('d-none');\r\n    }    \r\n\r\n    var handlePreferences = function() {\r\n        // Preference show handler\r\n        preferencesShowElement.addEventListener('click', function() {\r\n            wrapperElement.classList.add('d-none');\r\n            preferencesElement.classList.remove('d-none');\r\n        });\r\n\r\n        // Preference dismiss handler\r\n        preferencesDismissElement.addEventListener('click', function() {\r\n            wrapperElement.classList.remove('d-none');\r\n            preferencesElement.classList.add('d-none');\r\n        });\r\n    }\r\n\r\n    var handleAdvancedOptionsForm = function() {\r\n        // Show\r\n        advancedOptionsFormShowElement.addEventListener('click', function() {\r\n            wrapperElement.classList.add('d-none');\r\n            advancedOptionsFormElement.classList.remove('d-none');\r\n        });\r\n\r\n        // Cancel\r\n        advancedOptionsFormCancelElement.addEventListener('click', function() {\r\n            wrapperElement.classList.remove('d-none');\r\n            advancedOptionsFormElement.classList.add('d-none');\r\n        });\r\n\r\n        // Search\r\n        advancedOptionsFormSearchElement.addEventListener('click', function() {\r\n            \r\n        });\r\n    }\r\n\r\n    // Public methods\r\n\treturn {\r\n\t\tinit: function() {\r\n            // Elements\r\n            element = document.querySelector('#kt_header_search');\r\n\r\n            if (!element) {\r\n                return;\r\n            }\r\n\r\n            wrapperElement = element.querySelector('[data-kt-search-element=\"wrapper\"]');\r\n            formElement = element.querySelector('[data-kt-search-element=\"form\"]');\r\n            mainElement = element.querySelector('[data-kt-search-element=\"main\"]');\r\n            resultsElement = element.querySelector('[data-kt-search-element=\"results\"]');\r\n            emptyElement = element.querySelector('[data-kt-search-element=\"empty\"]');\r\n\r\n            preferencesElement = element.querySelector('[data-kt-search-element=\"preferences\"]');\r\n            preferencesShowElement = element.querySelector('[data-kt-search-element=\"preferences-show\"]');\r\n            preferencesDismissElement = element.querySelector('[data-kt-search-element=\"preferences-dismiss\"]');\r\n\r\n            advancedOptionsFormElement = element.querySelector('[data-kt-search-element=\"advanced-options-form\"]');\r\n            advancedOptionsFormShowElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-show\"]');\r\n            advancedOptionsFormCancelElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-cancel\"]');\r\n            advancedOptionsFormSearchElement = element.querySelector('[data-kt-search-element=\"advanced-options-form-search\"]');\r\n            \r\n            // Initialize search handler\r\n            searchObject = new KTSearch(element);\r\n\r\n            // Search handler\r\n            searchObject.on('kt.search.process', processs);\r\n\r\n            // Clear handler\r\n            searchObject.on('kt.search.clear', clear);\r\n\r\n            // Custom handlers\r\n            handlePreferences();\r\n            handleAdvancedOptionsForm();            \r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTLayoutSearch.init();\r\n});", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTLayoutToolbar = function () {\r\n    // Private variables\r\n    var toolbar;\r\n\r\n    // Private functions\r\n    var initForm = function () {\r\n        var rangeSlider = document.querySelector(\"#kt_toolbar_slider\");\r\n        var rangeSliderValueElement = document.querySelector(\"#kt_toolbar_slider_value\");\r\n\r\n        if (!rangeSlider) {\r\n            return;\r\n        }\r\n\r\n        noUiSlider.create(rangeSlider, {\r\n            start: [5],\r\n            connect: [true, false],\r\n            step: 1,\r\n            format: wNumb({\r\n                decimals: 1\r\n            }),\r\n            range: {\r\n                min: [1],\r\n                max: [10]\r\n            }\r\n        });\r\n\r\n        rangeSlider.noUiSlider.on(\"update\", function (values, handle) {\r\n            rangeSliderValueElement.innerHTML = values[handle];\r\n        });\r\n\r\n        var handle = rangeSlider.querySelector(\".noUi-handle\");\r\n\r\n        handle.setAttribute(\"tabindex\", 0);\r\n\r\n        handle.addEventListener(\"click\", function () {\r\n            this.focus();\r\n        });\r\n\r\n        handle.addEventListener(\"keydown\", function (event) {\r\n            var value = Number(rangeSlider.noUiSlider.get());\r\n\r\n            switch (event.which) {\r\n                case 37:\r\n                    rangeSlider.noUiSlider.set(value - 1);\r\n                    break;\r\n                case 39:\r\n                    rangeSlider.noUiSlider.set(value + 1);\r\n                    break;\r\n            }\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            // Elements\r\n            toolbar = document.querySelector('#kt_toolbar');\r\n\r\n            if (!toolbar) {\r\n                return;\r\n            }\r\n\r\n            initForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTLayoutToolbar.init();\r\n});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// Keenthemes' plugins\r\nwindow.KTUtil = require('@/src/js/components/util.js');\r\nwindow.KTBlockUI = require('@/src/js/components/blockui.js');\r\nwindow.KTCookie = require('@/src/js/components/cookie.js');\r\nwindow.KTDialer = require('@/src/js/components/dialer.js');\r\nwindow.KTDrawer = require('@/src/js/components/drawer.js');\r\nwindow.KTEventHandler = require('@/src/js/components/event-handler.js');\r\nwindow.KTFeedback = require('@/src/js/components/feedback.js');\r\nwindow.KTImageInput = require('@/src/js/components/image-input.js');\r\nwindow.KTMenu = require('@/src/js/components/menu.js');\r\nwindow.KTPasswordMeter = require('@/src/js/components/password-meter.js');\r\nwindow.KTScroll = require('@/src/js/components/scroll.js');\r\nwindow.KTScrolltop = require('@/src/js/components/scrolltop.js');\r\nwindow.KTSearch = require('@/src/js/components/search.js');\r\nwindow.KTStepper = require('@/src/js/components/stepper.js');\r\nwindow.KTSticky = require('@/src/js/components/sticky.js');\r\nwindow.KTSwapper = require('@/src/js/components/swapper.js');\r\nwindow.KTToggle = require('@/src/js/components/toggle.js');\r\n\r\n// Layout base js\r\nwindow.KTApp = require('@/src/js/layout/app.js');\r\nwindow.KTLayoutAside = require('@/src/js/layout/aside.js');\r\nwindow.KTLayoutExplore = require('@/src/js/layout/explore.js');\r\nwindow.KTLayoutSearch = require('@/src/js/layout/search.js');\r\nwindow.KTLayoutToolbar = require('@/src/js/layout/toolbar.js');\r\n"], "sourceRoot": ""}