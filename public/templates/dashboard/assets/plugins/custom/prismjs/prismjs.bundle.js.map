{"version": 3, "sources": ["webpack://keenthemes/../src/js/vendors/plugins/prism.init.js", "webpack://keenthemes/./webpack/plugins/custom/prismjs/prismjs.scss", "webpack://keenthemes/./node_modules/prismjs/components/prism-bash.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-css.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-javascript.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-markup-templating.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-markup.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-php-extras.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-php.js", "webpack://keenthemes/./node_modules/prismjs/components/prism-scss.js", "webpack://keenthemes/./node_modules/prismjs/plugins/normalize-whitespace/prism-normalize-whitespace.js", "webpack://keenthemes/./node_modules/prismjs/prism.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/webpack/runtime/global", "webpack://keenthemes/webpack/runtime/make namespace object", "webpack://keenthemes/./webpack/plugins/custom/prismjs/prismjs.js"], "names": [], "mappings": ";;;;;;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACXD;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,kBAAkB,GAAG,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,0CAA0C,IAAI,cAAc,IAAI,cAAc,EAAE,cAAc,EAAE;AAChG;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB;AACpB;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,4DAA4D;AAC5D;AACA;AACA,IAAI;AACJ;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,oBAAoB,s/CAAs/C;AAC1gD;AACA,GAAG;AACH;AACA,oBAAoB,kGAAkG;AACtH;AACA,GAAG;AACH;AACA;AACA,oBAAoB,+RAA+R;AACnT;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB,mCAAmC;AACvD;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,yCAAyC,IAAI;AAC7C;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uBAAuB;AACvC;AACA;;AAEA;AACA,CAAC;;;;;;;;;;;AClOD;;AAEA;;AAEA;AACA;AACA;AACA,0BAA0B,cAAc,QAAQ,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,0BAA0B,SAAS,YAAY,oBAAoB,oCAAoC;AACvG;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,wBAAwB;AACxB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,CAAC;;;;;;;;;;;AC/DD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,GAAG;AACH;AACA,qDAAqD,+JAA+J;AACpN;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,mFAAmF,EAAE;AACrF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,+HAA+H,IAAI,kDAAkD,EAAE;AACvL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,ufAAuf;AACvf;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,6BAA6B,OAAO,IAAI,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ;AAC1E;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,gCAAgC,EAAE,OAAO,OAAO,IAAI,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI;AAC9E;AACA;AACA;AACA,qBAAqB,GAAG;AACxB;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AClHA;;AAEA;AACA;AACA;AACA,YAAY,OAAO;AACnB,YAAY,cAAc;AAC1B,cAAc;AACd;AACA;AACA;AACA;;AAEA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,2BAA2B;AACzC;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,EAAE;;AAEF,CAAC;;;;;;;;;;;AC3HD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA,sBAAsB,KAAK;AAC3B;AACA,GAAG;AACH,eAAe,KAAK;AACpB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,sDAAsD;AACtD;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO;AACnB;AACA,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8IAA8I,gBAAgB,EAAE;AAChK;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO;AACnB;AACA,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;;AAEA,yDAAyD;AACzD;AACA;AACA;;;;;;;;;;;AC7KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;ACVD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qDAAqD;AACrD;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,gCAAgC,EAAE;AAClC,uBAAuB,SAAS;;AAEhC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA,cAAc,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,SAAS;AAC/D;AACA;AACA;;AAEA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA,GAAG;AACH;AACA,mDAAmD,mCAAmC;AACtF;AACA;AACA;AACA;AACA,mDAAmD;AACnD;AACA;AACA,gCAAgC;AAChC;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,EAAE;;AAEF;AACA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;AAEF;AACA;AACA,EAAE;;AAEF,CAAC;;;;;;;;;;;ACjWD;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,6DAA6D;AAC7D;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,KAAK,GAAG;AAC5E,2CAA2C,MAAM;AACjD;AACA;AACA;AACA,wBAAwB,aAAa,mBAAmB,UAAU,UAAU,KAAK,OAAO,MAAM,KAAK,IAAI;AACvG;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,4BAA4B,UAAU;AACtC;AACA,EAAE;AACF;AACA,gCAAgC,UAAU;AAC1C;AACA,4BAA4B,UAAU;AACtC;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,0BAA0B,UAAU;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,CAAC;;AAED;;;;;;;;;;;AChFA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,kCAAkC,eAAe;AACjD,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA,iCAAiC,4BAA4B,EAAE;;AAE/D;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;;AAEA;AACA;;AAEA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK,KAA6B;AAClC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,qBAAqB;AACtC;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF,CAAC;;;;;;;;;;;;ACzMD;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA,gFAAgF,yBAAyB;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL,uCAAuC,sBAAsB;AAC7D;AACA,IAAI;;AAEJ;AACA;AACA;AACA,cAAc,IAAI;AAClB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA,cAAc,OAAO;AACrB,gBAAgB;AAChB;AACA;AACA;AACA,yCAAyC,oBAAoB;AAC7D;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,cAAc,EAAE;AAChB,cAAc,oBAAoB;AAClC,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oBAAoB,OAAO;AACpD;;AAEA;AACA;AACA;AACA;AACA;;AAEA,wBAAwB,IAAI;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,MAAM,eAAe,IAAI;AAC3C;AACA,OAAO;;AAEP,wBAAwB,IAAI;;AAE5B;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,IAAI;AAC3B;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,cAAc,OAAO;AACrB,cAAc,QAAQ;AACtB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,QAAQ;AACtB,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA,sBAAsB,MAAM;AAC5B;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,MAAM;AAC/E;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB;AACA,cAAc,OAAO;AACrB,cAAc,QAAQ;AACtB,cAAc,oBAAoB;AAClC;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA,8BAA8B,IAAI;AAClC;AACA,eAAe,QAAQ;AACvB;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,IAAI;;AAEJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,aAAa;;AAEb;AACA;AACA,oFAAoF,6BAA6B;AACjH;AACA;AACA;AACA;AACA,aAAa,QAAQ,2BAA2B,8BAA8B;AAC9E,aAAa,kBAAkB,wBAAwB,8BAA8B;AACrF;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,MAAM,6BAA6B;AACnC;AACA;AACA;AACA;AACA,sBAAsB,6BAA6B;AACnD;AACA,aAAa,WAAW;AACxB,aAAa,QAAQ;AACrB,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA,2BAA2B,+BAA+B;AAC1D;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,sBAAsB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,YAAY;AACrC;AACA,aAAa,OAAO;AACpB,aAAa,QAAQ;AACrB;AACA;AACA,aAAa,OAAO;AACpB,eAAe,OAAO;AACtB;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB,aAAa,QAAQ;AACrB;AACA;AACA,eAAe,YAAY;AAC3B;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,mDAAmD,cAAc;AACjE;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,aAAa;AAC3B;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,oBAAoB;AAClC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAA6B,6BAA6B;AAC1D;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;;AAGA;AACA;AACA;AACA,gBAAgB,8CAA8C;;AAE9D;AACA;AACA;AACA,YAAY,OAAO,WAAW;AAC9B,YAAY,qBAAqB,cAAc;AAC/C,YAAY,gBAAgB;AAC5B,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,cAAc;AAC9D;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C,kBAAkB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sBAAsB;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC;AACA,YAAY,6BAA6B;AACzC,YAAY,OAAO;AACnB,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA,uFAAuF;AACvF;;AAEA;AACA;;AAEA;AACA,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB,YAAY,OAAO;AACnB,YAAY,QAAQ;AACpB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,OAAO;AACnB,YAAY,2BAA2B;AACvC,YAAY,IAAI;AAChB,YAAY,+BAA+B;AAC3C,YAAY,OAAO;AACnB,YAAY,eAAe;AAC3B,cAAc;AACd;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,qBAAqB;AACvC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,eAAe,OAAO;AACtB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,eAAe;AAChC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,EAAE;AACjB,eAAe,yBAAyB;AACxC,eAAe,yBAAyB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa,kBAAkB;AAC/B,cAAc;AACd,aAAa,kBAAkB;AAC/B,cAAc;AACd;;AAEA,aAAa,kBAAkB;AAC/B;AACA,aAAa,kBAAkB;AAC/B;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY,cAAc;AAC1B,YAAY,kBAAkB;AAC9B,YAAY,EAAE;AACd,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;;AAEA,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc;AAC1B,YAAY,kBAAkB;AAC9B,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA,iBAAiB,iCAAiC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc;AAC1B,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;;AAEA,CAAC;;AAED,IAAI,KAA6B;AACjC;AACA;;AAEA;AACA,WAAW,qBAAM;AACjB,CAAC,qBAAM;AACP;;AAEA;;AAEA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,cAAc,QAAQ;AACtB;AACA,cAAc,QAAQ;AACtB,cAAc,gBAAgB;AAC9B,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU;AACV,cAAc,QAAQ;AACtB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA,WAAW,oBAAoB;AAC/B,aAAa;AACb;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA,sBAAsB,KAAK;AAC3B;AACA,GAAG;AACH,eAAe,KAAK;AACpB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,sDAAsD;AACtD;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO;AACnB;AACA,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8IAA8I,gBAAgB,EAAE;AAChK;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO;AACnB;AACA,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;;AAEA,yDAAyD;AACzD;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,0BAA0B,cAAc,QAAQ,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,0BAA0B,SAAS,YAAY,oBAAoB,oCAAoC;AACvG;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,wBAAwB;AACxB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,CAAC;;;AAGD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,oBAAoB,IAAI;AACxB;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,GAAG;AACH;AACA,qDAAqD,+JAA+J;AACpN;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,mFAAmF,EAAE;AACrF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,+HAA+H,IAAI,kDAAkD,EAAE;AACvL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,ufAAuf;AACvf;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,6BAA6B,OAAO,IAAI,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ;AAC1E;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,gCAAgC,EAAE,OAAO,OAAO,IAAI,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI;AAC9E;AACA;AACA;AACA,qBAAqB,GAAG;AACxB;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAY,YAAY;AACxB,YAAY,OAAO;AACnB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,EAAE;;AAEF;AACA,uBAAuB,eAAe;AACtC;AACA,iBAAiB;;AAEjB,iDAAiD;;AAEjD;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,MAAM;AACN;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,WAAW;AACxB;AACA;AACA;;AAEA,2BAA2B,2BAA2B;AACtD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC;;;;;;;UCxvDD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,EAAE;WACF;WACA;WACA,CAAC,I;;;;;WCPD;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;;;;;;;ACNA;;AAEA,eAAe,mBAAO,CAAC,yDAAkB;AACzC,mBAAO,CAAC,6FAAoC;AAC5C,mBAAO,CAAC,mHAA+C;AACvD,mBAAO,CAAC,yFAAkC;AAC1C,mBAAO,CAAC,qGAAwC;AAChD,mBAAO,CAAC,yFAAkC;AAC1C,mBAAO,CAAC,uFAAiC;AACzC,mBAAO,CAAC,uFAAiC;AACzC,mBAAO,CAAC,qGAAwC;AAChD,mBAAO,CAAC,6JAAoE;AAC5E,mBAAO,CAAC,uFAAwC;;AAEhD,mBAAO,CAAC,qEAAgB", "file": "plugins/custom/prismjs/prismjs.bundle.js", "sourcesContent": ["\"use strict\";\r\n\r\n//\r\n// Prism Initialization\r\n//\r\n\r\nPrism.plugins.NormalizeWhitespace.setDefaults({\r\n    'remove-trailing': true,\r\n\t'remove-indent': true,\r\n\t'left-trim': true,\r\n\t'right-trim': true\r\n});\r\n", "// extracted by mini-css-extract-plugin\nexport {};", "(function (Prism) {\n\t// $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n\t// + LC_ALL, RANDOM, REPLY, SECONDS.\n\t// + make sure PS1..4 are here as they are not always set,\n\t// - some useless things.\n\tvar envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n\n\tvar commandAfterHeredoc = {\n\t\tpattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n\t\tlookbehind: true,\n\t\talias: 'punctuation', // this looks reasonably well in all themes\n\t\tinside: null // see below\n\t};\n\n\tvar insideString = {\n\t\t'bash': commandAfterHeredoc,\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': [\n\t\t\t// [0]: Arithmetic Environment\n\t\t\t{\n\t\t\t\tpattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t// If there is a $ sign at the beginning highlight $(( and )) as variable\n\t\t\t\t\t'variable': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t},\n\t\t\t\t\t\t/^\\$\\(\\(/\n\t\t\t\t\t],\n\t\t\t\t\t'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n\t\t\t\t\t// Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n\t\t\t\t\t'operator': /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n\t\t\t\t\t// If there is no $ sign at the beginning highlight (( and )) as punctuation\n\t\t\t\t\t'punctuation': /\\(\\(?|\\)\\)?|,|;/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [1]: Command Substitution\n\t\t\t{\n\t\t\t\tpattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'variable': /^\\$\\(|^`|\\)$|`$/\n\t\t\t\t}\n\t\t\t},\n\t\t\t// [2]: Brace expansion\n\t\t\t{\n\t\t\t\tpattern: /\\$\\{[^}]+\\}/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'operator': /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n\t\t\t\t\t'punctuation': /[\\[\\]]/,\n\t\t\t\t\t'environment': {\n\t\t\t\t\t\tpattern: RegExp('(\\\\{)' + envVars),\n\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\talias: 'constant'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t/\\$(?:\\w+|[#?*!@$])/\n\t\t],\n\t\t// Escape sequences from echo and printf's manuals, and escaped quotes.\n\t\t'entity': /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|x[0-9a-fA-F]{1,2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})/\n\t};\n\n\tPrism.languages.bash = {\n\t\t'shebang': {\n\t\t\tpattern: /^#!\\s*\\/.*/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'comment': {\n\t\t\tpattern: /(^|[^\"{\\\\$])#.*/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'function-name': [\n\t\t\t// a) function foo {\n\t\t\t// b) foo() {\n\t\t\t// c) function foo() {\n\t\t\t// but not “foo {”\n\t\t\t{\n\t\t\t\t// a) and c)\n\t\t\t\tpattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'function'\n\t\t\t},\n\t\t\t{\n\t\t\t\t// b)\n\t\t\t\tpattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n\t\t\t\talias: 'function'\n\t\t\t}\n\t\t],\n\t\t// Highlight variable names as variables in for and select beginnings.\n\t\t'for-or-select': {\n\t\t\tpattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t// Highlight variable names as variables in the left-hand part\n\t\t// of assignments (“=” and “+=”).\n\t\t'assign-left': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()\\w+(?=\\+?=)/,\n\t\t\tinside: {\n\t\t\t\t'environment': {\n\t\t\t\t\tpattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'constant'\n\t\t\t\t}\n\t\t\t},\n\t\t\talias: 'variable',\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': [\n\t\t\t// Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t// Here-document with quotes around the tag\n\t\t\t// → No expansion (so no “inside”).\n\t\t\t{\n\t\t\t\tpattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'bash': commandAfterHeredoc\n\t\t\t\t}\n\t\t\t},\n\t\t\t// “Normal” string\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n\t\t\t\tpattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: insideString\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n\t\t\t\tpattern: /(^|[^$\\\\])'[^']*'/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n\t\t\t\tpattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'entity': insideString.entity\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'environment': {\n\t\t\tpattern: RegExp('\\\\$?' + envVars),\n\t\t\talias: 'constant'\n\t\t},\n\t\t'variable': insideString.variable,\n\t\t'function': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|aptitude|apt-cache|apt-get|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'keyword': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:if|then|else|elif|fi|for|while|in|case|esac|function|select|do|done|until)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t// https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n\t\t'builtin': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|break|cd|continue|eval|exec|exit|export|getopts|hash|pwd|readonly|return|shift|test|times|trap|umask|unset|alias|bind|builtin|caller|command|declare|echo|enable|help|let|local|logout|mapfile|printf|read|readarray|source|type|typeset|ulimit|unalias|set|shopt)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true,\n\t\t\t// Alias added to make those easier to distinguish from strings.\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'boolean': {\n\t\t\tpattern: /(^|[\\s;|&]|[<>]\\()(?:true|false)(?=$|[)\\s;|&])/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'file-descriptor': {\n\t\t\tpattern: /\\B&\\d\\b/,\n\t\t\talias: 'important'\n\t\t},\n\t\t'operator': {\n\t\t\t// Lots of redirections here, but not just that.\n\t\t\tpattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n\t\t\tinside: {\n\t\t\t\t'file-descriptor': {\n\t\t\t\t\tpattern: /^\\d/,\n\t\t\t\t\talias: 'important'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'punctuation': /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n\t\t'number': {\n\t\t\tpattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n\t\t\tlookbehind: true\n\t\t}\n\t};\n\n\tcommandAfterHeredoc.inside = Prism.languages.bash;\n\n\t/* Patterns in command substitution. */\n\tvar toBeCopied = [\n\t\t'comment',\n\t\t'function-name',\n\t\t'for-or-select',\n\t\t'assign-left',\n\t\t'string',\n\t\t'environment',\n\t\t'function',\n\t\t'keyword',\n\t\t'builtin',\n\t\t'boolean',\n\t\t'file-descriptor',\n\t\t'operator',\n\t\t'punctuation',\n\t\t'number'\n\t];\n\tvar inside = insideString.variable[1].inside;\n\tfor (var i = 0; i < toBeCopied.length; i++) {\n\t\tinside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n\t}\n\n\tPrism.languages.shell = Prism.languages.bash;\n}(Prism));\n", "(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n", "Prism.languages.javascript = Prism.languages.extend('clike', {\n\t'class-name': [\n\t\tPrism.languages.clike['class-name'],\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:prototype|constructor))/,\n\t\t\tlookbehind: true\n\t\t}\n\t],\n\t'keyword': [\n\t\t{\n\t\t\tpattern: /((?:^|\\})\\s*)catch\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t],\n\t// Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n\t'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n\t'number': /\\b(?:(?:0[xX](?:[\\dA-Fa-f](?:_[\\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\\d(?:_\\d)?)+n|NaN|Infinity)\\b|(?:\\b(?:\\d(?:_\\d)?)+\\.?(?:\\d(?:_\\d)?)*|\\B\\.(?:\\d(?:_\\d)?)+)(?:[Ee][+-]?(?:\\d(?:_\\d)?)+)?/,\n\t'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\n\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|interface|extends|implements|instanceof|new)\\s+)[\\w.\\\\]+/;\n\nPrism.languages.insertBefore('javascript', 'keyword', {\n\t'regex': {\n\t\t// eslint-disable-next-line regexp/no-dupe-characters-character-class\n\t\tpattern: /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'regex-source': {\n\t\t\t\tpattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'language-regex',\n\t\t\t\tinside: Prism.languages.regex\n\t\t\t},\n\t\t\t'regex-delimiter': /^\\/|\\/$/,\n\t\t\t'regex-flags': /^[a-z]+$/,\n\t\t}\n\t},\n\t// This must be declared before keyword because we use \"function\" inside the look-forward\n\t'function-variable': {\n\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n\t\talias: 'function'\n\t},\n\t'parameter': [\n\t\t{\n\t\t\tpattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t}\n\t],\n\t'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\n\nPrism.languages.insertBefore('javascript', 'string', {\n\t'hashbang': {\n\t\tpattern: /^#!.*/,\n\t\tgreedy: true,\n\t\talias: 'comment'\n\t},\n\t'template-string': {\n\t\tpattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'template-punctuation': {\n\t\t\t\tpattern: /^`|`$/,\n\t\t\t\talias: 'string'\n\t\t\t},\n\t\t\t'interpolation': {\n\t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\t\tpattern: /^\\$\\{|\\}$/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: Prism.languages.javascript\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t}\n});\n\nif (Prism.languages.markup) {\n\tPrism.languages.markup.tag.addInlined('script', 'javascript');\n\n\t// add attribute support for all DOM events.\n\t// https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n\tPrism.languages.markup.tag.addAttribute(\n\t\t/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\n\t\t'javascript'\n\t);\n}\n\nPrism.languages.js = Prism.languages.javascript;\n", "(function (Prism) {\n\n\t/**\n\t * Returns the placeholder for the given language id and index.\n\t *\n\t * @param {string} language\n\t * @param {string|number} index\n\t * @returns {string}\n\t */\n\tfunction getPlaceholder(language, index) {\n\t\treturn '___' + language.toUpperCase() + index + '___';\n\t}\n\n\tObject.defineProperties(Prism.languages['markup-templating'] = {}, {\n\t\tbuildPlaceholders: {\n\t\t\t/**\n\t\t\t * Tokenize all inline templating expressions matching `placeholderPattern`.\n\t\t\t *\n\t\t\t * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n\t\t\t * `true` will be replaced.\n\t\t\t *\n\t\t\t * @param {object} env The environment of the `before-tokenize` hook.\n\t\t\t * @param {string} language The language id.\n\t\t\t * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n\t\t\t * @param {(match: string) => boolean} [replaceFilter]\n\t\t\t */\n\t\t\tvalue: function (env, language, placeholderPattern, replaceFilter) {\n\t\t\t\tif (env.language !== language) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar tokenStack = env.tokenStack = [];\n\n\t\t\t\tenv.code = env.code.replace(placeholderPattern, function (match) {\n\t\t\t\t\tif (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n\t\t\t\t\t\treturn match;\n\t\t\t\t\t}\n\t\t\t\t\tvar i = tokenStack.length;\n\t\t\t\t\tvar placeholder;\n\n\t\t\t\t\t// Check for existing strings\n\t\t\t\t\twhile (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n\t\t\t\t\t\t++i;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Create a sparse array\n\t\t\t\t\ttokenStack[i] = match;\n\n\t\t\t\t\treturn placeholder;\n\t\t\t\t});\n\n\t\t\t\t// Switch the grammar to markup\n\t\t\t\tenv.grammar = Prism.languages.markup;\n\t\t\t}\n\t\t},\n\t\ttokenizePlaceholders: {\n\t\t\t/**\n\t\t\t * Replace placeholders with proper tokens after tokenizing.\n\t\t\t *\n\t\t\t * @param {object} env The environment of the `after-tokenize` hook.\n\t\t\t * @param {string} language The language id.\n\t\t\t */\n\t\t\tvalue: function (env, language) {\n\t\t\t\tif (env.language !== language || !env.tokenStack) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Switch the grammar back\n\t\t\t\tenv.grammar = Prism.languages[language];\n\n\t\t\t\tvar j = 0;\n\t\t\t\tvar keys = Object.keys(env.tokenStack);\n\n\t\t\t\tfunction walkTokens(tokens) {\n\t\t\t\t\tfor (var i = 0; i < tokens.length; i++) {\n\t\t\t\t\t\t// all placeholders are replaced already\n\t\t\t\t\t\tif (j >= keys.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar token = tokens[i];\n\t\t\t\t\t\tif (typeof token === 'string' || (token.content && typeof token.content === 'string')) {\n\t\t\t\t\t\t\tvar k = keys[j];\n\t\t\t\t\t\t\tvar t = env.tokenStack[k];\n\t\t\t\t\t\t\tvar s = typeof token === 'string' ? token : token.content;\n\t\t\t\t\t\t\tvar placeholder = getPlaceholder(language, k);\n\n\t\t\t\t\t\t\tvar index = s.indexOf(placeholder);\n\t\t\t\t\t\t\tif (index > -1) {\n\t\t\t\t\t\t\t\t++j;\n\n\t\t\t\t\t\t\t\tvar before = s.substring(0, index);\n\t\t\t\t\t\t\t\tvar middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n\t\t\t\t\t\t\t\tvar after = s.substring(index + placeholder.length);\n\n\t\t\t\t\t\t\t\tvar replacement = [];\n\t\t\t\t\t\t\t\tif (before) {\n\t\t\t\t\t\t\t\t\treplacement.push.apply(replacement, walkTokens([before]));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treplacement.push(middle);\n\t\t\t\t\t\t\t\tif (after) {\n\t\t\t\t\t\t\t\t\treplacement.push.apply(replacement, walkTokens([after]));\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tif (typeof token === 'string') {\n\t\t\t\t\t\t\t\t\ttokens.splice.apply(tokens, [i, 1].concat(replacement));\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\ttoken.content = replacement;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (token.content /* && typeof token.content !== 'string' */) {\n\t\t\t\t\t\t\twalkTokens(token.content);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn tokens;\n\t\t\t\t}\n\n\t\t\t\twalkTokens(env.tokens);\n\t\t\t}\n\t\t}\n\t});\n\n}(Prism));\n", "Prism.languages.markup = {\n\t'comment': /<!--[\\s\\S]*?-->/,\n\t'prolog': /<\\?[\\s\\S]+?\\?>/,\n\t'doctype': {\n\t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n\t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'internal-subset': {\n\t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: null // see below\n\t\t\t},\n\t\t\t'string': {\n\t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t'punctuation': /^<!|>$|[[\\]]/,\n\t\t\t'doctype-tag': /^DOCTYPE/,\n\t\t\t'name': /[^\\s<>'\"]+/\n\t\t}\n\t},\n\t'cdata': /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t'tag': {\n\t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'tag': {\n\t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /^<\\/?/,\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t},\n\t\t\t'special-attr': [],\n\t\t\t'attr-value': {\n\t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\/?>/,\n\t\t\t'attr-name': {\n\t\t\t\tpattern: /[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t},\n\t'entity': [\n\t\t{\n\t\t\tpattern: /&[\\da-z]{1,8};/i,\n\t\t\talias: 'named-entity'\n\t\t},\n\t\t/&#x?[\\da-f]{1,8};/i\n\t]\n};\n\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n\tPrism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n\n\tif (env.type === 'entity') {\n\t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n\t}\n});\n\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n\t/**\n\t * Adds an inlined language to markup.\n\t *\n\t * An example of an inlined language is CSS with `<style>` tags.\n\t *\n\t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addInlined('style', 'css');\n\t */\n\tvalue: function addInlined(tagName, lang) {\n\t\tvar includedCdataInside = {};\n\t\tincludedCdataInside['language-' + lang] = {\n\t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n\t\tvar inside = {\n\t\t\t'included-cdata': {\n\t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\t\t\tinside: includedCdataInside\n\t\t\t}\n\t\t};\n\t\tinside['language-' + lang] = {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\n\t\tvar def = {};\n\t\tdef[tagName] = {\n\t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: inside\n\t\t};\n\n\t\tPrism.languages.insertBefore('markup', 'cdata', def);\n\t}\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n\t/**\n\t * Adds an pattern to highlight languages embedded in HTML attributes.\n\t *\n\t * An example of an inlined language is CSS with `style` attributes.\n\t *\n\t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addAttribute('style', 'css');\n\t */\n\tvalue: function (attrName, lang) {\n\t\tPrism.languages.markup.tag.inside['special-attr'].push({\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n\t\t\t\t'i'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'attr-name': /^[^\\s=]+/,\n\t\t\t\t'attr-value': {\n\t\t\t\t\tpattern: /=[\\s\\S]+/,\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'value': {\n\t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n\t\t\t\t\t\t\tinside: Prism.languages[lang]\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n});\n\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\n\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n", "Prism.languages.insertBefore('php', 'variable', {\n\t'this': /\\$this\\b/,\n\t'global': /\\$(?:_(?:SERVER|GET|POST|FILES|REQUEST|SESSION|ENV|COOKIE)|GLOBALS|HTTP_RAW_POST_DATA|argc|argv|php_errormsg|http_response_header)\\b/,\n\t'scope': {\n\t\tpattern: /\\b[\\w\\\\]+::/,\n\t\tinside: {\n\t\t\tkeyword: /static|self|parent/,\n\t\t\tpunctuation: /::|\\\\/\n\t\t}\n\t}\n});\n", "/**\n * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n * Modified by <PERSON>: http://milesj.me\n * Rewritten by <PERSON>\n *\n * Supports PHP 5.3 - 8.0\n */\n(function (Prism) {\n\tvar comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/;\n\tvar constant = [\n\t\t{\n\t\t\tpattern: /\\b(?:false|true)\\b/i,\n\t\t\talias: 'boolean'\n\t\t},\n\t\t{\n\t\t\tpattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n\t\t\tgreedy: true,\n\t\t\tlookbehind: true,\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n\t\t\tgreedy: true,\n\t\t\tlookbehind: true,\n\t\t},\n\t\t/\\b(?:null)\\b/i,\n\t\t/\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/,\n\t];\n\tvar number = /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i;\n\tvar operator = /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;\n\tvar punctuation = /[{}\\[\\](),:;]/;\n\n\tPrism.languages.php = {\n\t\t'delimiter': {\n\t\t\tpattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n\t\t\talias: 'important'\n\t\t},\n\t\t'comment': comment,\n\t\t'variable': /\\$+(?:\\w+\\b|(?=\\{))/i,\n\t\t'package': {\n\t\t\tpattern: /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\\\/\n\t\t\t}\n\t\t},\n\t\t'class-name-definition': {\n\t\t\tpattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\tlookbehind: true,\n\t\t\talias: 'class-name'\n\t\t},\n\t\t'function-definition': {\n\t\t\tpattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n\t\t\tlookbehind: true,\n\t\t\talias: 'function'\n\t\t},\n\t\t'keyword': [\n\t\t\t{\n\t\t\t\tpattern: /(\\(\\s*)\\b(?:bool|boolean|int|integer|float|string|object|array)\\b(?=\\s*\\))/i,\n\t\t\t\talias: 'type-casting',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)\\b(?:bool|int|float|string|object|array(?!\\s*\\()|mixed|self|static|callable|iterable|(?:null|false)(?=\\s*\\|))\\b(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-hint',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*[\\w|]\\|\\s*)(?:null|false)\\b(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-hint',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:bool|int|float|string|object|void|array(?!\\s*\\()|mixed|self|static|callable|iterable|(?:null|false)(?=\\s*\\|))\\b/i,\n\t\t\t\talias: 'return-type',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?[\\w|]\\|\\s*)(?:null|false)\\b/i,\n\t\t\t\talias: 'return-type',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b(?:bool|int|float|string|object|void|array(?!\\s*\\()|mixed|iterable|(?:null|false)(?=\\s*\\|))\\b/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)(?:null|false)\\b/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n\t\t\t\talias: 'static-context',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\t// yield from\n\t\t\t\tpattern: /(\\byield\\s+)from\\b/i,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t// `class` is always a keyword unlike other keywords\n\t\t\t/\\bclass\\b/i,\n\t\t\t{\n\t\t\t\t// https://www.php.net/manual/en/reserved.keywords.php\n\t\t\t\t//\n\t\t\t\t// keywords cannot be preceded by \"->\"\n\t\t\t\t// the complex lookbehind means `(?<!(?:->|::)\\s*)`\n\t\t\t\tpattern: /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:__halt_compiler|abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|namespace|match|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield)\\b/i,\n\t\t\t\tlookbehind: true\n\t\t\t}\n\t\t],\n\t\t'argument-name': {\n\t\t\tpattern: /([(,]\\s+)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'class-name': [\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\t\talias: 'class-name-fully-qualified',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-declaration',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'type-declaration'],\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n\t\t\t\talias: 'static-context',\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'static-context'],\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n\t\t\t\talias: 'type-hint',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'type-hint'],\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\talias: 'return-type',\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true\n\t\t\t},\n\t\t\t{\n\t\t\t\tpattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n\t\t\t\talias: ['class-name-fully-qualified', 'return-type'],\n\t\t\t\tgreedy: true,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\t'constant': constant,\n\t\t'function': {\n\t\t\tpattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'punctuation': /\\\\/\n\t\t\t}\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(->\\s*)\\w+/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'number': number,\n\t\t'operator': operator,\n\t\t'punctuation': punctuation\n\t};\n\n\tvar string_interpolation = {\n\t\tpattern: /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n\t\tlookbehind: true,\n\t\tinside: Prism.languages.php\n\t};\n\n\tvar string = [\n\t\t{\n\t\t\tpattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n\t\t\talias: 'nowdoc-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n\t\t\t\t\talias: 'symbol',\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'punctuation': /^<<<'?|[';]$/\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\tpattern: /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n\t\t\talias: 'heredoc-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n\t\t\t\t\talias: 'symbol',\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'punctuation': /^<<<\"?|[\";]$/\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t'interpolation': string_interpolation\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\tpattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n\t\t\talias: 'backtick-quoted-string',\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n\t\t\talias: 'single-quoted-string',\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n\t\t\talias: 'double-quoted-string',\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'interpolation': string_interpolation\n\t\t\t}\n\t\t}\n\t];\n\n\tPrism.languages.insertBefore('php', 'variable', {\n\t\t'string': string,\n\t\t'attribute': {\n\t\t\tpattern: /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'attribute-content': {\n\t\t\t\t\tpattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t// inside can appear subset of php\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'comment': comment,\n\t\t\t\t\t\t'string': string,\n\t\t\t\t\t\t'attribute-class-name': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n\t\t\t\t\t\t\t\talias: 'class-name',\n\t\t\t\t\t\t\t\tgreedy: true,\n\t\t\t\t\t\t\t\tlookbehind: true\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n\t\t\t\t\t\t\t\talias: [\n\t\t\t\t\t\t\t\t\t'class-name',\n\t\t\t\t\t\t\t\t\t'class-name-fully-qualified'\n\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\tgreedy: true,\n\t\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\t\tinside: {\n\t\t\t\t\t\t\t\t\t'punctuation': /\\\\/\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\t'constant': constant,\n\t\t\t\t\t\t'number': number,\n\t\t\t\t\t\t'operator': operator,\n\t\t\t\t\t\t'punctuation': punctuation\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t'delimiter': {\n\t\t\t\t\tpattern: /^#\\[|\\]$/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t});\n\n\tPrism.hooks.add('before-tokenize', function (env) {\n\t\tif (!/<\\?/.test(env.code)) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar phpPattern = /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/gi;\n\t\tPrism.languages['markup-templating'].buildPlaceholders(env, 'php', phpPattern);\n\t});\n\n\tPrism.hooks.add('after-tokenize', function (env) {\n\t\tPrism.languages['markup-templating'].tokenizePlaceholders(env, 'php');\n\t});\n\n}(Prism));\n", "Prism.languages.scss = Prism.languages.extend('css', {\n\t'comment': {\n\t\tpattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n\t\tlookbehind: true\n\t},\n\t'atrule': {\n\t\tpattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n\t\tinside: {\n\t\t\t'rule': /@[\\w-]+/\n\t\t\t// See rest below\n\t\t}\n\t},\n\t// url, compassified\n\t'url': /(?:[-a-z]+-)?url(?=\\()/i,\n\t// CSS selector regex is not appropriate for Sass\n\t// since there can be lot more things (var, @ directive, nesting..)\n\t// a selector must start at the end of a property or after a brace (end of other rules or nesting)\n\t// it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n\t// the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n\t// can \"pass\" as a selector- e.g: proper#{$erty})\n\t// this one was hard to do, so please be careful if you edit this one :)\n\t'selector': {\n\t\t// Initial look-ahead is used to prevent matching of blank selectors\n\t\tpattern: /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/m,\n\t\tinside: {\n\t\t\t'parent': {\n\t\t\t\tpattern: /&/,\n\t\t\t\talias: 'important'\n\t\t\t},\n\t\t\t'placeholder': /%[-\\w]+/,\n\t\t\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n\t\t}\n\t},\n\t'property': {\n\t\tpattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n\t\tinside: {\n\t\t\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n\t\t}\n\t}\n});\n\nPrism.languages.insertBefore('scss', 'atrule', {\n\t'keyword': [\n\t\t/@(?:if|else(?: if)?|forward|for|each|while|import|use|extend|debug|warn|mixin|include|function|return|content)\\b/i,\n\t\t{\n\t\t\tpattern: /( )(?:from|through)(?= )/,\n\t\t\tlookbehind: true\n\t\t}\n\t]\n});\n\nPrism.languages.insertBefore('scss', 'important', {\n\t// var and interpolated vars\n\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n});\n\nPrism.languages.insertBefore('scss', 'function', {\n\t'module-modifier': {\n\t\tpattern: /\\b(?:as|with|show|hide)\\b/i,\n\t\talias: 'keyword'\n\t},\n\t'placeholder': {\n\t\tpattern: /%[-\\w]+/,\n\t\talias: 'selector'\n\t},\n\t'statement': {\n\t\tpattern: /\\B!(?:default|optional)\\b/i,\n\t\talias: 'keyword'\n\t},\n\t'boolean': /\\b(?:true|false)\\b/,\n\t'null': {\n\t\tpattern: /\\bnull\\b/,\n\t\talias: 'keyword'\n\t},\n\t'operator': {\n\t\tpattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|or|not)(?=\\s)/,\n\t\tlookbehind: true\n\t}\n});\n\nPrism.languages.scss['atrule'].inside.rest = Prism.languages.scss;\n", "(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\tvar assign = Object.assign || function (obj1, obj2) {\n\t\tfor (var name in obj2) {\n\t\t\tif (obj2.hasOwnProperty(name)) {\n\t\t\t\tobj1[name] = obj2[name];\n\t\t\t}\n\t\t}\n\t\treturn obj1;\n\t};\n\n\tfunction NormalizeWhitespace(defaults) {\n\t\tthis.defaults = assign({}, defaults);\n\t}\n\n\tfunction toCamelCase(value) {\n\t\treturn value.replace(/-(\\w)/g, function (match, firstChar) {\n\t\t\treturn firstChar.toUpperCase();\n\t\t});\n\t}\n\n\tfunction tabLen(str) {\n\t\tvar res = 0;\n\t\tfor (var i = 0; i < str.length; ++i) {\n\t\t\tif (str.charCodeAt(i) == '\\t'.charCodeAt(0)) {\n\t\t\t\tres += 3;\n\t\t\t}\n\t\t}\n\t\treturn str.length + res;\n\t}\n\n\tNormalizeWhitespace.prototype = {\n\t\tsetDefaults: function (defaults) {\n\t\t\tthis.defaults = assign(this.defaults, defaults);\n\t\t},\n\t\tnormalize: function (input, settings) {\n\t\t\tsettings = assign(this.defaults, settings);\n\n\t\t\tfor (var name in settings) {\n\t\t\t\tvar methodName = toCamelCase(name);\n\t\t\t\tif (name !== 'normalize' && methodName !== 'setDefaults' &&\n\t\t\t\t\tsettings[name] && this[methodName]) {\n\t\t\t\t\tinput = this[methodName].call(this, input, settings[name]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn input;\n\t\t},\n\n\t\t/*\n\t\t * Normalization methods\n\t\t */\n\t\tleftTrim: function (input) {\n\t\t\treturn input.replace(/^\\s+/, '');\n\t\t},\n\t\trightTrim: function (input) {\n\t\t\treturn input.replace(/\\s+$/, '');\n\t\t},\n\t\ttabsToSpaces: function (input, spaces) {\n\t\t\tspaces = spaces|0 || 4;\n\t\t\treturn input.replace(/\\t/g, new Array(++spaces).join(' '));\n\t\t},\n\t\tspacesToTabs: function (input, spaces) {\n\t\t\tspaces = spaces|0 || 4;\n\t\t\treturn input.replace(RegExp(' {' + spaces + '}', 'g'), '\\t');\n\t\t},\n\t\tremoveTrailing: function (input) {\n\t\t\treturn input.replace(/\\s*?$/gm, '');\n\t\t},\n\t\t// Support for deprecated plugin remove-initial-line-feed\n\t\tremoveInitialLineFeed: function (input) {\n\t\t\treturn input.replace(/^(?:\\r?\\n|\\r)/, '');\n\t\t},\n\t\tremoveIndent: function (input) {\n\t\t\tvar indents = input.match(/^[^\\S\\n\\r]*(?=\\S)/gm);\n\n\t\t\tif (!indents || !indents[0].length) {\n\t\t\t\treturn input;\n\t\t\t}\n\n\t\t\tindents.sort(function (a, b) { return a.length - b.length; });\n\n\t\t\tif (!indents[0].length) {\n\t\t\t\treturn input;\n\t\t\t}\n\n\t\t\treturn input.replace(RegExp('^' + indents[0], 'gm'), '');\n\t\t},\n\t\tindent: function (input, tabs) {\n\t\t\treturn input.replace(/^[^\\S\\n\\r]*(?=\\S)/gm, new Array(++tabs).join('\\t') + '$&');\n\t\t},\n\t\tbreakLines: function (input, characters) {\n\t\t\tcharacters = (characters === true) ? 80 : characters|0 || 80;\n\n\t\t\tvar lines = input.split('\\n');\n\t\t\tfor (var i = 0; i < lines.length; ++i) {\n\t\t\t\tif (tabLen(lines[i]) <= characters) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tvar line = lines[i].split(/(\\s+)/g);\n\t\t\t\tvar len = 0;\n\n\t\t\t\tfor (var j = 0; j < line.length; ++j) {\n\t\t\t\t\tvar tl = tabLen(line[j]);\n\t\t\t\t\tlen += tl;\n\t\t\t\t\tif (len > characters) {\n\t\t\t\t\t\tline[j] = '\\n' + line[j];\n\t\t\t\t\t\tlen = tl;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlines[i] = line.join('');\n\t\t\t}\n\t\t\treturn lines.join('\\n');\n\t\t}\n\t};\n\n\t// Support node modules\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tmodule.exports = NormalizeWhitespace;\n\t}\n\n\t// Exit if prism is not loaded\n\tif (typeof Prism === 'undefined') {\n\t\treturn;\n\t}\n\n\tPrism.plugins.NormalizeWhitespace = new NormalizeWhitespace({\n\t\t'remove-trailing': true,\n\t\t'remove-indent': true,\n\t\t'left-trim': true,\n\t\t'right-trim': true,\n\t\t/*'break-lines': 80,\n\t\t'indent': 2,\n\t\t'remove-initial-line-feed': false,\n\t\t'tabs-to-spaces': 4,\n\t\t'spaces-to-tabs': 4*/\n\t});\n\n\tPrism.hooks.add('before-sanity-check', function (env) {\n\t\tvar Normalizer = Prism.plugins.NormalizeWhitespace;\n\n\t\t// Check settings\n\t\tif (env.settings && env.settings['whitespace-normalization'] === false) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Check classes\n\t\tif (!Prism.util.isActive(env.element, 'whitespace-normalization', true)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Simple mode if there is no env.element\n\t\tif ((!env.element || !env.element.parentNode) && env.code) {\n\t\t\tenv.code = Normalizer.normalize(env.code, env.settings);\n\t\t\treturn;\n\t\t}\n\n\t\t// Normal mode\n\t\tvar pre = env.element.parentNode;\n\t\tif (!env.code || !pre || pre.nodeName.toLowerCase() !== 'pre') {\n\t\t\treturn;\n\t\t}\n\n\t\tvar children = pre.childNodes;\n\t\tvar before = '';\n\t\tvar after = '';\n\t\tvar codeFound = false;\n\n\t\t// Move surrounding whitespace from the <pre> tag into the <code> tag\n\t\tfor (var i = 0; i < children.length; ++i) {\n\t\t\tvar node = children[i];\n\n\t\t\tif (node == env.element) {\n\t\t\t\tcodeFound = true;\n\t\t\t} else if (node.nodeName === '#text') {\n\t\t\t\tif (codeFound) {\n\t\t\t\t\tafter += node.nodeValue;\n\t\t\t\t} else {\n\t\t\t\t\tbefore += node.nodeValue;\n\t\t\t\t}\n\n\t\t\t\tpre.removeChild(node);\n\t\t\t\t--i;\n\t\t\t}\n\t\t}\n\n\t\tif (!env.element.children.length || !Prism.plugins.KeepMarkup) {\n\t\t\tenv.code = before + env.code + after;\n\t\t\tenv.code = Normalizer.normalize(env.code, env.settings);\n\t\t} else {\n\t\t\t// Preserve markup for keep-markup plugin\n\t\t\tvar html = before + env.element.innerHTML + after;\n\t\t\tenv.element.innerHTML = Normalizer.normalize(html, env.settings);\n\t\t\tenv.code = env.element.textContent;\n\t\t}\n\t});\n\n}());\n", "\n/* **********************************************\n     Begin prism-core.js\n********************************************** */\n\n/// <reference lib=\"WebWorker\"/>\n\nvar _self = (typeof window !== 'undefined')\n\t? window   // if in browser\n\t: (\n\t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n\t\t\t? self // if in worker\n\t\t\t: {}   // if in node js\n\t);\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <PERSON> <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function (_self) {\n\n\t// Private helper vars\n\tvar lang = /\\blang(?:uage)?-([\\w-]+)\\b/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n\t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n\t\t * additional languages or plugins yourself.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n\t\t *\n\t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.manual = true;\n\t\t * // add a new <script> to load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tmanual: _self.Prism && _self.Prism.manual,\n\t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element && !lang.test(element.className)) {\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\tif (element) {\n\t\t\t\t\treturn (element.className.match(lang) || [, 'none'])[1].toLowerCase();\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the script element that is currently executing.\n\t\t\t *\n\t\t\t * This does __not__ work for line script element.\n\t\t\t *\n\t\t\t * @returns {HTMLScriptElement | null}\n\t\t\t */\n\t\t\tcurrentScript: function () {\n\t\t\t\tif (typeof document === 'undefined') {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n\t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n\t\t\t\t}\n\n\t\t\t\t// IE11 workaround\n\t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n\t\t\t\t// this will not work for inline scripts\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new Error();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n\t\t\t\t\t// A stack will look like this:\n\t\t\t\t\t//\n\t\t\t\t\t// Error\n\t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n\t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n\t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):.+:.+\\)$/i.exec(err.stack) || [])[1];\n\t\t\t\t\tif (src) {\n\t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n\t\t\t\t\t\tfor (var i in scripts) {\n\t\t\t\t\t\t\tif (scripts[i].src == src) {\n\t\t\t\t\t\t\t\treturn scripts[i];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * This is the most high-level function in Prism’s API.\n\t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n\t\t * each one of them.\n\t\t *\n\t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n\t\t *\n\t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAll: function (async, callback) {\n\t\t\t_.highlightAllUnder(document, async, callback);\n\t\t},\n\n\t\t/**\n\t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n\t\t * {@link Prism.highlightElement} on each one of them.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-highlightall`\n\t\t * 2. `before-all-elements-highlight`\n\t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n\t\t *\n\t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n\t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAllUnder: function (container, async, callback) {\n\t\t\tvar env = {\n\t\t\t\tcallback: callback,\n\t\t\t\tcontainer: container,\n\t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n\t\t\t};\n\n\t\t\t_.hooks.run('before-highlightall', env);\n\n\t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n\t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n\t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n\t\t\t\t_.highlightElement(element, async === true, env.callback);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Highlights the code inside a single element.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-sanity-check`\n\t\t * 2. `before-highlight`\n\t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n\t\t * 4. `before-insert`\n\t\t * 5. `after-highlight`\n\t\t * 6. `complete`\n\t\t *\n\t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n\t\t * the element's language.\n\t\t *\n\t\t * @param {Element} element The element containing the code.\n\t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n\t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n\t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n\t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n\t\t *\n\t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n\t\t * asynchronous highlighting to work. You can build your own bundle on the\n\t\t * [Download page](https://prismjs.com/download.html).\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n\t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightElement: function (element, async, callback) {\n\t\t\t// Find language\n\t\t\tvar language = _.util.getLanguage(element);\n\t\t\tvar grammar = _.languages[language];\n\n\t\t\t// Set language on the element, if not present\n\t\t\telement.className = element.className.replace(lang, '').replace(/\\s+/g, ' ') + ' language-' + language;\n\n\t\t\t// Set language on the parent, for styling\n\t\t\tvar parent = element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n\t\t\t\tparent.className = parent.className.replace(lang, '').replace(/\\s+/g, ' ') + ' language-' + language;\n\t\t\t}\n\n\t\t\tvar code = element.textContent;\n\n\t\t\tvar env = {\n\t\t\t\telement: element,\n\t\t\t\tlanguage: language,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tcode: code\n\t\t\t};\n\n\t\t\tfunction insertHighlightedCode(highlightedCode) {\n\t\t\t\tenv.highlightedCode = highlightedCode;\n\n\t\t\t\t_.hooks.run('before-insert', env);\n\n\t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n\t\t\t\t_.hooks.run('after-highlight', env);\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t}\n\n\t\t\t_.hooks.run('before-sanity-check', env);\n\n\t\t\t// plugins may change/add the parent/element\n\t\t\tparent = env.element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n\t\t\t\tparent.setAttribute('tabindex', '0');\n\t\t\t}\n\n\t\t\tif (!env.code) {\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t_.hooks.run('before-highlight', env);\n\n\t\t\tif (!env.grammar) {\n\t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (async && _self.Worker) {\n\t\t\t\tvar worker = new Worker(_.filename);\n\n\t\t\t\tworker.onmessage = function (evt) {\n\t\t\t\t\tinsertHighlightedCode(evt.data);\n\t\t\t\t};\n\n\t\t\t\tworker.postMessage(JSON.stringify({\n\t\t\t\t\tlanguage: env.language,\n\t\t\t\t\tcode: env.code,\n\t\t\t\t\timmediateClose: true\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\t_self.Prism = _;\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\n\tif (!_self.document) {\n\t\tif (!_self.addEventListener) {\n\t\t\t// in Node.js\n\t\t\treturn _;\n\t\t}\n\n\t\tif (!_.disableWorkerMessageHandler) {\n\t\t\t// In worker\n\t\t\t_self.addEventListener('message', function (evt) {\n\t\t\t\tvar message = JSON.parse(evt.data);\n\t\t\t\tvar lang = message.language;\n\t\t\t\tvar code = message.code;\n\t\t\t\tvar immediateClose = message.immediateClose;\n\n\t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n\t\t\t\tif (immediateClose) {\n\t\t\t\t\t_self.close();\n\t\t\t\t}\n\t\t\t}, false);\n\t\t}\n\n\t\treturn _;\n\t}\n\n\t// Get current script and highlight\n\tvar script = _.util.currentScript();\n\n\tif (script) {\n\t\t_.filename = script.src;\n\n\t\tif (script.hasAttribute('data-manual')) {\n\t\t\t_.manual = true;\n\t\t}\n\t}\n\n\tfunction highlightAutomaticallyCallback() {\n\t\tif (!_.manual) {\n\t\t\t_.highlightAll();\n\t\t}\n\t}\n\n\tif (!_.manual) {\n\t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n\t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n\t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n\t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n\t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n\t\t// See https://github.com/PrismJS/prism/issues/2102\n\t\tvar readyState = document.readyState;\n\t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n\t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n\t\t} else {\n\t\t\tif (window.requestAnimationFrame) {\n\t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n\t\t\t} else {\n\t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn _;\n\n}(_self));\n\nif (typeof module !== 'undefined' && module.exports) {\n\tmodule.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n\tglobal.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n\n\n/* **********************************************\n     Begin prism-markup.js\n********************************************** */\n\nPrism.languages.markup = {\n\t'comment': /<!--[\\s\\S]*?-->/,\n\t'prolog': /<\\?[\\s\\S]+?\\?>/,\n\t'doctype': {\n\t\t// https://www.w3.org/TR/xml/#NT-doctypedecl\n\t\tpattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'internal-subset': {\n\t\t\t\tpattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tgreedy: true,\n\t\t\t\tinside: null // see below\n\t\t\t},\n\t\t\t'string': {\n\t\t\t\tpattern: /\"[^\"]*\"|'[^']*'/,\n\t\t\t\tgreedy: true\n\t\t\t},\n\t\t\t'punctuation': /^<!|>$|[[\\]]/,\n\t\t\t'doctype-tag': /^DOCTYPE/,\n\t\t\t'name': /[^\\s<>'\"]+/\n\t\t}\n\t},\n\t'cdata': /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t'tag': {\n\t\tpattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'tag': {\n\t\t\t\tpattern: /^<\\/?[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': /^<\\/?/,\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t},\n\t\t\t'special-attr': [],\n\t\t\t'attr-value': {\n\t\t\t\tpattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n\t\t\t\tinside: {\n\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t},\n\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t},\n\t\t\t'punctuation': /\\/?>/,\n\t\t\t'attr-name': {\n\t\t\t\tpattern: /[^\\s>\\/]+/,\n\t\t\t\tinside: {\n\t\t\t\t\t'namespace': /^[^\\s>\\/:]+:/\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t},\n\t'entity': [\n\t\t{\n\t\t\tpattern: /&[\\da-z]{1,8};/i,\n\t\t\talias: 'named-entity'\n\t\t},\n\t\t/&#x?[\\da-f]{1,8};/i\n\t]\n};\n\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n\tPrism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n\n\tif (env.type === 'entity') {\n\t\tenv.attributes['title'] = env.content.replace(/&amp;/, '&');\n\t}\n});\n\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n\t/**\n\t * Adds an inlined language to markup.\n\t *\n\t * An example of an inlined language is CSS with `<style>` tags.\n\t *\n\t * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addInlined('style', 'css');\n\t */\n\tvalue: function addInlined(tagName, lang) {\n\t\tvar includedCdataInside = {};\n\t\tincludedCdataInside['language-' + lang] = {\n\t\t\tpattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\t\tincludedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n\n\t\tvar inside = {\n\t\t\t'included-cdata': {\n\t\t\t\tpattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n\t\t\t\tinside: includedCdataInside\n\t\t\t}\n\t\t};\n\t\tinside['language-' + lang] = {\n\t\t\tpattern: /[\\s\\S]+/,\n\t\t\tinside: Prism.languages[lang]\n\t\t};\n\n\t\tvar def = {};\n\t\tdef[tagName] = {\n\t\t\tpattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () { return tagName; }), 'i'),\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: inside\n\t\t};\n\n\t\tPrism.languages.insertBefore('markup', 'cdata', def);\n\t}\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n\t/**\n\t * Adds an pattern to highlight languages embedded in HTML attributes.\n\t *\n\t * An example of an inlined language is CSS with `style` attributes.\n\t *\n\t * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n\t * case insensitive.\n\t * @param {string} lang The language key.\n\t * @example\n\t * addAttribute('style', 'css');\n\t */\n\tvalue: function (attrName, lang) {\n\t\tPrism.languages.markup.tag.inside['special-attr'].push({\n\t\t\tpattern: RegExp(\n\t\t\t\t/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n\t\t\t\t'i'\n\t\t\t),\n\t\t\tlookbehind: true,\n\t\t\tinside: {\n\t\t\t\t'attr-name': /^[^\\s=]+/,\n\t\t\t\t'attr-value': {\n\t\t\t\t\tpattern: /=[\\s\\S]+/,\n\t\t\t\t\tinside: {\n\t\t\t\t\t\t'value': {\n\t\t\t\t\t\t\tpattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n\t\t\t\t\t\t\tlookbehind: true,\n\t\t\t\t\t\t\talias: [lang, 'language-' + lang],\n\t\t\t\t\t\t\tinside: Prism.languages[lang]\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'punctuation': [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tpattern: /^=/,\n\t\t\t\t\t\t\t\talias: 'attr-equals'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/\"|'/\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n});\n\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\n\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n\n\n/* **********************************************\n     Begin prism-css.js\n********************************************** */\n\n(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n\n\n/* **********************************************\n     Begin prism-clike.js\n********************************************** */\n\nPrism.languages.clike = {\n\t'comment': [\n\t\t{\n\t\t\tpattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^\\\\:])\\/\\/.*/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true\n\t\t}\n\t],\n\t'string': {\n\t\tpattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n\t\tgreedy: true\n\t},\n\t'class-name': {\n\t\tpattern: /(\\b(?:class|interface|extends|implements|trait|instanceof|new)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n\t\tlookbehind: true,\n\t\tinside: {\n\t\t\t'punctuation': /[.\\\\]/\n\t\t}\n\t},\n\t'keyword': /\\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\\b/,\n\t'boolean': /\\b(?:true|false)\\b/,\n\t'function': /\\b\\w+(?=\\()/,\n\t'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n\t'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n\t'punctuation': /[{}[\\];(),.:]/\n};\n\n\n/* **********************************************\n     Begin prism-javascript.js\n********************************************** */\n\nPrism.languages.javascript = Prism.languages.extend('clike', {\n\t'class-name': [\n\t\tPrism.languages.clike['class-name'],\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:prototype|constructor))/,\n\t\t\tlookbehind: true\n\t\t}\n\t],\n\t'keyword': [\n\t\t{\n\t\t\tpattern: /((?:^|\\})\\s*)catch\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n\t\t\tlookbehind: true\n\t\t},\n\t],\n\t// Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n\t'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n\t'number': /\\b(?:(?:0[xX](?:[\\dA-Fa-f](?:_[\\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\\d(?:_\\d)?)+n|NaN|Infinity)\\b|(?:\\b(?:\\d(?:_\\d)?)+\\.?(?:\\d(?:_\\d)?)*|\\B\\.(?:\\d(?:_\\d)?)+)(?:[Ee][+-]?(?:\\d(?:_\\d)?)+)?/,\n\t'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\n\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|interface|extends|implements|instanceof|new)\\s+)[\\w.\\\\]+/;\n\nPrism.languages.insertBefore('javascript', 'keyword', {\n\t'regex': {\n\t\t// eslint-disable-next-line regexp/no-dupe-characters-character-class\n\t\tpattern: /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n\t\tlookbehind: true,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'regex-source': {\n\t\t\t\tpattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n\t\t\t\tlookbehind: true,\n\t\t\t\talias: 'language-regex',\n\t\t\t\tinside: Prism.languages.regex\n\t\t\t},\n\t\t\t'regex-delimiter': /^\\/|\\/$/,\n\t\t\t'regex-flags': /^[a-z]+$/,\n\t\t}\n\t},\n\t// This must be declared before keyword because we use \"function\" inside the look-forward\n\t'function-variable': {\n\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n\t\talias: 'function'\n\t},\n\t'parameter': [\n\t\t{\n\t\t\tpattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t},\n\t\t{\n\t\t\tpattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n\t\t\tlookbehind: true,\n\t\t\tinside: Prism.languages.javascript\n\t\t}\n\t],\n\t'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\n\nPrism.languages.insertBefore('javascript', 'string', {\n\t'hashbang': {\n\t\tpattern: /^#!.*/,\n\t\tgreedy: true,\n\t\talias: 'comment'\n\t},\n\t'template-string': {\n\t\tpattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n\t\tgreedy: true,\n\t\tinside: {\n\t\t\t'template-punctuation': {\n\t\t\t\tpattern: /^`|`$/,\n\t\t\t\talias: 'string'\n\t\t\t},\n\t\t\t'interpolation': {\n\t\t\t\tpattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n\t\t\t\tlookbehind: true,\n\t\t\t\tinside: {\n\t\t\t\t\t'interpolation-punctuation': {\n\t\t\t\t\t\tpattern: /^\\$\\{|\\}$/,\n\t\t\t\t\t\talias: 'punctuation'\n\t\t\t\t\t},\n\t\t\t\t\trest: Prism.languages.javascript\n\t\t\t\t}\n\t\t\t},\n\t\t\t'string': /[\\s\\S]+/\n\t\t}\n\t}\n});\n\nif (Prism.languages.markup) {\n\tPrism.languages.markup.tag.addInlined('script', 'javascript');\n\n\t// add attribute support for all DOM events.\n\t// https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n\tPrism.languages.markup.tag.addAttribute(\n\t\t/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\n\t\t'javascript'\n\t);\n}\n\nPrism.languages.js = Prism.languages.javascript;\n\n\n/* **********************************************\n     Begin prism-file-highlight.js\n********************************************** */\n\n(function () {\n\n\tif (typeof Prism === 'undefined' || typeof document === 'undefined') {\n\t\treturn;\n\t}\n\n\t// https://developer.mozilla.org/en-US/docs/Web/API/Element/matches#Polyfill\n\tif (!Element.prototype.matches) {\n\t\tElement.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n\t}\n\n\tvar LOADING_MESSAGE = 'Loading…';\n\tvar FAILURE_MESSAGE = function (status, message) {\n\t\treturn '✖ Error ' + status + ' while fetching file: ' + message;\n\t};\n\tvar FAILURE_EMPTY_MESSAGE = '✖ Error: File does not exist or is empty';\n\n\tvar EXTENSIONS = {\n\t\t'js': 'javascript',\n\t\t'py': 'python',\n\t\t'rb': 'ruby',\n\t\t'ps1': 'powershell',\n\t\t'psm1': 'powershell',\n\t\t'sh': 'bash',\n\t\t'bat': 'batch',\n\t\t'h': 'c',\n\t\t'tex': 'latex'\n\t};\n\n\tvar STATUS_ATTR = 'data-src-status';\n\tvar STATUS_LOADING = 'loading';\n\tvar STATUS_LOADED = 'loaded';\n\tvar STATUS_FAILED = 'failed';\n\n\tvar SELECTOR = 'pre[data-src]:not([' + STATUS_ATTR + '=\"' + STATUS_LOADED + '\"])'\n\t\t+ ':not([' + STATUS_ATTR + '=\"' + STATUS_LOADING + '\"])';\n\n\tvar lang = /\\blang(?:uage)?-([\\w-]+)\\b/i;\n\n\t/**\n\t * Sets the Prism `language-xxxx` or `lang-xxxx` class to the given language.\n\t *\n\t * @param {HTMLElement} element\n\t * @param {string} language\n\t * @returns {void}\n\t */\n\tfunction setLanguageClass(element, language) {\n\t\tvar className = element.className;\n\t\tclassName = className.replace(lang, ' ') + ' language-' + language;\n\t\telement.className = className.replace(/\\s+/g, ' ').trim();\n\t}\n\n\n\tPrism.hooks.add('before-highlightall', function (env) {\n\t\tenv.selector += ', ' + SELECTOR;\n\t});\n\n\tPrism.hooks.add('before-sanity-check', function (env) {\n\t\tvar pre = /** @type {HTMLPreElement} */ (env.element);\n\t\tif (pre.matches(SELECTOR)) {\n\t\t\tenv.code = ''; // fast-path the whole thing and go to complete\n\n\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADING); // mark as loading\n\n\t\t\t// add code element with loading message\n\t\t\tvar code = pre.appendChild(document.createElement('CODE'));\n\t\t\tcode.textContent = LOADING_MESSAGE;\n\n\t\t\tvar src = pre.getAttribute('data-src');\n\n\t\t\tvar language = env.language;\n\t\t\tif (language === 'none') {\n\t\t\t\t// the language might be 'none' because there is no language set;\n\t\t\t\t// in this case, we want to use the extension as the language\n\t\t\t\tvar extension = (/\\.(\\w+)$/.exec(src) || [, 'none'])[1];\n\t\t\t\tlanguage = EXTENSIONS[extension] || extension;\n\t\t\t}\n\n\t\t\t// set language classes\n\t\t\tsetLanguageClass(code, language);\n\t\t\tsetLanguageClass(pre, language);\n\n\t\t\t// preload the language\n\t\t\tvar autoloader = Prism.plugins.autoloader;\n\t\t\tif (autoloader) {\n\t\t\t\tautoloader.loadLanguages(language);\n\t\t\t}\n\n\t\t\t// load file\n\t\t\tvar xhr = new XMLHttpRequest();\n\t\t\txhr.open('GET', src, true);\n\t\t\txhr.onreadystatechange = function () {\n\t\t\t\tif (xhr.readyState == 4) {\n\t\t\t\t\tif (xhr.status < 400 && xhr.responseText) {\n\t\t\t\t\t\t// mark as loaded\n\t\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_LOADED);\n\n\t\t\t\t\t\t// highlight code\n\t\t\t\t\t\tcode.textContent = xhr.responseText;\n\t\t\t\t\t\tPrism.highlightElement(code);\n\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// mark as failed\n\t\t\t\t\t\tpre.setAttribute(STATUS_ATTR, STATUS_FAILED);\n\n\t\t\t\t\t\tif (xhr.status >= 400) {\n\t\t\t\t\t\t\tcode.textContent = FAILURE_MESSAGE(xhr.status, xhr.statusText);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcode.textContent = FAILURE_EMPTY_MESSAGE;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t\txhr.send(null);\n\t\t}\n\t});\n\n\tPrism.plugins.fileHighlight = {\n\t\t/**\n\t\t * Executes the File Highlight plugin for all matching `pre` elements under the given container.\n\t\t *\n\t\t * Note: Elements which are already loaded or currently loading will not be touched by this method.\n\t\t *\n\t\t * @param {ParentNode} [container=document]\n\t\t */\n\t\thighlight: function highlight(container) {\n\t\t\tvar elements = (container || document).querySelectorAll(SELECTOR);\n\n\t\t\tfor (var i = 0, element; (element = elements[i++]);) {\n\t\t\t\tPrism.highlightElement(element);\n\t\t\t}\n\t\t}\n\t};\n\n\tvar logged = false;\n\t/** @deprecated Use `Prism.plugins.fileHighlight.highlight` instead. */\n\tPrism.fileHighlight = function () {\n\t\tif (!logged) {\n\t\t\tconsole.warn('Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead.');\n\t\t\tlogged = true;\n\t\t}\n\t\tPrism.plugins.fileHighlight.highlight.apply(this, arguments);\n\t};\n\n}());\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Prism - is a lightweight, extensible syntax highlighter, built with modern web standards in mind: https://prismjs.com/\r\n\r\nwindow.Prism = require(\"prismjs/prism.js\");\r\nrequire(\"prismjs/components/prism-markup.js\");\r\nrequire(\"prismjs/components/prism-markup-templating.js\");\r\nrequire(\"prismjs/components/prism-bash.js\");\r\nrequire(\"prismjs/components/prism-javascript.js\");\r\nrequire(\"prismjs/components/prism-scss.js\");\r\nrequire(\"prismjs/components/prism-css.js\");\r\nrequire(\"prismjs/components/prism-php.js\");\r\nrequire(\"prismjs/components/prism-php-extras.js\");\r\nrequire(\"prismjs/plugins/normalize-whitespace/prism-normalize-whitespace.js\");\r\nrequire(\"@/src/js/vendors/plugins/prism.init.js\");\r\n\r\nrequire('./prismjs.scss');\r\n"], "sourceRoot": ""}