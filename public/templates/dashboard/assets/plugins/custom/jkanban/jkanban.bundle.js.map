{"version": 3, "sources": ["webpack://keenthemes/./node_modules/jkanban/dist/jkanban.js", "webpack://keenthemes/./webpack/plugins/custom/jkanban/jkanban.scss", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/webpack/runtime/global", "webpack://keenthemes/webpack/runtime/make namespace object", "webpack://keenthemes/./webpack/plugins/custom/jkanban/jkanban.js"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,kBAAkB,gBAAgB,UAAU,UAAU,MAAM,SAAmC,CAAC,gBAAgB,OAAC,OAAO,oBAAoB,8CAA8C,kCAAkC,YAAY,YAAY,mCAAmC,iBAAiB,eAAe,sBAAsB,oBAAoB,UAAU,SAAmC,KAAK,WAAW,YAAY,SAAS,SAAS,KAAK;AAC7c;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC,iCAAiC;AACjC,uDAAuD;AACvD,yCAAyC;AACzC,oCAAoC;AACpC,0DAA0D;AAC1D,6BAA6B;AAC7B;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;;AAEX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,qBAAqB,gCAAgC;AACrD;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb,WAAW;AACX;AACA;AACA,WAAW;AACX,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB,sCAAsC;AAC3D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED,CAAC,EAAE,YAAY;AACf,uCAAuC,yCAAyC;;AAEhF,CAAC,GAAG;AACJ;;AAEA;;AAEA;AACA,YAAY,QAAQ;AACpB;AACA;AACA,GAAG;AACH;;AAEA,CAAC,EAAE,WAAW;AACd;;AAEA;AACA;;AAEA;AACA;AACA;AACA,4BAA4B,YAAY;AACxC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,0CAA0C;AAC9G;AACA,yBAAyB,6BAA6B,EAAE,OAAO,yBAAyB;AACxF,2BAA2B,yBAAyB;AACpD,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA,CAAC,EAAE,wBAAwB;AAC3B,mBAAmB;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,kCAAkC,gBAAgB;AAClD;AACA;;AAEA;AACA;AACA;AACA;AACA,uEAAuE,uBAAuB;AAC9F,0EAA0E,uBAAuB;AACjG;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;;AAEA;AACA;AACA,aAAa,sBAAsB;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC,aAAa,mBAAmB,qBAAM,mBAAmB,qBAAM,mFAAmF;AACnJ,CAAC,EAAE,gCAAgC;AACnC,mBAAmB;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,CAAC,aAAa,mBAAmB,qBAAM,mBAAmB,qBAAM,mFAAmF;AACnJ,CAAC,GAAG;AACJ,mBAAmB;;AAEnB;;AAEA;AACA;AACA,0CAA0C,UAAU,aAAa,EAAE;AACnE;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC,aAAa,mBAAmB,qBAAM,mBAAmB,qBAAM,mFAAmF;AACnJ,CAAC,GAAG;AACJ;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,CAAC,GAAG;AACJ,mBAAmB;AACnB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,cAAc;AACd,YAAY;AACZ,eAAe;AACf,eAAe;AACf,aAAa;AACb,aAAa;AACb,sBAAsB;AACtB,sBAAsB;AACtB,YAAY;AACZ,mBAAmB;AACnB,6BAA6B;AAC7B,eAAe;;AAEf;AACA,2BAA2B,kBAAkB;AAC7C,6BAA6B,oBAAoB;AACjD,6BAA6B,2BAA2B;AACxD,gCAAgC,wCAAwC;AACxE,iCAAiC,uBAAuB;AACxD,0BAA0B,gBAAgB;AAC1C,oCAAoC,0BAA0B;AAC9D,mCAAmC,yBAAyB;AAC5D,mCAAmC,yBAAyB;AAC5D,+BAA+B,0BAA0B;AACzD,8CAA8C,mCAAmC;AACjF,qCAAqC,8BAA8B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,kEAAkE;AAClE;AACA;;AAEA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,qBAAqB;AACrB,OAAO;AACP,2BAA2B;AAC3B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,kDAAkD;AAC7E,sBAAsB,eAAe,eAAe,EAAE;AACtD,qBAAqB,uBAAuB,cAAc,EAAE;AAC5D;;AAEA;AACA;AACA;;AAEA;AACA,yBAAyB,4BAA4B;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA,6DAA6D,WAAW;AACxE,8DAA8D,WAAW;AACzE;AACA;AACA;;AAEA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA,6BAA6B,yBAAyB;AACtD,4CAA4C,gBAAgB,EAAE;AAC9D,6BAA6B,kBAAkB;AAC/C;AACA,0BAA0B;AAC1B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,cAAc;AACjC,oBAAoB,aAAa;AACjC,8BAA8B,+CAA+C;AAC7E,+BAA+B,gDAAgD;AAC/E,yBAAyB,qDAAqD;AAC9E,uBAAuB,yGAAyG;AAChI;AACA,YAAY,cAAc,EAAE;AAC5B,uCAAuC,cAAc,EAAE;AACvD,sCAAsC,aAAa,EAAE;AACrD,mCAAmC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,CAAC,aAAa,mBAAmB,qBAAM,mBAAmB,qBAAM,mFAAmF;AACnJ,CAAC,EAAE,+CAA+C;AAClD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;AAEtC,CAAC,GAAG;AACJ,yBAAyB;AACzB;AACA;AACA,wBAAwB,kBAAkB;AAC1C,CAAC;AACD,wBAAwB,mBAAmB;AAC3C;;AAEA;AACA,CAAC,aAAa;AACd,CAAC,EAAE,YAAY;AACf,wCAAwC;AACxC;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,iBAAiB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,CAAC,aAAa;AACd,CAAC,EAAE,oCAAoC,EAAE,GAAG;;;;;;;;;;;;;AC7sD5C;;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,EAAE;WACF;WACA;WACA,CAAC,I;;;;;WCPD;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;;;;;;;ACNA;;AAEA,mBAAO,CAAC,uEAAyB;;AAEjC,mBAAO,CAAC,qEAAgB", "file": "plugins/custom/jkanban/jkanban.bundle.js", "sourcesContent": ["(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){\n/**\n * jKanban\n * Vanilla Javascript plugin for manage kanban boards\n *\n * @site: http://www.riccardotartaglia.it/jkanban/\n * @author: <PERSON><PERSON><PERSON><PERSON>\n */\n\n//Require dragula\nvar dragula = require('dragula');\n\n(function () {\n  this.jKanban = function () {\n    var self = this\n    var __DEFAULT_ITEM_HANDLE_OPTIONS = {\n      enabled: false\n    }\n    var __DEFAULT_ITEM_ADD_OPTIONS = {\n      enabled: false\n    }\n    this._disallowedItemProperties = [\n      'id',\n      'title',\n      'click',\n      'drag',\n      'dragend',\n      'drop',\n      'order'\n    ]\n    this.element = ''\n    this.container = ''\n    this.boardContainer = []\n    this.handlers = []\n    this.dragula = dragula\n    this.drake = ''\n    this.drakeBoard = ''\n    this.itemAddOptions = __DEFAULT_ITEM_ADD_OPTIONS\n    this.itemHandleOptions = __DEFAULT_ITEM_HANDLE_OPTIONS\n    var defaults = {\n      element: '',\n      gutter: '15px',\n      widthBoard: '250px',\n      responsive: '700',\n      responsivePercentage: false,\n      boards: [],\n      dragBoards: true,\n      dragItems: true, //whether can drag cards or not, useful when set permissions on it.\n      itemAddOptions: __DEFAULT_ITEM_ADD_OPTIONS,\n      itemHandleOptions: __DEFAULT_ITEM_HANDLE_OPTIONS,\n      dragEl: function (el, source) {},\n      dragendEl: function (el) {},\n      dropEl: function (el, target, source, sibling) {},\n      dragBoard: function (el, source) {},\n      dragendBoard: function (el) {},\n      dropBoard: function (el, target, source, sibling) {},\n      click: function (el) {},\n      buttonClick: function (el, boardId) {}\n    }\n\n    if (arguments[0] && typeof arguments[0] === 'object') {\n      this.options = __extendDefaults(defaults, arguments[0])\n    }\n\n    this.__getCanMove = function (handle) {\n      if (!self.options.itemHandleOptions.enabled) {\n        return !!self.options.dragItems\n      }\n\n      if (self.options.itemHandleOptions.handleClass) {\n        return handle.classList.contains(self.options.itemHandleOptions.handleClass)\n      }\n\n      return handle.classList.contains('item_handle')\n    }\n\n    this.init = function () {\n      //set initial boards\n      __setBoard()\n      //set drag with dragula\n      if (window.innerWidth > self.options.responsive) {\n        //Init Drag Board\n        self.drakeBoard = self\n          .dragula([self.container], {\n            moves: function (el, source, handle, sibling) {\n              if (!self.options.dragBoards) return false\n              return (\n                handle.classList.contains('kanban-board-header') ||\n                handle.classList.contains('kanban-title-board')\n              )\n            },\n            accepts: function (el, target, source, sibling) {\n              return target.classList.contains('kanban-container')\n            },\n            revertOnSpill: true,\n            direction: 'horizontal'\n          })\n          .on('drag', function (el, source) {\n            el.classList.add('is-moving')\n            self.options.dragBoard(el, source)\n            if (typeof el.dragfn === 'function') el.dragfn(el, source)\n          })\n          .on('dragend', function (el) {\n            __updateBoardsOrder()\n            el.classList.remove('is-moving')\n            self.options.dragendBoard(el)\n            if (typeof el.dragendfn === 'function') el.dragendfn(el)\n          })\n          .on('drop', function (el, target, source, sibling) {\n            el.classList.remove('is-moving')\n            self.options.dropBoard(el, target, source, sibling)\n            if (typeof el.dropfn === 'function')\n              el.dropfn(el, target, source, sibling)\n          })\n\n        //Init Drag Item\n        self.drake = self\n          .dragula(self.boardContainer, {\n            moves: function (el, source, handle, sibling) {\n              return self.__getCanMove(handle)\n            },\n            revertOnSpill: true\n          })\n          .on('cancel', function (el, container, source) {\n            self.enableAllBoards()\n          })\n          .on('drag', function (el, source) {\n            var elClass = el.getAttribute('class')\n            if (elClass !== '' && elClass.indexOf('not-draggable') > -1) {\n              self.drake.cancel(true)\n              return\n            }\n\n            el.classList.add('is-moving')\n\n            self.options.dragEl(el, source)\n\n            var boardJSON = __findBoardJSON(source.parentNode.dataset.id)\n            if (boardJSON.dragTo !== undefined) {\n              self.options.boards.map(function (board) {\n                if (\n                  boardJSON.dragTo.indexOf(board.id) === -1 &&\n                  board.id !== source.parentNode.dataset.id\n                ) {\n                  self.findBoard(board.id).classList.add('disabled-board')\n                }\n              })\n            }\n\n            if (el !== null && typeof el.dragfn === 'function')\n              el.dragfn(el, source)\n          })\n          .on('dragend', function (el) {\n            self.options.dragendEl(el)\n            if (el !== null && typeof el.dragendfn === 'function')\n              el.dragendfn(el)\n          })\n          .on('drop', function (el, target, source, sibling) {\n            self.enableAllBoards()\n\n            var boardJSON = __findBoardJSON(source.parentNode.dataset.id)\n            if (boardJSON.dragTo !== undefined) {\n              if (\n                boardJSON.dragTo.indexOf(target.parentNode.dataset.id) === -1 &&\n                target.parentNode.dataset.id !== source.parentNode.dataset.id\n              ) {\n                self.drake.cancel(true)\n              }\n            }\n            if (el !== null) {\n              var result = self.options.dropEl(el, target, source, sibling)\n              if (result === false) {\n                self.drake.cancel(true)\n              }\n              el.classList.remove('is-moving')\n              if (typeof el.dropfn === 'function')\n                el.dropfn(el, target, source, sibling)\n            }\n          })\n      }\n    }\n\n    this.enableAllBoards = function () {\n      var allB = document.querySelectorAll('.kanban-board')\n      if (allB.length > 0 && allB !== undefined) {\n        for (var i = 0; i < allB.length; i++) {\n          allB[i].classList.remove('disabled-board')\n        }\n      }\n    }\n\n    this.addElement = function (boardID, element) {\n      var board = self.element.querySelector(\n        '[data-id=\"' + boardID + '\"] .kanban-drag'\n      )\n      var nodeItem = document.createElement('div')\n      nodeItem.classList.add('kanban-item')\n      if (typeof element.id !== 'undefined' && element.id !== '') {\n        nodeItem.setAttribute('data-eid', element.id)\n      }\n      if (element.class && Array.isArray(element.class)) {\n        element.class.forEach(function (cl) {\n          nodeItem.classList.add(cl)\n        })\n      }\n      nodeItem.innerHTML = __buildItemTitle(element.title)\n      //add function\n      nodeItem.clickfn = element.click\n      nodeItem.dragfn = element.drag\n      nodeItem.dragendfn = element.dragend\n      nodeItem.dropfn = element.drop\n      __appendCustomProperties(nodeItem, element)\n      __onclickHandler(nodeItem)\n      if (self.options.itemHandleOptions.enabled) {\n        nodeItem.style.cursor = 'default'\n      }\n      board.appendChild(nodeItem)\n      return self\n    }\n\n    this.addForm = function (boardID, formItem) {\n      var board = self.element.querySelector(\n        '[data-id=\"' + boardID + '\"] .kanban-drag'\n      )\n      var _attribute = formItem.getAttribute('class')\n      formItem.setAttribute('class', _attribute + ' not-draggable')\n      board.appendChild(formItem)\n      return self\n    }\n\n    this.addBoards = function (boards, isInit) {\n      if (self.options.responsivePercentage) {\n        self.container.style.width = '100%'\n        self.options.gutter = '1%'\n        if (window.innerWidth > self.options.responsive) {\n          var boardWidth = (100 - boards.length * 2) / boards.length\n        } else {\n          var boardWidth = 100 - boards.length * 2\n        }\n      } else {\n        var boardWidth = self.options.widthBoard\n      }\n      var addButton = self.options.itemAddOptions.enabled\n      var buttonContent = self.options.itemAddOptions.content\n      var buttonClass = self.options.itemAddOptions.class\n      var buttonFooter = self.options.itemAddOptions.footer\n\n      //for on all the boards\n      for (var boardkey in boards) {\n        // single board\n        var board = boards[boardkey]\n        if (!isInit) {\n          self.options.boards.push(board)\n        }\n\n        if (!self.options.responsivePercentage) {\n          //add width to container\n          if (self.container.style.width === '') {\n            self.container.style.width =\n              parseInt(boardWidth) + parseInt(self.options.gutter) * 2 + 'px'\n          } else {\n            self.container.style.width =\n              parseInt(self.container.style.width) +\n              parseInt(boardWidth) +\n              parseInt(self.options.gutter) * 2 +\n              'px'\n          }\n        }\n        //create node\n        var boardNode = document.createElement('div')\n        boardNode.dataset.id = board.id\n        boardNode.dataset.order = self.container.childNodes.length + 1\n        boardNode.classList.add('kanban-board')\n        //set style\n        if (self.options.responsivePercentage) {\n          boardNode.style.width = boardWidth + '%'\n        } else {\n          boardNode.style.width = boardWidth\n        }\n        boardNode.style.marginLeft = self.options.gutter\n        boardNode.style.marginRight = self.options.gutter\n        // header board\n        var headerBoard = document.createElement('header')\n        if (board.class !== '' && board.class !== undefined)\n          var allClasses = board.class.split(',')\n        else allClasses = []\n        headerBoard.classList.add('kanban-board-header')\n        allClasses.map(function (value) {\n          // Remove empty spaces\n          value = value.replace(/^[ ]+/g, '')\n          headerBoard.classList.add(value)\n        })\n        headerBoard.innerHTML =\n          '<div class=\"kanban-title-board\">' + board.title + '</div>'\n        //content board\n        var contentBoard = document.createElement('main')\n        contentBoard.classList.add('kanban-drag')\n        if (board.bodyClass !== '' && board.bodyClass !== undefined)\n          var bodyClasses = board.bodyClass.split(',')\n        else bodyClasses = []\n        bodyClasses.map(function (value) {\n          contentBoard.classList.add(value)\n        })\n        //add drag to array for dragula\n        self.boardContainer.push(contentBoard)\n        for (var itemkey in board.item) {\n          //create item\n          var itemKanban = board.item[itemkey]\n          var nodeItem = document.createElement('div')\n          nodeItem.classList.add('kanban-item')\n          if (itemKanban.id) {\n            nodeItem.dataset.eid = itemKanban.id\n          }\n          if (itemKanban.class && Array.isArray(itemKanban.class)) {\n            itemKanban.class.forEach(function (cl) {\n              nodeItem.classList.add(cl)\n            })\n          }\n          nodeItem.innerHTML = __buildItemTitle(itemKanban.title)\n          //add function\n          nodeItem.clickfn = itemKanban.click\n          nodeItem.dragfn = itemKanban.drag\n          nodeItem.dragendfn = itemKanban.dragend\n          nodeItem.dropfn = itemKanban.drop\n          __appendCustomProperties(nodeItem, itemKanban)\n          //add click handler of item\n          __onclickHandler(nodeItem)\n          if (self.options.itemHandleOptions.enabled) {\n            nodeItem.style.cursor = 'default'\n          }\n          contentBoard.appendChild(nodeItem)\n        }\n        //footer board\n        var footerBoard = document.createElement('footer')\n        // if add button is true, add button to the board\n        if (addButton) {\n          var btn = document.createElement('BUTTON')\n          var t = document.createTextNode(buttonContent ? buttonContent : '+')\n          btn.setAttribute(\n            'class',\n            buttonClass ? buttonClass : 'kanban-title-button btn btn-default btn-xs'\n          )\n          btn.appendChild(t)\n          //var buttonHtml = '<button class=\"kanban-title-button btn btn-default btn-xs\">'+buttonContent+'</button>'\n          if (buttonFooter) {\n            footerBoard.appendChild(btn)\n          } else {\n            headerBoard.appendChild(btn)\n          }\n          __onButtonClickHandler(btn, board.id)\n        }\n        //board assembly\n        boardNode.appendChild(headerBoard)\n        boardNode.appendChild(contentBoard)\n        boardNode.appendChild(footerBoard)\n        //board add\n        self.container.appendChild(boardNode)\n      }\n      return self\n    }\n\n    this.findBoard = function (id) {\n      var el = self.element.querySelector('[data-id=\"' + id + '\"]')\n      return el\n    }\n\n    this.getParentBoardID = function (el) {\n      if (typeof el === 'string') {\n        el = self.element.querySelector('[data-eid=\"' + el + '\"]')\n      }\n      if (el === null) {\n        return null\n      }\n      return el.parentNode.parentNode.dataset.id\n    }\n\n    this.moveElement = function (targetBoardID, elementID, element) {\n      if (targetBoardID === this.getParentBoardID(elementID)) {\n        return\n      }\n\n      this.removeElement(elementID)\n      return this.addElement(targetBoardID, element)\n    }\n\n    this.replaceElement = function (el, element) {\n      var nodeItem = el\n      if (typeof nodeItem === 'string') {\n        nodeItem = self.element.querySelector('[data-eid=\"' + el + '\"]')\n      }\n      nodeItem.innerHTML = element.title\n      // add function\n      nodeItem.clickfn = element.click\n      nodeItem.dragfn = element.drag\n      nodeItem.dragendfn = element.dragend\n      nodeItem.dropfn = element.drop\n      __appendCustomProperties(nodeItem, element)\n      return self\n    }\n\n    this.findElement = function (id) {\n      var el = self.element.querySelector('[data-eid=\"' + id + '\"]')\n      return el\n    }\n\n    this.getBoardElements = function (id) {\n      var board = self.element.querySelector(\n        '[data-id=\"' + id + '\"] .kanban-drag'\n      )\n      return board.childNodes\n    }\n\n    this.removeElement = function (el) {\n      if (typeof el === 'string')\n        el = self.element.querySelector('[data-eid=\"' + el + '\"]')\n      if (el !== null) {\n        //fallback for IE\n        if (typeof el.remove == 'function') {\n          el.remove()\n        } else {\n          el.parentNode.removeChild(el)\n        }\n      }\n      return self\n    }\n\n    this.removeBoard = function (board) {\n      var boardElement = null\n      if (typeof board === 'string')\n        boardElement = self.element.querySelector('[data-id=\"' + board + '\"]')\n      if (boardElement !== null) {\n        //fallback for IE\n        if (typeof boardElement.remove == 'function') {\n          boardElement.remove()\n        } else {\n          boardElement.parentNode.removeChild(boardElement)\n        }\n      }\n\n      // remove thboard in options.boards\n      for (var i = 0; i < self.options.boards.length; i++) {\n        if (self.options.boards[i].id === board) {\n          self.options.boards.splice(i, 1)\n          break\n        }\n      }\n\n      return self\n    }\n\n    // board button on click function\n    this.onButtonClick = function (el) {}\n\n    //PRIVATE FUNCTION\n    function __extendDefaults (source, properties) {\n      var property\n      for (property in properties) {\n        if (properties.hasOwnProperty(property)) {\n          source[property] = properties[property]\n        }\n      }\n      return source\n    }\n\n    function __setBoard () {\n      self.element = document.querySelector(self.options.element)\n      //create container\n      var boardContainer = document.createElement('div')\n      boardContainer.classList.add('kanban-container')\n      self.container = boardContainer\n      //add boards\n\n      if (document.querySelector(self.options.element).dataset.hasOwnProperty('board')) {\n        url = document.querySelector(self.options.element).dataset.board\n        window.fetch(url, {\n          method: 'GET',\n          headers: { 'Content-Type': 'application/json' }\n        })\n          .then((response) => {\n            // log response text\n            response.json().then(function (data) {\n              self.options.boards = data\n              self.addBoards(self.options.boards, true)\n            })\n\n          })\n          .catch((error) => {\n            console.log('Error: ', error)\n          })\n      } else {\n        self.addBoards(self.options.boards, true)\n      }\n\n      //appends to container\n      self.element.appendChild(self.container)\n    }\n\n    function __onclickHandler (nodeItem, clickfn) {\n      nodeItem.addEventListener('click', function (e) {\n        e.preventDefault()\n        self.options.click(this)\n        if (typeof this.clickfn === 'function') this.clickfn(this)\n      })\n    }\n\n    function __onButtonClickHandler (nodeItem, boardId) {\n      nodeItem.addEventListener('click', function (e) {\n        e.preventDefault()\n        self.options.buttonClick(this, boardId)\n        // if(typeof(this.clickfn) === 'function')\n        //     this.clickfn(this);\n      })\n    }\n\n    function __findBoardJSON (id) {\n      var el = []\n      self.options.boards.map(function (board) {\n        if (board.id === id) {\n          return el.push(board)\n        }\n      })\n      return el[0]\n    }\n\n    function __appendCustomProperties (element, parentObject) {\n      for (var propertyName in parentObject) {\n        if (self._disallowedItemProperties.indexOf(propertyName) > -1) {\n          continue\n        }\n\n        element.setAttribute(\n          'data-' + propertyName,\n          parentObject[propertyName]\n        )\n      }\n    }\n\n    function __updateBoardsOrder () {\n      var index = 1\n      for (var i = 0; i < self.container.childNodes.length; i++) {\n        self.container.childNodes[i].dataset.order = index++\n      }\n    }\n\n    function __buildItemTitle (title) {\n      var result = title\n      if (self.options.itemHandleOptions.enabled) {\n        if ((self.options.itemHandleOptions.customHandler || undefined) === undefined) {\n          var customCssHandler = self.options.itemHandleOptions.customCssHandler\n          var customCssIconHandler = self.options.itemHandleOptions.customCssIconHandler\n          if ((customCssHandler || undefined) === undefined) {\n            customCssHandler = 'drag_handler'\n          }\n          if ((customCssIconHandler || undefined) === undefined) {\n            customCssIconHandler = customCssHandler + '_icon'\n          }\n\n          result = '<div class=\\'item_handle ' + customCssHandler + '\\'><i class=\\'item_handle ' + customCssIconHandler + '\\'></i></div><div>' + result + '</div>'\n        } else {\n          result = self.options.itemHandleOptions.customHandler.replace('%s', result)\n        }\n      }\n      return result\n    }\n\n    //init plugin\n    this.init()\n  }\n})()\n\n},{\"dragula\":9}],2:[function(require,module,exports){\nmodule.exports = function atoa (a, n) { return Array.prototype.slice.call(a, n); }\n\n},{}],3:[function(require,module,exports){\n'use strict';\n\nvar ticky = require('ticky');\n\nmodule.exports = function debounce (fn, args, ctx) {\n  if (!fn) { return; }\n  ticky(function run () {\n    fn.apply(ctx || null, args || []);\n  });\n};\n\n},{\"ticky\":11}],4:[function(require,module,exports){\n'use strict';\n\nvar atoa = require('atoa');\nvar debounce = require('./debounce');\n\nmodule.exports = function emitter (thing, options) {\n  var opts = options || {};\n  var evt = {};\n  if (thing === undefined) { thing = {}; }\n  thing.on = function (type, fn) {\n    if (!evt[type]) {\n      evt[type] = [fn];\n    } else {\n      evt[type].push(fn);\n    }\n    return thing;\n  };\n  thing.once = function (type, fn) {\n    fn._once = true; // thing.off(fn) still works!\n    thing.on(type, fn);\n    return thing;\n  };\n  thing.off = function (type, fn) {\n    var c = arguments.length;\n    if (c === 1) {\n      delete evt[type];\n    } else if (c === 0) {\n      evt = {};\n    } else {\n      var et = evt[type];\n      if (!et) { return thing; }\n      et.splice(et.indexOf(fn), 1);\n    }\n    return thing;\n  };\n  thing.emit = function () {\n    var args = atoa(arguments);\n    return thing.emitterSnapshot(args.shift()).apply(this, args);\n  };\n  thing.emitterSnapshot = function (type) {\n    var et = (evt[type] || []).slice(0);\n    return function () {\n      var args = atoa(arguments);\n      var ctx = this || thing;\n      if (type === 'error' && opts.throws !== false && !et.length) { throw args.length === 1 ? args[0] : args; }\n      et.forEach(function emitter (listen) {\n        if (opts.async) { debounce(listen, args, ctx); } else { listen.apply(ctx, args); }\n        if (listen._once) { thing.off(type, listen); }\n      });\n      return thing;\n    };\n  };\n  return thing;\n};\n\n},{\"./debounce\":3,\"atoa\":2}],5:[function(require,module,exports){\n(function (global){(function (){\n'use strict';\n\nvar customEvent = require('custom-event');\nvar eventmap = require('./eventmap');\nvar doc = global.document;\nvar addEvent = addEventEasy;\nvar removeEvent = removeEventEasy;\nvar hardCache = [];\n\nif (!global.addEventListener) {\n  addEvent = addEventHard;\n  removeEvent = removeEventHard;\n}\n\nmodule.exports = {\n  add: addEvent,\n  remove: removeEvent,\n  fabricate: fabricateEvent\n};\n\nfunction addEventEasy (el, type, fn, capturing) {\n  return el.addEventListener(type, fn, capturing);\n}\n\nfunction addEventHard (el, type, fn) {\n  return el.attachEvent('on' + type, wrap(el, type, fn));\n}\n\nfunction removeEventEasy (el, type, fn, capturing) {\n  return el.removeEventListener(type, fn, capturing);\n}\n\nfunction removeEventHard (el, type, fn) {\n  var listener = unwrap(el, type, fn);\n  if (listener) {\n    return el.detachEvent('on' + type, listener);\n  }\n}\n\nfunction fabricateEvent (el, type, model) {\n  var e = eventmap.indexOf(type) === -1 ? makeCustomEvent() : makeClassicEvent();\n  if (el.dispatchEvent) {\n    el.dispatchEvent(e);\n  } else {\n    el.fireEvent('on' + type, e);\n  }\n  function makeClassicEvent () {\n    var e;\n    if (doc.createEvent) {\n      e = doc.createEvent('Event');\n      e.initEvent(type, true, true);\n    } else if (doc.createEventObject) {\n      e = doc.createEventObject();\n    }\n    return e;\n  }\n  function makeCustomEvent () {\n    return new customEvent(type, { detail: model });\n  }\n}\n\nfunction wrapperFactory (el, type, fn) {\n  return function wrapper (originalEvent) {\n    var e = originalEvent || global.event;\n    e.target = e.target || e.srcElement;\n    e.preventDefault = e.preventDefault || function preventDefault () { e.returnValue = false; };\n    e.stopPropagation = e.stopPropagation || function stopPropagation () { e.cancelBubble = true; };\n    e.which = e.which || e.keyCode;\n    fn.call(el, e);\n  };\n}\n\nfunction wrap (el, type, fn) {\n  var wrapper = unwrap(el, type, fn) || wrapperFactory(el, type, fn);\n  hardCache.push({\n    wrapper: wrapper,\n    element: el,\n    type: type,\n    fn: fn\n  });\n  return wrapper;\n}\n\nfunction unwrap (el, type, fn) {\n  var i = find(el, type, fn);\n  if (i) {\n    var wrapper = hardCache[i].wrapper;\n    hardCache.splice(i, 1); // free up a tad of memory\n    return wrapper;\n  }\n}\n\nfunction find (el, type, fn) {\n  var i, item;\n  for (i = 0; i < hardCache.length; i++) {\n    item = hardCache[i];\n    if (item.element === el && item.type === type && item.fn === fn) {\n      return i;\n    }\n  }\n}\n\n}).call(this)}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{\"./eventmap\":6,\"custom-event\":7}],6:[function(require,module,exports){\n(function (global){(function (){\n'use strict';\n\nvar eventmap = [];\nvar eventname = '';\nvar ron = /^on/;\n\nfor (eventname in global) {\n  if (ron.test(eventname)) {\n    eventmap.push(eventname.slice(2));\n  }\n}\n\nmodule.exports = eventmap;\n\n}).call(this)}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{}],7:[function(require,module,exports){\n(function (global){(function (){\n\nvar NativeCustomEvent = global.CustomEvent;\n\nfunction useNative () {\n  try {\n    var p = new NativeCustomEvent('cat', { detail: { foo: 'bar' } });\n    return  'cat' === p.type && 'bar' === p.detail.foo;\n  } catch (e) {\n  }\n  return false;\n}\n\n/**\n * Cross-browser `CustomEvent` constructor.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent.CustomEvent\n *\n * @public\n */\n\nmodule.exports = useNative() ? NativeCustomEvent :\n\n// IE >= 9\n'undefined' !== typeof document && 'function' === typeof document.createEvent ? function CustomEvent (type, params) {\n  var e = document.createEvent('CustomEvent');\n  if (params) {\n    e.initCustomEvent(type, params.bubbles, params.cancelable, params.detail);\n  } else {\n    e.initCustomEvent(type, false, false, void 0);\n  }\n  return e;\n} :\n\n// IE <= 8\nfunction CustomEvent (type, params) {\n  var e = document.createEventObject();\n  e.type = type;\n  if (params) {\n    e.bubbles = Boolean(params.bubbles);\n    e.cancelable = Boolean(params.cancelable);\n    e.detail = params.detail;\n  } else {\n    e.bubbles = false;\n    e.cancelable = false;\n    e.detail = void 0;\n  }\n  return e;\n}\n\n}).call(this)}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{}],8:[function(require,module,exports){\n'use strict';\n\nvar cache = {};\nvar start = '(?:^|\\\\s)';\nvar end = '(?:\\\\s|$)';\n\nfunction lookupClass (className) {\n  var cached = cache[className];\n  if (cached) {\n    cached.lastIndex = 0;\n  } else {\n    cache[className] = cached = new RegExp(start + className + end, 'g');\n  }\n  return cached;\n}\n\nfunction addClass (el, className) {\n  var current = el.className;\n  if (!current.length) {\n    el.className = className;\n  } else if (!lookupClass(className).test(current)) {\n    el.className += ' ' + className;\n  }\n}\n\nfunction rmClass (el, className) {\n  el.className = el.className.replace(lookupClass(className), ' ').trim();\n}\n\nmodule.exports = {\n  add: addClass,\n  rm: rmClass\n};\n\n},{}],9:[function(require,module,exports){\n(function (global){(function (){\n'use strict';\n\nvar emitter = require('contra/emitter');\nvar crossvent = require('crossvent');\nvar classes = require('./classes');\nvar doc = document;\nvar documentElement = doc.documentElement;\n\nfunction dragula (initialContainers, options) {\n  var len = arguments.length;\n  if (len === 1 && Array.isArray(initialContainers) === false) {\n    options = initialContainers;\n    initialContainers = [];\n  }\n  var _mirror; // mirror image\n  var _source; // source container\n  var _item; // item being dragged\n  var _offsetX; // reference x\n  var _offsetY; // reference y\n  var _moveX; // reference move x\n  var _moveY; // reference move y\n  var _initialSibling; // reference sibling when grabbed\n  var _currentSibling; // reference sibling now\n  var _copy; // item used for copying\n  var _renderTimer; // timer for setTimeout renderMirrorImage\n  var _lastDropTarget = null; // last container item was over\n  var _grabbed; // holds mousedown context until first mousemove\n\n  var o = options || {};\n  if (o.moves === void 0) { o.moves = always; }\n  if (o.accepts === void 0) { o.accepts = always; }\n  if (o.invalid === void 0) { o.invalid = invalidTarget; }\n  if (o.containers === void 0) { o.containers = initialContainers || []; }\n  if (o.isContainer === void 0) { o.isContainer = never; }\n  if (o.copy === void 0) { o.copy = false; }\n  if (o.copySortSource === void 0) { o.copySortSource = false; }\n  if (o.revertOnSpill === void 0) { o.revertOnSpill = false; }\n  if (o.removeOnSpill === void 0) { o.removeOnSpill = false; }\n  if (o.direction === void 0) { o.direction = 'vertical'; }\n  if (o.ignoreInputTextSelection === void 0) { o.ignoreInputTextSelection = true; }\n  if (o.mirrorContainer === void 0) { o.mirrorContainer = doc.body; }\n\n  var drake = emitter({\n    containers: o.containers,\n    start: manualStart,\n    end: end,\n    cancel: cancel,\n    remove: remove,\n    destroy: destroy,\n    canMove: canMove,\n    dragging: false\n  });\n\n  if (o.removeOnSpill === true) {\n    drake.on('over', spillOver).on('out', spillOut);\n  }\n\n  events();\n\n  return drake;\n\n  function isContainer (el) {\n    return drake.containers.indexOf(el) !== -1 || o.isContainer(el);\n  }\n\n  function events (remove) {\n    var op = remove ? 'remove' : 'add';\n    touchy(documentElement, op, 'mousedown', grab);\n    touchy(documentElement, op, 'mouseup', release);\n  }\n\n  function eventualMovements (remove) {\n    var op = remove ? 'remove' : 'add';\n    touchy(documentElement, op, 'mousemove', startBecauseMouseMoved);\n  }\n\n  function movements (remove) {\n    var op = remove ? 'remove' : 'add';\n    crossvent[op](documentElement, 'selectstart', preventGrabbed); // IE8\n    crossvent[op](documentElement, 'click', preventGrabbed);\n  }\n\n  function destroy () {\n    events(true);\n    release({});\n  }\n\n  function preventGrabbed (e) {\n    if (_grabbed) {\n      e.preventDefault();\n    }\n  }\n\n  function grab (e) {\n    _moveX = e.clientX;\n    _moveY = e.clientY;\n\n    var ignore = whichMouseButton(e) !== 1 || e.metaKey || e.ctrlKey;\n    if (ignore) {\n      return; // we only care about honest-to-god left clicks and touch events\n    }\n    var item = e.target;\n    var context = canStart(item);\n    if (!context) {\n      return;\n    }\n    _grabbed = context;\n    eventualMovements();\n    if (e.type === 'mousedown') {\n      if (isInput(item)) { // see also: https://github.com/bevacqua/dragula/issues/208\n        item.focus(); // fixes https://github.com/bevacqua/dragula/issues/176\n      } else {\n        e.preventDefault(); // fixes https://github.com/bevacqua/dragula/issues/155\n      }\n    }\n  }\n\n  function startBecauseMouseMoved (e) {\n    if (!_grabbed) {\n      return;\n    }\n    if (whichMouseButton(e) === 0) {\n      release({});\n      return; // when text is selected on an input and then dragged, mouseup doesn't fire. this is our only hope\n    }\n\n    // truthy check fixes #239, equality fixes #207, fixes #501\n    if ((e.clientX !== void 0 && Math.abs(e.clientX - _moveX) <= (o.slideFactorX || 0)) &&\n      (e.clientY !== void 0 && Math.abs(e.clientY - _moveY) <= (o.slideFactorY || 0))) {\n      return;\n    }\n\n    if (o.ignoreInputTextSelection) {\n      var clientX = getCoord('clientX', e) || 0;\n      var clientY = getCoord('clientY', e) || 0;\n      var elementBehindCursor = doc.elementFromPoint(clientX, clientY);\n      if (isInput(elementBehindCursor)) {\n        return;\n      }\n    }\n\n    var grabbed = _grabbed; // call to end() unsets _grabbed\n    eventualMovements(true);\n    movements();\n    end();\n    start(grabbed);\n\n    var offset = getOffset(_item);\n    _offsetX = getCoord('pageX', e) - offset.left;\n    _offsetY = getCoord('pageY', e) - offset.top;\n\n    classes.add(_copy || _item, 'gu-transit');\n    renderMirrorImage();\n    drag(e);\n  }\n\n  function canStart (item) {\n    if (drake.dragging && _mirror) {\n      return;\n    }\n    if (isContainer(item)) {\n      return; // don't drag container itself\n    }\n    var handle = item;\n    while (getParent(item) && isContainer(getParent(item)) === false) {\n      if (o.invalid(item, handle)) {\n        return;\n      }\n      item = getParent(item); // drag target should be a top element\n      if (!item) {\n        return;\n      }\n    }\n    var source = getParent(item);\n    if (!source) {\n      return;\n    }\n    if (o.invalid(item, handle)) {\n      return;\n    }\n\n    var movable = o.moves(item, source, handle, nextEl(item));\n    if (!movable) {\n      return;\n    }\n\n    return {\n      item: item,\n      source: source\n    };\n  }\n\n  function canMove (item) {\n    return !!canStart(item);\n  }\n\n  function manualStart (item) {\n    var context = canStart(item);\n    if (context) {\n      start(context);\n    }\n  }\n\n  function start (context) {\n    if (isCopy(context.item, context.source)) {\n      _copy = context.item.cloneNode(true);\n      drake.emit('cloned', _copy, context.item, 'copy');\n    }\n\n    _source = context.source;\n    _item = context.item;\n    _initialSibling = _currentSibling = nextEl(context.item);\n\n    drake.dragging = true;\n    drake.emit('drag', _item, _source);\n  }\n\n  function invalidTarget () {\n    return false;\n  }\n\n  function end () {\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    drop(item, getParent(item));\n  }\n\n  function ungrab () {\n    _grabbed = false;\n    eventualMovements(true);\n    movements(true);\n  }\n\n  function release (e) {\n    ungrab();\n\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    var clientX = getCoord('clientX', e) || 0;\n    var clientY = getCoord('clientY', e) || 0;\n    var elementBehindCursor = getElementBehindPoint(_mirror, clientX, clientY);\n    var dropTarget = findDropTarget(elementBehindCursor, clientX, clientY);\n    if (dropTarget && ((_copy && o.copySortSource) || (!_copy || dropTarget !== _source))) {\n      drop(item, dropTarget);\n    } else if (o.removeOnSpill) {\n      remove();\n    } else {\n      cancel();\n    }\n  }\n\n  function drop (item, target) {\n    var parent = getParent(item);\n    if (_copy && o.copySortSource && target === _source) {\n      parent.removeChild(_item);\n    }\n    if (isInitialPlacement(target)) {\n      drake.emit('cancel', item, _source, _source);\n    } else {\n      drake.emit('drop', item, target, _source, _currentSibling);\n    }\n    cleanup();\n  }\n\n  function remove () {\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    var parent = getParent(item);\n    if (parent) {\n      parent.removeChild(item);\n    }\n    drake.emit(_copy ? 'cancel' : 'remove', item, parent, _source);\n    cleanup();\n  }\n\n  function cancel (revert) {\n    if (!drake.dragging) {\n      return;\n    }\n    var reverts = arguments.length > 0 ? revert : o.revertOnSpill;\n    var item = _copy || _item;\n    var parent = getParent(item);\n    var initial = isInitialPlacement(parent);\n    if (initial === false && reverts) {\n      if (_copy) {\n        if (parent) {\n          parent.removeChild(_copy);\n        }\n      } else {\n        _source.insertBefore(item, _initialSibling);\n      }\n    }\n    if (initial || reverts) {\n      drake.emit('cancel', item, _source, _source);\n    } else {\n      drake.emit('drop', item, parent, _source, _currentSibling);\n    }\n    cleanup();\n  }\n\n  function cleanup () {\n    var item = _copy || _item;\n    ungrab();\n    removeMirrorImage();\n    if (item) {\n      classes.rm(item, 'gu-transit');\n    }\n    if (_renderTimer) {\n      clearTimeout(_renderTimer);\n    }\n    drake.dragging = false;\n    if (_lastDropTarget) {\n      drake.emit('out', item, _lastDropTarget, _source);\n    }\n    drake.emit('dragend', item);\n    _source = _item = _copy = _initialSibling = _currentSibling = _renderTimer = _lastDropTarget = null;\n  }\n\n  function isInitialPlacement (target, s) {\n    var sibling;\n    if (s !== void 0) {\n      sibling = s;\n    } else if (_mirror) {\n      sibling = _currentSibling;\n    } else {\n      sibling = nextEl(_copy || _item);\n    }\n    return target === _source && sibling === _initialSibling;\n  }\n\n  function findDropTarget (elementBehindCursor, clientX, clientY) {\n    var target = elementBehindCursor;\n    while (target && !accepted()) {\n      target = getParent(target);\n    }\n    return target;\n\n    function accepted () {\n      var droppable = isContainer(target);\n      if (droppable === false) {\n        return false;\n      }\n\n      var immediate = getImmediateChild(target, elementBehindCursor);\n      var reference = getReference(target, immediate, clientX, clientY);\n      var initial = isInitialPlacement(target, reference);\n      if (initial) {\n        return true; // should always be able to drop it right back where it was\n      }\n      return o.accepts(_item, target, _source, reference);\n    }\n  }\n\n  function drag (e) {\n    if (!_mirror) {\n      return;\n    }\n    e.preventDefault();\n\n    var clientX = getCoord('clientX', e) || 0;\n    var clientY = getCoord('clientY', e) || 0;\n    var x = clientX - _offsetX;\n    var y = clientY - _offsetY;\n\n    _mirror.style.left = x + 'px';\n    _mirror.style.top = y + 'px';\n\n    var item = _copy || _item;\n    var elementBehindCursor = getElementBehindPoint(_mirror, clientX, clientY);\n    var dropTarget = findDropTarget(elementBehindCursor, clientX, clientY);\n    var changed = dropTarget !== null && dropTarget !== _lastDropTarget;\n    if (changed || dropTarget === null) {\n      out();\n      _lastDropTarget = dropTarget;\n      over();\n    }\n    var parent = getParent(item);\n    if (dropTarget === _source && _copy && !o.copySortSource) {\n      if (parent) {\n        parent.removeChild(item);\n      }\n      return;\n    }\n    var reference;\n    var immediate = getImmediateChild(dropTarget, elementBehindCursor);\n    if (immediate !== null) {\n      reference = getReference(dropTarget, immediate, clientX, clientY);\n    } else if (o.revertOnSpill === true && !_copy) {\n      reference = _initialSibling;\n      dropTarget = _source;\n    } else {\n      if (_copy && parent) {\n        parent.removeChild(item);\n      }\n      return;\n    }\n    if (\n      (reference === null && changed) ||\n      reference !== item &&\n      reference !== nextEl(item)\n    ) {\n      _currentSibling = reference;\n      dropTarget.insertBefore(item, reference);\n      drake.emit('shadow', item, dropTarget, _source);\n    }\n    function moved (type) { drake.emit(type, item, _lastDropTarget, _source); }\n    function over () { if (changed) { moved('over'); } }\n    function out () { if (_lastDropTarget) { moved('out'); } }\n  }\n\n  function spillOver (el) {\n    classes.rm(el, 'gu-hide');\n  }\n\n  function spillOut (el) {\n    if (drake.dragging) { classes.add(el, 'gu-hide'); }\n  }\n\n  function renderMirrorImage () {\n    if (_mirror) {\n      return;\n    }\n    var rect = _item.getBoundingClientRect();\n    _mirror = _item.cloneNode(true);\n    _mirror.style.width = getRectWidth(rect) + 'px';\n    _mirror.style.height = getRectHeight(rect) + 'px';\n    classes.rm(_mirror, 'gu-transit');\n    classes.add(_mirror, 'gu-mirror');\n    o.mirrorContainer.appendChild(_mirror);\n    touchy(documentElement, 'add', 'mousemove', drag);\n    classes.add(o.mirrorContainer, 'gu-unselectable');\n    drake.emit('cloned', _mirror, _item, 'mirror');\n  }\n\n  function removeMirrorImage () {\n    if (_mirror) {\n      classes.rm(o.mirrorContainer, 'gu-unselectable');\n      touchy(documentElement, 'remove', 'mousemove', drag);\n      getParent(_mirror).removeChild(_mirror);\n      _mirror = null;\n    }\n  }\n\n  function getImmediateChild (dropTarget, target) {\n    var immediate = target;\n    while (immediate !== dropTarget && getParent(immediate) !== dropTarget) {\n      immediate = getParent(immediate);\n    }\n    if (immediate === documentElement) {\n      return null;\n    }\n    return immediate;\n  }\n\n  function getReference (dropTarget, target, x, y) {\n    var horizontal = o.direction === 'horizontal';\n    var reference = target !== dropTarget ? inside() : outside();\n    return reference;\n\n    function outside () { // slower, but able to figure out any position\n      var len = dropTarget.children.length;\n      var i;\n      var el;\n      var rect;\n      for (i = 0; i < len; i++) {\n        el = dropTarget.children[i];\n        rect = el.getBoundingClientRect();\n        if (horizontal && (rect.left + rect.width / 2) > x) { return el; }\n        if (!horizontal && (rect.top + rect.height / 2) > y) { return el; }\n      }\n      return null;\n    }\n\n    function inside () { // faster, but only available if dropped inside a child element\n      var rect = target.getBoundingClientRect();\n      if (horizontal) {\n        return resolve(x > rect.left + getRectWidth(rect) / 2);\n      }\n      return resolve(y > rect.top + getRectHeight(rect) / 2);\n    }\n\n    function resolve (after) {\n      return after ? nextEl(target) : target;\n    }\n  }\n\n  function isCopy (item, container) {\n    return typeof o.copy === 'boolean' ? o.copy : o.copy(item, container);\n  }\n}\n\nfunction touchy (el, op, type, fn) {\n  var touch = {\n    mouseup: 'touchend',\n    mousedown: 'touchstart',\n    mousemove: 'touchmove'\n  };\n  var pointers = {\n    mouseup: 'pointerup',\n    mousedown: 'pointerdown',\n    mousemove: 'pointermove'\n  };\n  var microsoft = {\n    mouseup: 'MSPointerUp',\n    mousedown: 'MSPointerDown',\n    mousemove: 'MSPointerMove'\n  };\n  if (global.navigator.pointerEnabled) {\n    crossvent[op](el, pointers[type], fn);\n  } else if (global.navigator.msPointerEnabled) {\n    crossvent[op](el, microsoft[type], fn);\n  } else {\n    crossvent[op](el, touch[type], fn);\n    crossvent[op](el, type, fn);\n  }\n}\n\nfunction whichMouseButton (e) {\n  if (e.touches !== void 0) { return e.touches.length; }\n  if (e.which !== void 0 && e.which !== 0) { return e.which; } // see https://github.com/bevacqua/dragula/issues/261\n  if (e.buttons !== void 0) { return e.buttons; }\n  var button = e.button;\n  if (button !== void 0) { // see https://github.com/jquery/jquery/blob/99e8ff1baa7ae341e94bb89c3e84570c7c3ad9ea/src/event.js#L573-L575\n    return button & 1 ? 1 : button & 2 ? 3 : (button & 4 ? 2 : 0);\n  }\n}\n\nfunction getOffset (el) {\n  var rect = el.getBoundingClientRect();\n  return {\n    left: rect.left + getScroll('scrollLeft', 'pageXOffset'),\n    top: rect.top + getScroll('scrollTop', 'pageYOffset')\n  };\n}\n\nfunction getScroll (scrollProp, offsetProp) {\n  if (typeof global[offsetProp] !== 'undefined') {\n    return global[offsetProp];\n  }\n  if (documentElement.clientHeight) {\n    return documentElement[scrollProp];\n  }\n  return doc.body[scrollProp];\n}\n\nfunction getElementBehindPoint (point, x, y) {\n  point = point || {};\n  var state = point.className || '';\n  var el;\n  point.className += ' gu-hide';\n  el = doc.elementFromPoint(x, y);\n  point.className = state;\n  return el;\n}\n\nfunction never () { return false; }\nfunction always () { return true; }\nfunction getRectWidth (rect) { return rect.width || (rect.right - rect.left); }\nfunction getRectHeight (rect) { return rect.height || (rect.bottom - rect.top); }\nfunction getParent (el) { return el.parentNode === doc ? null : el.parentNode; }\nfunction isInput (el) { return el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT' || isEditable(el); }\nfunction isEditable (el) {\n  if (!el) { return false; } // no parents were editable\n  if (el.contentEditable === 'false') { return false; } // stop the lookup\n  if (el.contentEditable === 'true') { return true; } // found a contentEditable element in the chain\n  return isEditable(getParent(el)); // contentEditable is set to 'inherit'\n}\n\nfunction nextEl (el) {\n  return el.nextElementSibling || manually();\n  function manually () {\n    var sibling = el;\n    do {\n      sibling = sibling.nextSibling;\n    } while (sibling && sibling.nodeType !== 1);\n    return sibling;\n  }\n}\n\nfunction getEventHost (e) {\n  // on touchend event, we have to use `e.changedTouches`\n  // see http://stackoverflow.com/questions/7192563/touchend-event-properties\n  // see https://github.com/bevacqua/dragula/issues/34\n  if (e.targetTouches && e.targetTouches.length) {\n    return e.targetTouches[0];\n  }\n  if (e.changedTouches && e.changedTouches.length) {\n    return e.changedTouches[0];\n  }\n  return e;\n}\n\nfunction getCoord (coord, e) {\n  var host = getEventHost(e);\n  var missMap = {\n    pageX: 'clientX', // IE8\n    pageY: 'clientY' // IE8\n  };\n  if (coord in missMap && !(coord in host) && missMap[coord] in host) {\n    coord = missMap[coord];\n  }\n  return host[coord];\n}\n\nmodule.exports = dragula;\n\n}).call(this)}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{\"./classes\":8,\"contra/emitter\":4,\"crossvent\":5}],10:[function(require,module,exports){\n// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n},{}],11:[function(require,module,exports){\n(function (setImmediate){(function (){\nvar si = typeof setImmediate === 'function', tick;\nif (si) {\n  tick = function (fn) { setImmediate(fn); };\n} else {\n  tick = function (fn) { setTimeout(fn, 0); };\n}\n\nmodule.exports = tick;\n}).call(this)}).call(this,require(\"timers\").setImmediate)\n},{\"timers\":12}],12:[function(require,module,exports){\n(function (setImmediate,clearImmediate){(function (){\nvar nextTick = require('process/browser.js').nextTick;\nvar apply = Function.prototype.apply;\nvar slice = Array.prototype.slice;\nvar immediateIds = {};\nvar nextImmediateId = 0;\n\n// DOM APIs, for completeness\n\nexports.setTimeout = function() {\n  return new Timeout(apply.call(setTimeout, window, arguments), clearTimeout);\n};\nexports.setInterval = function() {\n  return new Timeout(apply.call(setInterval, window, arguments), clearInterval);\n};\nexports.clearTimeout =\nexports.clearInterval = function(timeout) { timeout.close(); };\n\nfunction Timeout(id, clearFn) {\n  this._id = id;\n  this._clearFn = clearFn;\n}\nTimeout.prototype.unref = Timeout.prototype.ref = function() {};\nTimeout.prototype.close = function() {\n  this._clearFn.call(window, this._id);\n};\n\n// Does not start the time, just sets up the members needed.\nexports.enroll = function(item, msecs) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = msecs;\n};\n\nexports.unenroll = function(item) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = -1;\n};\n\nexports._unrefActive = exports.active = function(item) {\n  clearTimeout(item._idleTimeoutId);\n\n  var msecs = item._idleTimeout;\n  if (msecs >= 0) {\n    item._idleTimeoutId = setTimeout(function onTimeout() {\n      if (item._onTimeout)\n        item._onTimeout();\n    }, msecs);\n  }\n};\n\n// That's not how node.js implements it but the exposed api is the same.\nexports.setImmediate = typeof setImmediate === \"function\" ? setImmediate : function(fn) {\n  var id = nextImmediateId++;\n  var args = arguments.length < 2 ? false : slice.call(arguments, 1);\n\n  immediateIds[id] = true;\n\n  nextTick(function onNextTick() {\n    if (immediateIds[id]) {\n      // fn.call() is faster so we optimize for the common use-case\n      // @see http://jsperf.com/call-apply-segu\n      if (args) {\n        fn.apply(null, args);\n      } else {\n        fn.call(null);\n      }\n      // Prevent ids from leaking\n      exports.clearImmediate(id);\n    }\n  });\n\n  return id;\n};\n\nexports.clearImmediate = typeof clearImmediate === \"function\" ? clearImmediate : function(id) {\n  delete immediateIds[id];\n};\n}).call(this)}).call(this,require(\"timers\").setImmediate,require(\"timers\").clearImmediate)\n},{\"process/browser.js\":10,\"timers\":12}]},{},[1]);\n", "// extracted by mini-css-extract-plugin\nexport {};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// jKanban Board - Vanilla Javascript plugin for manage kanban boards: https://github.com/riktar/jkanban\r\n\r\nrequire('jkanban/dist/jkanban.js');\r\n\r\nrequire('./jkanban.scss');\r\n"], "sourceRoot": ""}