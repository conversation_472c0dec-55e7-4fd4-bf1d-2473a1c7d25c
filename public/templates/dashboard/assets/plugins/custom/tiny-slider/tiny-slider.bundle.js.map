{"version": 3, "sources": ["webpack://keenthemes/./node_modules/tiny-slider/src/helpers/addCSSRule.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/addClass.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/addEvents.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/arrayFromNodeList.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/caf.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/calc.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/checkStorageValue.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/classListSupport.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/createStyleSheet.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/docElement.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/events.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/extend.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/forEach.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getAttr.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getBody.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getCssRulesLength.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getEndProperty.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getSlideId.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/getTouchDirection.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/has3DTransforms.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/hasAttr.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/hasClass.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/hideElement.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/isNodeList.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/isVisible.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/jsTransform.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/mediaquerySupport.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/passiveOption.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/percentageLayout.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/raf.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/removeAttrs.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/removeCSSRule.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/removeClass.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/removeEvents.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/resetFakeBody.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/setAttrs.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/setFakeBody.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/setLocalStorage.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/showElement.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/toDegree.js", "webpack://keenthemes/./node_modules/tiny-slider/src/helpers/whichProperty.js", "webpack://keenthemes/./node_modules/tiny-slider/src/tiny-slider.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/webpack/runtime/define property getters", "webpack://keenthemes/webpack/runtime/hasOwnProperty shorthand", "webpack://keenthemes/webpack/runtime/make namespace object", "webpack://keenthemes/./webpack/plugins/custom/tiny-slider/tiny-slider.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAC+B;AACxB;AACP;AACA;AACA,oCAAoC,cAAc;AAClD;AACA,MAAM;AACN,C;;;;;;;;;;;;;;;;ACR2D;AAC3D,eAAe,0DAAgB;AAC/B;AACA,WAAW,sDAAQ,aAAa,uBAAuB;AACvD,KAAK;AACL;AACA,WAAW,sDAAQ,aAAa,2BAA2B;AAC3D;;;;;;;;;;;;;;;;;;ACPmD;;AAE5C;AACP;AACA,uFAAuF,4DAAa;AACpG;AACA;AACA,C;;;;;;;;;;;;;;;ACPO;AACP;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA,C;;;;;;;;;;;;;;;ACNA;;AAEO;AACP;AACA,kBAAkB,kBAAkB;;;;;;;;;;;;;;;;;;;ACJpC;AACA;AACA,iC;AACuC;AACQ;AACI;;AAE5C;AACP;AACA,aAAa,oDAAO;AACpB,oBAAoB,4DAAW;AAC/B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B;AACA;AACA,oC;AACA,sC;AACA;AACA;AACA;AACA,GAAG;;AAEH,cAAc,gEAAa;;AAE3B;AACA,C;;;;;;;;;;;;;;;AChCO;AACP;AACA,C;;;;;;;;;;;;;;;ACFO,kE;;;;;;;;;;;;;;;ACAP;AACO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,oCAAoC;;AAElD;AACA,cAAc,oCAAoC;;AAElD;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;;;;;;;;;ACrBO,0C;;;;;;;;;;;;;;;ACAA;AACP;AACA,cAAc;AACd;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,uBAAuB,mCAAmC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,E;;;;;;;;;;;;;;;AC1BO;AACP;AACA,iCAAiC;AACjC;AACA;;AAEA,QAAQ,YAAY;AACpB;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,C;;;;;;;;;;;;;;;ACpBA;AACO;AACP,iCAAiC,OAAO;AACxC;AACA;AACA,C;;;;;;;;;;;;;;;ACLO;AACP;AACA,C;;;;;;;;;;;;;;;ACFO;AACP;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,C;;;;;;;;;;;;;;;ACVO;AACP;AACA;AACA,C;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA,C;;;;;;;;;;;;;;;ACdO;AACP;AACA;;AAEA;AACA,C;;;;;;;;;;;;;;;ACLO;AACP;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA,C;;;;;;;;;;;;;;;;;;ACXuC;AACQ;AACI;;AAE5C;AACP,YAAY,cAAc;AAC1B,iCAAiC,cAAc;;AAE/C;AACA,aAAa,oDAAO;AACpB,oBAAoB,4DAAW;AAC/B;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA,cAAc,gEAAa;;AAE3B;AACA;;;;;;;;;;;;;;;;AC1BO;AACP;AACA,C;;;;;;;;;;;;;;;;;ACFyD;;AAEzD,eAAe,kEAAgB;AAC/B,wBAAwB,mCAAmC,EAAE;AAC7D,wBAAwB,uCAAuC;;;;;;;;;;;;;;;;;ACJxD;AACP,oCAAoC,2BAA2B;AAC/D,C;;;;;;;;;;;;;;;ACFO;AACP;AACA,wC;AACA,C;;;;;;;;;;;;;;;ACHO;AACP;AACA,C;;;;;;;;;;;;;;;ACFO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uB;AACA,oC;AACA,KAAK;AACL;AACA;AACA;AACA,C;;;;;;;;;;;;;;;;;;ACnBuC;AACQ;AACI;;AAE5C;AACP;AACA;AACA;;AAEA;AACA,aAAa,oDAAO;AACpB,oBAAoB,4DAAW;AAC/B;AACA;AACA,6CAA6C,aAAa,mBAAmB;AAC7E;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;AAEA,cAAc,gEAAa;;AAE3B;AACA;;;;;;;;;;;;;;;;AClCA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACM,uCAAuC,gBAAgB,S;;;;;;;;;;;;;;;;;;ACV9D;AACA;AACuC;AACQ;AACI;;AAE5C;AACP;AACA;AACA,aAAa,oDAAO;AACpB,oBAAoB,4DAAW;AAC/B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,iBAAiB,WAAW;AAC5B;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,cAAc,gEAAa;;AAE3B;AACA,C;;;;;;;;;;;;;;;AClCA;;AAEO;AACP;AACA;AACA;AACA,mBAAmB,2BAA2B;;;;;;;;;;;;;;;;;ACND;;AAEtC;AACP,SAAS,0DAAU;AACnB;;AAEA;AACA,0BAA0B,KAAK;AAC/B,4BAA4B,KAAK;AACjC;AACA;AACA;AACA,C;;;;;;;;;;;;;;;;ACZA;AAC+B;AACxB;AACP;AACA;AACA;AACA;AACA,MAAM;AACN,C;;;;;;;;;;;;;;;;ACR2D;AAC3D,kBAAkB,0DAAgB;AAClC;AACA,UAAU,sDAAQ,aAAa,0BAA0B;AACzD,KAAK;AACL;AACA,UAAU,sDAAQ,YAAY,8CAA8C;AAC5E;;;;;;;;;;;;;;;;;;ACPmD;;AAE5C;AACP;AACA,kEAAkE,4DAAa;AAC/E;AACA;AACA,C;;;;;;;;;;;;;;;;ACP6C;;AAEtC;AACP;AACA;AACA,IAAI,qEAAyB;AAC7B;AACA;AACA,IAAI,mEAAuB;AAC3B;AACA,C;;;;;;;;;;;;;;;;ACV6C;;AAEtC;AACP,SAAS,0DAAU;AACnB,oEAAoE,QAAQ;;AAE5E,0BAA0B,KAAK;AAC/B;AACA;AACA;AACA;AACA,C;;;;;;;;;;;;;;;;ACX6C;;AAEtC;AACP;AACA;AACA,kBAAkB,qEAAyB;AAC3C;AACA;AACA;AACA,0BAA0B,qEAAyB;AACnD,IAAI,kEAAsB;AAC1B;;AAEA;AACA,C;;;;;;;;;;;;;;;ACdO;AACP;AACA,SAAS,6BAA6B,EAAE;AACxC;AACA;AACA,C;;;;;;;;;;;;;;;ACLO;AACP,oCAAoC,uBAAuB;AAC3D,C;;;;;;;;;;;;;;;ACFO;AACP;AACA,C;;;;;;;;;;;;;;;ACFO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA,uCAAuC,aAAa;AACpD;;AAEA,eAAe;AACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEuC;AACA;AACM;AACsB;AACJ;AACV;AACZ;AACwB;AACE;AACF;AACZ;AACM;AACQ;AAClB;AACkB;AACpB;AACE;AACA;AACM;AACR;AACA;AACE;AACM;AACY;AACZ;AACA;AACJ;AACQ;AACI;AACF;AACV;AACM;AACZ;AACU;;AAEhD;AACP,YAAY,0DAAM;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,eAAe;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,gCAAgC,iBAAiB;AACjD,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,iHAAiH,6BAA6B,EAAE;AAChJ;AACA;AACA;AACA;AACA;;AAEA,gCAAgC,gFAAiB,qBAAqB,4EAAe,mBAAmB,sDAAI;AAC5G,6CAA6C,gFAAiB,sBAAsB,4EAAe,oBAAoB,8EAAgB;AACvI,kCAAkC,gFAAiB,sBAAsB,4EAAe,oBAAoB,gFAAiB;AAC7H,sCAAsC,gFAAiB,sBAAsB,4EAAe,oBAAoB,yEAAa;AAC7H,4CAA4C,gFAAiB,sBAAsB,4EAAe,oBAAoB,6EAAe;AACrI,gDAAgD,gFAAiB,uBAAuB,4EAAe,qBAAqB,yEAAa;AACzI,6CAA6C,gFAAiB,uBAAuB,4EAAe,qBAAqB,yEAAa;AACtI,+CAA+C,gFAAiB,uBAAuB,4EAAe,qBAAqB,yEAAa;AACxI,4CAA4C,gFAAiB,uBAAuB,4EAAe,qBAAqB,yEAAa;AACrI,0CAA0C,gFAAiB,sBAAsB,4EAAe,oBAAoB,2EAAc;AAClI,yCAAyC,gFAAiB,sBAAsB,4EAAe,oBAAoB,2EAAc;;AAEjI;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP,iCAAiC,4CAA4C;AAC7E;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,6BAA6B,uDAAuD;AACpF;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,0DAAM;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,WAAW;AAClD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gCAAgC,mBAAmB;AACnD,oCAAoC,kBAAkB;AACtD,mCAAmC,kBAAkB;AACrD;;AAEA;AACA,iCAAiC,yBAAyB;AAC1D;AACA;AACA,kBAAkB,wBAAwB;;;AAG1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC,iBAAiB,qCAAqC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,8EAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,8FAA8F;AAC3H,SAAS;AACT;AACA,2BAA2B,mBAAmB;AAC9C,yDAAyD,UAAU;AACnE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,uDAAM;AACzB;AACA;AACA,gCAAgC,kEAAU;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP,yBAAyB,uCAAuC;AAChE,6BAA6B,6BAA6B;AAC1D;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,kBAAkB,EAAE;AAC9C,0BAA0B,kBAAkB;AAC5C;;AAEA;AACA,mBAAmB,6CAA6C;;AAEhE;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA,iBAAiB,oEAAoE;AACrF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB,mBAAmB;AACxC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,WAAW;;AAE/B,mBAAmB,iBAAiB;AACpC,mBAAmB,iBAAiB;;AAEpC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD,oBAAoB;;AAEtE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA,oDAAoD,wBAAwB;;AAE5E;AACA;AACA;AACA,wDAAwD,eAAe;AACvE;AACA;;AAEA,wBAAwB,aAAa;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qCAAqC,aAAa;AAClD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qBAAqB,kBAAkB;;AAEvC;AACA;;AAEA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,yCAAyC,+BAA+B;AACxE;AACA;AACA;;AAEA,oDAAoD,6BAA6B;AACjF,kEAAkE,6BAA6B;;AAE/F;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA,4DAA4D;AAC5D,4DAA4D;AAC5D,KAAK;AACL;AACA;AACA,mCAAmC;AACnC;;AAEA,sEAAsE,6CAA6C;AACnH;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL,sBAAsB,iCAAiC;AACvD;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,0CAA0C,yBAAyB;AACnE;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;;AAEA;AACA;;AAEA;AACA;AACA,iBAAiB,6BAA6B;;AAE9C;AACA;;AAEA;AACA,6FAA6F;AAC7F;;AAEA;AACA,2FAA2F;AAC3F;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8BAA8B,wBAAwB;AACtD;AACA;AACA,oBAAoB,yCAAyC;AAC7D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,IAAI,6DAAO;AACX,MAAM,+DAAQ;AACd,qBAAqB,iCAAiC;AACtD,uCAAuC,CAAC,+DAAQ,sBAAsB;AACtE,MAAM,+DAAQ;AACd;AACA;AACA,OAAO;AACP,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,KAAK;AACnC;AACA;AACA,QAAQ,+DAAQ;AAChB,QAAQ,qEAAW;AACnB;;AAEA;AACA;AACA,UAAU,+DAAQ;AAClB,UAAU,qEAAW;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,MAAM,6DAAO;AACb;;AAEA;AACA;AACA;AACA;AACA,YAAY,iEAAS;AACrB,YAAY,+DAAQ;;AAEpB;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,OAAO;;AAEP;AACA,MAAM,oDAAG,YAAY,iBAAiB,iFAAiB,oBAAoB,qBAAqB,EAAE,EAAE,EAAE;;AAEtG;AACA,oCAAoC,6EAA6E;;AAEjH,mDAAmD,oDAAG,YAAY,iBAAiB,iFAAiB,uCAAuC,EAAE;;AAE7I,KAAK;AACL;AACA,qBAAqB,8BAA8B;;AAEnD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,gCAAgC,2BAA2B;AAC3D,OAAO;;AAEP,KAAK;AACL;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA,wBAAwB,sBAAsB;AAC9C,iCAAiC;AACjC;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA,mBAAmB,8BAA8B;;AAEjD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kEAAkE,OAAO;AACzE;AACA;AACA,QAAQ,+DAAQ;AAChB,QAAQ,qEAAW;AACnB;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,mEAAU,wGAAwG,GAAG,iFAAiB;AAC9I,QAAQ,mEAAU,oCAAoC,GAAG,iFAAiB;AAC1E,OAAO;AACP,QAAQ,6DAAO;AACf;AACA,SAAS;AACT;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA,QAAQ,mEAAU,oCAAoC,iFAAiB;AACvE;;AAEA;AACA;AACA,MAAM,mEAAU,oCAAoC,iFAAiB;;AAErE;AACA;AACA,6HAA6H;AAC7H,iCAAiC,0CAA0C;AAC3E,QAAQ,mEAAU,4BAA4B,iFAAiB;AAC/D;;AAEA;AACA;AACA,2BAA2B,4CAA4C;AACvE;AACA;AACA,iCAAiC,0CAA0C;AAC3E,gCAAgC,yCAAyC;AACzE;AACA,gBAAgB,CAAC,mEAAU,6CAA6C,iFAAiB,SAAS;;AAElG;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,oCAAoC;;AAEvD;AACA,gBAAgB,CAAC,mEAAU,6CAA6C,iFAAiB,SAAS;AAClG;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kDAAkD,4CAA4C;AAC9F;;AAEA;AACA;AACA,iDAAiD,4FAA4F;AAC7I;;AAEA;AACA;AACA,2FAA2F;AAC3F;AACA;AACA;AACA;AACA;AACA,2CAA2C,qBAAqB;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,iDAAiD;AACpF,kCAAkC,gDAAgD;AAClF;AACA,uBAAuB,0CAA0C,iBAAiB,EAAE;;AAEpF;AACA;;AAEA;AACA,mEAAmE,YAAY;AAC/E;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAQ,+DAAQ,kBAAkB,mBAAmB;AACrD,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA,QAAQ,iEAAS,kBAAkB,wBAAwB;AAC3D;;AAEA;AACA;AACA,iCAAiC,CAAC,iEAAS,yBAAyB;AACpE,wCAAwC,CAAC,iEAAS,6BAA6B;AAC/E;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,+DAAQ,gBAAgB,oCAAoC;AACpE;AACA,QAAQ,6DAAO;AACf,UAAU,+DAAQ;AAClB;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;;AAET;AACA,OAAO;AACP;AACA;AACA,uBAAuB,gBAAgB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,QAAQ,mEAAU,sDAAsD,iFAAiB;AACzF;;AAEA,MAAM,+DAAQ,6BAA6B,6DAA6D;AACxG,MAAM,qEAAW;AACjB,MAAM,+DAAQ;;AAEd;AACA,MAAM,iEAAS;AACf;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,QAAQ,+DAAQ;AAChB;AACA;AACA,SAAS;AACT;;AAEA;AACA,QAAQ,+DAAQ;AAChB;AACA;AACA,SAAS;AACT;;AAEA;AACA,QAAQ,+DAAQ,cAAc,yBAAyB;AACvD,QAAQ,+DAAQ,cAAc,yBAAyB;AACvD;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,QAAQ,iEAAS;AACjB,OAAO;AACP,QAAQ,iEAAS;AACjB,QAAQ,iEAAS;AACjB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM,iEAAS;AACf;;AAEA,gBAAgB,CAAC,iEAAS,uDAAuD;AACjF,oBAAoB,CAAC,iEAAS,wBAAwB;AACtD,oBAAoB,CAAC,iEAAS,2BAA2B;;AAEzD;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL,MAAM,iEAAS,OAAO,mBAAmB;AACzC;;AAEA;AACA;AACA;AACA,OAAO,qBAAqB,gBAAgB;AAC5C;;AAEA;AACA,kBAAkB,iBAAiB,EAAE,mBAAmB,gBAAgB;;AAExE;AACA,6BAA6B,oCAAoC;AACjE,uCAAuC,gBAAgB;AACvD;AACA;;AAEA;AACA;AACA;AACA,0BAA0B,0BAA0B;;AAEpD;AACA,IAAI,uEAAY,OAAO,mBAAmB;;AAE1C;AACA,oBAAoB,CAAC,uEAAY,2BAA2B;AAC5D,4BAA4B,CAAC,uEAAY,oCAAoC;AAC7E,uBAAuB,CAAC,uEAAY,0BAA0B;;AAE9D;AACA,IAAI,uEAAY;AAChB,IAAI,uEAAY;AAChB,yBAAyB,CAAC,uEAAY,kBAAkB,wBAAwB,EAAE;AAClF,mBAAmB,8BAA8B;;AAEjD;AACA;AACA;AACA;AACA,MAAM,uEAAY;AAClB;AACA,gBAAgB,CAAC,uEAAY,yBAAyB;AACtD,oBAAoB,CAAC,uEAAY,wBAAwB;;AAEzD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA,0kDAA0kD,qBAAqB,mBAAmB,EAAE,EAAE;;AAEtnD;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,oDAAG,YAAY,0BAA0B,EAAE;AAC/C;;AAEA;AACA,gBAAgB,QAAQ;AACxB,6BAA6B,sCAAsC;AACnE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gCAAgC,+BAA+B,EAAE;AACjE,sBAAsB,4CAA4C;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC;AAClC;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,+BAA+B;AAC/B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,wCAAwC,0BAA0B,EAAE;AACpE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA,gDAAgD;AAChD,oBAAoB,wDAAwD;;AAE5E;AACA;AACA,QAAQ,iEAAS;AACjB,QAAQ,uEAAY;AACpB;AACA;AACA;AACA;AACA,UAAU,qEAAW;AACrB,SAAS;AACT,2BAA2B,CAAC,qEAAW,aAAa;AACpD,2BAA2B,CAAC,qEAAW,aAAa;AACpD;AACA,OAAO;AACP;AACA,UAAU,qEAAW;AACrB,SAAS;AACT,2BAA2B,CAAC,qEAAW,aAAa;AACpD,2BAA2B,CAAC,qEAAW,aAAa;AACpD;AACA;AACA;AACA;AACA;AACA,QAAQ,qEAAW;AACnB;AACA,OAAO;AACP,QAAQ,qEAAW;AACnB;AACA;AACA;AACA;AACA,QAAQ,iEAAS;AACjB,QAAQ,uEAAY;AACpB;AACA;AACA;AACA,QAAQ,iEAAS;AACjB,QAAQ,uEAAY;AACpB;AACA;AACA;AACA,6BAA6B,CAAC,qEAAW,iBAAiB;AAC1D,gDAAgD,iBAAiB;AACjE,OAAO;AACP,6BAA6B,CAAC,qEAAW,iBAAiB;AAC1D,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA,QAAQ,iEAAS;AACjB,QAAQ,uEAAY;AACpB;AACA;AACA;AACA,QAAQ,iEAAS;AACjB,QAAQ,uEAAY;AACpB;;AAEA;AACA,iEAAiE,+BAA+B;;AAEhG;AACA,0BAA0B,gCAAgC;AAC1D;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,gDAAgD,+BAA+B;AAC/E;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,wBAAwB,qBAAqB;AAC7C,KAAK;AACL;AACA;AACA;AACA;;AAEA,oCAAoC,+BAA+B;;AAEnE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU,yEAAa,QAAQ,iFAAiB;AAChD,UAAU,mEAAU,6CAA6C,iFAAiB;AAClF;AACA;;AAEA;AACA,uBAAuB,gBAAgB;;AAEvC;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,0CAA0C;AAC9D;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB;AACxB,8BAA8B,qBAAqB;AACnD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,4DAA4D,gBAAgB;AAC5E;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,oDAAoD,qBAAqB;AACzE,WAAW;AACX,oDAAoD,qBAAqB;AACzE;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,sCAAsC,CAAC,qEAAW,iBAAiB;AACnE,+BAA+B,CAAC,qEAAW,eAAe;AAC1D;AACA;AACA,QAAQ,qEAAW;AACnB,OAAO;AACP,yBAAyB,CAAC,qEAAW,aAAa;AAClD,yBAAyB,CAAC,qEAAW,aAAa;AAClD;AACA;AACA;;AAEA;AACA,qCAAqC,CAAC,qEAAW,iBAAiB;AAClE,8BAA8B,CAAC,qEAAW,eAAe;AACzD;AACA;AACA,QAAQ,qEAAW;AACnB,OAAO;AACP,yBAAyB,CAAC,qEAAW,aAAa;AAClD,yBAAyB,CAAC,qEAAW,aAAa;AAClD;AACA;AACA;;AAEA;AACA,iBAAiB,QAAQ;;AAEzB;AACA,sBAAsB,mCAAmC;;AAEzD;AACA;AACA;AACA,8BAA8B,KAAK;AACnC,uBAAuB,CAAC,+DAAQ,qBAAqB;AACrD,QAAQ,+DAAQ;AAChB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB,QAAQ;;AAE1B;AACA;AACA,+BAA+B,gCAAgC;;AAE/D;AACA;AACA;AACA,8BAA8B,KAAK;AACnC,uBAAuB,CAAC,qEAAW,qBAAqB;AACxD,QAAQ,qEAAW;AACnB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,mBAAmB,QAAQ;;AAE3B;AACA;AACA,IAAI,qEAAW;AACf;AACA,8BAA8B,KAAK;AACnC,uBAAuB,CAAC,qEAAW,gBAAgB;AACnD,QAAQ,qEAAW;AACnB;AACA;;AAEA;AACA,mCAAmC,CAAC,qEAAW,0BAA0B;;AAEzE;AACA;AACA,iDAAiD,OAAO;AACxD;AACA,QAAQ,qEAAW;AACnB,QAAQ,qEAAW;AACnB,QAAQ,qEAAW;AACnB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB,QAAQ;;AAE5B;AACA;AACA;;AAEA;AACA,8BAA8B,KAAK;AACnC,uBAAuB,CAAC,qEAAW,gBAAgB;AACnD,QAAQ,qEAAW;AACnB;AACA;;AAEA;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA,QAAQ,+DAAQ;AAChB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,8CAA8C,mCAAmC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,oCAAoC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qEAAqE,WAAW;AAChF,wCAAwC,SAAS;AACjD;AACA,OAAO;;AAEP;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,aAAa,+DAAQ;AACrB;AACA;AACA,6CAA6C,qBAAqB;AAClE,UAAU,iEAAS;;AAEnB,UAAU,iEAAS;;AAEnB;AACA,oBAAoB,6DAAO;;AAE3B;AACA,uBAAuB,6DAAO;AAC9B,uBAAuB,qBAAqB;;AAE5C,UAAU,+DAAQ;AAClB;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,IAAI,+DAAQ;AACZ;AACA;;AAEA;AACA,IAAI,+DAAQ;AACZ;AACA;;AAEA;AACA,IAAI,+DAAQ;AACZ,IAAI,qEAAW;AACf,IAAI,uEAAY;AAChB;;AAEA;AACA;AACA,uBAAuB,qBAAqB;;AAE5C;AACA,MAAM,6DAAO,kEAAkE,gBAAgB,EAAE;AACjG;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI,oDAAG,YAAY,iDAAiD,EAAE;AACtE;;AAEA;AACA;AACA,uBAAuB,aAAa;;AAEpC;AACA;AACA,sCAAsC,mBAAmB,EAAE;AAC3D,UAAU,+DAAQ,0BAA0B,uBAAuB;AACnE,KAAK;;AAEL;AACA,uBAAuB,aAAa;;AAEpC;AACA,IAAI,oDAAG,YAAY,2BAA2B,EAAE;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kFAAkF,OAAO;AACzF;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC,oCAAoC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI,6DAAO;AACX;AACA,cAAc,gEAAgE;AAC9E;AACA,oCAAoC,iEAAiE;AACrG,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA,IAAI,6DAAO;AACX;AACA;AACA,YAAY,6DAAO;AACnB,UAAU,qEAAW;AACrB,UAAU,+DAAQ;AAClB;AACA;AACA,OAAO;AACP,aAAa,6DAAO;AACpB,UAAU,+DAAQ;AAClB;AACA;AACA,WAAW;AACX,UAAU,qEAAW;AACrB;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA,+BAA+B,KAAK;AACpC;;AAEA;AACA;AACA,QAAQ,+DAAQ;;AAEhB;AACA,QAAQ,+DAAQ;AAChB,QAAQ,qEAAW;AACnB,OAAO;AACP;AACA,QAAQ,+DAAQ;AAChB,QAAQ,qEAAW;AACnB;;AAEA;AACA,MAAM,qEAAW;AACjB;;AAEA;AACA;AACA,MAAM,6DAAO;AACb,QAAQ,qEAAW;AACnB,OAAO;AACP,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,QAAQ,+DAAQ;AAChB;AACA;AACA,SAAS;AACT,QAAQ,qEAAW;;AAEnB,QAAQ,+DAAQ,cAAc,6DAA6D;AAC3F,QAAQ,qEAAW;AACnB,QAAQ,+DAAQ;;AAEhB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA,sCAAsC,QAAQ;;AAE9C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,6BAA6B,oCAAoC;AACjE;;AAEA;AACA;AACA;;AAEA;AACA,sBAAsB,aAAa;;AAEnC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB,YAAY;;AAEjC;AACA;;AAEA;AACA,sBAAsB,aAAa;;AAEnC;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C,OAAO;AACP;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA,2BAA2B,oCAAoC;;AAE/D;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,oCAAoC;AAC1D;AACA;;AAEA;AACA;AACA,gBAAgB,gCAAgC;;AAEhD,wBAAwB,OAAO;AAC/B;;AAEA;AACA,mBAAmB,mDAAmD;;AAEtE;AACA;AACA;AACA,MAAM,qEAAW;AACjB,MAAM,+DAAQ;;AAEd,kBAAkB,0BAA0B;AAC5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,iEAAS,cAAc,mBAAmB;;AAEnE,SAAS;AACT;AACA,UAAU,qEAAW;AACrB;;AAEA,0BAA0B,8BAA8B;AACxD,OAAO;AACP;AACA;;AAEA;AACA;AACA,QAAQ,uEAAY;AACpB,QAAQ,iEAAS;;AAEjB;AACA;;AAEA;AACA;AACA,0DAA0D,iEAAS,cAAc,mBAAmB;AACpG;AACA,GAAG;;AAEH;AACA,qCAAqC,eAAe;;AAEpD;AACA;AACA;AACA;AACA;AACA,uBAAuB,gBAAgB;;AAEvC;AACA,wEAAwE,gBAAgB;;AAExF;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uBAAuB,0BAA0B;AACjD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU,qEAAW;AACrB,UAAU,+DAAQ;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iCAAiC,oCAAoC;AACrE;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,iBAAiB,QAAQ;;AAEzB;AACA;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;AACA,KAAK;AACL;AACA,uCAAuC,QAAQ,EAAE,OAAO,mBAAmB;AAC3E;;AAEA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP,8CAA8C,qCAAqC;;AAEnF;AACA;AACA,mBAAmB,kEAAkE;;AAErF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,qCAAqC,QAAQ,EAAE,OAAO,mBAAmB;AACzE;AACA;;AAEA;AACA;AACA;;AAEA,4FAA4F,4BAA4B;;AAExH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB,2BAA2B;AACjD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qCAAqC,QAAQ,EAAE,OAAO,mBAAmB;AACzE;;AAEA;AACA;;AAEA;AACA,uCAAuC,6DAAO,uBAAuB,4BAA4B;AACjG,QAAQ,6DAAO;AACf,yCAAyC,6DAAO;AAChD;AACA;AACA;;AAEA;AACA,wBAAwB,gBAAgB;AACxC,wBAAwB;AACxB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI,+DAAQ,kBAAkB,sBAAsB;AACpD;AACA;;AAEA;AACA;AACA,yBAAyB,+CAA+C;AACxE;;AAEA;AACA;AACA,yBAAyB,gDAAgD;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,mCAAmC,wBAAwB;AAC3D,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,6DAAO,2BAA2B,QAAQ;;AAEnD;AACA;AACA,0BAA0B,6DAAO;;AAEjC;AACA;AACA,2BAA2B,kCAAkC;AAC7D,OAAO;AACP,mCAAmC,kCAAkC;AACrE,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,WAAW,iFAAiB,CAAC,+DAAQ;AACrC;;AAEA;AACA;AACA,qCAAqC,QAAQ,EAAE,OAAO,mBAAmB;AACzE;;AAEA,gCAAgC,qBAAqB;;AAErD;AACA;AACA,MAAM,oDAAG;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,YAAY,oDAAG,YAAY,cAAc,EAAE,EAAE;AACrE,OAAO;AACP,4CAA4C,oDAAoD;AAChG,oCAAoC,sBAAsB;AAC1D;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI,oDAAG;AACP,mBAAmB,YAAY,oDAAG,YAAY,cAAc,EAAE,EAAE;;AAEhE,wCAAwC,oDAAoD;AAC5F;AACA,8CAA8C,sBAAsB;;AAEpE;AACA,qBAAqB,kEAAkE;AACvF,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,oDAAG;AACX;AACA;AACA,qBAAqB,8BAA8B;AACnD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU,iEAAS,UAAU;AAC7B;AACA,YAAY,uEAAY,UAAU,sBAAsB;AACxD,YAAY;AACZ;;AAEA;AACA,qBAAqB,oDAAG;AACxB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,eAAe;AACf;AACA,eAAe;AACf;AACA;AACA;AACA,8DAA8D,YAAY;AAC1E;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kDAAkD,uBAAuB;AACzE,qBAAqB,6BAA6B;AAClD,iCAAiC,oBAAoB;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,QAAQ;;AAE1C;AACA;AACA;AACA,eAAe,iEAAW;;AAE1B;AACA;AACA;AACA,aAAa,iEAAW;AACxB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,0DAAM;AACvB;AACA;AACA;;;;;;;UC7sFA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,wCAAwC,yCAAyC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;;;;;;;ACNA;;AAEA,aAAa,8GAA6C", "file": "plugins/custom/tiny-slider/tiny-slider.bundle.js", "sourcesContent": ["// cross browsers addRule method\nimport { raf } from './raf.js';\nexport function addCSSRule(sheet, selector, rules, index) {\n  // return raf(function() {\n    'insertRule' in sheet ?\n      sheet.insertRule(selector + '{' + rules + '}', index) :\n      sheet.addRule(selector, rules, index);\n  // });\n}", "import { classListSupport, hasClass } from './hasClass.js';\nvar addClass = classListSupport ?\n    function (el, str) {\n      if (!hasClass(el,  str)) { el.classList.add(str); }\n    } :\n    function (el, str) {\n      if (!hasClass(el,  str)) { el.className += ' ' + str; }\n    };\n\nexport { addClass };", "import { passiveOption } from './passiveOption.js';\n\nexport function addEvents(el, obj, preventScrolling) {\n  for (var prop in obj) {\n    var option = ['touchstart', 'touchmove'].indexOf(prop) >= 0 && !preventScrolling ? passiveOption : false;\n    el.addEventListener(prop, obj[prop], option);\n  }\n}", "export function arrayFromNodeList (nl) {\n  var arr = [];\n  for (var i = 0, l = nl.length; i < l; i++) {\n    arr.push(nl[i]);\n  }\n  return arr;\n}", "var win = window;\n\nexport var caf = win.cancelAnimationFrame\n  || win.mozCancelAnimationFrame\n  || function(id){ clearTimeout(id); };\n", "// get css-calc \n// @return - false | calc | -webkit-calc | -moz-calc\n// @usage - var calc = getCalc(); \nimport { getBody } from './getBody.js';\nimport { setFakeBody } from './setFakeBody.js';\nimport { resetFakeBody } from './resetFakeBody.js';\n\nexport function calc() {\n  var doc = document, \n      body = getBody(),\n      docOverflow = setFakeBody(body),\n      div = doc.createElement('div'), \n      result = false;\n\n  body.appendChild(div);\n  try {\n    var str = '(10px * 10)',\n        vals = ['calc' + str, '-moz-calc' + str, '-webkit-calc' + str],\n        val;\n    for (var i = 0; i < 3; i++) {\n      val = vals[i];\n      div.style.width = val;\n      if (div.offsetWidth === 100) { \n        result = val.replace(str, ''); \n        break;\n      }\n    }\n  } catch (e) {}\n  \n  body.fake ? resetFakeBody(body, docOverflow) : div.remove();\n\n  return result;\n}", "export function checkStorageValue (value) {\n  return ['true', 'false'].indexOf(value) >= 0 ? JSON.parse(value) : value;\n}", "export var classListSupport = 'classList' in document.createElement('_');", "// create and append style sheet\nexport function createStyleSheet (media, nonce) {\n  // Create the <style> tag\n  var style = document.createElement(\"style\");\n  // style.setAttribute(\"type\", \"text/css\");\n\n  // Add a media (and/or media query) here if you'd like!\n  // style.setAttribute(\"media\", \"screen\")\n  // style.setAttribute(\"media\", \"only screen and (max-width : 1024px)\")\n  if (media) { style.setAttribute(\"media\", media); }\n\n  // Add nonce attribute for Content Security Policy\n  if (nonce) { style.setAttribute(\"nonce\", nonce); }\n\n  // WebKit hack :(\n  // style.appendChild(document.createTextNode(\"\"));\n\n  // Add the <style> element to the page\n  document.querySelector('head').appendChild(style);\n\n  return style.sheet ? style.sheet : style.styleSheet;\n};", "export var docElement = document.documentElement;", "export function Events() {\n  return {\n    topics: {},\n    on: function (eventName, fn) {\n      this.topics[eventName] = this.topics[eventName] || [];\n      this.topics[eventName].push(fn);\n    },\n    off: function(eventName, fn) {\n      if (this.topics[eventName]) {\n        for (var i = 0; i < this.topics[eventName].length; i++) {\n          if (this.topics[eventName][i] === fn) {\n            this.topics[eventName].splice(i, 1);\n            break;\n          }\n        }\n      }\n    },\n    emit: function (eventName, data) {\n      data.type = eventName;\n      if (this.topics[eventName]) {\n        this.topics[eventName].forEach(function(fn) {\n          fn(data, eventName);\n        });\n      }\n    }\n  };\n};", "export function extend() {\n  var obj, name, copy,\n      target = arguments[0] || {},\n      i = 1,\n      length = arguments.length;\n\n  for (; i < length; i++) {\n    if ((obj = arguments[i]) !== null) {\n      for (name in obj) {\n        copy = obj[name];\n\n        if (target === copy) {\n          continue;\n        } else if (copy !== undefined) {\n          target[name] = copy;\n        }\n      }\n    }\n  }\n  return target;\n}", "// https://toddmotto.com/ditch-the-array-foreach-call-nodelist-hack/\nexport function forEach (arr, callback, scope) {\n  for (var i = 0, l = arr.length; i < l; i++) {\n    callback.call(scope, arr[i], i);\n  }\n}", "export function getAttr(el, attr) {\n  return el.getAttribute(attr);\n}", "export function getBody () {\n  var doc = document,\n      body = doc.body;\n\n  if (!body) {\n    body = doc.createElement('body');\n    body.fake = true;\n  }\n\n  return body;\n}", "export function getCssRulesLength(sheet) {\n  var rule = ('insertRule' in sheet) ? sheet.cssRules : sheet.rules;\n  return rule.length;\n}", "// get transitionend, animationend based on transitionDuration\n// @propin: string\n// @propOut: string, first-letter uppercase\n// Usage: getEndProperty('WebkitTransitionDuration', 'Transition') => webkitTransitionEnd\nexport function getEndProperty(propIn, propOut) {\n  var endProp = false;\n  if (/^Webkit/.test(propIn)) {\n    endProp = 'webkit' + propOut + 'End';\n  } else if (/^O/.test(propIn)) {\n    endProp = 'o' + propOut + 'End';\n  } else if (propIn) {\n    endProp = propOut.toLowerCase() + 'end';\n  }\n  return endProp;\n}", "export function getSlideId() {\n  var id = window.tnsId;\n  window.tnsId = !id ? 1 : id + 1;\n  \n  return 'tns' + window.tnsId;\n}", "export function getTouchDirection(angle, range) {\n  var direction = false,\n      gap = Math.abs(90 - Math.abs(angle));\n      \n  if (gap >= 90 - range) {\n    direction = 'horizontal';\n  } else if (gap <= range) {\n    direction = 'vertical';\n  }\n\n  return direction;\n}", "import { getBody } from './getBody.js';\nimport { setFakeBody } from './setFakeBody.js';\nimport { resetFakeBody } from './resetFakeBody.js';\n\nexport function has3DTransforms(tf){\n  if (!tf) { return false; }\n  if (!window.getComputedStyle) { return false; }\n  \n  var doc = document,\n      body = getBody(),\n      docOverflow = setFakeBody(body),\n      el = doc.createElement('p'),\n      has3d,\n      cssTF = tf.length > 9 ? '-' + tf.slice(0, -9).toLowerCase() + '-' : '';\n\n  cssTF += 'transform';\n\n  // Add it to the body to get the computed style\n  body.insertBefore(el, null);\n\n  el.style[tf] = 'translate3d(1px,1px,1px)';\n  has3d = window.getComputedStyle(el).getPropertyValue(cssTF);\n\n  body.fake ? resetFakeBody(body, docOverflow) : el.remove();\n\n  return (has3d !== undefined && has3d.length > 0 && has3d !== \"none\");\n}\n", "export function hasAttr(el, attr) {\n  return el.hasAttribute(attr);\n}", "import { classListSupport } from './classListSupport.js';\n\nvar hasClass = classListSupport ?\n    function (el, str) { return el.classList.contains(str); } :\n    function (el, str) { return el.className.indexOf(str) >= 0; };\n\nexport { classListSupport, hasClass };", "export function hideElement(el, forceHide) {\n  if (el.style.display !== 'none') { el.style.display = 'none'; }\n}", "export function isNodeList(el) {\n  // Only NodeList has the \"item()\" function\n  return typeof el.item !== \"undefined\"; \n}", "export function isVisible(el) {\n  return window.getComputedStyle(el).display !== 'none';\n}", "export function jsTransform(element, attr, prefix, postfix, to, duration, callback) {\n  var tick = Math.min(duration, 10),\n      unit = (to.indexOf('%') >= 0) ? '%' : 'px',\n      to = to.replace(unit, ''),\n      from = Number(element.style[attr].replace(prefix, '').replace(postfix, '').replace(unit, '')),\n      positionTick = (to - from) / duration * tick,\n      running;\n\n  setTimeout(moveElement, tick);\n  function moveElement() {\n    duration -= tick;\n    from += positionTick;\n    element.style[attr] = prefix + from + unit + postfix;\n    if (duration > 0) { \n      setTimeout(moveElement, tick); \n    } else {\n      callback();\n    }\n  }\n}", "import { getBody } from './getBody.js';\nimport { setFakeBody } from './setFakeBody.js';\nimport { resetFakeBody } from './resetFakeBody.js';\n\nexport function mediaquerySupport () {\n  if (window.matchMedia || window.msMatchMedia) {\n    return true;\n  }\n  \n  var doc = document,\n      body = getBody(),\n      docOverflow = setFakeBody(body),\n      div = doc.createElement('div'),\n      style = doc.createElement('style'),\n      rule = '@media all and (min-width:1px){.tns-mq-test{position:absolute}}',\n      position;\n\n  style.type = 'text/css';\n  div.className = 'tns-mq-test';\n\n  body.appendChild(style);\n  body.appendChild(div);\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = rule;\n  } else {\n    style.appendChild(doc.createTextNode(rule));\n  }\n\n  position = window.getComputedStyle ? window.getComputedStyle(div).position : div.currentStyle['position'];\n\n  body.fake ? resetFakeBody(body, docOverflow) : div.remove();\n\n  return position === \"absolute\";\n}\n", "// Test via a getter in the options object to see if the passive property is accessed\nvar supportsPassive = false;\ntry {\n  var opts = Object.defineProperty({}, 'passive', {\n    get: function() {\n      supportsPassive = true;\n    }\n  });\n  window.addEventListener(\"test\", null, opts);\n} catch (e) {}\nexport var passiveOption = supportsPassive ? { passive: true } : false;", "// get subpixel support value\n// @return - boolean\nimport { getBody } from './getBody.js';\nimport { setFakeBody } from './setFakeBody.js';\nimport { resetFakeBody } from './resetFakeBody.js';\n\nexport function percentageLayout() {\n  // check subpixel layout supporting\n  var doc = document,\n      body = getBody(),\n      docOverflow = setFakeBody(body),\n      wrapper = doc.createElement('div'),\n      outer = doc.createElement('div'),\n      str = '',\n      count = 70,\n      perPage = 3,\n      supported = false;\n\n  wrapper.className = \"tns-t-subp2\";\n  outer.className = \"tns-t-ct\";\n\n  for (var i = 0; i < count; i++) {\n    str += '<div></div>';\n  }\n\n  outer.innerHTML = str;\n  wrapper.appendChild(outer);\n  body.appendChild(wrapper);\n\n  supported = Math.abs(wrapper.getBoundingClientRect().left - outer.children[count - perPage].getBoundingClientRect().left) < 2;\n\n  body.fake ? resetFakeBody(body, docOverflow) : wrapper.remove();\n\n  return supported;\n}", "var win = window;\n\nexport var raf = win.requestAnimationFrame\n  || win.webkitRequestAnimationFrame\n  || win.mozRequestAnimationFrame\n  || win.msRequestAnimationFrame\n  || function(cb) { return setTimeout(cb, 16); };\n", "import { isNodeList } from \"./isNodeList.js\";\n\nexport function removeAttrs(els, attrs) {\n  els = (isNodeList(els) || els instanceof Array) ? els : [els];\n  attrs = (attrs instanceof Array) ? attrs : [attrs];\n\n  var attrLength = attrs.length;\n  for (var i = els.length; i--;) {\n    for (var j = attrLength; j--;) {\n      els[i].removeAttribute(attrs[j]);\n    }\n  }\n}", "// cross browsers addRule method\nimport { raf } from './raf.js';\nexport function removeCSSRule(sheet, index) {\n  // return raf(function() {\n    'deleteRule' in sheet ?\n      sheet.deleteRule(index) :\n      sheet.removeRule(index);\n  // });\n}", "import { classListSupport, hasClass } from './hasClass.js';\nvar removeClass = classListSupport ?\n    function (el, str) {\n      if (hasClass(el,  str)) { el.classList.remove(str); }\n    } :\n    function (el, str) {\n      if (hasClass(el, str)) { el.className = el.className.replace(str, ''); }\n    };\n\nexport { removeClass };", "import { passiveOption } from './passiveOption.js';\n\nexport function removeEvents(el, obj) {\n  for (var prop in obj) {\n    var option = ['touchstart', 'touchmove'].indexOf(prop) >= 0 ? passiveOption : false;\n    el.removeEventListener(prop, obj[prop], option);\n  }\n}", "import { docElement } from './docElement.js';\n\nexport function resetFakeBody (body, docOverflow) {\n  if (body.fake) {\n    body.remove();\n    docElement.style.overflow = docOverflow;\n    // Trigger layout so kinetic scrolling isn't disabled in iOS6+\n    // eslint-disable-next-line\n    docElement.offsetHeight;\n  }\n}", "import { isNodeList } from \"./isNodeList.js\";\n\nexport function setAttrs(els, attrs) {\n  els = (isNodeList(els) || els instanceof Array) ? els : [els];\n  if (Object.prototype.toString.call(attrs) !== '[object Object]') { return; }\n\n  for (var i = els.length; i--;) {\n    for(var key in attrs) {\n      els[i].setAttribute(key, attrs[key]);\n    }\n  }\n}", "import { docElement } from './docElement.js';\n\nexport function setFakeBody (body) {\n  var docOverflow = '';\n  if (body.fake) {\n    docOverflow = docElement.style.overflow;\n    //avoid crashing IE8, if background image is used\n    body.style.background = '';\n    //Safari 5.13/5.1.4 OSX stops loading if ::-webkit-scrollbar is used and scrollbars are visible\n    body.style.overflow = docElement.style.overflow = 'hidden';\n    docElement.appendChild(body);\n  }\n\n  return docOverflow;\n}", "export function setLocalStorage(storage, key, value, access) {\n  if (access) {\n    try { storage.setItem(key, value); } catch (e) {}\n  }\n  return value;\n}", "export function showElement(el, forceHide) {\n  if (el.style.display === 'none') { el.style.display = ''; }\n}", "export function toDegree (y, x) {\n  return Math.atan2(y, x) * (180 / Math.PI);\n}", "export function whichProperty(props){\n  if (typeof props === 'string') {\n    var arr = [props],\n        Props = props.charAt(0).toUpperCase() + props.substr(1),\n        prefixes = ['Webkit', 'Moz', 'ms', 'O'];\n        \n    prefixes.forEach(function(prefix) {\n      if (prefix !== 'ms' || props === 'transform') {\n        arr.push(prefix + Props);\n      }\n    });\n\n    props = arr;\n  }\n\n  var el = document.createElement('fakeelement'),\n      len = props.length;\n  for(var i = 0; i < props.length; i++){\n    var prop = props[i];\n    if( el.style[prop] !== undefined ){ return prop; }\n  }\n\n  return false; // explicit for ie9-\n}\n", "// Object.keys\nif (!Object.keys) {\n  Object.keys = function(object) {\n    var keys = [];\n    for (var name in object) {\n      if (Object.prototype.hasOwnProperty.call(object, name)) {\n        keys.push(name);\n      }\n    }\n    return keys;\n  };\n}\n\n// ChildNode.remove\nif(!(\"remove\" in Element.prototype)){\n  Element.prototype.remove = function(){\n    if(this.parentNode) {\n      this.parentNode.removeChild(this);\n    }\n  };\n}\n\nimport { raf } from './helpers/raf.js';\nimport { caf } from './helpers/caf.js';\nimport { extend } from './helpers/extend.js';\nimport { checkStorageValue } from './helpers/checkStorageValue.js';\nimport { setLocalStorage } from './helpers/setLocalStorage.js';\nimport { getSlideId } from './helpers/getSlideId.js';\nimport { calc } from './helpers/calc.js';\nimport { percentageLayout } from './helpers/percentageLayout.js';\nimport { mediaquerySupport } from './helpers/mediaquerySupport.js';\nimport { createStyleSheet } from './helpers/createStyleSheet.js';\nimport { addCSSRule } from './helpers/addCSSRule.js';\nimport { removeCSSRule } from './helpers/removeCSSRule.js';\nimport { getCssRulesLength } from './helpers/getCssRulesLength.js';\nimport { toDegree } from './helpers/toDegree.js';\nimport { getTouchDirection } from './helpers/getTouchDirection.js';\nimport { forEach } from './helpers/forEach.js';\nimport { hasClass } from './helpers/hasClass.js';\nimport { addClass } from './helpers/addClass.js';\nimport { removeClass } from './helpers/removeClass.js';\nimport { hasAttr } from './helpers/hasAttr.js';\nimport { getAttr } from './helpers/getAttr.js';\nimport { setAttrs } from './helpers/setAttrs.js';\nimport { removeAttrs } from './helpers/removeAttrs.js';\nimport { arrayFromNodeList } from './helpers/arrayFromNodeList.js';\nimport { hideElement } from './helpers/hideElement.js';\nimport { showElement } from './helpers/showElement.js';\nimport { isVisible } from './helpers/isVisible.js';\nimport { whichProperty } from './helpers/whichProperty.js';\nimport { has3DTransforms } from './helpers/has3DTransforms.js';\nimport { getEndProperty } from './helpers/getEndProperty.js';\nimport { addEvents } from './helpers/addEvents.js';\nimport { removeEvents } from './helpers/removeEvents.js';\nimport { Events } from './helpers/events.js';\nimport { jsTransform } from './helpers/jsTransform.js';\n\nexport var tns = function(options) {\n  options = extend({\n    container: '.slider',\n    mode: 'carousel',\n    axis: 'horizontal',\n    items: 1,\n    gutter: 0,\n    edgePadding: 0,\n    fixedWidth: false,\n    autoWidth: false,\n    viewportMax: false,\n    slideBy: 1,\n    center: false,\n    controls: true,\n    controlsPosition: 'top',\n    controlsText: ['prev', 'next'],\n    controlsContainer: false,\n    prevButton: false,\n    nextButton: false,\n    nav: true,\n    navPosition: 'top',\n    navContainer: false,\n    navAsThumbnails: false,\n    arrowKeys: false,\n    speed: 300,\n    autoplay: false,\n    autoplayPosition: 'top',\n    autoplayTimeout: 5000,\n    autoplayDirection: 'forward',\n    autoplayText: ['start', 'stop'],\n    autoplayHoverPause: false,\n    autoplayButton: false,\n    autoplayButtonOutput: true,\n    autoplayResetOnVisibility: true,\n    animateIn: 'tns-fadeIn',\n    animateOut: 'tns-fadeOut',\n    animateNormal: 'tns-normal',\n    animateDelay: false,\n    loop: true,\n    rewind: false,\n    autoHeight: false,\n    responsive: false,\n    lazyload: false,\n    lazyloadSelector: '.tns-lazy-img',\n    touch: true,\n    mouseDrag: false,\n    swipeAngle: 15,\n    nested: false,\n    preventActionWhenRunning: false,\n    preventScrollOnTouch: false,\n    freezable: true,\n    onInit: false,\n    useLocalStorage: true,\n    nonce: false\n  }, options || {});\n\n  var doc = document,\n      win = window,\n      KEYS = {\n        ENTER: 13,\n        SPACE: 32,\n        LEFT: 37,\n        RIGHT: 39\n      },\n      tnsStorage = {},\n      localStorageAccess = options.useLocalStorage;\n\n  if (localStorageAccess) {\n    // check browser version and local storage access\n    var browserInfo = navigator.userAgent;\n    var uid = new Date;\n\n    try {\n      tnsStorage = win.localStorage;\n      if (tnsStorage) {\n        tnsStorage.setItem(uid, uid);\n        localStorageAccess = tnsStorage.getItem(uid) == uid;\n        tnsStorage.removeItem(uid);\n      } else {\n        localStorageAccess = false;\n      }\n      if (!localStorageAccess) { tnsStorage = {}; }\n    } catch(e) {\n      localStorageAccess = false;\n    }\n\n    if (localStorageAccess) {\n      // remove storage when browser version changes\n      if (tnsStorage['tnsApp'] && tnsStorage['tnsApp'] !== browserInfo) {\n        ['tC', 'tPL', 'tMQ', 'tTf', 't3D', 'tTDu', 'tTDe', 'tADu', 'tADe', 'tTE', 'tAE'].forEach(function(item) { tnsStorage.removeItem(item); });\n      }\n      // update browserInfo\n      localStorage['tnsApp'] = browserInfo;\n    }\n  }\n\n  var CALC = tnsStorage['tC'] ? checkStorageValue(tnsStorage['tC']) : setLocalStorage(tnsStorage, 'tC', calc(), localStorageAccess),\n      PERCENTAGELAYOUT = tnsStorage['tPL'] ? checkStorageValue(tnsStorage['tPL']) : setLocalStorage(tnsStorage, 'tPL', percentageLayout(), localStorageAccess),\n      CSSMQ = tnsStorage['tMQ'] ? checkStorageValue(tnsStorage['tMQ']) : setLocalStorage(tnsStorage, 'tMQ', mediaquerySupport(), localStorageAccess),\n      TRANSFORM = tnsStorage['tTf'] ? checkStorageValue(tnsStorage['tTf']) : setLocalStorage(tnsStorage, 'tTf', whichProperty('transform'), localStorageAccess),\n      HAS3DTRANSFORMS = tnsStorage['t3D'] ? checkStorageValue(tnsStorage['t3D']) : setLocalStorage(tnsStorage, 't3D', has3DTransforms(TRANSFORM), localStorageAccess),\n      TRANSITIONDURATION = tnsStorage['tTDu'] ? checkStorageValue(tnsStorage['tTDu']) : setLocalStorage(tnsStorage, 'tTDu', whichProperty('transitionDuration'), localStorageAccess),\n      TRANSITIONDELAY = tnsStorage['tTDe'] ? checkStorageValue(tnsStorage['tTDe']) : setLocalStorage(tnsStorage, 'tTDe', whichProperty('transitionDelay'), localStorageAccess),\n      ANIMATIONDURATION = tnsStorage['tADu'] ? checkStorageValue(tnsStorage['tADu']) : setLocalStorage(tnsStorage, 'tADu', whichProperty('animationDuration'), localStorageAccess),\n      ANIMATIONDELAY = tnsStorage['tADe'] ? checkStorageValue(tnsStorage['tADe']) : setLocalStorage(tnsStorage, 'tADe', whichProperty('animationDelay'), localStorageAccess),\n      TRANSITIONEND = tnsStorage['tTE'] ? checkStorageValue(tnsStorage['tTE']) : setLocalStorage(tnsStorage, 'tTE', getEndProperty(TRANSITIONDURATION, 'Transition'), localStorageAccess),\n      ANIMATIONEND = tnsStorage['tAE'] ? checkStorageValue(tnsStorage['tAE']) : setLocalStorage(tnsStorage, 'tAE', getEndProperty(ANIMATIONDURATION, 'Animation'), localStorageAccess);\n\n  // get element nodes from selectors\n  var supportConsoleWarn = win.console && typeof win.console.warn === \"function\",\n      tnsList = ['container', 'controlsContainer', 'prevButton', 'nextButton', 'navContainer', 'autoplayButton'],\n      optionsElements = {};\n\n  tnsList.forEach(function(item) {\n    if (typeof options[item] === 'string') {\n      var str = options[item],\n          el = doc.querySelector(str);\n      optionsElements[item] = str;\n\n      if (el && el.nodeName) {\n        options[item] = el;\n      } else {\n        if (supportConsoleWarn) { console.warn('Can\\'t find', options[item]); }\n        return;\n      }\n    }\n  });\n\n  // make sure at least 1 slide\n  if (options.container.children.length < 1) {\n    if (supportConsoleWarn) { console.warn('No slides found in', options.container); }\n    return;\n   }\n\n  // update options\n  var responsive = options.responsive,\n      nested = options.nested,\n      carousel = options.mode === 'carousel' ? true : false;\n\n  if (responsive) {\n    // apply responsive[0] to options and remove it\n    if (0 in responsive) {\n      options = extend(options, responsive[0]);\n      delete responsive[0];\n    }\n\n    var responsiveTem = {};\n    for (var key in responsive) {\n      var val = responsive[key];\n      // update responsive\n      // from: 300: 2\n      // to:\n      //   300: {\n      //     items: 2\n      //   }\n      val = typeof val === 'number' ? {items: val} : val;\n      responsiveTem[key] = val;\n    }\n    responsive = responsiveTem;\n    responsiveTem = null;\n  }\n\n  // update options\n  function updateOptions (obj) {\n    for (var key in obj) {\n      if (!carousel) {\n        if (key === 'slideBy') { obj[key] = 'page'; }\n        if (key === 'edgePadding') { obj[key] = false; }\n        if (key === 'autoHeight') { obj[key] = false; }\n      }\n\n      // update responsive options\n      if (key === 'responsive') { updateOptions(obj[key]); }\n    }\n  }\n  if (!carousel) { updateOptions(options); }\n\n\n  // === define and set variables ===\n  if (!carousel) {\n    options.axis = 'horizontal';\n    options.slideBy = 'page';\n    options.edgePadding = false;\n\n    var animateIn = options.animateIn,\n        animateOut = options.animateOut,\n        animateDelay = options.animateDelay,\n        animateNormal = options.animateNormal;\n  }\n\n  var horizontal = options.axis === 'horizontal' ? true : false,\n      outerWrapper = doc.createElement('div'),\n      innerWrapper = doc.createElement('div'),\n      middleWrapper,\n      container = options.container,\n      containerParent = container.parentNode,\n      containerHTML = container.outerHTML,\n      slideItems = container.children,\n      slideCount = slideItems.length,\n      breakpointZone,\n      windowWidth = getWindowWidth(),\n      isOn = false;\n  if (responsive) { setBreakpointZone(); }\n  if (carousel) { container.className += ' tns-vpfix'; }\n\n  // fixedWidth: viewport > rightBoundary > indexMax\n  var autoWidth = options.autoWidth,\n      fixedWidth = getOption('fixedWidth'),\n      edgePadding = getOption('edgePadding'),\n      gutter = getOption('gutter'),\n      viewport = getViewportWidth(),\n      center = getOption('center'),\n      items = !autoWidth ? Math.floor(getOption('items')) : 1,\n      slideBy = getOption('slideBy'),\n      viewportMax = options.viewportMax || options.fixedWidthViewportWidth,\n      arrowKeys = getOption('arrowKeys'),\n      speed = getOption('speed'),\n      rewind = options.rewind,\n      loop = rewind ? false : options.loop,\n      autoHeight = getOption('autoHeight'),\n      controls = getOption('controls'),\n      controlsText = getOption('controlsText'),\n      nav = getOption('nav'),\n      touch = getOption('touch'),\n      mouseDrag = getOption('mouseDrag'),\n      autoplay = getOption('autoplay'),\n      autoplayTimeout = getOption('autoplayTimeout'),\n      autoplayText = getOption('autoplayText'),\n      autoplayHoverPause = getOption('autoplayHoverPause'),\n      autoplayResetOnVisibility = getOption('autoplayResetOnVisibility'),\n      sheet = createStyleSheet(null, getOption('nonce')),\n      lazyload = options.lazyload,\n      lazyloadSelector = options.lazyloadSelector,\n      slidePositions, // collection of slide positions\n      slideItemsOut = [],\n      cloneCount = loop ? getCloneCountForLoop() : 0,\n      slideCountNew = !carousel ? slideCount + cloneCount : slideCount + cloneCount * 2,\n      hasRightDeadZone = (fixedWidth || autoWidth) && !loop ? true : false,\n      rightBoundary = fixedWidth ? getRightBoundary() : null,\n      updateIndexBeforeTransform = (!carousel || !loop) ? true : false,\n      // transform\n      transformAttr = horizontal ? 'left' : 'top',\n      transformPrefix = '',\n      transformPostfix = '',\n      // index\n      getIndexMax = (function () {\n        if (fixedWidth) {\n          return function() { return center && !loop ? slideCount - 1 : Math.ceil(- rightBoundary / (fixedWidth + gutter)); };\n        } else if (autoWidth) {\n          return function() {\n            for (var i = 0; i < slideCountNew; i++) {\n              if (slidePositions[i] >= - rightBoundary) { return i; }\n            }\n          };\n        } else {\n          return function() {\n            if (center && carousel && !loop) {\n              return slideCount - 1;\n            } else {\n              return loop || carousel ? Math.max(0, slideCountNew - Math.ceil(items)) : slideCountNew - 1;\n            }\n          };\n        }\n      })(),\n      index = getStartIndex(getOption('startIndex')),\n      indexCached = index,\n      displayIndex = getCurrentSlide(),\n      indexMin = 0,\n      indexMax = !autoWidth ? getIndexMax() : null,\n      // resize\n      resizeTimer,\n      preventActionWhenRunning = options.preventActionWhenRunning,\n      swipeAngle = options.swipeAngle,\n      moveDirectionExpected = swipeAngle ? '?' : true,\n      running = false,\n      onInit = options.onInit,\n      events = new Events(),\n      // id, class\n      newContainerClasses = ' tns-slider tns-' + options.mode,\n      slideId = container.id || getSlideId(),\n      disable = getOption('disable'),\n      disabled = false,\n      freezable = options.freezable,\n      freeze = freezable && !autoWidth ? getFreeze() : false,\n      frozen = false,\n      controlsEvents = {\n        'click': onControlsClick,\n        'keydown': onControlsKeydown\n      },\n      navEvents = {\n        'click': onNavClick,\n        'keydown': onNavKeydown\n      },\n      hoverEvents = {\n        'mouseover': mouseoverPause,\n        'mouseout': mouseoutRestart\n      },\n      visibilityEvent = {'visibilitychange': onVisibilityChange},\n      docmentKeydownEvent = {'keydown': onDocumentKeydown},\n      touchEvents = {\n        'touchstart': onPanStart,\n        'touchmove': onPanMove,\n        'touchend': onPanEnd,\n        'touchcancel': onPanEnd\n      }, dragEvents = {\n        'mousedown': onPanStart,\n        'mousemove': onPanMove,\n        'mouseup': onPanEnd,\n        'mouseleave': onPanEnd\n      },\n      hasControls = hasOption('controls'),\n      hasNav = hasOption('nav'),\n      navAsThumbnails = autoWidth ? true : options.navAsThumbnails,\n      hasAutoplay = hasOption('autoplay'),\n      hasTouch = hasOption('touch'),\n      hasMouseDrag = hasOption('mouseDrag'),\n      slideActiveClass = 'tns-slide-active',\n      slideClonedClass = 'tns-slide-cloned',\n      imgCompleteClass = 'tns-complete',\n      imgEvents = {\n        'load': onImgLoaded,\n        'error': onImgFailed\n      },\n      imgsComplete,\n      liveregionCurrent,\n      preventScroll = options.preventScrollOnTouch === 'force' ? true : false;\n\n  // controls\n  if (hasControls) {\n    var controlsContainer = options.controlsContainer,\n        controlsContainerHTML = options.controlsContainer ? options.controlsContainer.outerHTML : '',\n        prevButton = options.prevButton,\n        nextButton = options.nextButton,\n        prevButtonHTML = options.prevButton ? options.prevButton.outerHTML : '',\n        nextButtonHTML = options.nextButton ? options.nextButton.outerHTML : '',\n        prevIsButton,\n        nextIsButton;\n  }\n\n  // nav\n  if (hasNav) {\n    var navContainer = options.navContainer,\n        navContainerHTML = options.navContainer ? options.navContainer.outerHTML : '',\n        navItems,\n        pages = autoWidth ? slideCount : getPages(),\n        pagesCached = 0,\n        navClicked = -1,\n        navCurrentIndex = getCurrentNavIndex(),\n        navCurrentIndexCached = navCurrentIndex,\n        navActiveClass = 'tns-nav-active',\n        navStr = 'Carousel Page ',\n        navStrCurrent = ' (Current Slide)';\n  }\n\n  // autoplay\n  if (hasAutoplay) {\n    var autoplayDirection = options.autoplayDirection === 'forward' ? 1 : -1,\n        autoplayButton = options.autoplayButton,\n        autoplayButtonHTML = options.autoplayButton ? options.autoplayButton.outerHTML : '',\n        autoplayHtmlStrings = ['<span class=\\'tns-visually-hidden\\'>', ' animation</span>'],\n        autoplayTimer,\n        animating,\n        autoplayHoverPaused,\n        autoplayUserPaused,\n        autoplayVisibilityPaused;\n  }\n\n  if (hasTouch || hasMouseDrag) {\n    var initPosition = {},\n        lastPosition = {},\n        translateInit,\n        disX,\n        disY,\n        panStart = false,\n        rafIndex,\n        getDist = horizontal ?\n          function(a, b) { return a.x - b.x; } :\n          function(a, b) { return a.y - b.y; };\n  }\n\n  // disable slider when slidecount <= items\n  if (!autoWidth) { resetVariblesWhenDisable(disable || freeze); }\n\n  if (TRANSFORM) {\n    transformAttr = TRANSFORM;\n    transformPrefix = 'translate';\n\n    if (HAS3DTRANSFORMS) {\n      transformPrefix += horizontal ? '3d(' : '3d(0px, ';\n      transformPostfix = horizontal ? ', 0px, 0px)' : ', 0px)';\n    } else {\n      transformPrefix += horizontal ? 'X(' : 'Y(';\n      transformPostfix = ')';\n    }\n\n  }\n\n  if (carousel) { container.className = container.className.replace('tns-vpfix', ''); }\n  initStructure();\n  initSheet();\n  initSliderTransform();\n\n  // === COMMON FUNCTIONS === //\n  function resetVariblesWhenDisable (condition) {\n    if (condition) {\n      controls = nav = touch = mouseDrag = arrowKeys = autoplay = autoplayHoverPause = autoplayResetOnVisibility = false;\n    }\n  }\n\n  function getCurrentSlide () {\n    var tem = carousel ? index - cloneCount : index;\n    while (tem < 0) { tem += slideCount; }\n    return tem%slideCount + 1;\n  }\n\n  function getStartIndex (ind) {\n    ind = ind ? Math.max(0, Math.min(loop ? slideCount - 1 : slideCount - items, ind)) : 0;\n    return carousel ? ind + cloneCount : ind;\n  }\n\n  function getAbsIndex (i) {\n    if (i == null) { i = index; }\n\n    if (carousel) { i -= cloneCount; }\n    while (i < 0) { i += slideCount; }\n\n    return Math.floor(i%slideCount);\n  }\n\n  function getCurrentNavIndex () {\n    var absIndex = getAbsIndex(),\n        result;\n\n    result = navAsThumbnails ? absIndex :\n      fixedWidth || autoWidth ? Math.ceil((absIndex + 1) * pages / slideCount - 1) :\n          Math.floor(absIndex / items);\n\n    // set active nav to the last one when reaches the right edge\n    if (!loop && carousel && index === indexMax) { result = pages - 1; }\n\n    return result;\n  }\n\n  function getItemsMax () {\n    // fixedWidth or autoWidth while viewportMax is not available\n    if (autoWidth || (fixedWidth && !viewportMax)) {\n      return slideCount - 1;\n    // most cases\n    } else {\n      var str = fixedWidth ? 'fixedWidth' : 'items',\n          arr = [];\n\n      if (fixedWidth || options[str] < slideCount) { arr.push(options[str]); }\n\n      if (responsive) {\n        for (var bp in responsive) {\n          var tem = responsive[bp][str];\n          if (tem && (fixedWidth || tem < slideCount)) { arr.push(tem); }\n        }\n      }\n\n      if (!arr.length) { arr.push(0); }\n\n      return Math.ceil(fixedWidth ? viewportMax / Math.min.apply(null, arr) : Math.max.apply(null, arr));\n    }\n  }\n\n  function getCloneCountForLoop () {\n    var itemsMax = getItemsMax(),\n        result = carousel ? Math.ceil((itemsMax * 5 - slideCount)/2) : (itemsMax * 4 - slideCount);\n    result = Math.max(itemsMax, result);\n\n    return hasOption('edgePadding') ? result + 1 : result;\n  }\n\n  function getWindowWidth () {\n    return win.innerWidth || doc.documentElement.clientWidth || doc.body.clientWidth;\n  }\n\n  function getInsertPosition (pos) {\n    return pos === 'top' ? 'afterbegin' : 'beforeend';\n  }\n\n  function getClientWidth (el) {\n    if (el == null) { return; }\n    var div = doc.createElement('div'), rect, width;\n    el.appendChild(div);\n    rect = div.getBoundingClientRect();\n    width = rect.right - rect.left;\n    div.remove();\n    return width || getClientWidth(el.parentNode);\n  }\n\n  function getViewportWidth () {\n    var gap = edgePadding ? edgePadding * 2 - gutter : 0;\n    return getClientWidth(containerParent) - gap;\n  }\n\n  function hasOption (item) {\n    if (options[item]) {\n      return true;\n    } else {\n      if (responsive) {\n        for (var bp in responsive) {\n          if (responsive[bp][item]) { return true; }\n        }\n      }\n      return false;\n    }\n  }\n\n  // get option:\n  // fixed width: viewport, fixedWidth, gutter => items\n  // others: window width => all variables\n  // all: items => slideBy\n  function getOption (item, ww) {\n    if (ww == null) { ww = windowWidth; }\n\n    if (item === 'items' && fixedWidth) {\n      return Math.floor((viewport + gutter) / (fixedWidth + gutter)) || 1;\n\n    } else {\n      var result = options[item];\n\n      if (responsive) {\n        for (var bp in responsive) {\n          // bp: convert string to number\n          if (ww >= parseInt(bp)) {\n            if (item in responsive[bp]) { result = responsive[bp][item]; }\n          }\n        }\n      }\n\n      if (item === 'slideBy' && result === 'page') { result = getOption('items'); }\n      if (!carousel && (item === 'slideBy' || item === 'items')) { result = Math.floor(result); }\n\n      return result;\n    }\n  }\n\n  function getSlideMarginLeft (i) {\n    return CALC ?\n      CALC + '(' + i * 100 + '% / ' + slideCountNew + ')' :\n      i * 100 / slideCountNew + '%';\n  }\n\n  function getInnerWrapperStyles (edgePaddingTem, gutterTem, fixedWidthTem, speedTem, autoHeightBP) {\n    var str = '';\n\n    if (edgePaddingTem !== undefined) {\n      var gap = edgePaddingTem;\n      if (gutterTem) { gap -= gutterTem; }\n      str = horizontal ?\n        'margin: 0 ' + gap + 'px 0 ' + edgePaddingTem + 'px;' :\n        'margin: ' + edgePaddingTem + 'px 0 ' + gap + 'px 0;';\n    } else if (gutterTem && !fixedWidthTem) {\n      var gutterTemUnit = '-' + gutterTem + 'px',\n          dir = horizontal ? gutterTemUnit + ' 0 0' : '0 ' + gutterTemUnit + ' 0';\n      str = 'margin: 0 ' + dir + ';'\n    }\n\n    if (!carousel && autoHeightBP && TRANSITIONDURATION && speedTem) { str += getTransitionDurationStyle(speedTem); }\n    return str;\n  }\n\n  function getContainerWidth (fixedWidthTem, gutterTem, itemsTem) {\n    if (fixedWidthTem) {\n      return (fixedWidthTem + gutterTem) * slideCountNew + 'px';\n    } else {\n      return CALC ?\n        CALC + '(' + slideCountNew * 100 + '% / ' + itemsTem + ')' :\n        slideCountNew * 100 / itemsTem + '%';\n    }\n  }\n\n  function getSlideWidthStyle (fixedWidthTem, gutterTem, itemsTem) {\n    var width;\n\n    if (fixedWidthTem) {\n      width = (fixedWidthTem + gutterTem) + 'px';\n    } else {\n      if (!carousel) { itemsTem = Math.floor(itemsTem); }\n      var dividend = carousel ? slideCountNew : itemsTem;\n      width = CALC ?\n        CALC + '(100% / ' + dividend + ')' :\n        100 / dividend + '%';\n    }\n\n    width = 'width:' + width;\n\n    // inner slider: overwrite outer slider styles\n    return nested !== 'inner' ? width + ';' : width + ' !important;';\n  }\n\n  function getSlideGutterStyle (gutterTem) {\n    var str = '';\n\n    // gutter maybe interger || 0\n    // so can't use 'if (gutter)'\n    if (gutterTem !== false) {\n      var prop = horizontal ? 'padding-' : 'margin-',\n          dir = horizontal ? 'right' : 'bottom';\n      str = prop +  dir + ': ' + gutterTem + 'px;';\n    }\n\n    return str;\n  }\n\n  function getCSSPrefix (name, num) {\n    var prefix = name.substring(0, name.length - num).toLowerCase();\n    if (prefix) { prefix = '-' + prefix + '-'; }\n\n    return prefix;\n  }\n\n  function getTransitionDurationStyle (speed) {\n    return getCSSPrefix(TRANSITIONDURATION, 18) + 'transition-duration:' + speed / 1000 + 's;';\n  }\n\n  function getAnimationDurationStyle (speed) {\n    return getCSSPrefix(ANIMATIONDURATION, 17) + 'animation-duration:' + speed / 1000 + 's;';\n  }\n\n  function initStructure () {\n    var classOuter = 'tns-outer',\n        classInner = 'tns-inner',\n        hasGutter = hasOption('gutter');\n\n    outerWrapper.className = classOuter;\n    innerWrapper.className = classInner;\n    outerWrapper.id = slideId + '-ow';\n    innerWrapper.id = slideId + '-iw';\n\n    // set container properties\n    if (container.id === '') { container.id = slideId; }\n    newContainerClasses += PERCENTAGELAYOUT || autoWidth ? ' tns-subpixel' : ' tns-no-subpixel';\n    newContainerClasses += CALC ? ' tns-calc' : ' tns-no-calc';\n    if (autoWidth) { newContainerClasses += ' tns-autowidth'; }\n    newContainerClasses += ' tns-' + options.axis;\n    container.className += newContainerClasses;\n\n    // add constrain layer for carousel\n    if (carousel) {\n      middleWrapper = doc.createElement('div');\n      middleWrapper.id = slideId + '-mw';\n      middleWrapper.className = 'tns-ovh';\n\n      outerWrapper.appendChild(middleWrapper);\n      middleWrapper.appendChild(innerWrapper);\n    } else {\n      outerWrapper.appendChild(innerWrapper);\n    }\n\n    if (autoHeight) {\n      var wp = middleWrapper ? middleWrapper : innerWrapper;\n      wp.className += ' tns-ah';\n    }\n\n    containerParent.insertBefore(outerWrapper, container);\n    innerWrapper.appendChild(container);\n\n    // add id, class, aria attributes\n    // before clone slides\n    forEach(slideItems, function(item, i) {\n      addClass(item, 'tns-item');\n      if (!item.id) { item.id = slideId + '-item' + i; }\n      if (!carousel && animateNormal) { addClass(item, animateNormal); }\n      setAttrs(item, {\n        'aria-hidden': 'true',\n        'tabindex': '-1'\n      });\n    });\n\n    // ## clone slides\n    // carousel: n + slides + n\n    // gallery:      slides + n\n    if (cloneCount) {\n      var fragmentBefore = doc.createDocumentFragment(),\n          fragmentAfter = doc.createDocumentFragment();\n\n      for (var j = cloneCount; j--;) {\n        var num = j%slideCount,\n            cloneFirst = slideItems[num].cloneNode(true);\n        addClass(cloneFirst, slideClonedClass);\n        removeAttrs(cloneFirst, 'id');\n        fragmentAfter.insertBefore(cloneFirst, fragmentAfter.firstChild);\n\n        if (carousel) {\n          var cloneLast = slideItems[slideCount - 1 - num].cloneNode(true);\n          addClass(cloneLast, slideClonedClass);\n          removeAttrs(cloneLast, 'id');\n          fragmentBefore.appendChild(cloneLast);\n        }\n      }\n\n      container.insertBefore(fragmentBefore, container.firstChild);\n      container.appendChild(fragmentAfter);\n      slideItems = container.children;\n    }\n\n  }\n\n  function initSliderTransform () {\n    // ## images loaded/failed\n    if (hasOption('autoHeight') || autoWidth || !horizontal) {\n      var imgs = container.querySelectorAll('img');\n\n      // add img load event listener\n      forEach(imgs, function(img) {\n        var src = img.src;\n\n        if (!lazyload) {\n          // not data img\n          if (src && src.indexOf('data:image') < 0) {\n            img.src = '';\n            addEvents(img, imgEvents);\n            addClass(img, 'loading');\n\n            img.src = src;\n          // data img\n          } else {\n            imgLoaded(img);\n          }\n        }\n      });\n\n      // set imgsComplete\n      raf(function(){ imgsLoadedCheck(arrayFromNodeList(imgs), function() { imgsComplete = true; }); });\n\n      // reset imgs for auto height: check visible imgs only\n      if (hasOption('autoHeight')) { imgs = getImageArray(index, Math.min(index + items - 1, slideCountNew - 1)); }\n\n      lazyload ? initSliderTransformStyleCheck() : raf(function(){ imgsLoadedCheck(arrayFromNodeList(imgs), initSliderTransformStyleCheck); });\n\n    } else {\n      // set container transform property\n      if (carousel) { doContainerTransformSilent(); }\n\n      // update slider tools and events\n      initTools();\n      initEvents();\n    }\n  }\n\n  function initSliderTransformStyleCheck () {\n    if (autoWidth && slideCount > 1) {\n      // check styles application\n      var num = loop ? index : slideCount - 1;\n\n      (function stylesApplicationCheck() {\n        var left = slideItems[num].getBoundingClientRect().left;\n        var right = slideItems[num - 1].getBoundingClientRect().right;\n\n        (Math.abs(left - right) <= 1) ?\n          initSliderTransformCore() :\n          setTimeout(function(){ stylesApplicationCheck() }, 16);\n      })();\n\n    } else {\n      initSliderTransformCore();\n    }\n  }\n\n\n  function initSliderTransformCore () {\n    // run Fn()s which are rely on image loading\n    if (!horizontal || autoWidth) {\n      setSlidePositions();\n\n      if (autoWidth) {\n        rightBoundary = getRightBoundary();\n        if (freezable) { freeze = getFreeze(); }\n        indexMax = getIndexMax(); // <= slidePositions, rightBoundary <=\n        resetVariblesWhenDisable(disable || freeze);\n      } else {\n        updateContentWrapperHeight();\n      }\n    }\n\n    // set container transform property\n    if (carousel) { doContainerTransformSilent(); }\n\n    // update slider tools and events\n    initTools();\n    initEvents();\n  }\n\n  function initSheet () {\n    // gallery:\n    // set animation classes and left value for gallery slider\n    if (!carousel) {\n      for (var i = index, l = index + Math.min(slideCount, items); i < l; i++) {\n        var item = slideItems[i];\n        item.style.left = (i - index) * 100 / items + '%';\n        addClass(item, animateIn);\n        removeClass(item, animateNormal);\n      }\n    }\n\n    // #### LAYOUT\n\n    // ## INLINE-BLOCK VS FLOAT\n\n    // ## PercentageLayout:\n    // slides: inline-block\n    // remove blank space between slides by set font-size: 0\n\n    // ## Non PercentageLayout:\n    // slides: float\n    //         margin-right: -100%\n    //         margin-left: ~\n\n    // Resource: https://docs.google.com/spreadsheets/d/147up245wwTXeQYve3BRSAD4oVcvQmuGsFteJOeA5xNQ/edit?usp=sharing\n    if (horizontal) {\n      if (PERCENTAGELAYOUT || autoWidth) {\n        addCSSRule(sheet, '#' + slideId + ' > .tns-item', 'font-size:' + win.getComputedStyle(slideItems[0]).fontSize + ';', getCssRulesLength(sheet));\n        addCSSRule(sheet, '#' + slideId, 'font-size:0;', getCssRulesLength(sheet));\n      } else if (carousel) {\n        forEach(slideItems, function (slide, i) {\n          slide.style.marginLeft = getSlideMarginLeft(i);\n        });\n      }\n    }\n\n\n    // ## BASIC STYLES\n    if (CSSMQ) {\n      // middle wrapper style\n      if (TRANSITIONDURATION) {\n        var str = middleWrapper && options.autoHeight ? getTransitionDurationStyle(options.speed) : '';\n        addCSSRule(sheet, '#' + slideId + '-mw', str, getCssRulesLength(sheet));\n      }\n\n      // inner wrapper styles\n      str = getInnerWrapperStyles(options.edgePadding, options.gutter, options.fixedWidth, options.speed, options.autoHeight);\n      addCSSRule(sheet, '#' + slideId + '-iw', str, getCssRulesLength(sheet));\n\n      // container styles\n      if (carousel) {\n        str = horizontal && !autoWidth ? 'width:' + getContainerWidth(options.fixedWidth, options.gutter, options.items) + ';' : '';\n        if (TRANSITIONDURATION) { str += getTransitionDurationStyle(speed); }\n        addCSSRule(sheet, '#' + slideId, str, getCssRulesLength(sheet));\n      }\n\n      // slide styles\n      str = horizontal && !autoWidth ? getSlideWidthStyle(options.fixedWidth, options.gutter, options.items) : '';\n      if (options.gutter) { str += getSlideGutterStyle(options.gutter); }\n      // set gallery items transition-duration\n      if (!carousel) {\n        if (TRANSITIONDURATION) { str += getTransitionDurationStyle(speed); }\n        if (ANIMATIONDURATION) { str += getAnimationDurationStyle(speed); }\n      }\n      if (str) { addCSSRule(sheet, '#' + slideId + ' > .tns-item', str, getCssRulesLength(sheet)); }\n\n    // non CSS mediaqueries: IE8\n    // ## update inner wrapper, container, slides if needed\n    // set inline styles for inner wrapper & container\n    // insert stylesheet (one line) for slides only (since slides are many)\n    } else {\n      // middle wrapper styles\n      update_carousel_transition_duration();\n\n      // inner wrapper styles\n      innerWrapper.style.cssText = getInnerWrapperStyles(edgePadding, gutter, fixedWidth, autoHeight);\n\n      // container styles\n      if (carousel && horizontal && !autoWidth) {\n        container.style.width = getContainerWidth(fixedWidth, gutter, items);\n      }\n\n      // slide styles\n      var str = horizontal && !autoWidth ? getSlideWidthStyle(fixedWidth, gutter, items) : '';\n      if (gutter) { str += getSlideGutterStyle(gutter); }\n\n      // append to the last line\n      if (str) { addCSSRule(sheet, '#' + slideId + ' > .tns-item', str, getCssRulesLength(sheet)); }\n    }\n\n    // ## MEDIAQUERIES\n    if (responsive && CSSMQ) {\n      for (var bp in responsive) {\n        // bp: convert string to number\n        bp = parseInt(bp);\n\n        var opts = responsive[bp],\n            str = '',\n            middleWrapperStr = '',\n            innerWrapperStr = '',\n            containerStr = '',\n            slideStr = '',\n            itemsBP = !autoWidth ? getOption('items', bp) : null,\n            fixedWidthBP = getOption('fixedWidth', bp),\n            speedBP = getOption('speed', bp),\n            edgePaddingBP = getOption('edgePadding', bp),\n            autoHeightBP = getOption('autoHeight', bp),\n            gutterBP = getOption('gutter', bp);\n\n        // middle wrapper string\n        if (TRANSITIONDURATION && middleWrapper && getOption('autoHeight', bp) && 'speed' in opts) {\n          middleWrapperStr = '#' + slideId + '-mw{' + getTransitionDurationStyle(speedBP) + '}';\n        }\n\n        // inner wrapper string\n        if ('edgePadding' in opts || 'gutter' in opts) {\n          innerWrapperStr = '#' + slideId + '-iw{' + getInnerWrapperStyles(edgePaddingBP, gutterBP, fixedWidthBP, speedBP, autoHeightBP) + '}';\n        }\n\n        // container string\n        if (carousel && horizontal && !autoWidth && ('fixedWidth' in opts || 'items' in opts || (fixedWidth && 'gutter' in opts))) {\n          containerStr = 'width:' + getContainerWidth(fixedWidthBP, gutterBP, itemsBP) + ';';\n        }\n        if (TRANSITIONDURATION && 'speed' in opts) {\n          containerStr += getTransitionDurationStyle(speedBP);\n        }\n        if (containerStr) {\n          containerStr = '#' + slideId + '{' + containerStr + '}';\n        }\n\n        // slide string\n        if ('fixedWidth' in opts || (fixedWidth && 'gutter' in opts) || !carousel && 'items' in opts) {\n          slideStr += getSlideWidthStyle(fixedWidthBP, gutterBP, itemsBP);\n        }\n        if ('gutter' in opts) {\n          slideStr += getSlideGutterStyle(gutterBP);\n        }\n        // set gallery items transition-duration\n        if (!carousel && 'speed' in opts) {\n          if (TRANSITIONDURATION) { slideStr += getTransitionDurationStyle(speedBP); }\n          if (ANIMATIONDURATION) { slideStr += getAnimationDurationStyle(speedBP); }\n        }\n        if (slideStr) { slideStr = '#' + slideId + ' > .tns-item{' + slideStr + '}'; }\n\n        // add up\n        str = middleWrapperStr + innerWrapperStr + containerStr + slideStr;\n\n        if (str) {\n          sheet.insertRule('@media (min-width: ' + bp / 16 + 'em) {' + str + '}', sheet.cssRules.length);\n        }\n      }\n    }\n  }\n\n  function initTools () {\n    // == slides ==\n    updateSlideStatus();\n\n    // == live region ==\n    outerWrapper.insertAdjacentHTML('afterbegin', '<div class=\"tns-liveregion tns-visually-hidden\" aria-live=\"polite\" aria-atomic=\"true\">slide <span class=\"current\">' + getLiveRegionStr() + '</span>  of ' + slideCount + '</div>');\n    liveregionCurrent = outerWrapper.querySelector('.tns-liveregion .current');\n\n    // == autoplayInit ==\n    if (hasAutoplay) {\n      var txt = autoplay ? 'stop' : 'start';\n      if (autoplayButton) {\n        setAttrs(autoplayButton, {'data-action': txt});\n      } else if (options.autoplayButtonOutput) {\n        outerWrapper.insertAdjacentHTML(getInsertPosition(options.autoplayPosition), '<button type=\"button\" data-action=\"' + txt + '\">' + autoplayHtmlStrings[0] + txt + autoplayHtmlStrings[1] + autoplayText[0] + '</button>');\n        autoplayButton = outerWrapper.querySelector('[data-action]');\n      }\n\n      // add event\n      if (autoplayButton) {\n        addEvents(autoplayButton, {'click': toggleAutoplay});\n      }\n\n      if (autoplay) {\n        startAutoplay();\n        if (autoplayHoverPause) { addEvents(container, hoverEvents); }\n        if (autoplayResetOnVisibility) { addEvents(container, visibilityEvent); }\n      }\n    }\n\n    // == navInit ==\n    if (hasNav) {\n      var initIndex = !carousel ? 0 : cloneCount;\n      // customized nav\n      // will not hide the navs in case they're thumbnails\n      if (navContainer) {\n        setAttrs(navContainer, {'aria-label': 'Carousel Pagination'});\n        navItems = navContainer.children;\n        forEach(navItems, function(item, i) {\n          setAttrs(item, {\n            'data-nav': i,\n            'tabindex': '-1',\n            'aria-label': navStr + (i + 1),\n            'aria-controls': slideId,\n          });\n        });\n\n      // generated nav\n      } else {\n        var navHtml = '',\n            hiddenStr = navAsThumbnails ? '' : 'style=\"display:none\"';\n        for (var i = 0; i < slideCount; i++) {\n          // hide nav items by default\n          navHtml += '<button type=\"button\" data-nav=\"' + i +'\" tabindex=\"-1\" aria-controls=\"' + slideId + '\" ' + hiddenStr + ' aria-label=\"' + navStr + (i + 1) +'\"></button>';\n        }\n        navHtml = '<div class=\"tns-nav\" aria-label=\"Carousel Pagination\">' + navHtml + '</div>';\n        outerWrapper.insertAdjacentHTML(getInsertPosition(options.navPosition), navHtml);\n\n        navContainer = outerWrapper.querySelector('.tns-nav');\n        navItems = navContainer.children;\n      }\n\n      updateNavVisibility();\n\n      // add transition\n      if (TRANSITIONDURATION) {\n        var prefix = TRANSITIONDURATION.substring(0, TRANSITIONDURATION.length - 18).toLowerCase(),\n            str = 'transition: all ' + speed / 1000 + 's';\n\n        if (prefix) {\n          str = '-' + prefix + '-' + str;\n        }\n\n        addCSSRule(sheet, '[aria-controls^=' + slideId + '-item]', str, getCssRulesLength(sheet));\n      }\n\n      setAttrs(navItems[navCurrentIndex], {'aria-label': navStr + (navCurrentIndex + 1) + navStrCurrent});\n      removeAttrs(navItems[navCurrentIndex], 'tabindex');\n      addClass(navItems[navCurrentIndex], navActiveClass);\n\n      // add events\n      addEvents(navContainer, navEvents);\n    }\n\n\n\n    // == controlsInit ==\n    if (hasControls) {\n      if (!controlsContainer && (!prevButton || !nextButton)) {\n        outerWrapper.insertAdjacentHTML(getInsertPosition(options.controlsPosition), '<div class=\"tns-controls\" aria-label=\"Carousel Navigation\" tabindex=\"0\"><button type=\"button\" data-controls=\"prev\" tabindex=\"-1\" aria-controls=\"' + slideId +'\">' + controlsText[0] + '</button><button type=\"button\" data-controls=\"next\" tabindex=\"-1\" aria-controls=\"' + slideId +'\">' + controlsText[1] + '</button></div>');\n\n        controlsContainer = outerWrapper.querySelector('.tns-controls');\n      }\n\n      if (!prevButton || !nextButton) {\n        prevButton = controlsContainer.children[0];\n        nextButton = controlsContainer.children[1];\n      }\n\n      if (options.controlsContainer) {\n        setAttrs(controlsContainer, {\n          'aria-label': 'Carousel Navigation',\n          'tabindex': '0'\n        });\n      }\n\n      if (options.controlsContainer || (options.prevButton && options.nextButton)) {\n        setAttrs([prevButton, nextButton], {\n          'aria-controls': slideId,\n          'tabindex': '-1',\n        });\n      }\n\n      if (options.controlsContainer || (options.prevButton && options.nextButton)) {\n        setAttrs(prevButton, {'data-controls' : 'prev'});\n        setAttrs(nextButton, {'data-controls' : 'next'});\n      }\n\n      prevIsButton = isButton(prevButton);\n      nextIsButton = isButton(nextButton);\n\n      updateControlsStatus();\n\n      // add events\n      if (controlsContainer) {\n        addEvents(controlsContainer, controlsEvents);\n      } else {\n        addEvents(prevButton, controlsEvents);\n        addEvents(nextButton, controlsEvents);\n      }\n    }\n\n    // hide tools if needed\n    disableUI();\n  }\n\n  function initEvents () {\n    // add events\n    if (carousel && TRANSITIONEND) {\n      var eve = {};\n      eve[TRANSITIONEND] = onTransitionEnd;\n      addEvents(container, eve);\n    }\n\n    if (touch) { addEvents(container, touchEvents, options.preventScrollOnTouch); }\n    if (mouseDrag) { addEvents(container, dragEvents); }\n    if (arrowKeys) { addEvents(doc, docmentKeydownEvent); }\n\n    if (nested === 'inner') {\n      events.on('outerResized', function () {\n        resizeTasks();\n        events.emit('innerLoaded', info());\n      });\n    } else if (responsive || fixedWidth || autoWidth || autoHeight || !horizontal) {\n      addEvents(win, {'resize': onResize});\n    }\n\n    if (autoHeight) {\n      if (nested === 'outer') {\n        events.on('innerLoaded', doAutoHeight);\n      } else if (!disable) { doAutoHeight(); }\n    }\n\n    doLazyLoad();\n    if (disable) { disableSlider(); } else if (freeze) { freezeSlider(); }\n\n    events.on('indexChanged', additionalUpdates);\n    if (nested === 'inner') { events.emit('innerLoaded', info()); }\n    if (typeof onInit === 'function') { onInit(info()); }\n    isOn = true;\n  }\n\n  function destroy () {\n    // sheet\n    sheet.disabled = true;\n    if (sheet.ownerNode) { sheet.ownerNode.remove(); }\n\n    // remove win event listeners\n    removeEvents(win, {'resize': onResize});\n\n    // arrowKeys, controls, nav\n    if (arrowKeys) { removeEvents(doc, docmentKeydownEvent); }\n    if (controlsContainer) { removeEvents(controlsContainer, controlsEvents); }\n    if (navContainer) { removeEvents(navContainer, navEvents); }\n\n    // autoplay\n    removeEvents(container, hoverEvents);\n    removeEvents(container, visibilityEvent);\n    if (autoplayButton) { removeEvents(autoplayButton, {'click': toggleAutoplay}); }\n    if (autoplay) { clearInterval(autoplayTimer); }\n\n    // container\n    if (carousel && TRANSITIONEND) {\n      var eve = {};\n      eve[TRANSITIONEND] = onTransitionEnd;\n      removeEvents(container, eve);\n    }\n    if (touch) { removeEvents(container, touchEvents); }\n    if (mouseDrag) { removeEvents(container, dragEvents); }\n\n    // cache Object values in options && reset HTML\n    var htmlList = [containerHTML, controlsContainerHTML, prevButtonHTML, nextButtonHTML, navContainerHTML, autoplayButtonHTML];\n\n    tnsList.forEach(function(item, i) {\n      var el = item === 'container' ? outerWrapper : options[item];\n\n      if (typeof el === 'object' && el) {\n        var prevEl = el.previousElementSibling ? el.previousElementSibling : false,\n            parentEl = el.parentNode;\n        el.outerHTML = htmlList[i];\n        options[item] = prevEl ? prevEl.nextElementSibling : parentEl.firstElementChild;\n      }\n    });\n\n\n    // reset variables\n    tnsList = animateIn = animateOut = animateDelay = animateNormal = horizontal = outerWrapper = innerWrapper = container = containerParent = containerHTML = slideItems = slideCount = breakpointZone = windowWidth = autoWidth = fixedWidth = edgePadding = gutter = viewport = items = slideBy = viewportMax = arrowKeys = speed = rewind = loop = autoHeight = sheet = lazyload = slidePositions = slideItemsOut = cloneCount = slideCountNew = hasRightDeadZone = rightBoundary = updateIndexBeforeTransform = transformAttr = transformPrefix = transformPostfix = getIndexMax = index = indexCached = indexMin = indexMax = resizeTimer = swipeAngle = moveDirectionExpected = running = onInit = events = newContainerClasses = slideId = disable = disabled = freezable = freeze = frozen = controlsEvents = navEvents = hoverEvents = visibilityEvent = docmentKeydownEvent = touchEvents = dragEvents = hasControls = hasNav = navAsThumbnails = hasAutoplay = hasTouch = hasMouseDrag = slideActiveClass = imgCompleteClass = imgEvents = imgsComplete = controls = controlsText = controlsContainer = controlsContainerHTML = prevButton = nextButton = prevIsButton = nextIsButton = nav = navContainer = navContainerHTML = navItems = pages = pagesCached = navClicked = navCurrentIndex = navCurrentIndexCached = navActiveClass = navStr = navStrCurrent = autoplay = autoplayTimeout = autoplayDirection = autoplayText = autoplayHoverPause = autoplayButton = autoplayButtonHTML = autoplayResetOnVisibility = autoplayHtmlStrings = autoplayTimer = animating = autoplayHoverPaused = autoplayUserPaused = autoplayVisibilityPaused = initPosition = lastPosition = translateInit = disX = disY = panStart = rafIndex = getDist = touch = mouseDrag = null;\n    // check variables\n    // [animateIn, animateOut, animateDelay, animateNormal, horizontal, outerWrapper, innerWrapper, container, containerParent, containerHTML, slideItems, slideCount, breakpointZone, windowWidth, autoWidth, fixedWidth, edgePadding, gutter, viewport, items, slideBy, viewportMax, arrowKeys, speed, rewind, loop, autoHeight, sheet, lazyload, slidePositions, slideItemsOut, cloneCount, slideCountNew, hasRightDeadZone, rightBoundary, updateIndexBeforeTransform, transformAttr, transformPrefix, transformPostfix, getIndexMax, index, indexCached, indexMin, indexMax, resizeTimer, swipeAngle, moveDirectionExpected, running, onInit, events, newContainerClasses, slideId, disable, disabled, freezable, freeze, frozen, controlsEvents, navEvents, hoverEvents, visibilityEvent, docmentKeydownEvent, touchEvents, dragEvents, hasControls, hasNav, navAsThumbnails, hasAutoplay, hasTouch, hasMouseDrag, slideActiveClass, imgCompleteClass, imgEvents, imgsComplete, controls, controlsText, controlsContainer, controlsContainerHTML, prevButton, nextButton, prevIsButton, nextIsButton, nav, navContainer, navContainerHTML, navItems, pages, pagesCached, navClicked, navCurrentIndex, navCurrentIndexCached, navActiveClass, navStr, navStrCurrent, autoplay, autoplayTimeout, autoplayDirection, autoplayText, autoplayHoverPause, autoplayButton, autoplayButtonHTML, autoplayResetOnVisibility, autoplayHtmlStrings, autoplayTimer, animating, autoplayHoverPaused, autoplayUserPaused, autoplayVisibilityPaused, initPosition, lastPosition, translateInit, disX, disY, panStart, rafIndex, getDist, touch, mouseDrag ].forEach(function(item) { if (item !== null) { console.log(item); } });\n\n    for (var a in this) {\n      if (a !== 'rebuild') { this[a] = null; }\n    }\n    isOn = false;\n  }\n\n// === ON RESIZE ===\n  // responsive || fixedWidth || autoWidth || !horizontal\n  function onResize (e) {\n    raf(function(){ resizeTasks(getEvent(e)); });\n  }\n\n  function resizeTasks (e) {\n    if (!isOn) { return; }\n    if (nested === 'outer') { events.emit('outerResized', info(e)); }\n    windowWidth = getWindowWidth();\n    var bpChanged,\n        breakpointZoneTem = breakpointZone,\n        needContainerTransform = false;\n\n    if (responsive) {\n      setBreakpointZone();\n      bpChanged = breakpointZoneTem !== breakpointZone;\n      // if (hasRightDeadZone) { needContainerTransform = true; } // *?\n      if (bpChanged) { events.emit('newBreakpointStart', info(e)); }\n    }\n\n    var indChanged,\n        itemsChanged,\n        itemsTem = items,\n        disableTem = disable,\n        freezeTem = freeze,\n        arrowKeysTem = arrowKeys,\n        controlsTem = controls,\n        navTem = nav,\n        touchTem = touch,\n        mouseDragTem = mouseDrag,\n        autoplayTem = autoplay,\n        autoplayHoverPauseTem = autoplayHoverPause,\n        autoplayResetOnVisibilityTem = autoplayResetOnVisibility,\n        indexTem = index;\n\n    if (bpChanged) {\n      var fixedWidthTem = fixedWidth,\n          autoHeightTem = autoHeight,\n          controlsTextTem = controlsText,\n          centerTem = center,\n          autoplayTextTem = autoplayText;\n\n      if (!CSSMQ) {\n        var gutterTem = gutter,\n            edgePaddingTem = edgePadding;\n      }\n    }\n\n    // get option:\n    // fixed width: viewport, fixedWidth, gutter => items\n    // others: window width => all variables\n    // all: items => slideBy\n    arrowKeys = getOption('arrowKeys');\n    controls = getOption('controls');\n    nav = getOption('nav');\n    touch = getOption('touch');\n    center = getOption('center');\n    mouseDrag = getOption('mouseDrag');\n    autoplay = getOption('autoplay');\n    autoplayHoverPause = getOption('autoplayHoverPause');\n    autoplayResetOnVisibility = getOption('autoplayResetOnVisibility');\n\n    if (bpChanged) {\n      disable = getOption('disable');\n      fixedWidth = getOption('fixedWidth');\n      speed = getOption('speed');\n      autoHeight = getOption('autoHeight');\n      controlsText = getOption('controlsText');\n      autoplayText = getOption('autoplayText');\n      autoplayTimeout = getOption('autoplayTimeout');\n\n      if (!CSSMQ) {\n        edgePadding = getOption('edgePadding');\n        gutter = getOption('gutter');\n      }\n    }\n    // update options\n    resetVariblesWhenDisable(disable);\n\n    viewport = getViewportWidth(); // <= edgePadding, gutter\n    if ((!horizontal || autoWidth) && !disable) {\n      setSlidePositions();\n      if (!horizontal) {\n        updateContentWrapperHeight(); // <= setSlidePositions\n        needContainerTransform = true;\n      }\n    }\n    if (fixedWidth || autoWidth) {\n      rightBoundary = getRightBoundary(); // autoWidth: <= viewport, slidePositions, gutter\n                                          // fixedWidth: <= viewport, fixedWidth, gutter\n      indexMax = getIndexMax(); // autoWidth: <= rightBoundary, slidePositions\n                                // fixedWidth: <= rightBoundary, fixedWidth, gutter\n    }\n\n    if (bpChanged || fixedWidth) {\n      items = getOption('items');\n      slideBy = getOption('slideBy');\n      itemsChanged = items !== itemsTem;\n\n      if (itemsChanged) {\n        if (!fixedWidth && !autoWidth) { indexMax = getIndexMax(); } // <= items\n        // check index before transform in case\n        // slider reach the right edge then items become bigger\n        updateIndex();\n      }\n    }\n\n    if (bpChanged) {\n      if (disable !== disableTem) {\n        if (disable) {\n          disableSlider();\n        } else {\n          enableSlider(); // <= slidePositions, rightBoundary, indexMax\n        }\n      }\n    }\n\n    if (freezable && (bpChanged || fixedWidth || autoWidth)) {\n      freeze = getFreeze(); // <= autoWidth: slidePositions, gutter, viewport, rightBoundary\n                            // <= fixedWidth: fixedWidth, gutter, rightBoundary\n                            // <= others: items\n\n      if (freeze !== freezeTem) {\n        if (freeze) {\n          doContainerTransform(getContainerTransformValue(getStartIndex(0)));\n          freezeSlider();\n        } else {\n          unfreezeSlider();\n          needContainerTransform = true;\n        }\n      }\n    }\n\n    resetVariblesWhenDisable(disable || freeze); // controls, nav, touch, mouseDrag, arrowKeys, autoplay, autoplayHoverPause, autoplayResetOnVisibility\n    if (!autoplay) { autoplayHoverPause = autoplayResetOnVisibility = false; }\n\n    if (arrowKeys !== arrowKeysTem) {\n      arrowKeys ?\n        addEvents(doc, docmentKeydownEvent) :\n        removeEvents(doc, docmentKeydownEvent);\n    }\n    if (controls !== controlsTem) {\n      if (controls) {\n        if (controlsContainer) {\n          showElement(controlsContainer);\n        } else {\n          if (prevButton) { showElement(prevButton); }\n          if (nextButton) { showElement(nextButton); }\n        }\n      } else {\n        if (controlsContainer) {\n          hideElement(controlsContainer);\n        } else {\n          if (prevButton) { hideElement(prevButton); }\n          if (nextButton) { hideElement(nextButton); }\n        }\n      }\n    }\n    if (nav !== navTem) {\n      if (nav) {\n        showElement(navContainer);\n        updateNavVisibility();\n      } else {\n        hideElement(navContainer)\n      }\n    }\n    if (touch !== touchTem) {\n      touch ?\n        addEvents(container, touchEvents, options.preventScrollOnTouch) :\n        removeEvents(container, touchEvents);\n    }\n    if (mouseDrag !== mouseDragTem) {\n      mouseDrag ?\n        addEvents(container, dragEvents) :\n        removeEvents(container, dragEvents);\n    }\n    if (autoplay !== autoplayTem) {\n      if (autoplay) {\n        if (autoplayButton) { showElement(autoplayButton); }\n        if (!animating && !autoplayUserPaused) { startAutoplay(); }\n      } else {\n        if (autoplayButton) { hideElement(autoplayButton); }\n        if (animating) { stopAutoplay(); }\n      }\n    }\n    if (autoplayHoverPause !== autoplayHoverPauseTem) {\n      autoplayHoverPause ?\n        addEvents(container, hoverEvents) :\n        removeEvents(container, hoverEvents);\n    }\n    if (autoplayResetOnVisibility !== autoplayResetOnVisibilityTem) {\n      autoplayResetOnVisibility ?\n        addEvents(doc, visibilityEvent) :\n        removeEvents(doc, visibilityEvent);\n    }\n\n    if (bpChanged) {\n      if (fixedWidth !== fixedWidthTem || center !== centerTem) { needContainerTransform = true; }\n\n      if (autoHeight !== autoHeightTem) {\n        if (!autoHeight) { innerWrapper.style.height = ''; }\n      }\n\n      if (controls && controlsText !== controlsTextTem) {\n        prevButton.innerHTML = controlsText[0];\n        nextButton.innerHTML = controlsText[1];\n      }\n\n      if (autoplayButton && autoplayText !== autoplayTextTem) {\n        var i = autoplay ? 1 : 0,\n            html = autoplayButton.innerHTML,\n            len = html.length - autoplayTextTem[i].length;\n        if (html.substring(len) === autoplayTextTem[i]) {\n          autoplayButton.innerHTML = html.substring(0, len) + autoplayText[i];\n        }\n      }\n    } else {\n      if (center && (fixedWidth || autoWidth)) { needContainerTransform = true; }\n    }\n\n    if (itemsChanged || fixedWidth && !autoWidth) {\n      pages = getPages();\n      updateNavVisibility();\n    }\n\n    indChanged = index !== indexTem;\n    if (indChanged) {\n      events.emit('indexChanged', info());\n      needContainerTransform = true;\n    } else if (itemsChanged) {\n      if (!indChanged) { additionalUpdates(); }\n    } else if (fixedWidth || autoWidth) {\n      doLazyLoad();\n      updateSlideStatus();\n      updateLiveRegion();\n    }\n\n    if (itemsChanged && !carousel) { updateGallerySlidePositions(); }\n\n    if (!disable && !freeze) {\n      // non-mediaqueries: IE8\n      if (bpChanged && !CSSMQ) {\n        // middle wrapper styles\n\n        // inner wrapper styles\n        if (edgePadding !== edgePaddingTem || gutter !== gutterTem) {\n          innerWrapper.style.cssText = getInnerWrapperStyles(edgePadding, gutter, fixedWidth, speed, autoHeight);\n        }\n\n        if (horizontal) {\n          // container styles\n          if (carousel) {\n            container.style.width = getContainerWidth(fixedWidth, gutter, items);\n          }\n\n          // slide styles\n          var str = getSlideWidthStyle(fixedWidth, gutter, items) +\n                    getSlideGutterStyle(gutter);\n\n          // remove the last line and\n          // add new styles\n          removeCSSRule(sheet, getCssRulesLength(sheet) - 1);\n          addCSSRule(sheet, '#' + slideId + ' > .tns-item', str, getCssRulesLength(sheet));\n        }\n      }\n\n      // auto height\n      if (autoHeight) { doAutoHeight(); }\n\n      if (needContainerTransform) {\n        doContainerTransformSilent();\n        indexCached = index;\n      }\n    }\n\n    if (bpChanged) { events.emit('newBreakpointEnd', info(e)); }\n  }\n\n\n\n\n\n  // === INITIALIZATION FUNCTIONS === //\n  function getFreeze () {\n    if (!fixedWidth && !autoWidth) {\n      var a = center ? items - (items - 1) / 2 : items;\n      return  slideCount <= a;\n    }\n\n    var width = fixedWidth ? (fixedWidth + gutter) * slideCount : slidePositions[slideCount],\n        vp = edgePadding ? viewport + edgePadding * 2 : viewport + gutter;\n\n    if (center) {\n      vp -= fixedWidth ? (viewport - fixedWidth) / 2 : (viewport - (slidePositions[index + 1] - slidePositions[index] - gutter)) / 2;\n    }\n\n    return width <= vp;\n  }\n\n  function setBreakpointZone () {\n    breakpointZone = 0;\n    for (var bp in responsive) {\n      bp = parseInt(bp); // convert string to number\n      if (windowWidth >= bp) { breakpointZone = bp; }\n    }\n  }\n\n  // (slideBy, indexMin, indexMax) => index\n  var updateIndex = (function () {\n    return loop ?\n      carousel ?\n        // loop + carousel\n        function () {\n          var leftEdge = indexMin,\n              rightEdge = indexMax;\n\n          leftEdge += slideBy;\n          rightEdge -= slideBy;\n\n          // adjust edges when has edge paddings\n          // or fixed-width slider with extra space on the right side\n          if (edgePadding) {\n            leftEdge += 1;\n            rightEdge -= 1;\n          } else if (fixedWidth) {\n            if ((viewport + gutter)%(fixedWidth + gutter)) { rightEdge -= 1; }\n          }\n\n          if (cloneCount) {\n            if (index > rightEdge) {\n              index -= slideCount;\n            } else if (index < leftEdge) {\n              index += slideCount;\n            }\n          }\n        } :\n        // loop + gallery\n        function() {\n          if (index > indexMax) {\n            while (index >= indexMin + slideCount) { index -= slideCount; }\n          } else if (index < indexMin) {\n            while (index <= indexMax - slideCount) { index += slideCount; }\n          }\n        } :\n      // non-loop\n      function() {\n        index = Math.max(indexMin, Math.min(indexMax, index));\n      };\n  })();\n\n  function disableUI () {\n    if (!autoplay && autoplayButton) { hideElement(autoplayButton); }\n    if (!nav && navContainer) { hideElement(navContainer); }\n    if (!controls) {\n      if (controlsContainer) {\n        hideElement(controlsContainer);\n      } else {\n        if (prevButton) { hideElement(prevButton); }\n        if (nextButton) { hideElement(nextButton); }\n      }\n    }\n  }\n\n  function enableUI () {\n    if (autoplay && autoplayButton) { showElement(autoplayButton); }\n    if (nav && navContainer) { showElement(navContainer); }\n    if (controls) {\n      if (controlsContainer) {\n        showElement(controlsContainer);\n      } else {\n        if (prevButton) { showElement(prevButton); }\n        if (nextButton) { showElement(nextButton); }\n      }\n    }\n  }\n\n  function freezeSlider () {\n    if (frozen) { return; }\n\n    // remove edge padding from inner wrapper\n    if (edgePadding) { innerWrapper.style.margin = '0px'; }\n\n    // add class tns-transparent to cloned slides\n    if (cloneCount) {\n      var str = 'tns-transparent';\n      for (var i = cloneCount; i--;) {\n        if (carousel) { addClass(slideItems[i], str); }\n        addClass(slideItems[slideCountNew - i - 1], str);\n      }\n    }\n\n    // update tools\n    disableUI();\n\n    frozen = true;\n  }\n\n  function unfreezeSlider () {\n    if (!frozen) { return; }\n\n    // restore edge padding for inner wrapper\n    // for mordern browsers\n    if (edgePadding && CSSMQ) { innerWrapper.style.margin = ''; }\n\n    // remove class tns-transparent to cloned slides\n    if (cloneCount) {\n      var str = 'tns-transparent';\n      for (var i = cloneCount; i--;) {\n        if (carousel) { removeClass(slideItems[i], str); }\n        removeClass(slideItems[slideCountNew - i - 1], str);\n      }\n    }\n\n    // update tools\n    enableUI();\n\n    frozen = false;\n  }\n\n  function disableSlider () {\n    if (disabled) { return; }\n\n    sheet.disabled = true;\n    container.className = container.className.replace(newContainerClasses.substring(1), '');\n    removeAttrs(container, ['style']);\n    if (loop) {\n      for (var j = cloneCount; j--;) {\n        if (carousel) { hideElement(slideItems[j]); }\n        hideElement(slideItems[slideCountNew - j - 1]);\n      }\n    }\n\n    // vertical slider\n    if (!horizontal || !carousel) { removeAttrs(innerWrapper, ['style']); }\n\n    // gallery\n    if (!carousel) {\n      for (var i = index, l = index + slideCount; i < l; i++) {\n        var item = slideItems[i];\n        removeAttrs(item, ['style']);\n        removeClass(item, animateIn);\n        removeClass(item, animateNormal);\n      }\n    }\n\n    // update tools\n    disableUI();\n\n    disabled = true;\n  }\n\n  function enableSlider () {\n    if (!disabled) { return; }\n\n    sheet.disabled = false;\n    container.className += newContainerClasses;\n    doContainerTransformSilent();\n\n    if (loop) {\n      for (var j = cloneCount; j--;) {\n        if (carousel) { showElement(slideItems[j]); }\n        showElement(slideItems[slideCountNew - j - 1]);\n      }\n    }\n\n    // gallery\n    if (!carousel) {\n      for (var i = index, l = index + slideCount; i < l; i++) {\n        var item = slideItems[i],\n            classN = i < index + items ? animateIn : animateNormal;\n        item.style.left = (i - index) * 100 / items + '%';\n        addClass(item, classN);\n      }\n    }\n\n    // update tools\n    enableUI();\n\n    disabled = false;\n  }\n\n  function updateLiveRegion () {\n    var str = getLiveRegionStr();\n    if (liveregionCurrent.innerHTML !== str) { liveregionCurrent.innerHTML = str; }\n  }\n\n  function getLiveRegionStr () {\n    var arr = getVisibleSlideRange(),\n        start = arr[0] + 1,\n        end = arr[1] + 1;\n    return start === end ? start + '' : start + ' to ' + end;\n  }\n\n  function getVisibleSlideRange (val) {\n    if (val == null) { val = getContainerTransformValue(); }\n    var start = index, end, rangestart, rangeend;\n\n    // get range start, range end for autoWidth and fixedWidth\n    if (center || edgePadding) {\n      if (autoWidth || fixedWidth) {\n        rangestart = - (parseFloat(val) + edgePadding);\n        rangeend = rangestart + viewport + edgePadding * 2;\n      }\n    } else {\n      if (autoWidth) {\n        rangestart = slidePositions[index];\n        rangeend = rangestart + viewport;\n      }\n    }\n\n    // get start, end\n    // - check auto width\n    if (autoWidth) {\n      slidePositions.forEach(function(point, i) {\n        if (i < slideCountNew) {\n          if ((center || edgePadding) && point <= rangestart + 0.5) { start = i; }\n          if (rangeend - point >= 0.5) { end = i; }\n        }\n      });\n\n    // - check percentage width, fixed width\n    } else {\n\n      if (fixedWidth) {\n        var cell = fixedWidth + gutter;\n        if (center || edgePadding) {\n          start = Math.floor(rangestart/cell);\n          end = Math.ceil(rangeend/cell - 1);\n        } else {\n          end = start + Math.ceil(viewport/cell) - 1;\n        }\n\n      } else {\n        if (center || edgePadding) {\n          var a = items - 1;\n          if (center) {\n            start -= a / 2;\n            end = index + a / 2;\n          } else {\n            end = index + a;\n          }\n\n          if (edgePadding) {\n            var b = edgePadding * items / viewport;\n            start -= b;\n            end += b;\n          }\n\n          start = Math.floor(start);\n          end = Math.ceil(end);\n        } else {\n          end = start + items - 1;\n        }\n      }\n\n      start = Math.max(start, 0);\n      end = Math.min(end, slideCountNew - 1);\n    }\n\n    return [start, end];\n  }\n\n  function doLazyLoad () {\n    if (lazyload && !disable) {\n      var arg = getVisibleSlideRange();\n      arg.push(lazyloadSelector);\n\n      getImageArray.apply(null, arg).forEach(function (img) {\n        if (!hasClass(img, imgCompleteClass)) {\n          // stop propagation transitionend event to container\n          var eve = {};\n          eve[TRANSITIONEND] = function (e) { e.stopPropagation(); };\n          addEvents(img, eve);\n\n          addEvents(img, imgEvents);\n\n          // update src\n          img.src = getAttr(img, 'data-src');\n\n          // update srcset\n          var srcset = getAttr(img, 'data-srcset');\n          if (srcset) { img.srcset = srcset; }\n\n          addClass(img, 'loading');\n        }\n      });\n    }\n  }\n\n  function onImgLoaded (e) {\n    imgLoaded(getTarget(e));\n  }\n\n  function onImgFailed (e) {\n    imgFailed(getTarget(e));\n  }\n\n  function imgLoaded (img) {\n    addClass(img, 'loaded');\n    imgCompleted(img);\n  }\n\n  function imgFailed (img) {\n    addClass(img, 'failed');\n    imgCompleted(img);\n  }\n\n  function imgCompleted (img) {\n    addClass(img, imgCompleteClass);\n    removeClass(img, 'loading');\n    removeEvents(img, imgEvents);\n  }\n\n  function getImageArray (start, end, imgSelector) {\n    var imgs = [];\n    if (!imgSelector) { imgSelector = 'img'; }\n\n    while (start <= end) {\n      forEach(slideItems[start].querySelectorAll(imgSelector), function (img) { imgs.push(img); });\n      start++;\n    }\n\n    return imgs;\n  }\n\n  // check if all visible images are loaded\n  // and update container height if it's done\n  function doAutoHeight () {\n    var imgs = getImageArray.apply(null, getVisibleSlideRange());\n    raf(function(){ imgsLoadedCheck(imgs, updateInnerWrapperHeight); });\n  }\n\n  function imgsLoadedCheck (imgs, cb) {\n    // execute callback function if all images are complete\n    if (imgsComplete) { return cb(); }\n\n    // check image classes\n    imgs.forEach(function (img, index) {\n      if (!lazyload && img.complete) { imgCompleted(img); } // Check image.complete\n      if (hasClass(img, imgCompleteClass)) { imgs.splice(index, 1); }\n    });\n\n    // execute callback function if selected images are all complete\n    if (!imgs.length) { return cb(); }\n\n    // otherwise execute this functiona again\n    raf(function(){ imgsLoadedCheck(imgs, cb); });\n  }\n\n  function additionalUpdates () {\n    doLazyLoad();\n    updateSlideStatus();\n    updateLiveRegion();\n    updateControlsStatus();\n    updateNavStatus();\n  }\n\n\n  function update_carousel_transition_duration () {\n    if (carousel && autoHeight) {\n      middleWrapper.style[TRANSITIONDURATION] = speed / 1000 + 's';\n    }\n  }\n\n  function getMaxSlideHeight (slideStart, slideRange) {\n    var heights = [];\n    for (var i = slideStart, l = Math.min(slideStart + slideRange, slideCountNew); i < l; i++) {\n      heights.push(slideItems[i].offsetHeight);\n    }\n\n    return Math.max.apply(null, heights);\n  }\n\n  // update inner wrapper height\n  // 1. get the max-height of the visible slides\n  // 2. set transitionDuration to speed\n  // 3. update inner wrapper height to max-height\n  // 4. set transitionDuration to 0s after transition done\n  function updateInnerWrapperHeight () {\n    var maxHeight = autoHeight ? getMaxSlideHeight(index, items) : getMaxSlideHeight(cloneCount, slideCount),\n        wp = middleWrapper ? middleWrapper : innerWrapper;\n\n    if (wp.style.height !== maxHeight) { wp.style.height = maxHeight + 'px'; }\n  }\n\n  // get the distance from the top edge of the first slide to each slide\n  // (init) => slidePositions\n  function setSlidePositions () {\n    slidePositions = [0];\n    var attr = horizontal ? 'left' : 'top',\n        attr2 = horizontal ? 'right' : 'bottom',\n        base = slideItems[0].getBoundingClientRect()[attr];\n\n    forEach(slideItems, function(item, i) {\n      // skip the first slide\n      if (i) { slidePositions.push(item.getBoundingClientRect()[attr] - base); }\n      // add the end edge\n      if (i === slideCountNew - 1) { slidePositions.push(item.getBoundingClientRect()[attr2] - base); }\n    });\n  }\n\n  // update slide\n  function updateSlideStatus () {\n    var range = getVisibleSlideRange(),\n        start = range[0],\n        end = range[1];\n\n    forEach(slideItems, function(item, i) {\n      // show slides\n      if (i >= start && i <= end) {\n        if (hasAttr(item, 'aria-hidden')) {\n          removeAttrs(item, ['aria-hidden', 'tabindex']);\n          addClass(item, slideActiveClass);\n        }\n      // hide slides\n      } else {\n        if (!hasAttr(item, 'aria-hidden')) {\n          setAttrs(item, {\n            'aria-hidden': 'true',\n            'tabindex': '-1'\n          });\n          removeClass(item, slideActiveClass);\n        }\n      }\n    });\n  }\n\n  // gallery: update slide position\n  function updateGallerySlidePositions () {\n    var l = index + Math.min(slideCount, items);\n    for (var i = slideCountNew; i--;) {\n      var item = slideItems[i];\n\n      if (i >= index && i < l) {\n        // add transitions to visible slides when adjusting their positions\n        addClass(item, 'tns-moving');\n\n        item.style.left = (i - index) * 100 / items + '%';\n        addClass(item, animateIn);\n        removeClass(item, animateNormal);\n      } else if (item.style.left) {\n        item.style.left = '';\n        addClass(item, animateNormal);\n        removeClass(item, animateIn);\n      }\n\n      // remove outlet animation\n      removeClass(item, animateOut);\n    }\n\n    // removing '.tns-moving'\n    setTimeout(function() {\n      forEach(slideItems, function(el) {\n        removeClass(el, 'tns-moving');\n      });\n    }, 300);\n  }\n\n  // set tabindex on Nav\n  function updateNavStatus () {\n    // get current nav\n    if (nav) {\n      navCurrentIndex = navClicked >= 0 ? navClicked : getCurrentNavIndex();\n      navClicked = -1;\n\n      if (navCurrentIndex !== navCurrentIndexCached) {\n        var navPrev = navItems[navCurrentIndexCached],\n            navCurrent = navItems[navCurrentIndex];\n\n        setAttrs(navPrev, {\n          'tabindex': '-1',\n          'aria-label': navStr + (navCurrentIndexCached + 1)\n        });\n        removeClass(navPrev, navActiveClass);\n\n        setAttrs(navCurrent, {'aria-label': navStr + (navCurrentIndex + 1) + navStrCurrent});\n        removeAttrs(navCurrent, 'tabindex');\n        addClass(navCurrent, navActiveClass);\n\n        navCurrentIndexCached = navCurrentIndex;\n      }\n    }\n  }\n\n  function getLowerCaseNodeName (el) {\n    return el.nodeName.toLowerCase();\n  }\n\n  function isButton (el) {\n    return getLowerCaseNodeName(el) === 'button';\n  }\n\n  function isAriaDisabled (el) {\n    return el.getAttribute('aria-disabled') === 'true';\n  }\n\n  function disEnableElement (isButton, el, val) {\n    if (isButton) {\n      el.disabled = val;\n    } else {\n      el.setAttribute('aria-disabled', val.toString());\n    }\n  }\n\n  // set 'disabled' to true on controls when reach the edges\n  function updateControlsStatus () {\n    if (!controls || rewind || loop) { return; }\n\n    var prevDisabled = (prevIsButton) ? prevButton.disabled : isAriaDisabled(prevButton),\n        nextDisabled = (nextIsButton) ? nextButton.disabled : isAriaDisabled(nextButton),\n        disablePrev = (index <= indexMin) ? true : false,\n        disableNext = (!rewind && index >= indexMax) ? true : false;\n\n    if (disablePrev && !prevDisabled) {\n      disEnableElement(prevIsButton, prevButton, true);\n    }\n    if (!disablePrev && prevDisabled) {\n      disEnableElement(prevIsButton, prevButton, false);\n    }\n    if (disableNext && !nextDisabled) {\n      disEnableElement(nextIsButton, nextButton, true);\n    }\n    if (!disableNext && nextDisabled) {\n      disEnableElement(nextIsButton, nextButton, false);\n    }\n  }\n\n  // set duration\n  function resetDuration (el, str) {\n    if (TRANSITIONDURATION) { el.style[TRANSITIONDURATION] = str; }\n  }\n\n  function getSliderWidth () {\n    return fixedWidth ? (fixedWidth + gutter) * slideCountNew : slidePositions[slideCountNew];\n  }\n\n  function getCenterGap (num) {\n    if (num == null) { num = index; }\n\n    var gap = edgePadding ? gutter : 0;\n    return autoWidth ? ((viewport - gap) - (slidePositions[num + 1] - slidePositions[num] - gutter))/2 :\n      fixedWidth ? (viewport - fixedWidth) / 2 :\n        (items - 1) / 2;\n  }\n\n  function getRightBoundary () {\n    var gap = edgePadding ? gutter : 0,\n        result = (viewport + gap) - getSliderWidth();\n\n    if (center && !loop) {\n      result = fixedWidth ? - (fixedWidth + gutter) * (slideCountNew - 1) - getCenterGap() :\n        getCenterGap(slideCountNew - 1) - slidePositions[slideCountNew - 1];\n    }\n    if (result > 0) { result = 0; }\n\n    return result;\n  }\n\n  function getContainerTransformValue (num) {\n    if (num == null) { num = index; }\n\n    var val;\n    if (horizontal && !autoWidth) {\n      if (fixedWidth) {\n        val = - (fixedWidth + gutter) * num;\n        if (center) { val += getCenterGap(); }\n      } else {\n        var denominator = TRANSFORM ? slideCountNew : items;\n        if (center) { num -= getCenterGap(); }\n        val = - num * 100 / denominator;\n      }\n    } else {\n      val = - slidePositions[num];\n      if (center && autoWidth) {\n        val += getCenterGap();\n      }\n    }\n\n    if (hasRightDeadZone) { val = Math.max(val, rightBoundary); }\n\n    val += (horizontal && !autoWidth && !fixedWidth) ? '%' : 'px';\n\n    return val;\n  }\n\n  function doContainerTransformSilent (val) {\n    resetDuration(container, '0s');\n    doContainerTransform(val);\n  }\n\n  function doContainerTransform (val) {\n    if (val == null) { val = getContainerTransformValue(); }\n    container.style[transformAttr] = transformPrefix + val + transformPostfix;\n  }\n\n  function animateSlide (number, classOut, classIn, isOut) {\n    var l = number + items;\n    if (!loop) { l = Math.min(l, slideCountNew); }\n\n    for (var i = number; i < l; i++) {\n        var item = slideItems[i];\n\n      // set item positions\n      if (!isOut) { item.style.left = (i - index) * 100 / items + '%'; }\n\n      if (animateDelay && TRANSITIONDELAY) {\n        item.style[TRANSITIONDELAY] = item.style[ANIMATIONDELAY] = animateDelay * (i - number) / 1000 + 's';\n      }\n      removeClass(item, classOut);\n      addClass(item, classIn);\n\n      if (isOut) { slideItemsOut.push(item); }\n    }\n  }\n\n  // make transfer after click/drag:\n  // 1. change 'transform' property for mordern browsers\n  // 2. change 'left' property for legacy browsers\n  var transformCore = (function () {\n    return carousel ?\n      function () {\n        resetDuration(container, '');\n        if (TRANSITIONDURATION || !speed) {\n          // for morden browsers with non-zero duration or\n          // zero duration for all browsers\n          doContainerTransform();\n          // run fallback function manually\n          // when duration is 0 / container is hidden\n          if (!speed || !isVisible(container)) { onTransitionEnd(); }\n\n        } else {\n          // for old browser with non-zero duration\n          jsTransform(container, transformAttr, transformPrefix, transformPostfix, getContainerTransformValue(), speed, onTransitionEnd);\n        }\n\n        if (!horizontal) { updateContentWrapperHeight(); }\n      } :\n      function () {\n        slideItemsOut = [];\n\n        var eve = {};\n        eve[TRANSITIONEND] = eve[ANIMATIONEND] = onTransitionEnd;\n        removeEvents(slideItems[indexCached], eve);\n        addEvents(slideItems[index], eve);\n\n        animateSlide(indexCached, animateIn, animateOut, true);\n        animateSlide(index, animateNormal, animateIn);\n\n        // run fallback function manually\n        // when transition or animation not supported / duration is 0\n        if (!TRANSITIONEND || !ANIMATIONEND || !speed || !isVisible(container)) { onTransitionEnd(); }\n      };\n  })();\n\n  function render (e, sliderMoved) {\n    if (updateIndexBeforeTransform) { updateIndex(); }\n\n    // render when slider was moved (touch or drag) even though index may not change\n    if (index !== indexCached || sliderMoved) {\n      // events\n      events.emit('indexChanged', info());\n      events.emit('transitionStart', info());\n      if (autoHeight) { doAutoHeight(); }\n\n      // pause autoplay when click or keydown from user\n      if (animating && e && ['click', 'keydown'].indexOf(e.type) >= 0) { stopAutoplay(); }\n\n      running = true;\n      transformCore();\n    }\n  }\n\n  /*\n   * Transfer prefixed properties to the same format\n   * CSS: -Webkit-Transform => webkittransform\n   * JS: WebkitTransform => webkittransform\n   * @param {string} str - property\n   *\n   */\n  function strTrans (str) {\n    return str.toLowerCase().replace(/-/g, '');\n  }\n\n  // AFTER TRANSFORM\n  // Things need to be done after a transfer:\n  // 1. check index\n  // 2. add classes to visible slide\n  // 3. disable controls buttons when reach the first/last slide in non-loop slider\n  // 4. update nav status\n  // 5. lazyload images\n  // 6. update container height\n  function onTransitionEnd (event) {\n    // check running on gallery mode\n    // make sure trantionend/animationend events run only once\n    if (carousel || running) {\n      events.emit('transitionEnd', info(event));\n\n      if (!carousel && slideItemsOut.length > 0) {\n        for (var i = 0; i < slideItemsOut.length; i++) {\n          var item = slideItemsOut[i];\n          // set item positions\n          item.style.left = '';\n\n          if (ANIMATIONDELAY && TRANSITIONDELAY) {\n            item.style[ANIMATIONDELAY] = '';\n            item.style[TRANSITIONDELAY] = '';\n          }\n          removeClass(item, animateOut);\n          addClass(item, animateNormal);\n        }\n      }\n\n      /* update slides, nav, controls after checking ...\n       * => legacy browsers who don't support 'event'\n       *    have to check event first, otherwise event.target will cause an error\n       * => or 'gallery' mode:\n       *   + event target is slide item\n       * => or 'carousel' mode:\n       *   + event target is container,\n       *   + event.property is the same with transform attribute\n       */\n      if (!event ||\n          !carousel && event.target.parentNode === container ||\n          event.target === container && strTrans(event.propertyName) === strTrans(transformAttr)) {\n\n        if (!updateIndexBeforeTransform) {\n          var indexTem = index;\n          updateIndex();\n          if (index !== indexTem) {\n            events.emit('indexChanged', info());\n\n            doContainerTransformSilent();\n          }\n        }\n\n        if (nested === 'inner') { events.emit('innerLoaded', info()); }\n        running = false;\n        indexCached = index;\n      }\n    }\n\n  }\n\n  // # ACTIONS\n  function goTo (targetIndex, e) {\n    if (freeze) { return; }\n\n    // prev slideBy\n    if (targetIndex === 'prev') {\n      onControlsClick(e, -1);\n\n    // next slideBy\n    } else if (targetIndex === 'next') {\n      onControlsClick(e, 1);\n\n    // go to exact slide\n    } else {\n      if (running) {\n        if (preventActionWhenRunning) { return; } else { onTransitionEnd(); }\n      }\n\n      var absIndex = getAbsIndex(),\n          indexGap = 0;\n\n      if (targetIndex === 'first') {\n        indexGap = - absIndex;\n      } else if (targetIndex === 'last') {\n        indexGap = carousel ? slideCount - items - absIndex : slideCount - 1 - absIndex;\n      } else {\n        if (typeof targetIndex !== 'number') { targetIndex = parseInt(targetIndex); }\n\n        if (!isNaN(targetIndex)) {\n          // from directly called goTo function\n          if (!e) { targetIndex = Math.max(0, Math.min(slideCount - 1, targetIndex)); }\n\n          indexGap = targetIndex - absIndex;\n        }\n      }\n\n      // gallery: make sure new page won't overlap with current page\n      if (!carousel && indexGap && Math.abs(indexGap) < items) {\n        var factor = indexGap > 0 ? 1 : -1;\n        indexGap += (index + indexGap - slideCount) >= indexMin ? slideCount * factor : slideCount * 2 * factor * -1;\n      }\n\n      index += indexGap;\n\n      // make sure index is in range\n      if (carousel && loop) {\n        if (index < indexMin) { index += slideCount; }\n        if (index > indexMax) { index -= slideCount; }\n      }\n\n      // if index is changed, start rendering\n      if (getAbsIndex(index) !== getAbsIndex(indexCached)) {\n        render(e);\n      }\n\n    }\n  }\n\n  // on controls click\n  function onControlsClick (e, dir) {\n    if (running) {\n      if (preventActionWhenRunning) { return; } else { onTransitionEnd(); }\n    }\n    var passEventObject;\n\n    if (!dir) {\n      e = getEvent(e);\n      var target = getTarget(e);\n\n      while (target !== controlsContainer && [prevButton, nextButton].indexOf(target) < 0) { target = target.parentNode; }\n\n      var targetIn = [prevButton, nextButton].indexOf(target);\n      if (targetIn >= 0) {\n        passEventObject = true;\n        dir = targetIn === 0 ? -1 : 1;\n      }\n    }\n\n    if (rewind) {\n      if (index === indexMin && dir === -1) {\n        goTo('last', e);\n        return;\n      } else if (index === indexMax && dir === 1) {\n        goTo('first', e);\n        return;\n      }\n    }\n\n    if (dir) {\n      index += slideBy * dir;\n      if (autoWidth) { index = Math.floor(index); }\n      // pass e when click control buttons or keydown\n      render((passEventObject || (e && e.type === 'keydown')) ? e : null);\n    }\n  }\n\n  // on nav click\n  function onNavClick (e) {\n    if (running) {\n      if (preventActionWhenRunning) { return; } else { onTransitionEnd(); }\n    }\n\n    e = getEvent(e);\n    var target = getTarget(e), navIndex;\n\n    // find the clicked nav item\n    while (target !== navContainer && !hasAttr(target, 'data-nav')) { target = target.parentNode; }\n    if (hasAttr(target, 'data-nav')) {\n      var navIndex = navClicked = Number(getAttr(target, 'data-nav')),\n          targetIndexBase = fixedWidth || autoWidth ? navIndex * slideCount / pages : navIndex * items,\n          targetIndex = navAsThumbnails ? navIndex : Math.min(Math.ceil(targetIndexBase), slideCount - 1);\n      goTo(targetIndex, e);\n\n      if (navCurrentIndex === navIndex) {\n        if (animating) { stopAutoplay(); }\n        navClicked = -1; // reset navClicked\n      }\n    }\n  }\n\n  // autoplay functions\n  function setAutoplayTimer () {\n    autoplayTimer = setInterval(function () {\n      onControlsClick(null, autoplayDirection);\n    }, autoplayTimeout);\n\n    animating = true;\n  }\n\n  function stopAutoplayTimer () {\n    clearInterval(autoplayTimer);\n    animating = false;\n  }\n\n  function updateAutoplayButton (action, txt) {\n    setAttrs(autoplayButton, {'data-action': action});\n    autoplayButton.innerHTML = autoplayHtmlStrings[0] + action + autoplayHtmlStrings[1] + txt;\n  }\n\n  function startAutoplay () {\n    setAutoplayTimer();\n    if (autoplayButton) { updateAutoplayButton('stop', autoplayText[1]); }\n  }\n\n  function stopAutoplay () {\n    stopAutoplayTimer();\n    if (autoplayButton) { updateAutoplayButton('start', autoplayText[0]); }\n  }\n\n  // programaitcally play/pause the slider\n  function play () {\n    if (autoplay && !animating) {\n      startAutoplay();\n      autoplayUserPaused = false;\n    }\n  }\n  function pause () {\n    if (animating) {\n      stopAutoplay();\n      autoplayUserPaused = true;\n    }\n  }\n\n  function toggleAutoplay () {\n    if (animating) {\n      stopAutoplay();\n      autoplayUserPaused = true;\n    } else {\n      startAutoplay();\n      autoplayUserPaused = false;\n    }\n  }\n\n  function onVisibilityChange () {\n    if (doc.hidden) {\n      if (animating) {\n        stopAutoplayTimer();\n        autoplayVisibilityPaused = true;\n      }\n    } else if (autoplayVisibilityPaused) {\n      setAutoplayTimer();\n      autoplayVisibilityPaused = false;\n    }\n  }\n\n  function mouseoverPause () {\n    if (animating) {\n      stopAutoplayTimer();\n      autoplayHoverPaused = true;\n    }\n  }\n\n  function mouseoutRestart () {\n    if (autoplayHoverPaused) {\n      setAutoplayTimer();\n      autoplayHoverPaused = false;\n    }\n  }\n\n  // keydown events on document\n  function onDocumentKeydown (e) {\n    e = getEvent(e);\n    var keyIndex = [KEYS.LEFT, KEYS.RIGHT].indexOf(e.keyCode);\n\n    if (keyIndex >= 0) {\n      onControlsClick(e, keyIndex === 0 ? -1 : 1);\n    }\n  }\n\n  // on key control\n  function onControlsKeydown (e) {\n    e = getEvent(e);\n    var keyIndex = [KEYS.LEFT, KEYS.RIGHT].indexOf(e.keyCode);\n\n    if (keyIndex >= 0) {\n      if (keyIndex === 0) {\n        if (!prevButton.disabled) { onControlsClick(e, -1); }\n      } else if (!nextButton.disabled) {\n        onControlsClick(e, 1);\n      }\n    }\n  }\n\n  // set focus\n  function setFocus (el) {\n    el.focus();\n  }\n\n  // on key nav\n  function onNavKeydown (e) {\n    e = getEvent(e);\n    var curElement = doc.activeElement;\n    if (!hasAttr(curElement, 'data-nav')) { return; }\n\n    // var code = e.keyCode,\n    var keyIndex = [KEYS.LEFT, KEYS.RIGHT, KEYS.ENTER, KEYS.SPACE].indexOf(e.keyCode),\n        navIndex = Number(getAttr(curElement, 'data-nav'));\n\n    if (keyIndex >= 0) {\n      if (keyIndex === 0) {\n        if (navIndex > 0) { setFocus(navItems[navIndex - 1]); }\n      } else if (keyIndex === 1) {\n        if (navIndex < pages - 1) { setFocus(navItems[navIndex + 1]); }\n      } else {\n        navClicked = navIndex;\n        goTo(navIndex, e);\n      }\n    }\n  }\n\n  function getEvent (e) {\n    e = e || win.event;\n    return isTouchEvent(e) ? e.changedTouches[0] : e;\n  }\n  function getTarget (e) {\n    return e.target || win.event.srcElement;\n  }\n\n  function isTouchEvent (e) {\n    return e.type.indexOf('touch') >= 0;\n  }\n\n  function preventDefaultBehavior (e) {\n    e.preventDefault ? e.preventDefault() : e.returnValue = false;\n  }\n\n  function getMoveDirectionExpected () {\n    return getTouchDirection(toDegree(lastPosition.y - initPosition.y, lastPosition.x - initPosition.x), swipeAngle) === options.axis;\n  }\n\n  function onPanStart (e) {\n    if (running) {\n      if (preventActionWhenRunning) { return; } else { onTransitionEnd(); }\n    }\n\n    if (autoplay && animating) { stopAutoplayTimer(); }\n\n    panStart = true;\n    if (rafIndex) {\n      caf(rafIndex);\n      rafIndex = null;\n    }\n\n    var $ = getEvent(e);\n    events.emit(isTouchEvent(e) ? 'touchStart' : 'dragStart', info(e));\n\n    if (!isTouchEvent(e) && ['img', 'a'].indexOf(getLowerCaseNodeName(getTarget(e))) >= 0) {\n      preventDefaultBehavior(e);\n    }\n\n    lastPosition.x = initPosition.x = $.clientX;\n    lastPosition.y = initPosition.y = $.clientY;\n    if (carousel) {\n      translateInit = parseFloat(container.style[transformAttr].replace(transformPrefix, ''));\n      resetDuration(container, '0s');\n    }\n  }\n\n  function onPanMove (e) {\n    if (panStart) {\n      var $ = getEvent(e);\n      lastPosition.x = $.clientX;\n      lastPosition.y = $.clientY;\n\n      if (carousel) {\n        if (!rafIndex) { rafIndex = raf(function(){ panUpdate(e); }); }\n      } else {\n        if (moveDirectionExpected === '?') { moveDirectionExpected = getMoveDirectionExpected(); }\n        if (moveDirectionExpected) { preventScroll = true; }\n      }\n\n      if ((typeof e.cancelable !== 'boolean' || e.cancelable) && preventScroll) {\n        e.preventDefault();\n      }\n    }\n  }\n\n  function panUpdate (e) {\n    if (!moveDirectionExpected) {\n      panStart = false;\n      return;\n    }\n    caf(rafIndex);\n    if (panStart) { rafIndex = raf(function(){ panUpdate(e); }); }\n\n    if (moveDirectionExpected === '?') { moveDirectionExpected = getMoveDirectionExpected(); }\n    if (moveDirectionExpected) {\n      if (!preventScroll && isTouchEvent(e)) { preventScroll = true; }\n\n      try {\n        if (e.type) { events.emit(isTouchEvent(e) ? 'touchMove' : 'dragMove', info(e)); }\n      } catch(err) {}\n\n      var x = translateInit,\n          dist = getDist(lastPosition, initPosition);\n      if (!horizontal || fixedWidth || autoWidth) {\n        x += dist;\n        x += 'px';\n      } else {\n        var percentageX = TRANSFORM ? dist * items * 100 / ((viewport + gutter) * slideCountNew): dist * 100 / (viewport + gutter);\n        x += percentageX;\n        x += '%';\n      }\n\n      container.style[transformAttr] = transformPrefix + x + transformPostfix;\n    }\n  }\n\n  function onPanEnd (e) {\n    if (panStart) {\n      if (rafIndex) {\n        caf(rafIndex);\n        rafIndex = null;\n      }\n      if (carousel) { resetDuration(container, ''); }\n      panStart = false;\n\n      var $ = getEvent(e);\n      lastPosition.x = $.clientX;\n      lastPosition.y = $.clientY;\n      var dist = getDist(lastPosition, initPosition);\n\n      if (Math.abs(dist)) {\n        // drag vs click\n        if (!isTouchEvent(e)) {\n          // prevent \"click\"\n          var target = getTarget(e);\n          addEvents(target, {'click': function preventClick (e) {\n            preventDefaultBehavior(e);\n            removeEvents(target, {'click': preventClick});\n          }});\n        }\n\n        if (carousel) {\n          rafIndex = raf(function() {\n            if (horizontal && !autoWidth) {\n              var indexMoved = - dist * items / (viewport + gutter);\n              indexMoved = dist > 0 ? Math.floor(indexMoved) : Math.ceil(indexMoved);\n              index += indexMoved;\n            } else {\n              var moved = - (translateInit + dist);\n              if (moved <= 0) {\n                index = indexMin;\n              } else if (moved >= slidePositions[slideCountNew - 1]) {\n                index = indexMax;\n              } else {\n                var i = 0;\n                while (i < slideCountNew && moved >= slidePositions[i]) {\n                  index = i;\n                  if (moved > slidePositions[i] && dist < 0) { index += 1; }\n                  i++;\n                }\n              }\n            }\n\n            render(e, dist);\n            events.emit(isTouchEvent(e) ? 'touchEnd' : 'dragEnd', info(e));\n          });\n        } else {\n          if (moveDirectionExpected) {\n            onControlsClick(e, dist > 0 ? -1 : 1);\n          }\n        }\n      }\n    }\n\n    // reset\n    if (options.preventScrollOnTouch === 'auto') { preventScroll = false; }\n    if (swipeAngle) { moveDirectionExpected = '?'; }\n    if (autoplay && !animating) { setAutoplayTimer(); }\n  }\n\n  // === RESIZE FUNCTIONS === //\n  // (slidePositions, index, items) => vertical_conentWrapper.height\n  function updateContentWrapperHeight () {\n    var wp = middleWrapper ? middleWrapper : innerWrapper;\n    wp.style.height = slidePositions[index + items] - slidePositions[index] + 'px';\n  }\n\n  function getPages () {\n    var rough = fixedWidth ? (fixedWidth + gutter) * slideCount / viewport : slideCount / items;\n    return Math.min(Math.ceil(rough), slideCount);\n  }\n\n  /*\n   * 1. update visible nav items list\n   * 2. add \"hidden\" attributes to previous visible nav items\n   * 3. remove \"hidden\" attrubutes to new visible nav items\n   */\n  function updateNavVisibility () {\n    if (!nav || navAsThumbnails) { return; }\n\n    if (pages !== pagesCached) {\n      var min = pagesCached,\n          max = pages,\n          fn = showElement;\n\n      if (pagesCached > pages) {\n        min = pages;\n        max = pagesCached;\n        fn = hideElement;\n      }\n\n      while (min < max) {\n        fn(navItems[min]);\n        min++;\n      }\n\n      // cache pages\n      pagesCached = pages;\n    }\n  }\n\n  function info (e) {\n    return {\n      container: container,\n      slideItems: slideItems,\n      navContainer: navContainer,\n      navItems: navItems,\n      controlsContainer: controlsContainer,\n      hasControls: hasControls,\n      prevButton: prevButton,\n      nextButton: nextButton,\n      items: items,\n      slideBy: slideBy,\n      cloneCount: cloneCount,\n      slideCount: slideCount,\n      slideCountNew: slideCountNew,\n      index: index,\n      indexCached: indexCached,\n      displayIndex: getCurrentSlide(),\n      navCurrentIndex: navCurrentIndex,\n      navCurrentIndexCached: navCurrentIndexCached,\n      pages: pages,\n      pagesCached: pagesCached,\n      sheet: sheet,\n      isOn: isOn,\n      event: e || {},\n    };\n  }\n\n  return {\n    version: '2.9.3',\n    getInfo: info,\n    events: events,\n    goTo: goTo,\n    play: play,\n    pause: pause,\n    isOn: isOn,\n    updateSliderHeight: updateInnerWrapperHeight,\n    refresh: initSliderTransform,\n    destroy: destroy,\n    rebuild: function() {\n      return tns(extend(options, optionsElements));\n    }\n  };\n};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Tiny slider - for all purposes, inspired by Owl Carousel.\r\n\r\nwindow.tns = require('tiny-slider/src/tiny-slider.js').tns;\r\n"], "sourceRoot": ""}