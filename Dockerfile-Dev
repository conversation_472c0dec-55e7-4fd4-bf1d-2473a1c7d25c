
FROM php:8.3-fpm

# Copy your custom php.ini to the correct location
COPY php.ini /usr/local/etc/php/conf.d/

RUN echo "deb https://deb.debian.org/debian/ stable main" > /etc/apt/sources.list

# Install apt-transport-https and ca-certificates for SSL support
RUN apt-get update && apt-get install -y \
    apt-transport-https \
    ca-certificates

# Install dependencies
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    zip \
    unzip \
    libpq-dev \
    librdkafka-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install pdo pdo_mysql pdo_pgsql zip pgsql

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Cài đặt phần mở rộng rdkafka
RUN pecl install rdkafka && docker-php-ext-enable rdkafka

WORKDIR /app
COPY . /app

# Install PHP dependencies
# RUN composer install
# RUN composer install --no-interaction --prefer-dist --optimize-autoloader

RUN apt-get update && apt-get install -y libmemcached-dev libssl-dev zlib1g-dev \
	&& pecl install memcached-3.2.0 \
	&& docker-php-ext-enable memcached

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

RUN echo 'date.timezone="Asia/Ho_Chi_Minh"' >> /usr/local/etc/php/conf.d/date.ini \
    && echo 'opcache.enable=1' >> /usr/local/etc/php/conf.d/opcache.conf \
    && echo 'opcache.validate_timestamps=1' >> /usr/local/etc/php/conf.d/opcache.conf
    # && echo 'opcache.fast_shutdown=1' >> /usr/local/etc/php/conf.d/opcache

RUN cp .env.development .env
RUN cat .env
RUN php artisan key:generate
RUN php artisan l5-swagger:generate

CMD php artisan serve --host=0.0.0.0 --port=8000
EXPOSE 8000
