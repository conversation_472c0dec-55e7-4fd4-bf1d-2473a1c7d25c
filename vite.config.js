import {defineConfig} from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel({
            // input: 'resources/js/app.js',
            input: [
                'resources/js/app.js',
            ],
            plugins: [
                'templates/dashboard/assets/js/scripts.bundle.js',
                'templates/dashboard/assets/plugins/global/plugins.bundle.js',
                'templates/dashboard/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js',
                'templates/dashboard/assets/js/custom/widgets.js',
                'templates/dashboard/assets/js/custom/modals/create-app.js',
                'templates/dashboard/assets/js/custom/modals/upgrade-plan.js',
            ],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
});
