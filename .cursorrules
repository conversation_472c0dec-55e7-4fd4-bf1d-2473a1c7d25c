{"version": 1, "rules": {"general": {"indentation": 4, "maxLineLength": 120, "trailingComma": "es5", "singleQuote": true, "endOfLine": "lf", "tabWidth": 4, "useTabs": false, "printWidth": 120, "bracketSpacing": true}, "php": {"strictTypes": true, "propertyTypeHints": true, "returnTypeHints": true, "parameterTypeHints": true, "namespace": {"controllers": "App\\Http\\Controllers\\Dash", "services": "App\\Services", "dtos": "App\\DTO", "resources": "App\\Http\\Resources"}, "naming": {"controllers": "PascalCase", "methods": "camelCase", "properties": "camelCase", "variables": "camelCase", "constants": "UPPER_CASE"}, "formatting": {"controlStructures": "same-line", "classDeclarations": "same-line", "methodDeclarations": "same-line"}}, "vue": {"componentSyntax": "composition-api", "useTsCheck": false, "propTypes": "runtime", "indentation": 4, "templateIndentation": 4, "maxAttrsPerLine": 1, "attributes": {"order": ["id", "ref", "class", "v-model", "v-for", "v-if", "v-else", "v-else-if", "v-show"]}, "componentStructure": {"order": ["script", "template", "style"]}, "scriptSetup": true, "scopedStyles": true, "naming": {"components": "PascalCase", "props": "camelCase", "methods": "camelCase", "computedProperties": "camelCase", "events": "camelCase"}}, "javascript": {"semi": true, "useConst": true, "avoidVar": true, "preferArrowFunctions": true, "moduleSystem": "es6", "quoteStyle": "single", "objectShorthand": true, "arrayBracketSpacing": true, "objectBracketSpacing": true}, "css": {"preferTailwind": true, "classCombining": "alphabetical", "specificity": "low", "units": {"prefer": ["rem", "em", "%", "px"], "avoid": ["pt", "pc"]}}, "inertia": {"pageNameFormat": "PascalCase", "propValidation": true, "usePropTypes": true}, "laravel": {"routeNaming": "kebab-case", "routeActionNaming": "resource", "validationRules": "array", "modelPropagation": "explicit"}, "routing": {"apiRoutePrefix": "api/", "webRoutePrefix": "cms/", "resourceRouteVerbs": ["index", "show", "create", "store", "edit", "update", "destroy"], "routeModelBinding": true}, "grpc": {"clientMethodNaming": "camelCase", "requestNaming": "PascalCase", "responseNaming": "PascalCase", "errorHandling": "explicit"}, "internationalization": {"defaultLocale": "vi", "fallbackLocale": "en", "useTranslationKeys": true}, "elementPlus": {"dialogConfigs": {"modelValue": true, "closeOnClickModal": false}, "formComponentPreferences": {"labelPosition": "top", "requiredAsteriskPosition": "right"}}}}