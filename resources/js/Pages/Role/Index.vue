<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, useForm, Link} from '@inertiajs/vue3'
// import IconFilter from "@/Components/Icons/IconFilter.vue";
import Toolbar from "@/Components/Toolbar.vue";
import ThirdButton from "@/Components/ThirdButton.vue";
import RoleDialog from "@/Pages/Role/Partials/RoleDialog.vue";
import {reactive, ref} from "vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import {View, Edit, Delete, CopyDocument, Plus} from '@element-plus/icons-vue'
import DeleteDialog from "@/Components/DeleteDialog.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import {ElMessageBox} from "element-plus";

const props = defineProps({
    cateName: String,
    items: Object,
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},*/
    // { text: props.cateName },
    // {text: 'Roles List'}
];
// <el-icon><Plus/></el-icon>


// Begin: Add/Update Role
// =====================================================================================================================
let titleModal = ref('Cập nhật Role')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
let actionForm = ref('add')
let loadingContainer = ref(false)

/*const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})*/
let form = useForm({
    id: 0,
    name: '',
    slug: '',
    description: '',
    status: 1
})

const resetForm = () => {
    form.id = 0
    form.name = ''
    form.slug = ''
    form.description = ''
    form.status = 1
}

const handleSubmit = async (data) => {
    loadingContainer.value = true
    console.log('handleSubmit >>  data: ', data, ' -> actionForm: ', actionForm.value)
    // showMessage('Submit success.');
    if (actionForm.value === 'add') {
        console.log('add new item >> data: ', data)
        const form = useForm({
            ...data
        })
        form.post(route('cms.roles.store'), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                console.log('handleSubmit >> add >> onSuccess >> res: ', res)
                dialogFormVisible.value = false

                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                // resetForm()
                loadingContainer.value = false
            }
        })
    } else {
        console.log('update item >> data: ', data)
        let url = route('cms.roles.update', data.id)
        let form = useForm({
            _method: 'PUT',
            ...data
        })
        console.log('update >> form: ', form)
        form.put(url, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                // console.log('onSuccess >> res: ', res)
                dialogFormVisible.value = false
                // showMessage('Submit success.', res);
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
                loadingContainer.value = false
            }
        })
    } // end if
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}
const updateRole = row => {
    console.log('updateRole >> row: ', row)
    titleModal.value = 'Cập nhật Role'
    actionForm.value = 'update'
    dialogFormVisible.value = true

    form = row
    incrementDialogKey()
}

const addRole = () => {
    console.log('addRole')
    titleModal.value = 'Thêm mới Role'
    dialogFormVisible.value = true
    actionForm.value = 'add'
    incrementDialogKey()
    resetForm()
}

const actions = [
    // {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    {name: 'Thêm Mới', className: 'btn-sm2 btn-primary', icon: IconAdd, click: addRole},
];

// =====================================================================================================================
// End: Add/Update Role

// Begin: Delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
let singleItemName = ref('')
let itemsDelete = reactive({})
const deleteDialogVisible = ref(false)

const deleteBtnClick = (item) => {
    console.log('deleteBtnClick >> item: ', item)
    singleItemName.value = item.name
    itemsDelete = item
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
}

const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemsDelete: ', itemsDelete)
    // const ids = itemsDelete.map(item => item.id);
    const ids = Array.isArray(itemsDelete) ? itemsDelete.map(item => item.id) : [itemsDelete.id];
    console.log('ids: ', ids)
    deleteDialogVisible.value = false

    form.delete(route('cms.roles.destroy', itemsDelete), {
        onSuccess: (res) => {
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })

    // reset data
    // ---------------------------------------------------------------------------
    // selectedRows.value = []
    singleItemName.value = ''
    itemsDelete = {}
}
// =====================================================================================================================
// End: Delete

const handelDuplicate = (item) => {
    const id = item?.id ?? 0

    form = useForm({})
    ElMessageBox.confirm('Bạn có chắc chắn muốn sao chép nhóm này ?')
        .then(() => {
            form.get(route('cms.roles.duplicate', id))
        })
        .catch(() => {
            // catch error
            /*ElMessage({
                type: 'info',
                message: 'Duplicate canceled'
            })*/
            showMessage('Duplicate canceled', 'info');
        })
}

</script>

<template>
    <Head title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!--begin::Post-->
        <div id="kt_post" class="post d-flex flex-column-fluid">
            <!--begin::Container-->
            <div id="kt_content_container" class="container">
                <!--begin::Row-->
                <div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-5 g-xl-9">
                    <!--begin::Col-->
                    <div v-for="item in items.data" class="col-md-4">
                        <!--begin::Card-->
                        <div class="card card-flush h-md-100">
                            <!--begin::Card header-->
                            <div class="card-header">
                                <!--begin::Card title-->
                                <div class="card-title">
                                    <h2>{{ item?.name ?? '' }}</h2>
                                </div>
                                <!--end::Card title-->
                            </div>
                            <!--end::Card header-->
                            <!--begin::Card body-->
                            <div class="card-body pt-1">
                                <!--begin::Users-->
                                <div class="fw-bolder text-gray-600 mb-5">Total users with this role:
                                    {{ item?.total_users ?? 0 }}
                                </div>
                                <!--end::Users-->
                                <!--begin::Permissions-->
                                <div class="d-flex flex-column text-gray-600">
                                    <div v-for="permission in item?.permissions" class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>{{ permission }}
                                    </div>
                                </div>
                                <!--end::Permissions-->
                            </div>
                            <!--end::Card body-->
                            <!--begin::Card footer-->
                            <div class="card-footer flex-wrap pt-0">
                                <Link :href="route('cms.roles.show', item.id)">
                                    <el-tooltip
                                        class="box-item"
                                        effect="dark"
                                        content="Xem chi tiết"
                                        placement="top"
                                    >
                                        <ThirdButton
                                            class-name="btn-light btn-light-primary my-1 me-2"
                                        >
                                            &nbsp;<el-icon size="21"><View /></el-icon>
                                        </ThirdButton>
                                    </el-tooltip>
                                </Link>
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    content="Cập nhật"
                                    placement="top"
                                >
                                    <ThirdButton
                                        class-name="btn-light btn-light-primary my-1 me-2"
                                        name=""
                                        @click.prevent="updateRole(item)"
                                    >
                                        &nbsp;<el-icon size="21"><Edit /></el-icon>
                                    </ThirdButton>
                                </el-tooltip>
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    content="Xóa bản ghi"
                                    placement="top"
                                >
                                    <ThirdButton
                                        name=""
                                        class-name="btn-danger btn-light-danger my-1 me-2"
                                        @click.prevent="deleteBtnClick(item)"
                                    >
                                        &nbsp;<el-icon size="20"><Delete /></el-icon>
                                    </ThirdButton>
                                </el-tooltip>
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    content="Copy nhân bản 1 bản ghi mới"
                                    placement="top"
                                >
                                    <ThirdButton
                                        class-name="btn-light btn-light-primary my-1 me-2"
                                        name=""
                                        @click.prevent="handelDuplicate(item)"
                                    >
                                        &nbsp;<el-icon size="21"><CopyDocument /></el-icon>
                                    </ThirdButton>
                                </el-tooltip>
                            </div>
                            <!--end::Card footer-->
                        </div>
                        <!--end::Card-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Add new card-->
                    <div class="ol-md-4">
                        <!--begin::Card-->
                        <div class="card h-md-100">
                            <!--begin::Card body-->
                            <div class="card-body d-flex flex-center">
                                <!--begin::Button-->
                                <ThirdButton class-name="btn-clear d-flex flex-column flex-center"
                                             @click.prevent="addRole"
                                >
                                    <!--begin::Illustration-->
                                    <img alt="" class="mw-100 mh-150px mb-7"
                                         src="templates/dashboard/assets/media/illustrations/user-role.png"/>
                                    <!--end::Illustration-->
                                    <!--begin::Label-->
                                    <div class="fw-bolder fs-3 text-gray-600 text-hover-primary">Thêm mới role</div>
                                    <!--end::Label-->
                                </ThirdButton>
                                <!--begin::Button-->
                            </div>
                            <!--begin::Card body-->
                        </div>
                        <!--begin::Card-->
                    </div>
                    <!--begin::Add new card-->
                </div>
                <!--end::Row-->
                <!--begin::Modals-->
                <!--begin::Modal - Add/Update role-->
                <RoleDialog
                    :key="dialogKey"
                    :dialogFormVisible="dialogFormVisible"
                    :form="form"
                    :titleModal="titleModal"
                    @onSubmit="handleSubmit"
                    @update:dialogFormVisible="dialogFormVisible = $event"
                />
                <!--end::Modal - Add/Update role-->

                <!--begin::Modal - Delete permissions-->
                <DeleteDialog
                    :key="deleteDialogKey"
                    :itemName="singleItemName ?? ''"
                    :visible="deleteDialogVisible"
                    @delete="handleDeleteAction"
                    @update:visible="deleteDialogVisible = $event"
                />
                <!--end::Modal - Delete permissions-->

                <!--end::Modals-->
            </div>
            <!--end::Container-->
        </div>
        <!--end::Post-->

    </MainLayout>
</template>
