<script setup>

import ThirdButton from "@/Components/ThirdButton.vue";
import RoleDialog from "@/Pages/Role/Partials/RoleDialog.vue";
import {reactive, ref} from "vue";
import {showMessage} from "@/Helpers/messageHelper.js";

const props = defineProps({
    // allPermissions: Object,
    defaultPermissions: String
})

// const emit = defineEmits(['update:Permissions'])

// Begin: Update Role
// =====================================================================================================================
let titleModal = ref('Update Role')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})
const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}
const updateRole = row => {
    console.log('updatePermission >> row: ', row)
    titleModal.value = 'Update Role'
    dialogFormVisible.value = true
    form.name = row.name
    incrementDialogKey()
}
// =====================================================================================================================
// End: Update Role


</script>

<template>
    <div class="flex-column flex-lg-row-auto w-100 w-lg-300px mb-10">
        <!--begin::Card-->
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2 class="mb-0">Developer</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Permissions-->
                <div class="d-flex flex-column text-gray-600">
                    <div class="d-flex align-items-center py-2">
                        <span class="bullet bg-primary me-3"></span>Some Admin Controls
                    </div>
                    <div class="d-flex align-items-center py-2">
                        <span class="bullet bg-primary me-3"></span>View Financial Summaries only
                    </div>
                    <div class="d-flex align-items-center py-2">
                        <span class="bullet bg-primary me-3"></span>View and Edit API Controls
                    </div>
                    <div class="d-flex align-items-center py-2">
                        <span class="bullet bg-primary me-3"></span>View Payouts only
                    </div>
                    <div class="d-flex align-items-center py-2">
                        <span class="bullet bg-primary me-3"></span>View and Edit Disputes
                    </div>
                    <div class="d-flex align-items-center py-2 d-none">
                        <span class='bullet bg-primary me-3'></span>
                        <em>and 3 more...</em>
                    </div>
                </div>
                <!--end::Permissions-->
            </div>
            <!--end::Card body-->
            <!--begin::Card footer-->
            <div class="card-footer pt-0">
                <!--
                <button class="btn btn-light btn-active-primary" data-bs-target="#kt_modal_update_role"
                        data-bs-toggle="modal"
                        type="button">Edit Role
                </button>
                -->
                <ThirdButton class-name="btn-light btn-active-primary"
                             name="Edit Role"
                             @click="updateRole"
                >
                </ThirdButton>
            </div>
            <!--end::Card footer-->
        </div>
        <!--end::Card-->

        <!--begin::Modal-->
        <!--begin::Modal - Update role-->
        <RoleDialog
            :key="dialogKey"
            :dialogFormVisible="dialogFormVisible"
            :form="form"
            :titleModal="titleModal"
            @onSubmit="handleSubmit"
            @update:dialogFormVisible="dialogFormVisible = $event"
        />
        <!--end::Modal - Update role-->
        <!--end::Modal-->
    </div>
</template>
