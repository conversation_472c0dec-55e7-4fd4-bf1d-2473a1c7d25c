<script setup>
import LoginLayout from '@/Layouts/LoginLayout.vue';
import {Head, useForm} from '@inertiajs/vue3';
import TextInput from "@/Components/TextInput.vue";
import InputError from "@/Components/InputError.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import {ref} from "vue";

defineProps({
    canResetPassword: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

// let loading = ref(false)

const submit = () => {
    // loading.value = true
    console.log('submit >> form: ', form);
    form.post(route('cms.login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <LoginLayout>
        <Head title="Log in"/>
        <div class="d-flex flex-column flex-lg-row-fluid py-10">
            <!--begin::Content-->
            <div class="d-flex flex-center flex-column flex-column-fluid">
                <!--begin::Wrapper-->
                <div class="w-lg-500px p-10 p-lg-15 mx-auto">
                    <!--begin::Form-->
                    <form id="kt_sign_in_form" action="" class="form w-100" novalidate="novalidate"
                          @submit.prevent="submit">
                        <!--begin::Heading-->
                        <div class="text-center mb-10">
                            <!--begin::Title-->
                            <h1 class="text-dark mb-3">Đăng nhập</h1>
                            <!--end::Title-->
                        </div>
                        <!--begin::Heading-->
                        <!--begin::Input group-->
                        <div class="fv-row mb-10">
                            <!--begin::Label-->
                            <label class="form-label fs-6 fw-bolder text-dark">Email</label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <!--
                            <input autocomplete="off" class="form-control form-control-lg form-control-solid"
                                   name="email"
                                   type="text"/>
                            -->
                            <!--
                            <TextInput
                                id="email"
                                v-model="form.email"
                                autocomplete="username"
                                autofocus
                                class="form-control form-control-lg form-control-solid"
                                required
                                type="email"
                            />
                            -->
                            <el-input v-model="form.email" size="large" placeholder="Nhập địa chỉ email" clearable />
                            <InputError :message="form.errors.email" class="mt-2"/>
                            <!--end::Input-->
                        </div>
                        <!--end::Input group-->
                        <!--begin::Input group-->
                        <div class="fv-row mb-2">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-stack mb-2">
                                <!--begin::Label-->
                                <label class="form-label fw-bolder text-dark fs-6 mb-0">Mật khẩu</label>
                                <!--end::Label-->
                            </div>
                            <!--end::Wrapper-->
                            <!--begin::Input-->
                            <!--
                            <input autocomplete="off" class="form-control form-control-lg form-control-solid"
                                   name="password" type="password"/>
                            -->
                            <!--
                            <TextInput
                                id="password"
                                v-model="form.password"
                                autocomplete="current-password"
                                class="form-control form-control-lg form-control-solid"
                                required
                                type="password"
                            />
                            -->
                            <el-input
                                v-model="form.password"
                                type="password"
                                placeholder="Nhập mật khẩu ..."
                                size="large"
                                show-password
                            />
                            <!--end::Input-->
                            <InputError :message="form.errors.password" class="mt-2"/>
                        </div>
                        <!--end::Input group-->


                        <!--begin::Input group-->
                        <div class="fv-row mb-0">
                            <!--begin::Wrapper-->
                            <div class="d-flex flex-stack mb-5">
                                <el-checkbox v-model="form.remember"
                                             class="form-check-input2"
                                             label="Ghi nhớ đăng nhập"
                                             size="large"
                                />
                            </div>
                        </div>
                        <!--end::Input group-->


                        <!--begin::Actions-->
                        <div class="text-center">
                            <!--begin::Submit button-->
                            <!--
                            <button id="kt_sign_in_submit" class="btn btn-lg btn-primary w-100 mb-5" type="submit">
                                <span class="indicator-label">Login</span>
                                <span class="indicator-progress">
                                        Please wait...
						                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                    </span>
                            </button>
                            -->
                            <!--
                            <PrimaryButton :class="{ 'opacity-25': form.processing }"
                                           :disabled="form.processing"
                                           class="btn btn-lg btn-primary w-100 mb-5"
                            >
                                Đăng nhập
                            </PrimaryButton>
                            -->

                            <el-button :loading="form.processing" native-type="submit" type="primary" class="w-100" size="large">Đăng nhập</el-button>
                            <!--end::Submit button-->
                        </div>
                        <!--end::Actions-->
                    </form>
                    <!--end::Form-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Content-->
        </div>
    </LoginLayout>
</template>
