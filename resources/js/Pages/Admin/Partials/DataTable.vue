<script setup>
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {ElMessage, ElTable} from 'element-plus'
import {computed, reactive, ref, watch} from "vue";
import ThirdButton from "@/Components/ThirdButton.vue";
// import IconAdd2 from "@/Components/Icons/IconAdd2.vue";
// import PermissionDialog from "@/Components/PermissionDialog.vue";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import {useForm, usePage, Link} from "@inertiajs/vue3";
import {showMessage} from "@/Helpers/messageHelper.js";
import DropdownButton from "@/Components/DropdownButton.vue";
// import IconFilter from "@/Components/Icons/IconFilter.vue";
// import IconExport from "@/Components/Icons/IconExport.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
    /*type: {
        type: String,
        default: 'users'
    },
    roleId: {
        type: Number,
        default: 0
    },
    */
    loading: Boolean,
    cateName: String,
    data: Array,
    // meta: Object,
    // statusArr: Array
})

const emit = defineEmits(['cb:onAddUser', 'cb:onUpdateUser', 'cb:onChangeStatus', 'cb:onReload']);

/*const roleClassMapping = {
    'Administrator': 'badge-light-primary',
    'Developer': 'badge-light-danger',
    'Analyst': 'badge-light-success',
    'Support': 'badge-light-info',
    'Trial': 'badge-light-warning'
};*/

const errors = computed(() => usePage().props.errors)

// check error and display message
watch(
    () => errors.value,
    () => {
        Object.entries(errors.value).forEach(([key, value]) => {
            // console.log(`${key}: ${value}`);
            ElMessage({
                message: `${key}: ${value}`,
                type: 'error'
            })
        });
    }
)

const dialogFormVisible = ref(false)
// const formLabelWidth = '140px'
const form = useForm({
    name: '',
    description: '',
    status: 1
})
let titleModal = ref('Add a ' + props.cateName)
let dialogKey = ref(1)

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemsDelete = reactive({})
let singleItemName = ref('')
const selectedRows = ref([]);
let isMultiDeleteType = ref(false)

const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
};

const deleteItemMultiple = (row, type) => {
    console.log('deleteItemMultiple >> row: ', row, ' -> type: ', type)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemsDelete = row
    if (type === 1) {
        isMultiDeleteType.value = false
        singleItemName.value = row?.name ?? ''
    } else {
        isMultiDeleteType.value = true
        singleItemName.value = ''
    }
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemsDelete: ', itemsDelete)
    // // const ids = itemsDelete.map(item => item.id);
    const idsArr = Array.isArray(itemsDelete) ? itemsDelete.map(item => item.id) : [itemsDelete.id];
    // console.log('ids: ', ids)

    //
    // // emit('cb:onDelete', itemsDelete.id)
    //

    if (isMultiDeleteType.value) {
        console.log('handleDeleteAction >> deleteSelected: ', idsArr)
        const formPost = useForm({
            ids: idsArr
        })
        formPost.post(route('cms.admin.destroys'), {
            onSuccess: (res) => {
                // loading.value = false
                // idsArr = []
                // const checkedCount = idsArr.length
                // isIndeterminate.value = checkedCount > 0 && checkedCount < props.items.data.length
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        console.log('handleDeleteAction >> dataDelete.value: ', itemsDelete.id)
        form.delete(route('cms.admin.destroy', itemsDelete), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    deleteDialogVisible.value = false

    // reset data
    // ---------------------------------------------------------------------------
    selectedRows.value = []
    singleItemName.value = ''
    itemsDelete = {}

}
// =====================================================================================================================
// End: delete

// Begin: Search
// =====================================================================================================================
const searchQuery = ref('');
const filteredData = ref([...props.data]);

const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Thực hiện hành động tìm kiếm ở đây
};

const filterData = () => {
    filteredData.value = props.data.filter(item =>
        item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
};
// =====================================================================================================================
// End: Search

// Begin: Add User
// =====================================================================================================================
const addUser = () => {
    console.log('addUser')
    emit('cb:onAddUser', true);
}
// =====================================================================================================================
// End: Add User

const getDetailUser = async (id) => {
    console.log('getDetailUser >> id: ', id)

    // Step 1: call api get invoice detail
    // =============================================
    const csrfToken = document.head.querySelector('meta[name="csrf-token"]').content
    console.log('csrfToken: ', csrfToken)
    let url = route('user.show', id)
    console.log('url: ', url)

    return axios.get(url, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            // 'Authorization': 'Bearer f11e52232eb6ed369b27beff966abfbf5aeabae71ec816f83ff380dfdc2f50b5'
        }
    }).then(res => {
        console.log('[getDetailUser]: ', res.data)
        // currentItem.value = res.data.data
        return res.data
    }).catch(error => {
        console.log('MINHError:', error)
    })

    // Step 2: set data
    // =============================================
    // dialogInvoiceVisible.value = true
    // valueIncreaseDialog.value += 1
}

const ActionCbHandler = async (item) => {
    console.log('UserTable >> ActionCbHandler >> item: ', item)
    const {action, row} = item
    // Handle the command and row here
    switch (action) {
        case 'edit':
            console.log('UserTable >> ActionCbHandler >> edit >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            // const item = await getDetailUser(row.id)
            // console.log('item: ', item)
            // emit('cb:onUpdateUser', item.data)
            emit('cb:onUpdateUser', row)
            break
        case 'delete':
            console.log('UserTable >> ActionCbHandler >> delete >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            itemsDelete = row
            deleteDialogKey.value += 1
            deleteDialogVisible.value = true
            isMultiDeleteType.value = false
            break
        default:
            console.log('UserTable >> ActionCbHandler >> default >> row: ', row)
            ElMessage(`click on item ${action} ${row?.name}`)
            break
    }
}

const handleEditItem = async (row) => {
    console.log('handleEditItem >> row: ', row)
    // const item = await getDetailUser(row.id)
    // console.log('item: ', item)
    // emit('cb:onUpdateUser', item.data)
    emit('cb:onUpdateUser', row)
}

const changeStatus = (item, val) => {
    console.log('changeStatus: ', item, ' => val: ', val)
    emit('cb:onChangeStatus', {id: item.id, status: val ? 1 : 0})
}

// Watch the searchQuery to update filteredData
watch(searchQuery, filterData);

// Initial filter
filterData();


const reloadData = () => {
    console.log('reloadData')
    emit('cb:onReload')
}
</script>


<style>
.el-table .cell, .el-scrollbar {
    padding: 0 !important;
}

.el-table__header {
    padding: 0 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    background-color: #b5b5c3 !important;
}

.el-dialog__headerbtn {
    top: 13px !important;
}
</style>

<template>
    <div id="kt_post" class="post d-flex flex-column-fluid mb-10">
        <!--begin::Container-->
        <div id="kt_content_container" class="container">
            <!--begin::Card-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header mt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1 me-5">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <IconSearch/>
                            </span>
                            <input v-model="searchQuery"
                                   class="form-control form-control-solid w-250px ps-15"
                                   data-kt-permissions-table-filter="search"
                                   :placeholder="'Search ' + props.cateName"
                                   type="text"
                                   @keyup.enter="filterData"
                            />
                        </div>
                        <!--end::Search-->
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Toolbar-->
                        <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                            <!--begin::Filter-->
                            <!--
                            <ThirdButton class-name="btn-light-primary me-3" name="Filter">
                                <IconFilter/>
                            </ThirdButton>
                            -->
                            <!--end::Filter-->
                            <!--begin::Export-->
                            <!--
                            <ThirdButton class-name="btn-light-primary me-3" name="Export">
                                <IconExport/>
                            </ThirdButton>
                            -->
                            <!--end::Export-->

                            <!--begin::Reload-->
                            <ThirdButton class-name="btn-light-primary me-3"
                                         name="Reload"
                                         @click.prevent="reloadData"
                            >
                                <el-icon size="22"><Refresh /></el-icon>
                            </ThirdButton>
                            <!--end::Reload-->

                            <!--begin::Add user-->
                            <ThirdButton class-name="btn-primary"
                                         name="Thêm mới"
                                         @click.prevent="addUser"
                            >
                                <IconAdd/>
                            </ThirdButton>
                            <!--end::Add user-->
                        </div>
                        <!--end::Toolbar-->

                        <!--begin::Button-->
                        <!--
                        <ThirdButton class="ml-5"
                                     class-name="btn-light-primary ml-5"
                                     :name="'Add ' + props.cateName"
                                     @click="addPermission"
                                     v-if="selectedRows.length <= 0"
                        >
                            <span class="svg-icon svg-icon-3">
                                <IconAdd2/>
                            </span>
                        </ThirdButton>
                        -->
                        <!--end::Button-->

                        <!--begin::Button-->
                        <div class="fw-bolder me-5 ms-2" v-if="selectedRows.length > 0">
                            <span class="me-2">{{ selectedRows.length }}</span>Selected
                        </div>
                        <button v-if="selectedRows.length > 0"
                                type="button"
                                class="btn btn-danger"
                                @click.prevent="deleteItemMultiple(selectedRows, 2)"
                        >Delete Selected</button>
                        <!--end::Button-->

                    </div>
                    <!--end::Card toolbar-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <el-table :data="filteredData" class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
                              @selection-change="handleSelectionChange"
                              v-loading="loading"
                              style="width: 100%; margin-left: 0; padding-left: 0;">
                        <el-table-column type="selection" width="55" />

                        <!--
                        <el-table-column label="Ảnh" width="80">
                            <template #default="scope">
                                <el-image style="width: 60px; height: 60px" :src="scope.row.image" fit="cover" />
                            </template>
                        </el-table-column>
                        -->

                        <!--
                        <el-table-column label="Id" width="50">
                            <template #default="scope">{{ scope.row?.id }}</template>
                        </el-table-column>
                        -->

                        <el-table-column label="Họ và Tên" min-width="200">
                            <template #default="scope">
                                <span class="fw-bolder">
                                    <!--
                                    <Link :href="route('cms.users.show', {id: scope.row.id})" class="text-gray-800 text-hover-primary mb-1">
                                    -->
                                    <span class="cursor-pointer" @click.prevent="handleEditItem(scope.row)">{{ scope.row?.name ?? '' }}</span>
                                    <!--
                                    </Link>
                                    -->
                                </span>
                                <br />
                                <span>{{ scope.row?.username }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column label="Email" min-width="100">
                            <template #default="scope">
                                {{ scope.row?.email ?? '' }}
                                <!--
                                <span class="badge badge-success fw-bolder">
                                    {{ scope.row?.email ?? '' }}
                                </span>
                                -->
                            </template>
                        </el-table-column>
                        <!--
                        <el-table-column label="App">
                            <template #default="scope">{{ scope.row?.appId ?? 0 }}</template>
                        </el-table-column>
                        -->

                        <el-table-column label="Trạng thái">
                            <template #default="scope">
                                <!--
                                {{ scope.row?.status ?? 0 }}
                                -->
                                <el-switch
                                    v-model="scope.row.isStatus"
                                    active-text="Hoạt động"
                                    class="ml-2"
                                    inactive-text="Khóa"
                                    inline-prompt
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                                    @change="changeStatus(scope.row, $event)"
                                />
                            </template>
                        </el-table-column>

                        <!--
                        <el-table-column label="Danh mục" min-width="110">
                            <template #default="scope">
                                <div class="badge badge-light fw-bolder">{{ scope.row?.category ?? '' }}</div>
                            </template>
                        </el-table-column>
                        -->
                        <!--
                        <el-table-column label="Cập nhật" property="updatedAt" />
                        -->
                        <el-table-column label="Ngày Tạo" property="created_at">
                            <template #default="scope">
                                <span>{{ scope.row.created_at.datetime ?? '' }}</span>
                            </template>
                        </el-table-column>
                        <!--begin::Action=-->
                        <el-table-column align="right" label="Hành động" width="100">
                            <template #default="scope">
                                <!--begin::Update-->
                                <!--
                                <ThirdButton
                                    class="btn btn-icon btn-active-light-primary w-30px h-30px me-3"
                                    @click.prevent="updatePermission(scope.row)"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit/>
                                    </span>
                                </ThirdButton>
                                -->
                                <!--end::Update-->
                                <!--begin::Delete-->
                                <!--
                                <ThirdButton class="btn btn-icon btn-active-light-primary w-30px h-30px"
                                             @click.prevent="deleteItem(scope.row, 1)"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconDelete/>
                                    </span>
                                </ThirdButton>
                                -->
                                <!--end::Delete-->

                                <DropdownButton :row="scope.row" @cb:action="ActionCbHandler" :button-name="'Chọn'">
                                    <template #default>
                                        <el-dropdown-menu>
                                            <el-dropdown-item command="edit">Cập nhật</el-dropdown-item>
<!--                                            <el-dropdown-item>
                                                <Link :href="route('cms.users.show', {id: scope?.row?.id})">Cập nhật</Link>
                                            </el-dropdown-item>-->
                                            <el-dropdown-item command="delete">Xóa</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </DropdownButton>

                            </template>
                        </el-table-column>
                        <!--end::Action=-->
                    </el-table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
                <!--begin::Pagination-->
                <!--
                <div class="d-flex flex-center mb-5">
                    <el-pagination :page-sizes="[10, 20, 50, 100, 200, 300]"
                                   :total="1000" background layout="total, sizes, prev, pager, next"/>
                </div>
                -->
                <!--end::Pagination-->
            </div>
            <!--end::Card-->

            <!--begin::Modals-->
            <!--begin::Modal - Add/Update permissions-->
            <!--
            <PermissionDialog
                :key="dialogKey"
                :dialogFormVisible="dialogFormVisible"
                :form="form"
                :titleModal="titleModal"
                @onSubmit="handleSubmit"
                @update:dialogFormVisible="dialogFormVisible = $event"
            />
            -->
            <!--end::Modal - Add/Update permissions-->

            <!--begin::Modal - Delete permissions-->
            <DeleteDialog
                :key="deleteDialogKey"
                :itemName="singleItemName ?? ''"
                :visible="deleteDialogVisible"
                @delete="handleDeleteAction"
                @update:visible="deleteDialogVisible = $event"
            />
            <!--end::Modal - Delete permissions-->
            <!--end::Modals-->
        </div>
        <!--end::Container-->
    </div>
</template>
