<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, useForm} from '@inertiajs/vue3'

// import {onMounted} from "vue";
import DataTable from "@/Pages/Replay/Partials/DataTable.vue";
import IconFilter from "@/Components/Icons/IconFilter.vue";
import Toolbar from "@/Components/Toolbar.vue";
// import UserDialog from "@/Pages/Users/<USER>/UserDialog.vue";
import {computed, reactive, ref} from "vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";

const props = defineProps({
    cateName: String,
    statusArr: Array,
    items: Object,
    items2: Object,
    querySearch: Object,
    paginate: Object,
    limit: Number,
    keyword: String,
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},*/
    // {text: 'Users'},
    {text: props.cateName + ' List'}
];

// Begin: Add User
// =====================================================================================================================
let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})
const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}
// =====================================================================================================================
// End: Add User

const objectSearch = reactive({
    page: props.queryParams?.page || 1,
    per_page: props.paginate?.per_page || 10,
    s: props.keyword, // props.queryParams?.s || null,
    // roleId: props.queryParams?.roleId || 0,
    // status: props.queryParams?.status || -1,
    // sort: props.queryParams?.sort || 'id:desc'
})


let searchParams = computed(() => {
    return new URLSearchParams(Object.fromEntries(Object.entries(objectSearch).filter(([_, v]) => v != null))).toString();
})

let loading = ref(false)
const fetchData = () => {
    // loadingContainer.value = true
    loading.value = true
    let form = useForm({})
    // const params = Object.fromEntries( Object.entries(objectSearch).filter(([key, value]) => value !== '' && value !== null) );
    form.get(route('cms.replays.index') + '?' + searchParams.value, {
        onSuccess: () => {
            // loadingContainer.value = false
            loading.value = false
        }
    })
}

const actions = [
    {
        name: 'Reload',
        className: 'btn-sm btn-flex btn-light-primary btn-active-primary fw-bolder',
        icon: IconRefresh,
        click: fetchData
    },
    {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    // {name: 'Create', className: 'btn-sm btn-primary'}
];

const handleMetaChange = (meta) => {
    console.log('[Replay >> Index] handleMetaChange >> meta: ', meta)
    objectSearch.page = meta.page
    objectSearch.per_page = meta.per_page
    objectSearch.s = meta.s
    fetchData();
}

const changePerPage = (perPage) => {
    console.log('[Index] changePerPage >> perPage: ', perPage)
    objectSearch.per_page = perPage
    fetchData()
}

const handleChangePage = (val) => {
    console.log(`[Index] handleChangePage: ${val}`)
    objectSearch.page = val
    fetchData()
}

const handleSearch = (data) => {
    console.log('[Replays >> Index] handleSearch >> data: ', data)
    objectSearch.s = data
    fetchData()
}

// Add/update these methods to handle pagination
const handlePageChange = (page, perPage) => {
    console.log('[Replay >> Index] handlePageChange >> page:', page, 'perPage:', perPage);
    objectSearch.page = page;
    // Ensure we're using the current perPage value, not resetting it
    if (perPage) {
        objectSearch.per_page = perPage;
    }
    fetchData();
};

const handlePerPageChange = (perPage) => {
    console.log('[Replay >> Index] handlePerPageChange >> perPage:', perPage);
    // Reset to page 1 when changing items per page
    objectSearch.page = 1;
    objectSearch.per_page = perPage;
    fetchData();
};

</script>

<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!-- pagination: {{ paginate}} -> limit: {{ limit }} -->
        <div class="container-fluid mb-15">
            <!--            <pre>
                            {{ items2.data }}
                        </pre>
                        <pre>
                            {{ paginate.data }}
                        </pre>-->
            <!--begin::Post-->
            <DataTable
                :data="items2.data"
                :meta="paginate.data"
                :totalCount="paginate?.total ?? 0"
                :totalPages="paginate?.total_pages ?? 0"
                :loading="loading"
                :cateName="cateName"
                :keyword="keyword"
                :per-page-prop="paginate?.per_page ?? 10"
                @cb:changePage="handlePageChange"
                @cb:changePerPage="handlePerPageChange"
                @cb:onSearch="handleSearch"
            />
            <!--end::Post-->
        </div>

    </MainLayout>
</template>
