<script setup>
import {onMounted, ref} from 'vue';
// import { Inertia } from '@inertiajs/inertia';
import {showMessage} from "@/Helpers/messageHelper.js";
import EmptyState from "@/Components/EmptyState.vue";

const props = defineProps({
    userId: {
        type: Number,
        required: true
    }
});

const transactions = ref([]);
const loading = ref(true);
const pagination = ref({
    currentPage: 1,
    perPage: 10,
    total: 0
});
const sortBy = ref('id:desc');
const keyword = ref('');

const fetchTransactions = async (page = 1) => {
    loading.value = true;
    try {
        const url = route('transactions.by-user', props.userId);
        // const response = await axios.get(`/api/transactions/user/${props.userId}`, {
        const response = await axios.get(`${url}`, {
            params: {
                page: page,
                per_page: pagination.value.perPage,
                sort: sortBy.value,
                s: keyword.value
            }
        });

        transactions.value = response.data.data;
        console.log(response.data.data);
        pagination.value = {
            currentPage: response.data.meta.current_page,
            perPage: response.data.meta.per_page,
            total: response.data.meta.total
        };
    } catch (error) {
        console.error('Error fetching transactions:', error);
        showMessage('Failed to load transaction logs', 'error');
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page) => {
    pagination.value.currentPage = page;
    fetchTransactions(page);
};

const handleSort = (column) => {
    // Toggle between asc and desc
    const currentDir = sortBy.value.split(':')[1];
    const newDir = currentDir === 'asc' ? 'desc' : 'asc';
    sortBy.value = `${column}:${newDir}`;
    fetchTransactions(1);
};

const handleSearch = () => {
    fetchTransactions(1);
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
};

const getTransactionTypeLabel = (type) => {
    const typeMap = {
        'CASH_IN': 'Nạp tiền',
        'CASH_OUT': 'Rút tiền',
        'TRANSFER': 'Chuyển tiền',
        'BET': 'Đặt cược',
        'WIN': 'Thắng cược',
        'REFUND': 'Hoàn tiền',
        'ADJUSTMENT': 'Điều chỉnh'
    };
    return typeMap[type] || type;
};

const getTransactionStatusClass = (status) => {
    const statusMap = {
        'PENDING': 'badge-light-warning',
        'COMPLETED': 'badge-light-success',
        'FAILED': 'badge-light-danger',
        'CANCELLED': 'badge-light-dark'
    };
    return statusMap[status] || 'badge-light-info';
};

onMounted(() => {
    fetchTransactions();
});
</script>

<template>
    <div id="kt_user_view_overview_tab" class="tab-pane fade show active" role="tabpanel">
        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Logs Giao Dịch</h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative my-1">
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                 fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                      transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <input type="text" v-model="keyword" class="form-control form-control-solid w-250px ps-15"
                               placeholder="Tìm kiếm..." @keyup.enter="handleSearch"/>
                    </div>
                    <!--end::Search-->

                    <!--begin::Button-->
                    <button class="btn btn-sm btn-light-primary ms-2" type="button" @click="handleSearch">
                        <i class="fa fa-search me-1"></i>Tìm kiếm
                    </button>
                    <!--end::Button-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body py-0">
                <div v-if="loading" class="d-flex justify-content-center py-10">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <EmptyState v-else-if="transactions.length === 0"
                            title="Không có dữ liệu"
                            description="Không tìm thấy logs giao dịch nào cho người dùng này."
                            icon="fa fa-file-text-o"/>

                <!--begin::Table-->
                <table v-else class="table align-middle table-row-dashed fs-6 text-gray-600 fw-bold gy-5">
                    <!--begin::Table head-->
                    <thead>
                    <tr class="text-start text-muted fw-bolder fs-7 text-uppercase gs-0">
                        <th class="min-w-100px" @click="handleSort('id')">ID</th>
                        <th class="min-w-150px" @click="handleSort('type')">Loại giao dịch</th>
                        <th class="min-w-150px" @click="handleSort('amount')">Số tiền</th>
                        <th class="min-w-150px" @click="handleSort('status')">Trạng thái</th>
                        <th class="min-w-150px" @click="handleSort('notes')">Ghi chú</th>
                        <th class="min-w-150px text-end" @click="handleSort('created_at')">Thời gian</th>
                    </tr>
                    </thead>
                    <!--end::Table head-->
                    <!--begin::Table body-->
                    <tbody>
                    <tr v-for="transaction in transactions" :key="transaction.id">
                        <td>{{ transaction.id }}</td>
                        <td>{{ getTransactionTypeLabel(transaction.type) }}</td>
                        <td>{{ formatAmount(transaction.amount) }}</td>
                        <td>
                                    <span :class="['badge', getTransactionStatusClass(transaction.status)]">
                                        {{ transaction.status }}
                                    </span>
                        </td>
                        <td>{{ transaction.notes || 'N/A' }}</td>
                        <td class="text-end">{{ formatDate(transaction.created_at) }}</td>
                    </tr>
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->

                <!--begin::Pagination-->
                <div v-if="transactions.length > 0"
                     class="d-flex justify-content-between align-items-center flex-wrap mt-5">
                    <div class="d-flex flex-wrap py-2 mr-3">
                        <button v-for="page in Math.ceil(pagination.total / pagination.perPage)"
                                :key="page"
                                class="btn btn-icon btn-sm"
                                :class="{ 'btn-active-light-primary active': page === pagination.currentPage, 'btn-light': page !== pagination.currentPage }"
                                @click="handlePageChange(page)">
                            {{ page }}
                        </button>
                    </div>
                    <div class="d-flex align-items-center py-3">
                        <span class="text-muted">Hiển thị {{ transactions.length }} trên {{
                                pagination.total
                            }} kết quả</span>
                    </div>
                </div>
                <!--end::Pagination-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
</template>
