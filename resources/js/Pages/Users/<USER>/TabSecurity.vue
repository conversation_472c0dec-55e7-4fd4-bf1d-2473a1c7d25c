<script setup>
import ThirdButton from "@/Components/ThirdButton.vue";
import IconEdit2 from "@/Components/Icons/IconEdit2.vue";

const emit = defineEmits(['cb:changePass', 'cb:changeRole', 'cb:changeEmail']);

const handleBtnChangePass = () => {
    console.log('handleBtnChangePass')
    emit('cb:changePass');
}

const handleBtnChangeRole = () => {
    console.log('handleBtnChangeRole')
    emit('cb:changeRole');
}

const handleBtnChangeEmail = () => {
    console.log('handleBtnChangeEmail')
    emit('cb:changeEmail');
}
</script>

<template>
    <div id="kt_user_view_overview_security" class="tab-pane fade" role="tabpanel">
        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Profile</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0 pb-5">
                <!--begin::Table wrapper-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table id="kt_table_users_login_session" class="table align-middle table-row-dashed gy-5">
                        <!--begin::Table body-->
                        <tbody class="fs-6 fw-bold text-gray-600">
                        <tr>
                            <td>Email</td>
                            <td><EMAIL></td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeEmail"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Password</td>
                            <td>******</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangePass"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Role</td>
                            <td>Administrator</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table wrapper-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->

        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title flex-column">
                    <h2>Email Notifications</h2>
                    <div class="fs-6 fw-bold text-muted">Choose what messages you’d like to receive for each of your
                        accounts.
                    </div>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">
                <!--begin::Form-->
                <form id="kt_users_email_notification_form" class="form">
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_0" checked='checked'
                                   class="form-check-input me-3" name="email_notification_0"
                                   type="checkbox" value="0"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_0">
                                <div class="fw-bolder">Successful Payments</div>
                                <div class="text-gray-600">Receive a notification for every successful payment.</div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_1" class="form-check-input me-3"
                                   name="email_notification_1" type="checkbox"
                                   value="1"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_1">
                                <div class="fw-bolder">Payouts</div>
                                <div class="text-gray-600">Receive a notification for every initiated payout.</div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_2" class="form-check-input me-3"
                                   name="email_notification_2" type="checkbox"
                                   value="2"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_2">
                                <div class="fw-bolder">Application fees</div>
                                <div class="text-gray-600">Receive a notification each time you collect a fee from an
                                    account.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_3" checked='checked'
                                   class="form-check-input me-3" name="email_notification_3"
                                   type="checkbox" value="3"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_3">
                                <div class="fw-bolder">Disputes</div>
                                <div class="text-gray-600">Receive a notification if a payment is disputed by a customer
                                    and for dispute resolutions.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_4" checked='checked'
                                   class="form-check-input me-3" name="email_notification_4"
                                   type="checkbox" value="4"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_4">
                                <div class="fw-bolder">Payment reviews</div>
                                <div class="text-gray-600">Receive a notification if a payment is marked as an elevated
                                    risk.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_5" class="form-check-input me-3"
                                   name="email_notification_5" type="checkbox"
                                   value="5"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_5">
                                <div class="fw-bolder">Mentions</div>
                                <div class="text-gray-600">Receive a notification if a teammate mentions you in a
                                    note.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_6" class="form-check-input me-3"
                                   name="email_notification_6" type="checkbox"
                                   value="6"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_6">
                                <div class="fw-bolder">Invoice Mispayments</div>
                                <div class="text-gray-600">Receive a notification if a customer sends an incorrect
                                    amount to pay their invoice.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_7" class="form-check-input me-3"
                                   name="email_notification_7" type="checkbox"
                                   value="7"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_7">
                                <div class="fw-bolder">Webhooks</div>
                                <div class="text-gray-600">Receive notifications about consistently failing webhook
                                    endpoints.
                                </div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <div class='separator separator-dashed my-5'></div>
                    <!--begin::Item-->
                    <div class="d-flex">
                        <!--begin::Checkbox-->
                        <div class="form-check form-check-custom form-check-solid">
                            <!--begin::Input-->
                            <input id="kt_modal_update_email_notification_8" class="form-check-input me-3"
                                   name="email_notification_8" type="checkbox"
                                   value="8"/>
                            <!--end::Input-->
                            <!--begin::Label-->
                            <label class="form-check-label" for="kt_modal_update_email_notification_8">
                                <div class="fw-bolder">Trial</div>
                                <div class="text-gray-600">Receive helpful tips when you try out our products.</div>
                            </label>
                            <!--end::Label-->
                        </div>
                        <!--end::Checkbox-->
                    </div>
                    <!--end::Item-->
                    <!--begin::Action buttons-->
                    <div class="d-flex justify-content-end align-items-center mt-12">
                        <!--begin::Button-->
                        <button id="kt_users_email_notification_cancel" class="btn btn-light me-5" type="button">
                            Cancel
                        </button>
                        <!--end::Button-->
                        <!--begin::Button-->
                        <button id="kt_users_email_notification_submit" class="btn btn-primary" type="button">
                            <span class="indicator-label">Save</span>
                            <span class="indicator-progress">Please wait...
																	<span
                                                                        class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                        <!--end::Button-->
                    </div>
                    <!--begin::Action buttons-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Card body-->
            <!--begin::Card footer-->
            <!--end::Card footer-->
        </div>
        <!--end::Card-->
    </div>
</template>
