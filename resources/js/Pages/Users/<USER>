<script setup>
import MainLayout from '@/Layouts/MainLayout.vue'
import {Head} from '@inertiajs/vue3'
import {reactive, ref} from 'vue'
import ThirdButton from "@/Components/ThirdButton.vue";
import IconFilter from "@/Components/Icons/IconFilter.vue";
import IconBack from "@/Components/Icons/IconBack.vue";
import Toolbar from "@/Components/Toolbar.vue";
import TabOverview from "@/Pages/Users/<USER>/TabOverview.vue";
import TabSecurity from "@/Pages/Users/<USER>/TabSecurity.vue";
import TabEventLog from "@/Pages/Users/<USER>/TabEventLog.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import UserUpdateDialog from "@/Pages/Users/<USER>/UserUpdateDialog.vue";
import UserUpdateEmailDialog from "@/Pages/Users/<USER>/UserUpdateEmailDialog.vue";
import UserUpdateRoleDialog from "@/Pages/Users/<USER>/UserUpdateRoleDialog.vue";
import UserUpdatePassDialog from "@/Pages/Users/<USER>/UserUpdatePassDialog.vue";
import DeleteDialog from "@/Components/DeleteDialog.vue";
import IconUp from "@/Components/Icons/IconUp.vue";
import IconDown from "@/Components/Icons/IconDown.vue";
import IconAngleDown from "@/Components/Icons/IconAngleDown.vue";
import TabReplay from "@/Pages/Users/<USER>/TabReplay.vue";
import TabTransaction from "@/Pages/Users/<USER>/TabTransaction.vue";

const props = defineProps({
    cateName: String,
    item: Object,
    userInfo: Object,
    userBalance: Object,
    page: Number,
    routeIndex: String,
    roleId: Number,
    orderStatusObj: Object,
    tab: String,
    userId: Number, // user id
    routerUpdateUserAddress: String,
    products: Object,
    accountList: Array,
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},
    {text: 'Users'},*/
    // {text: 'View User'}
];

const actions = [
    // {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    // {name: 'Create', className: 'btn-sm btn-primary'},
    {type: 'back', name: 'Quay lại', className: 'btn-sm2 btn-flex2 btn-light btn-light-primary fw-bolder2', icon: IconBack, href: '/users'},
];

// Begin: Update User
// =====================================================================================================================
let titleModal = ref('Update User Details')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})
const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}

const editUser = row => {
    // console.log('updatePermission >> row: ', row)
    // titleModal.value = 'Update Role'
    dialogFormVisible.value = true
    form.name = "" // row.name
    incrementDialogKey()
}

// =====================================================================================================================
// End: Update User

// Begin: Update Email
// =====================================================================================================================
let dialogKeyEmail = ref(1)
const dialogFormVisibleEmail = ref(false)
const handleClickBtnChangeEmail = () => {
    dialogFormVisibleEmail.value = true
    dialogKeyEmail.value += 1
}
const handleSubmitUpdateEmail = (data) => {
    console.log('handleSubmitUpdateEmail >>  data: ', data)
    showMessage('Submit change email success.');
}
// =====================================================================================================================
// End: Update Email

// Begin: Update Role
// =====================================================================================================================
let dialogKeyRole = ref(1)
const dialogFormVisibleRole = ref(false)
const handleClickBtnChangeRole = () => {
    dialogFormVisibleRole.value = true
    dialogKeyRole.value += 1
}
const handleSubmitUpdateRole = (data) => {
    console.log('handleSubmitUpdateRole >>  data: ', data)
    showMessage('Submit change role success.');
}
// =====================================================================================================================
// End: Update Role

// Begin: Update Password
// =====================================================================================================================
let dialogKeyPass = ref(1)
const dialogFormVisiblePass = ref(false)
const handleClickBtnChangePass = () => {
    dialogFormVisiblePass.value = true
    dialogKeyPass.value += 1
}
const handleSubmitUpdatePass = (data) => {
    console.log('handleSubmitUpdatePass >>  data: ', data)
    showMessage('Submit change pass success.');
}
// =====================================================================================================================
// End: Update Password

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemDelete = reactive({})
const deleteUser = row => {
    console.log('deletePermission >> row: ', row)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemDelete = row
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemDelete: ', itemDelete)
    deleteDialogVisible.value = false
}
// =====================================================================================================================
// End: delete

</script>

<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!--begin::Post-->
        <div id="kt_post" class="post d-flex flex-column-fluid">
            <!--begin::Container-->
            <div id="kt_content_container" class="container">
                <!--begin::Layout-->
                <div class="d-flex flex-column flex-xl-row">
                    <!--begin::Sidebar-->
                    <div class="flex-column flex-lg-row-auto w-100 w-xl-400px mb-10">
                        <!--begin::Card-->
                        <div class="card mb-5 mb-xl-8">
                            <!--begin::Card body-->
                            <div class="card-body pt-0 pt-lg-1">
                                <!--begin::Summary-->
                                <!--begin::Card-->
                                <div class="card">
                                    <!--begin::Card body-->
                                    <div class="card-body d-flex flex-center flex-column pt-12 p-9 px-0">
                                        <!--begin::Avatar-->
                                        <div class="symbol symbol-100px symbol-circle mb-7">
                                            <img alt="image" src="templates/dashboard/assets/media/avatars/150-1.jpg"/>
                                        </div>
                                        <!--end::Avatar-->
                                        <!--begin::Name-->
                                        <a class="fs-3 text-gray-800 text-hover-primary fw-bolder mb-3" href="#">{{ userInfo?.fullName }}</a>
                                        <!--end::Name-->
                                        <!--begin::Position-->
                                        <div class="mb-9">
                                            <!--begin::Badge-->
                                            <div class="badge badge-lg badge-light-primary d-inline">
                                                <!-- Administrator -->
                                                {{ userBalance?.balance ?? 0}}
                                            </div>
                                            <!--begin::Badge-->
                                        </div>
                                        <!--end::Position-->
                                        <!--begin::Info-->
                                        <!--begin::Info heading-->
                                        <div class="fw-bolder mb-3">Thống kê ván đánh
                                            <i class="fas fa-exclamation-circle ms-2 fs-7"
                                               data-bs-content="Number of support tickets assigned, closed and pending this week."
                                               data-bs-html="true" data-bs-toggle="popover"
                                               data-bs-trigger="hover"></i>
                                        </div>
                                        <!--end::Info heading-->
                                        <div class="d-flex flex-wrap flex-center">
                                            <!--begin::Stats-->
                                            <div class="border border-gray-300 border-dashed rounded py-3 px-3 mb-3">
                                                <div class="fs-4 fw-bolder text-gray-700">
                                                    <span class="w-75px">243</span>
                                                    <!--begin::Svg Icon-->
                                                    <span class="svg-icon svg-icon-3 svg-icon-success">
                                                        <IconUp/>
                                                    </span>
                                                    <!--end::Svg Icon-->
                                                </div>
                                                <div class="fw-bold text-muted">Tổng</div>
                                            </div>
                                            <!--end::Stats-->
                                            <!--begin::Stats-->
                                            <div
                                                class="border border-gray-300 border-dashed rounded py-3 px-3 mx-4 mb-3">
                                                <div class="fs-4 fw-bolder text-gray-700">
                                                    <span class="w-50px">56</span>
                                                    <!--begin::Svg Icon-->
                                                    <span class="svg-icon svg-icon-3 svg-icon-danger">
                                                        <IconDown/>
                                                    </span>
                                                    <!--end::Svg Icon-->
                                                </div>
                                                <div class="fw-bold text-muted">Thắng</div>
                                            </div>
                                            <!--end::Stats-->
                                            <!--begin::Stats-->
                                            <div class="border border-gray-300 border-dashed rounded py-3 px-3 mb-3">
                                                <div class="fs-4 fw-bolder text-gray-700">
                                                    <span class="w-50px">188</span>
                                                    <!--begin::Svg Icon-->
                                                    <span class="svg-icon svg-icon-3 svg-icon-success">
                                                        <IconUp/>
                                                    </span>
                                                    <!--end::Svg Icon-->
                                                </div>
                                                <div class="fw-bold text-muted">Thua</div>
                                            </div>
                                            <!--end::Stats-->
                                        </div>
                                        <!--end::Info-->
                                    </div>
                                    <!--end::Card body-->
                                </div>
                                <!--end::Card-->
                                <!--end::Summary-->
                                <!--begin::Details toggle-->
                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div aria-controls="kt_user_view_details" aria-expanded="false"
                                         class="fw-bolder rotate collapsible" data-bs-toggle="collapse"
                                         href="#kt_user_view_details"
                                         role="button">Details
                                        <span class="ms-2 rotate-180">
                                            <!--begin::Svg Icon | path: icons/duotone/Navigation/Angle-down.svg-->
                                            <span class="svg-icon svg-icon-3">
                                                <IconAngleDown/>
                                            </span>
                                            <!--end::Svg Icon-->
										</span>
                                    </div>
                                    <span>
                                        <el-tooltip
                                            class="box-item"
                                            content="Edit customer details"
                                            effect="dark"
                                            placement="top"
                                        >
                                            <ThirdButton class-name="btn-sm btn-light-primary"
                                                         name="Edit"
                                                         @click.prevent="editUser">
                                            </ThirdButton>
                                        </el-tooltip>
                                    </span>
                                </div>
                                <!--end::Details toggle-->
                                <div class="separator"></div>
                                <!--begin::Details content-->
                                <div id="kt_user_view_details" class="collapse show">
                                    <div class="pb-5 fs-6">
                                        <!--begin::Details item-->
                                        <div class="fw-bolder mt-5">Account ID</div>
                                        <div class="text-gray-600">{{ userInfo?.userId }}</div>
                                        <!--begin::Details item-->
                                        <!--begin::Details item-->
                                        <div class="fw-bolder mt-5">Email</div>
                                        <div class="text-gray-600">
                                            <a class="text-gray-600 text-hover-primary" href="#">{{ userInfo?.email }}</a>
                                        </div>
                                        <!--begin::Details item-->
                                        <!--begin::Details item-->
                                        <!--
                                        <div class="fw-bolder mt-5">Address</div>
                                        <div class="text-gray-600">101 Collin Street,
                                            <br/>Melbourne 3000 VIC
                                            <br/>Australia
                                        </div>
                                        -->
                                        <!--begin::Details item-->
                                        <!--begin::Details item-->
                                        <!--
                                        <div class="fw-bolder mt-5">Language</div>
                                        <div class="text-gray-600">English</div>
                                        -->
                                        <!--begin::Details item-->
                                        <!--begin::Details item-->
                                        <div class="fw-bolder mt-5">Last Login</div>
                                        <div class="text-gray-600">{{ userInfo?.lastLogin }}</div>
                                        <!--begin::Details item-->
                                    </div>
                                </div>
                                <!--end::Details content-->
                            </div>
                            <!--end::Card body-->
                        </div>
                        <!--end::Card-->
                    </div>
                    <!--end::Sidebar-->
                    <!--begin::Content-->
                    <div class="flex-lg-row-fluid ms-lg-15">
                        <!--begin:::Tabs-->
                        <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-bold mb-8">
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab"
                                   href="#kt_user_view_overview_tab">Tổng quan</a>
                            </li>
                            <!--end:::Tab item-->

                            <!--begin:::Tab item-->
                            <!--
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                   data-kt-countup-tabs="true" href="#kt_user_view_overview_security">Thông tin</a>
                            </li>
                            -->
                            <!--end:::Tab item-->
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                   href="#kt_user_view_overview_events_and_logs_tab">Thông báo</a>
                            </li>
                            <!--end:::Tab item-->

                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                   href="#kt_user_view_overview_transaction_tab">Logs giao dịch</a>
                            </li>
                            <!--end:::Tab item-->

                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                   href="#kt_user_view_overview_replay_tab">Logs ván đánh</a>
                            </li>
                            <!--end:::Tab item-->

                            <!--begin:::Tab item-->
                            <li class="nav-item ms-auto">
                                <ThirdButton class-name="btn-danger" name="Xóa User" @click.prevent="deleteUser">
                                    <i aria-hidden="true" class="fa fa-trash-o"></i>
                                </ThirdButton>
                            </li>
                            <!--end:::Tab item-->
                        </ul>
                        <!--end:::Tabs-->
                        <!--begin:::Tab content-->
                        <div id="myTabContent" class="tab-content">
                            <!--begin:::Tab pane - Tổng quan-->
                            <TabOverview :accountList="accountList"
                                         @cb:changeEmail="handleClickBtnChangeEmail"
                                         @cb:changeRole="handleClickBtnChangeRole"
                                         @cb:changePass="handleClickBtnChangePass"
                            />
                            <!--end:::Tab pane-->
                            <!--begin:::Tab pane - Security-->
                            <!--
                            <TabSecurity
                                @cb:changeEmail="handleClickBtnChangeEmail"
                                @cb:changeRole="handleClickBtnChangeRole"
                                @cb:changePass="handleClickBtnChangePass"
                            />
                            -->
                            <!--end:::Tab pane-->

                            <!--begin:::Tab thông báo -->
                            <TabEventLog/>
                            <!--end:::Tab thông báo-->

                            <!--begin:::Tab logs giao dịch -->
                            <TabTransaction :userId="userId" />
                            <!--end:::Tab logs giao dịch -->

                            <!--begin:::Tab logs ván đánh -->
                            <TabReplay :userId="userId" />
                            <!--end:::Tab logs ván đánh -->

                            <!--begin:::Tab pane - Event&Logs-->
                            <!-- <TabEventLog/> -->
                            <!--end:::Tab pane-->
                        </div>
                        <!--end:::Tab content-->
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Layout-->
                <!--begin::Modals-->

                <!--begin::Modal - Update User-->
                <UserUpdateDialog
                    :key="dialogKey"
                    :dialogFormVisible="dialogFormVisible"
                    :form="form"
                    :titleModal="titleModal"
                    @onSubmit="handleSubmit"
                    @update:dialogFormVisible="dialogFormVisible = $event"
                />
                <!--end::Modal - Update User-->

                <!--begin::Modal - Update email-->
                <UserUpdateEmailDialog
                    :key="dialogKeyEmail"
                    :dialogFormVisible="dialogFormVisibleEmail"
                    :form="form"
                    :titleModal="'Update Email Address'"
                    @onSubmit="handleSubmitUpdateEmail"
                    @update:dialogFormVisible="dialogFormVisibleEmail = $event"
                />
                <!--end::Modal - Update email-->

                <!--begin::Modal - Update password-->
                <UserUpdatePassDialog
                    :key="dialogKeyPass"
                    :dialogFormVisible="dialogFormVisiblePass"
                    :form="form"
                    :titleModal="'Update Password'"
                    @onSubmit="handleSubmitUpdatePass"
                    @update:dialogFormVisible="dialogFormVisiblePass = $event"
                />
                <!--end::Modal - Update password-->

                <!--begin::Modal - Update role-->
                <UserUpdateRoleDialog
                    :key="dialogKeyRole"
                    :dialogFormVisible="dialogFormVisibleRole"
                    :form="form"
                    :titleModal="'Update User Role'"
                    @onSubmit="handleSubmitUpdateRole"
                    @update:dialogFormVisible="dialogFormVisibleRole = $event"
                />
                <!--end::Modal - Update role-->

                <!--begin::Modal - Delete permissions-->
                <DeleteDialog
                    :key="deleteDialogKey"
                    :itemName="itemDelete?.name ?? ''"
                    :visible="deleteDialogVisible"
                    @delete="handleDeleteAction"
                    @update:visible="deleteDialogVisible = $event"
                />
                <!--end::Modal - Delete permissions-->

                <!--end::Modals-->
            </div>
            <!--end::Container-->
        </div>
        <!--end::Post-->


    </MainLayout>
</template>
