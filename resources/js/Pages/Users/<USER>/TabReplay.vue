<script setup>
import { ref, onMounted } from 'vue';
// import { Inertia } from '@inertiajs/inertia';
import { showMessage } from "@/Helpers/messageHelper.js";
import EmptyState from "@/Components/EmptyState.vue";

const props = defineProps({
    userId: {
        type: Number,
        required: true
    }
});

const gameReplays = ref([]);
const loading = ref(true);
const pagination = ref({
    currentPage: 1,
    perPage: 10,
    total: 0
});
const sortBy = ref('created_at:desc');
const keyword = ref('');
const selectedReplay = ref(null);
const showReplayDialog = ref(false);

const fetchGameReplays = async (page = 1) => {
    loading.value = true;
    try {
        const url = route('game-replays.by-user', props.userId);
        const response = await axios.get(`${url}`, {
            params: {
                page: page,
                per_page: pagination.value.perPage,
                sort: sortBy.value,
                s: keyword.value
            }
        });

        gameReplays.value = response.data.data;
        pagination.value = {
            currentPage: response.data.meta.current_page,
            perPage: response.data.meta.per_page,
            total: response.data.meta.total
        };
    } catch (error) {
        console.error('Error fetching game replays:', error);
        showMessage('Failed to load game replay logs', 'error');
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page) => {
    pagination.value.currentPage = page;
    fetchGameReplays(page);
};

const handleSort = (column) => {
    // Toggle between asc and desc
    const currentDir = sortBy.value.split(':')[1];
    const newDir = currentDir === 'asc' ? 'desc' : 'asc';
    sortBy.value = `${column}:${newDir}`;
    fetchGameReplays(1);
};

const handleSearch = () => {
    fetchGameReplays(1);
};

const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
};

const getGameTypeLabel = (type) => {
    const typeMap = {
        'POKER': 'Poker',
        'TEXAS_HOLDEM': 'Texas Hold\'em',
        'BLACKJACK': 'Blackjack',
        'BACCARAT': 'Baccarat',
        'ROULETTE': 'Roulette'
    };
    return typeMap[type] || type;
};

const getGameResultClass = (result) => {
    if (result === 'WIN') return 'badge-light-success';
    if (result === 'LOSE') return 'badge-light-danger';
    if (result === 'DRAW') return 'badge-light-warning';
    return 'badge-light-info';
};

const viewReplay = (replay) => {
    selectedReplay.value = replay;
    showReplayDialog.value = true;
};

onMounted(() => {
    fetchGameReplays();
});
</script>

<template>
    <div id="kt_user_view_overview_replay_tab" class="tab-pane fade" role="tabpanel">
        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Logs Ván Đánh</h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative my-1">
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"></path>
                            </svg>
                        </span>
                        <input type="text" v-model="keyword" class="form-control form-control-solid w-250px ps-15" placeholder="Tìm kiếm..." @keyup.enter="handleSearch" />
                    </div>
                    <!--end::Search-->

                    <!--begin::Button-->
                    <button class="btn btn-sm btn-light-primary ms-2" type="button" @click="handleSearch">
                        <i class="fa fa-search me-1"></i>Tìm kiếm
                    </button>
                    <!--end::Button-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body py-0">
                <div v-if="loading" class="d-flex justify-content-center py-10">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <EmptyState v-else-if="gameReplays.length === 0"
                           title="Không có dữ liệu"
                           description="Không tìm thấy logs ván đánh nào cho người dùng này."
                           icon="fa fa-gamepad" />

                <!--begin::Table-->
                <table v-else class="table align-middle table-row-dashed fs-6 text-gray-600 fw-bold gy-5">
                    <!--begin::Table head-->
                    <thead>
                        <tr class="text-start text-muted fw-bolder fs-7 text-uppercase gs-0">
                            <th class="min-w-100px" @click="handleSort('id')">ID</th>
                            <th class="min-w-150px" @click="handleSort('game_type')">Loại game</th>
                            <th class="min-w-150px" @click="handleSort('table_id')">Bàn chơi</th>
                            <th class="min-w-150px" @click="handleSort('bet_amount')">Số tiền cược</th>
                            <th class="min-w-150px" @click="handleSort('result')">Kết quả</th>
                            <th class="min-w-150px text-end" @click="handleSort('created_at')">Thời gian</th>
                            <th class="min-w-100px text-center">Hành động</th>
                        </tr>
                    </thead>
                    <!--end::Table head-->
                    <!--begin::Table body-->
                    <tbody>
                        <tr v-for="replay in gameReplays" :key="replay.id">
                            <td>{{ replay.id }}</td>
                            <td>{{ getGameTypeLabel(replay.game_type) }}</td>
                            <td>{{ replay.table_id }}</td>
                            <td>{{ formatAmount(replay.bet_amount) }}</td>
                            <td>
                                <span :class="['badge', getGameResultClass(replay.result)]">
                                    {{ replay.result }}
                                </span>
                            </td>
                            <td class="text-end">{{ formatDate(replay.created_at) }}</td>
                            <td class="text-center">
                                <button class="btn btn-icon btn-light-primary btn-sm" @click="viewReplay(replay)" title="Xem chi tiết">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->

                <!--begin::Pagination-->
                <div v-if="gameReplays.length > 0" class="d-flex justify-content-between align-items-center flex-wrap mt-5">
                    <div class="d-flex flex-wrap py-2 mr-3">
                        <button v-for="page in Math.ceil(pagination.total / pagination.perPage)"
                                :key="page"
                                class="btn btn-icon btn-sm"
                                :class="{ 'btn-active-light-primary active': page === pagination.currentPage, 'btn-light': page !== pagination.currentPage }"
                                @click="handlePageChange(page)">
                            {{ page }}
                        </button>
                    </div>
                    <div class="d-flex align-items-center py-3">
                        <span class="text-muted">Hiển thị {{ gameReplays.length }} trên {{ pagination.total }} kết quả</span>
                    </div>
                </div>
                <!--end::Pagination-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->

        <!-- Replay Dialog -->
        <el-dialog
            v-model="showReplayDialog"
            title="Chi tiết ván đánh"
            width="70%"
            :before-close="() => { showReplayDialog = false; selectedReplay = null; }">
            <div v-if="selectedReplay" class="game-replay-details">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h3 class="fs-4">Thông tin ván đánh</h3>
                        <div class="table-responsive">
                            <table class="table table-hover table-rounded border gy-7 gs-7">
                                <tbody>
                                    <tr>
                                        <td class="fw-bold">ID</td>
                                        <td>{{ selectedReplay.id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Loại game</td>
                                        <td>{{ getGameTypeLabel(selectedReplay.game_type) }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Bàn chơi</td>
                                        <td>{{ selectedReplay.table_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Số tiền cược</td>
                                        <td>{{ formatAmount(selectedReplay.bet_amount) }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Kết quả</td>
                                        <td>
                                            <span :class="['badge', getGameResultClass(selectedReplay.result)]">
                                                {{ selectedReplay.result }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Thời gian</td>
                                        <td>{{ formatDate(selectedReplay.created_at) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h3 class="fs-4">Thông tin chi tiết</h3>
                        <div v-if="selectedReplay.game_data" class="p-4 bg-light-primary rounded border border-primary">
                            <pre class="mb-0">{{ JSON.stringify(selectedReplay.game_data, null, 2) }}</pre>
                        </div>
                        <div v-else class="alert alert-info">
                            Không có dữ liệu chi tiết cho ván đánh này.
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showReplayDialog = false">Đóng</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style scoped>
.game-replay-details pre {
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
