<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal" style="margin-top: 5%; !important;" width="40%">
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>
        <el-form :model="form" class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7">

            <!--begin::Notice-->
            <!--begin::Notice-->
            <div
                class="notice d-flex bg-light-primary rounded border-primary border border-dashed mb-9 p-6">
                <!--begin::Icon-->
                <!--begin::Svg Icon | path: icons/duotone/Code/Warning-1-circle.svg-->
                <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
															<svg height="24px" version="1.1"
                                                                 viewBox="0 0 24 24" width="24px"
                                                                 xmlns="http://www.w3.org/2000/svg">
																<circle cx="12" cy="12" fill="#000000" opacity="0.3"
                                                                        r="10"/>
																<rect fill="#000000" height="8" rx="1" width="2" x="11"
                                                                      y="7"/>
																<rect fill="#000000" height="2" rx="1" width="2" x="11"
                                                                      y="16"/>
															</svg>
														</span>
                <!--end::Svg Icon-->
                <!--end::Icon-->
                <!--begin::Wrapper-->
                <div class="d-flex flex-stack flex-grow-1">
                    <!--begin::Content-->
                    <div class="fw-bold">
                        <div class="fs-6 text-gray-700">Please note that a valid email address
                            is required to complete the email verification.
                        </div>
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Notice-->
            <!--end::Notice-->
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fs-6 fw-bold form-label mb-2">
                    <span class="required">Email Address</span>
                </label>
                <!--end::Label-->
                <!--begin::Input-->
                <input class="form-control form-control-solid" name="profile_email"
                       placeholder="" value="<EMAIL>"/>
                <!--end::Input-->
            </div>
            <!--end::Input group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Discard</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Submit
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = () => {
    emit('onSubmit', props.form);
    localDialogFormVisible.value = false;
};
</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
