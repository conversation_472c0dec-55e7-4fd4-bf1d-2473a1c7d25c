<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal" style="margin-top: 5%; !important;" width="40%">
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>
        <el-form :model="form" class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7">

            <!--begin::Input group=-->
            <div class="fv-row mb-10">
                <label class="required form-label fs-6 mb-2">Current Password</label>
                <input autocomplete="off"
                       class="form-control form-control-lg form-control-solid"
                       name="current_password" placeholder="" type="password"/>
            </div>
            <!--end::Input group=-->
            <!--begin::Input group-->
            <div class="mb-10 fv-row" data-kt-password-meter="true">
                <!--begin::Wrapper-->
                <div class="mb-1">
                    <!--begin::Label-->
                    <label class="form-label fw-bold fs-6 mb-2">New Password</label>
                    <!--end::Label-->
                    <!--begin::Input wrapper-->
                    <div class="position-relative mb-3">
                        <input autocomplete="off"
                               class="form-control form-control-lg form-control-solid"
                               name="new_password" placeholder=""
                               type="password"/>
                        <span
                            class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2"
                            data-kt-password-meter-control="visibility">
																	<i class="bi bi-eye-slash fs-2"></i>
																	<i class="bi bi-eye fs-2 d-none"></i>
																</span>
                    </div>
                    <!--end::Input wrapper-->
                    <!--begin::Meter-->
                    <div class="d-flex align-items-center mb-3"
                         data-kt-password-meter-control="highlight">
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                    </div>
                    <!--end::Meter-->
                </div>
                <!--end::Wrapper-->
                <!--begin::Hint-->
                <div class="text-muted">Use 8 or more characters with a mix of letters, numbers
                    &amp; symbols.
                </div>
                <!--end::Hint-->
            </div>
            <!--end::Input group=-->
            <!--begin::Input group=-->
            <div class="fv-row mb-10">
                <label class="form-label fw-bold fs-6 mb-2">Confirm New Password</label>
                <input autocomplete="off"
                       class="form-control form-control-lg form-control-solid"
                       name="confirm_password" placeholder="" type="password"/>
            </div>
            <!--end::Input group=-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Discard</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Submit
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = () => {
    emit('onSubmit', props.form);
    localDialogFormVisible.value = false;
};
</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
