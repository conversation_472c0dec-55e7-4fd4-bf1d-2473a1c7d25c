<script setup>
import ThirdButton from "@/Components/ThirdButton.vue";
import IconEdit2 from "@/Components/Icons/IconEdit2.vue";
import {Minus, Plus} from '@element-plus/icons-vue'

const props = defineProps({
    // cateName: String,
    // item: Object,
    // userInfo: Object,
    // userBalance: Object,
    // page: Number,
    // routeIndex: String,
    // roleId: Number,
    // orderStatusObj: Object,
    // tab: String,
    // userId: Number, // user id
    // routerUpdateUserAddress: String,
    // products: Object,
    userInfo: Object,
    playerInfo: Object,
    accountList: Array,
})


const emit = defineEmits(['cb:changePass', 'cb:changeRole', 'cb:changeEmail']);

const handleBtnChangePass = () => {
    console.log('handleBtnChangePass')
    emit('cb:changePass');
}

const handleBtnChangeRole = () => {
    console.log('handleBtnChangeRole')
    emit('cb:changeRole');
}

const handleBtnChangeEmail = () => {
    console.log('handleBtnChangeEmail')
    emit('cb:changeEmail');
}

</script>

<template>
    <div id="kt_user_view_overview_tab" class="tab-pane fade show active" role="tabpanel">

        <!--begin::Danh sách tài khoản-->
        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title flex-column">
                    <h2 class="mb-1">Danh sách tài khoản</h2>
                    <div class="fs-6 fw-bold text-muted">Tổng có {{ accountList.length }} tài khoản</div>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Filter-->
                    <!--
                    <button id="kt_modal_sign_out_sesions" class="btn btn-sm btn-flex btn-light-primary" type="button">
                        <span class="svg-icon svg-icon-3">
                            <svg fill="none" height="24"
                                 viewBox="0 0 24 24" width="24"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M13.1409 21.8611C11.5331 21.7101 10.3267 20.4628 10.2092 18.8523C10.0992 17.3432 10 15.118 10 12C10 8.882 10.0992 6.65682 10.2092 5.14773C10.3267 3.5372 11.5331 2.2899 13.1408 2.13886C13.9779 2.06022 14.9589 2 16 2C17.0411 2 18.0221 2.06022 18.8592 2.13886C20.4669 2.2899 21.6733 3.53721 21.7908 5.14773C21.9008 6.65683 22 8.882 22 12C22 15.118 21.9008 17.3432 21.7908 18.8523C21.6733 20.4628 20.4669 21.7101 18.8591 21.8611C18.0221 21.9398 17.0411 22 16 22C14.9589 22 13.9779 21.9398 13.1409 21.8611Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path clip-rule="evenodd"
                                      d="M7.20711 14.7929C7.59763 15.1834 7.59763 15.8166 7.20711 16.2071C6.81658 16.5976 6.18342 16.5976 5.79289 16.2071L2.29289 12.7071C1.90237 12.3166 1.90237 11.6834 2.29289 11.2929L5.79289 7.79289C6.18342 7.40237 6.81658 7.40237 7.20711 7.79289C7.59763 8.18342 7.59763 8.81658 7.20711 9.20711L5.41421 11H15C15.5523 11 16 11.4477 16 12C16 12.5523 15.5523 13 15 13L5.41421 13L7.20711 14.7929Z"
                                      fill="#12131A"
                                      fill-rule="evenodd"/>
                            </svg>
                        </span>
                        Sign out all sessions
                    </button>
                    -->
                    <!--end::Filter-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0 pb-5">
                <!--begin::Table wrapper-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table id="kt_table_users_login_session" class="table align-middle table-row-dashed gy-5">
                        <!--begin::Table head-->
                        <thead class="border-bottom border-gray-200 fs-7 fw-bolder">
                        <!--begin::Table row-->
                        <tr class="text-start text-muted text-uppercase gs-0">
                            <!--                            <th class="min-w-100px">Id</th>-->
                            <th class="">Id</th>
                            <th>App</th>
                            <th>Tên</th>
                            <th class="min-w-125px">Giá trị</th>
                            <th class="min-w-70px">Ngày tạo</th>
                            <th class="min-w-70px">Hành động</th>
                        </tr>
                        <!--end::Table row-->
                        </thead>
                        <!--end::Table head-->
                        <!--begin::Table body-->
                        <tbody class="fs-6 fw-bold text-gray-600">
                        <tr v-for="account in accountList" :key="account.id">
                            <!--begin::Invoice=-->
                            <td>{{ account.id }}</td>
                            <!--end::Invoice=-->
                            <!--begin::Status=-->
                            <td>{{ account.appName }}({{ account.appId }})</td>
                            <!--end::Status=-->
                            <!--begin::Amount=-->
                            <td>{{ account.fullName }}({{ account.name }})</td>
                            <!--end::Amount=-->
                            <!--begin::Date=-->
                            <td>{{ account.balance }}</td>
                            <!--end::Date=-->
                            <!--begin::Action=-->
                            <td>{{ account.createdAt }}</td>
                            <!--end::Action=-->
                            <!--begin::Action=-->
                            <td style="text-align: center">
                                <!--
                                <el-button type="primary" :icon="Edit" circle/>
                                -->

                                <el-tooltip
                                    class="box-item"
                                    content="Cộng giá trị"
                                    effect="dark"
                                    placement="top"
                                >
                                    <el-button :icon="Plus"
                                               @click.stop.prevent="handleEditItem(scope.row)"
                                               type="primary"></el-button>
                                </el-tooltip>

                                <el-tooltip
                                    class="box-item"
                                    content="Trừ giá trị"
                                    effect="dark"
                                    placement="top"
                                >
                                    <el-button :icon="Minus"
                                               @click.stop.prevent="handleEditItem(scope.row)"
                                               type="danger"></el-button>
                                </el-tooltip>

                            </td>
                            <!--end::Action=-->
                        </tr>
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table wrapper-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
        <!--end::Danh sách tài khoản-->

        playerInfo: {{ playerInfo }} <br/>
        userInfo: {{ userInfo }}

        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Thông tin cá nhân</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0 pb-5">
                <!--begin::Table wrapper-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table id="kt_table_users_login_session" class="table align-middle table-row-dashed gy-5">
                        <!--begin::Table body-->
                        <tbody class="fs-6 fw-bold text-gray-600">
                        <tr>
                            <td>Email</td>
                            <td>{{ userInfo?.email ?? '' }}</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeEmail"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Password</td>
                            <td>******</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangePass"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Role</td>
                            <td>Administrator</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Giới tính</td>
                            <td>{{ userInfo?.gender ?? '' }}</td>
                            <td class="text-end">
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                    <!--end::Svg Icon-->
                                </ThirdButton>
                            </td>
                        </tr>
                        <tr>
                            <td>Login Type</td>
                            <td>{{ userInfo?.loginType ?? '' }}</td>
                            <td class="text-end">
                                <!--
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                </ThirdButton>
                                -->
                            </td>
                        </tr>
                        <tr>
                            <td>Ngày tham gia</td>
                            <td>{{ userInfo?.createdAt ?? '' }}</td>
                            <td class="text-end">
                                <!--
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                </ThirdButton>
                                -->
                            </td>
                        </tr>
                        <tr>
                            <td>Đăng nhập lần cuối</td>
                            <td>{{ userInfo?.lastLogin ?? '' }}</td>
                            <td class="text-end">
                                <!--
                                <ThirdButton
                                    class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                    @click.prevent="handleBtnChangeRole"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit2/>
                                    </span>
                                </ThirdButton>
                                -->
                            </td>
                        </tr>
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table wrapper-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->

        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Login Sessions</h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Filter-->
                    <button id="kt_modal_sign_out_sesions" class="btn btn-sm btn-flex btn-light-primary" type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Sign-Out.svg-->
                        <span class="svg-icon svg-icon-3">
                            <svg fill="none" height="24"
                                 viewBox="0 0 24 24" width="24"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M13.1409 21.8611C11.5331 21.7101 10.3267 20.4628 10.2092 18.8523C10.0992 17.3432 10 15.118 10 12C10 8.882 10.0992 6.65682 10.2092 5.14773C10.3267 3.5372 11.5331 2.2899 13.1408 2.13886C13.9779 2.06022 14.9589 2 16 2C17.0411 2 18.0221 2.06022 18.8592 2.13886C20.4669 2.2899 21.6733 3.53721 21.7908 5.14773C21.9008 6.65683 22 8.882 22 12C22 15.118 21.9008 17.3432 21.7908 18.8523C21.6733 20.4628 20.4669 21.7101 18.8591 21.8611C18.0221 21.9398 17.0411 22 16 22C14.9589 22 13.9779 21.9398 13.1409 21.8611Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path clip-rule="evenodd"
                                      d="M7.20711 14.7929C7.59763 15.1834 7.59763 15.8166 7.20711 16.2071C6.81658 16.5976 6.18342 16.5976 5.79289 16.2071L2.29289 12.7071C1.90237 12.3166 1.90237 11.6834 2.29289 11.2929L5.79289 7.79289C6.18342 7.40237 6.81658 7.40237 7.20711 7.79289C7.59763 8.18342 7.59763 8.81658 7.20711 9.20711L5.41421 11H15C15.5523 11 16 11.4477 16 12C16 12.5523 15.5523 13 15 13L5.41421 13L7.20711 14.7929Z"
                                      fill="#12131A"
                                      fill-rule="evenodd"/>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->Sign out all sessions
                    </button>
                    <!--end::Filter-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0 pb-5">
                <!--begin::Table wrapper-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table id="kt_table_users_login_session" class="table align-middle table-row-dashed gy-5">
                        <!--begin::Table head-->
                        <thead class="border-bottom border-gray-200 fs-7 fw-bolder">
                        <!--begin::Table row-->
                        <tr class="text-start text-muted text-uppercase gs-0">
                            <th class="min-w-100px">Location</th>
                            <th>Device</th>
                            <th>IP Address</th>
                            <th class="min-w-125px">Time</th>
                            <th class="min-w-70px">Actions</th>
                        </tr>
                        <!--end::Table row-->
                        </thead>
                        <!--end::Table head-->
                        <!--begin::Table body-->
                        <tbody class="fs-6 fw-bold text-gray-600">
                        <tr>
                            <!--begin::Invoice=-->
                            <td>Australia</td>
                            <!--end::Invoice=-->
                            <!--begin::Status=-->
                            <td>Chome - Windows</td>
                            <!--end::Status=-->
                            <!--begin::Amount=-->
                            <td>207.24.21.386</td>
                            <!--end::Amount=-->
                            <!--begin::Date=-->
                            <td>23 seconds ago</td>
                            <!--end::Date=-->
                            <!--begin::Action=-->
                            <td>Current session</td>
                            <!--end::Action=-->
                        </tr>
                        <tr>
                            <!--begin::Invoice=-->
                            <td>Australia</td>
                            <!--end::Invoice=-->
                            <!--begin::Status=-->
                            <td>Safari - iOS</td>
                            <!--end::Status=-->
                            <!--begin::Amount=-->
                            <td>207.43.36.266</td>
                            <!--end::Amount=-->
                            <!--begin::Date=-->
                            <td>3 days ago</td>
                            <!--end::Date=-->
                            <!--begin::Action=-->
                            <td>
                                <a data-kt-users-sign-out="single_user" href="#">Sign out</a>
                            </td>
                            <!--end::Action=-->
                        </tr>
                        <tr>
                            <!--begin::Invoice=-->
                            <td>Australia</td>
                            <!--end::Invoice=-->
                            <!--begin::Status=-->
                            <td>Chrome - Windows</td>
                            <!--end::Status=-->
                            <!--begin::Amount=-->
                            <td>207.40.37.391</td>
                            <!--end::Amount=-->
                            <!--begin::Date=-->
                            <td>last week</td>
                            <!--end::Date=-->
                            <!--begin::Action=-->
                            <td>Expired</td>
                            <!--end::Action=-->
                        </tr>
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table wrapper-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->

    </div>
</template>
