<script setup>
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {ElMessage, ElTable} from 'element-plus'
import {computed, reactive, ref, watch} from "vue";
import PermissionDialog from "@/Components/PermissionDialog.vue";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import {Link, router, useForm, usePage} from "@inertiajs/vue3";
import {showMessage} from "@/Helpers/messageHelper.js";
import DropdownButton from "@/Components/DropdownButton.vue";

const props = defineProps({
    /*type: {
        type: String,
        default: 'users'
    },
    roleId: {
        type: Number,
        default: 0
    },
    loading: Boolean,*/
    cateName: String,
    data: Array,
    meta: Object,
    totalCount: Number,
    totalPages: Number,
    page: {
        type: Number,
        default: 1
    },
    perPageProp: {
        type: Number,
        default: 10
    },
    keyword: {
        type: String,
        default: ''
    }
})

const emit = defineEmits([
    'cb:onAddUser',
    'cb:metaChange',
    'cb:onEditUser',
    'cb:onPerPageChange',
    'cb:onPageChange'
])

// Pagination state
let currentPage = ref(props.page || 1)
let perPage = ref(props.perPageProp || 10)
// let keywordLocal = ref(props.keyword || '')
let searchQuery = ref(props.keyword || '');
const total = computed(() => props.totalCount || 0)

// Watch for prop changes to update local state
watch(() => props.page, (newPage) => {
    if (newPage !== currentPage.value) {
        currentPage.value = newPage
    }
})

watch(() => props.perPageProp, (newPerPage) => {
    if (newPerPage !== perPage.value) {
        perPage.value = newPerPage
    }
})

watch(() => props.keyword, (newKeyword) => {
    if (newKeyword !== searchQuery.value) {
        searchQuery.value = newKeyword
    }
})

let metaData = reactive({
    page: currentPage.value,
    per_page: perPage.value,
    s: searchQuery.value
})

// Watch for changes in pagination parameters
watch([currentPage, perPage, searchQuery], ([newPage, newPerPage, newKeyword]) => {
    console.log('[DataTable] watch: currentPage: ', currentPage)
    console.log('[DataTable] watch: perPage: ', perPage)
    console.log('[DataTable] watch: newPage: ', newPage)
    console.log('[DataTable] watch: newPerPage: ', newPerPage)
    metaData.per_page = newPerPage
    metaData.page = newPage
    metaData.s = newKeyword
    emit('cb:metaChange', metaData)
    // handlePaginationChange(newPage, newPerPage)
})

// Handle pagination changes
const handlePaginationChange = (page, itemsPerPage) => {
    // Emit events to parent component
    emit('update:page', page)
    emit('update:perPage', itemsPerPage)

    // Calculate firstResult and maxResult for backend pagination
    const firstResult = (page - 1) * itemsPerPage
    const maxResult = itemsPerPage

    // Navigate with Inertia to update the data
    router.get(
        route(route().current()),
        {
            page: page,
            per_page: itemsPerPage,
            first_result: firstResult,
            max_result: maxResult,
            search: searchQuery.value
        },
        {
            preserveState: true,
            preserveScroll: true,
            only: ['data', 'meta', 'totalCount', 'totalPages']
        }
    )
}

// Handle page size change
const handleSizeChange = (val) => {
    // perPage.value = val
    metaData.per_page = val
    // Emit the perPage change to parent
    emit('cb:onPerPageChange', val)
}

// Handle current page change
const handleCurrentChange = (val) => {
    // currentPage.value = val
    metaData.page = val
    emit('cb:onPageChange', val)
}

const errors = computed(() => usePage().props.errors)

// check error and display message
watch(
    () => errors.value,
    () => {
        Object.entries(errors.value).forEach(([key, value]) => {
            // console.log(`${key}: ${value}`);
            ElMessage({
                message: `${key}: ${value}`,
                type: 'error'
            })
        });
    }
)

const dialogFormVisible = ref(false)
// const formLabelWidth = '140px'
const form = useForm({
    name: '',
    description: '',
    status: 1
})
let titleModal = ref('Add a ' + props.cateName)
let dialogKey = ref(1)

const incrementDialogKey = () => {
    dialogKey.value += 1
}

/*const addPermission = () => {
    console.log('addPermission')
    titleModal.value = 'Add a ' + props.cateName
    dialogFormVisible.value = true
    incrementDialogKey()

    form.name = ''
    form.description = ''
    form.status = 1
}*/

/*const updatePermission = row => {
    console.log('updatePermission >> row: ', row)
    titleModal.value = 'Update ' + props.cateName
    dialogFormVisible.value = true
    form.name = row.name
    incrementDialogKey()
}*/

const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    // dialogFormVisible.value = false
    form.post(route('cms.permissions.store'), {
        preserveScroll: true,
        onSuccess: (res) => {
            dialogFormVisible.value = false
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemsDelete = reactive({})
let singleItemName = ref('')
const selectedRows = ref([]);

const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
};

const deleteItem = (row, type) => {
    console.log('deleteItem >> row: ', row, ' -> type: ', type)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemsDelete = row
    if (type === 1) {
        singleItemName.value = row.title
    } else {
        singleItemName.value = ''
    }
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemsDelete: ', itemsDelete)

    // Check if we're deleting a single item or multiple items
    if (Array.isArray(itemsDelete)) {
        const ids = itemsDelete.map(item => item.uid);

        if (ids.length > 0) {
            // Call API to delete multiple users
            router.delete(route('cms.users.destroy-multiple'), {
                data: {ids: ids},
                onSuccess: () => {
                    showMessage('Xóa người dùng thành công!', 'success');
                    // Reset selection
                    selectedRows.value = [];
                },
                onError: (errors) => {
                    showMessage('Lỗi khi xóa người dùng: ' + Object.values(errors).join(', '), 'error');
                }
            });
        }
    } else {
        // Deleting a single user
        if (itemsDelete?.uid) {
            router.delete(route('cms.users.destroy', {id: itemsDelete.uid}), {
                onSuccess: () => {
                    showMessage('Xóa người dùng thành công!', 'success');
                },
                onError: (errors) => {
                    showMessage('Lỗi khi xóa người dùng: ' + Object.values(errors).join(', '), 'error');
                }
            });
        }
    }

    // Close delete dialog
    deleteDialogVisible.value = false;

    // Reset data
    singleItemName.value = '';
    itemsDelete = {};
}
// =====================================================================================================================
// End: delete

// Begin: Search
// =====================================================================================================================

const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Reset to first page when searching
    currentPage.value = 1
    // searchQuery.value =
    // handlePaginationChange(currentPage.value, perPage.value)
};
// =====================================================================================================================
// End: Search

// Begin: Add User
// =====================================================================================================================
/*let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})*/
/*const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}*/
const addUser = () => {
    console.log('addUser')
    emit('cb:onAddUser', true);
}
// =====================================================================================================================
// End: Add User

const ActionCbHandler = (item) => {
    console.log('UserTable >> ActionCbHandler >> item: ', item)
    const {action, row} = item
    // Handle the command and row here
    switch (action) {
        case 'edit':
            console.log('UserTable >> ActionCbHandler >> edit >> row: ', row)
            // Emit edit event to parent component
            emit('cb:onEditUser', row);
            break
        case 'delete':
            console.log('UserTable >> ActionCbHandler >> delete >> row: ', row)
            // Open delete confirmation dialog
            deleteDialogKey.value += 1
            deleteDialogVisible.value = true
            itemsDelete = row
            singleItemName.value = row.fullName || row.username
            break
        default:
            console.log('UserTable >> ActionCbHandler >> default >> row: ', row)
            ElMessage(`click on item ${action} ${row?.name}`)
            break
    }
}


const changeStatus = (item, val) => {
    console.log('changeStatus: ', item, ' => val: ', val)
    // const form = useForm({
    //     name: item.name,
    //     // email: item.email,
    //     // status: item.status
    //     status: val ? 1 : 0
    // })
    //
    // form.patch(route('cms.roles.update', item))
}

</script>


<style>
.el-table .cell, .el-scrollbar {
    padding: 0 !important;
}

.el-table__header {
    padding: 0 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    background-color: #b5b5c3 !important;
}

.el-dialog__headerbtn {
    top: 13px !important;
}
</style>

<template>
    <!--
    <div id="kt_post" class="post d-flex flex-column-fluid mb-10">
        <div id="kt_content_container" class="container">
    -->

    <div class="card">
        <div class="card-header mt-6">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 me-5">
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <IconSearch/>
                    </span>
                    <input
                        v-model="searchQuery"
                        class="form-control form-control-solid w-450px ps-15"
                        data-kt-permissions-table-filter="search"
                        :placeholder="'Search ' + props.cateName"
                        type="text"
                        @keyup.enter="search"
                    />
                </div>
                <!--end::Search-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <!--begin::Toolbar-->
                <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                    <!--begin::Filter-->
                    <!--
                    <ThirdButton class-name="btn-light-primary me-3" name="Filter">
                        <IconFilter/>
                    </ThirdButton>
                    -->
                    <!--end::Filter-->
                    <!--begin::Export-->
                    <!--
                    <ThirdButton class-name="btn-light-primary me-3" name="Export">
                        <IconExport/>
                    </ThirdButton>
                    -->
                    <!--end::Export-->
                    <!--begin::Add user-->
                    <!--
                    <ThirdButton
                        class-name="btn-primary"
                        name="Thêm mới"
                        @click.prevent="addUser"
                    >
                        <IconAdd/>
                    </ThirdButton>
                    -->
                    <!--end::Add user-->
                </div>
                <!--end::Toolbar-->

                <!--begin::Button-->
                <!--
                <ThirdButton class="ml-5"
                             class-name="btn-light-primary ml-5"
                             :name="'Add ' + props.cateName"
                             @click="addPermission"
                             v-if="selectedRows.length <= 0"
                >
                    <span class="svg-icon svg-icon-3">
                        <IconAdd2/>
                    </span>
                </ThirdButton>
                -->
                <!--end::Button-->

                <!--begin::Button-->
                <!--
                <div class="fw-bolder me-5" v-if="selectedRows.length > 0">
                    <span class="me-2">{{ selectedRows.length }}</span>Selected
                </div>
                <button v-if="selectedRows.length > 0"
                        type="button"
                        class="btn btn-danger"
                        @click.prevent="deleteItem(selectedRows, 2)"
                >Delete Selected
                </button>
                -->
                <!--end::Button-->

            </div>
            <!--end::Card toolbar-->

        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">
            <!--begin::Table-->
            <el-table :data="data" class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
                      @selection-change="handleSelectionChange"
                      style="width: 100%; margin-left: 0; padding-left: 0;">
                <el-table-column type="selection" width="55"/>

                <!--
                <el-table-column label="Ảnh" width="80">
                    <template #default="scope">
                        <el-image style="width: 60px; height: 60px" :src="scope.row.image" fit="cover" />
                    </template>
                </el-table-column>
                -->
                <el-table-column label="Id" width="50">
                    <template #default="scope">{{ scope.row?.id }}</template>
                </el-table-column>
                <el-table-column label="Uid" min-width="60">
                    <template #default="scope">
                                <span class="badge2 badge-primary2">
                                    <Link :href="route('cms.players.show', {id: scope.row?.id ?? 0})"
                                          class="text-gray-800222 mb-1">
                                        {{ scope.row?.uid }}
                                    </Link>
                                </span>
                    </template>
                </el-table-column>

                <el-table-column label="Avatar" min-width="40">
                    <template #default="scope">
                        <div class="symbol symbol-45px me-5">
                            <img :alt="scope.row.display_name || scope.row.nick_name"
                                 :src="scope.row.avatar || 'templates/dashboard/assets/media/avatars/150-11.jpg'"/>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="Họ Tên" min-width="200">
                    <template #default="scope">
                                <span class="fw-bolder">
                                    <Link :href="route('cms.players.show', {id: scope.row?.id ?? 0})"
                                          class="text-gray-800 text-hover-primary mb-1">
                                        {{ scope.row?.display_name }}
                                    </Link>
                                </span>
                        <br/>
                        <span>{{ scope.row?.nick_name }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="Số Tiền" min-width="100">
                    <template #default="scope">
                                <span class="badge badge-success fw-bolder">
                                    {{ scope.row?.balance.toLocaleString('de-DE') }}
                                </span>
                    </template>
                </el-table-column>
                <!--
                <el-table-column label="App">
                    <template #default="scope">{{ scope.row?.appId ?? 0 }}</template>
                </el-table-column>
                -->

                <el-table-column label="Trạng thái">
                    <template #default="scope">
                        <!--
                        {{ scope.row?.status ?? 0 }}
                        -->
                        <el-switch
                            v-model="scope.row.isStatus"
                            active-text="Hoạt động"
                            class="ml-2"
                            inactive-text="Khóa"
                            inline-prompt
                            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                            @change="changeStatus(scope.row, $event)"
                        />
                    </template>
                </el-table-column>

                <!--
                <el-table-column label="Danh mục" min-width="110">
                    <template #default="scope">
                        <div class="badge badge-light fw-bolder">{{ scope.row?.category ?? '' }}</div>
                    </template>
                </el-table-column>
                -->
                <!--
                <el-table-column label="Cập nhật" property="updated_at"/>
                -->
                <el-table-column label="Last Login" property="last_login"/>
                <el-table-column label="Ngày Tham Gia" property="created_at"/>
                <!--begin::Action=-->
                <el-table-column align="right" label="Hành động" width="100">
                    <template #default="scope">
                        <!--begin::Update-->
                        <!--
                        <ThirdButton
                            class="btn btn-icon btn-active-light-primary w-30px h-30px me-3"
                            @click.prevent="updatePermission(scope.row)"
                        >
                            <span class="svg-icon svg-icon-3">
                                <IconEdit/>
                            </span>
                        </ThirdButton>
                        -->
                        <!--end::Update-->
                        <!--begin::Delete-->
                        <!--
                        <ThirdButton class="btn btn-icon btn-active-light-primary w-30px h-30px"
                                     @click.prevent="deleteItem(scope.row, 1)"
                        >
                            <span class="svg-icon svg-icon-3">
                                <IconDelete/>
                            </span>
                        </ThirdButton>
                        -->
                        <!--end::Delete-->

                        <DropdownButton :row="scope.row" @cb:action="ActionCbHandler" :button-name="'Chọn'">
                            <template #default>
                                <el-dropdown-menu>
                                    <!--
                                    <el-dropdown-item command="edit">Xem chi tiết</el-dropdown-item>
                                    -->
                                    <el-dropdown-item>
                                        <Link :href="route('cms.players.show', {id: scope.row?.id ?? 0})">Xem chi
                                            tiết
                                        </Link>
                                    </el-dropdown-item>
                                    <!--
                                    <el-dropdown-item command="edit">Chỉnh sửa</el-dropdown-item>
                                    -->
                                    <el-dropdown-item command="delete">Xóa</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </DropdownButton>

                    </template>
                </el-table-column>
                <!--end::Action=-->
            </el-table>
            <!--end::Table-->
        </div>
        <!--end::Card body-->
        <!--begin::Pagination-->
        <div class="d-flex flex-center mb-5">
            <el-pagination
                v-model:current-page="meta.current_page"
                v-model:page-size="meta.per_page"
                :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                :total="meta.total"
                background
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!--end::Pagination-->
    </div>
    <!--end::Card-->

    <!--begin::Modals-->
    <!--begin::Modal - Add/Update permissions-->
    <PermissionDialog
        :key="dialogKey"
        :dialogFormVisible="dialogFormVisible"
        :form="form"
        :titleModal="titleModal"
        @onSubmit="handleSubmit"
        @update:dialogFormVisible="dialogFormVisible = $event"
    />
    <!--end::Modal - Add/Update permissions-->

    <!--begin::Modal - Delete -->
    <DeleteDialog
        :key="deleteDialogKey"
        :itemName="singleItemName ?? ''"
        :visible="deleteDialogVisible"
        @delete="handleDeleteAction"
        @update:visible="deleteDialogVisible = $event"
    />
    <!--end::Modal - Delete -->
    <!--end::Modals-->
    <!--
        </div>
    </div>
    -->
</template>
