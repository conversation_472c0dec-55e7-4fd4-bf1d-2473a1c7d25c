<script setup>
import {onMounted, reactive, ref, watch} from "vue";
import {ElMessage} from "element-plus";
import axios from 'axios';

const props = defineProps({
    dialogFormVisible: {
        type: Boolean,
        default: false
    },
    form: {
        type: Object,
        default: () => ({})
    },
    titleModal: {
        type: String,
        default: 'Update User'
    },
    userId: {
        type: Number,
        required: true
    }
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);

const formSubmitting = ref(false);
const isLoading = ref(false);
const userForm = reactive({
    username: props.form.username || '',
    email: props.form.email || '',
    first_name: props.form.first_name || '',
    last_name: props.form.last_name || '',
    // Don't include password for edits unless explicitly requested
    password: '',
    change_password: false,
});

// Fetch user data from API - define the function before using it in watch
const fetchUserData = async (userId) => {
    if (!userId) return;

    isLoading.value = true;
    try {
        const url = route('user.show', { id: userId });

        // Using axios with global config from bootstrap.js
        const response = await axios.get(url);

        if (response.data.success) {
            const userData = response.data.data;
            userForm.username = userData.username || '';
            userForm.email = userData.email || '';
            userForm.first_name = userData.firstName || userData.first_name || '';
            userForm.last_name = userData.lastName || userData.last_name || '';
        } else {
            ElMessage({
                message: response.data.message || 'Failed to load user data',
                type: 'error',
            });
        }
    } catch (error) {
        console.error('Error fetching user data:', error);
        ElMessage({
            message: 'Error fetching user data: ' + (error.response?.data?.message || error.message),
            type: 'error',
        });
    } finally {
        isLoading.value = false;
    }
};

// Fetch user data when dialog becomes visible and userId changes
watch(() => [props.dialogFormVisible, props.userId], ([visible, userId]) => {
    if (visible && userId) {
        fetchUserData(userId);
    }
}, { immediate: true });

const resetForm = () => {
    userForm.username = '';
    userForm.email = '';
    userForm.first_name = '';
    userForm.last_name = '';
    userForm.password = '';
    userForm.change_password = false;
};

const closeDialog = () => {
    resetForm();
    emit('update:dialogFormVisible', false);
};

const submitForm = () => {
    // Basic validation
    if (!userForm.username || !userForm.email || !userForm.first_name || !userForm.last_name) {
        ElMessage({
            message: 'Vui lòng điền đầy đủ thông tin!',
            type: 'error',
        });
        return;
    }

    // If change_password is true but no password, show error
    if (userForm.change_password && !userForm.password) {
        ElMessage({
            message: 'Vui lòng nhập mật khẩu mới!',
            type: 'error',
        });
        return;
    }

    // Prepare data for submission
    formSubmitting.value = true;
    const userData = {
        username: userForm.username,
        email: userForm.email,
        first_name: userForm.first_name,
        last_name: userForm.last_name,
    };

    // Only include password if change_password is checked
    if (userForm.change_password) {
        userData.password = userForm.password;
    }

    // Call parent's onSubmit
    emit('onSubmit', userData);
    formSubmitting.value = false;
};
</script>

<template>
    <el-dialog
        :modelValue="dialogFormVisible"
        @update:modelValue="emit('update:dialogFormVisible', $event)"
        :title="titleModal"
        @close="closeDialog"
        width="500px"
    >
        <el-form :model="userForm" label-position="top" v-loading="isLoading">
            <el-form-item label="Username">
                <el-input v-model="userForm.username" placeholder="Nhập username" disabled />
            </el-form-item>

            <el-form-item label="Email">
                <el-input v-model="userForm.email" placeholder="Nhập email" disabled />
            </el-form-item>

            <el-form-item label="Họ" required>
                <el-input v-model="userForm.first_name" placeholder="Nhập họ" />
            </el-form-item>

            <el-form-item label="Tên" required>
                <el-input v-model="userForm.last_name" placeholder="Nhập tên" />
            </el-form-item>

            <el-form-item>
                <el-checkbox v-model="userForm.change_password">Đổi mật khẩu</el-checkbox>
            </el-form-item>

            <el-form-item v-if="userForm.change_password" label="Mật khẩu mới" required>
                <el-input
                    v-model="userForm.password"
                    type="password"
                    placeholder="Nhập mật khẩu mới"
                    show-password
                />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeDialog">Hủy</el-button>
                <el-button type="primary" @click="submitForm" :loading="formSubmitting">
                    Cập nhật
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>
