<script setup>
import DropdownButton from "@/Components/DropdownButton.vue";
import {showMessage} from '@/Helpers/messageHelper';
import DeleteDialog from "@/Components/DeleteDialog.vue";
import {reactive, ref} from "vue";

const props = defineProps({
    users: Object,
    // loading: Boolean,
})

const emit = defineEmits(['cb:onAddUser', 'cb:onUpdateUser', 'cb:onChangeStatus', 'cb:onReload']);

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemDelete = reactive({})
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemDelete: ', itemDelete)
    deleteDialogVisible.value = false
}
// =====================================================================================================================
// End: delete

const getDetailUser = async (id) => {
  console.log('getDetailUser >> id: ', id)

  // Step 1: call api get invoice detail
  // =============================================
  const csrfToken = document.head.querySelector('meta[name="csrf-token"]').content
  console.log('csrfToken: ', csrfToken)
  let url = route('user.show', id)
  console.log('url: ', url)

  return axios.get(url, {
    headers: {
      'X-CSRF-TOKEN': csrfToken,
    }
  }).then(res => {
    console.log('[getDetailUser]: ', res.data)
    // currentItem.value = res.data.data
    return res.data
  }).catch(error => {
    console.log('MINHError:', error)
  })

  // Step 2: set data
  // =============================================
  // dialogInvoiceVisible.value = true
  // valueIncreaseDialog.value += 1
}

const ActionCbHandler = async (item) => {
    console.log('UserTable >> ActionCbHandler >> item: ', item)
    const {action, row} = item
    // Handle the command and row here
    switch (action) {
        case 'view':
            console.log('UserTable >> ActionCbHandler >> view >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            // showMessage(`click on item ${action} ${row?.name}`);
            // this.$router.push({name: 'UserDetail', params: {id: row.id}});
            // route('cms.users.show', {id: row.id})
            window.location.href = route('cms.partners.show', {id: row.id});
            break
        case 'edit':
          // console.log('UserTable >> ActionCbHandler >> edit >> row: ', row)
          // showMessage(`click on item ${action} ${row?.name}`)
          const item = await getDetailUser(row.id)
          // console.log('item: ', item)
          emit('cb:onUpdateUser', item.data)
          break
        case 'delete':
            console.log('UserTable >> ActionCbHandler >> delete >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            // showMessage(`click on item ${action} ${row?.name}`);
            deleteDialogKey.value += 1
            deleteDialogVisible.value = true
            itemDelete = row

            break
        default:
            console.log('UserTable >> ActionCbHandler >> default >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            showMessage(`click on item ${action} ${row?.name}`);
            break
    }
}

const changeStatus = (item, val) => {
    console.log('[UserTable] changeStatus: ', item, ' => val: ', val)
    emit('cb:onChangeStatus', {id: item.id, status: val ? 1 : 0})
}

</script>

<template>
    <table id="kt_roles_view_table"
           class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
    >
        <!--begin::Table head-->
        <thead>
        <!--begin::Table row-->
        <tr class="text-start text-muted fw-bolder fs-7 text-uppercase gs-0">
            <!--
            <th class="w-10px pe-2">
                <div
                    class="form-check form-check-sm form-check-custom form-check-solid me-3">
                    <input class="form-check-input" data-kt-check="true"
                           data-kt-check-target="#kt_roles_view_table .form-check-input"
                           type="checkbox"
                           value="1"
                    />
                </div>
            </th>
            -->
            <!--
            <th class="min-w-50px">ID</th>
            -->
            <th class="min-w-20px">#</th>
            <th class="min-w-150px">Đại lý</th>
            <th>Điện thoại</th>
            <th>Trạng thái</th>
            <th class="min-w-125px">Ngày tham gia</th>
            <th class="min-w-125px">Last Login</th>
            <th class="text-end min-w-100px">Hành động</th>
        </tr>
        <!--end::Table row-->
        </thead>
        <!--end::Table head-->
        <!--begin::Table body-->
        <tbody class="fw-bold text-gray-600">
        <tr v-for="(user, idx) in users.data" :key="user.id">
            <!--begin::Checkbox-->
            <!--
            <td>
                <div class="form-check form-check-sm form-check-custom form-check-solid">
                    <input :value="user.id" class="form-check-input" type="checkbox"/>
                </div>
            </td>
            -->
            <!--end::Checkbox-->
            <!--begin::ID-->
            <!--
            <td>{{ user.code }}</td>
            -->
          <td>{{ idx + 1 }}</td>
          <!--begin::ID-->
            <!--begin::User=-->
            <td class="d-flex align-items-center">
                <!--begin:: Avatar -->
                <div class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                    <a href="javascript:void(0)">
                        <div v-if="!user?.avatar_bg" class="symbol-label">
                            <img :alt="user?.name" :src="user?.avatar"
                                 class="w-100"/>
                        </div>
                        <div v-else :class="user?.avatar_bg" class="symbol-label fs-3 text-danger">{{
                                user?.avatar ?? ''
                            }}
                        </div>
                    </a>
                </div>
                <!--end::Avatar-->
                <!--begin::User details-->
                <div class="d-flex flex-column">
                    <a class="text-gray-800 text-hover-primary mb-1"
                       href="#">{{ user?.name ?? '' }}</a>
                    <span>{{ user?.email ?? '' }}</span>
                </div>
                <!--begin::User details-->
            </td>
            <!--end::user=-->

            <!--begin::Phone=-->
            <td>{{ user?.phone ?? '' }}</td>
            <!--end::Phone=-->

            <!--begin::Status=-->
            <td>
                <el-switch
                    v-model="user.isStatus"
                    active-text="Hoạt động"
                    class="ml-2"
                    inactive-text="Khóa"
                    inline-prompt
                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                    @change="changeStatus(user, $event)"
                />
            </td>
            <!--end::Status=-->

            <!--begin::Joined date=-->
            <td>{{ user?.created_at.datetime ?? '' }}</td>
            <!--end::Joined date=-->

            <!--begin::Last Login=-->
            <td>{{ user?.last_login ?? '' }}</td>
            <!--end::Last Login=-->

            <!--begin::Action=-->
            <td class="text-end">
                <DropdownButton :row="user" :button-name="'Chọn'" @cb:action="ActionCbHandler">
                    <template #default>
                        <el-dropdown-menu>
                            <el-dropdown-item command="view">Chi tiết</el-dropdown-item>
                          <el-dropdown-item command="edit">Cập nhật</el-dropdown-item>
                            <el-dropdown-item command="delete">Xóa</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </DropdownButton>
            </td>
            <!--end::Action=-->
        </tr>
        </tbody>
        <!--end::Table body-->
    </table>

    <!--begin::Modal - Delete Role-->
    <DeleteDialog
        :key="deleteDialogKey"
        :itemName="itemDelete?.name ?? ''"
        :visible="deleteDialogVisible"
        @delete="handleDeleteAction"
        @update:visible="deleteDialogVisible = $event"
    />
    <!--end::Modal - Delete Role-->

</template>
