<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal"
               style="margin-top: 5%; !important;"
               width="50%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

<!--        {{ localForm }}-->

        <el-form :model="localForm"
                 @submit.prevent="handleSubmit"
                 ref="ruleFormRef"
                 :rules="rules"
                 class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7"
                 style="margin-top: 0 !important;padding-top: 0 !important;"
        >
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Họ và tên</label>
                <!--end::Label-->
                <el-form-item prop="name">
                    <!--begin::Input-->
                    <el-input v-model="localForm.name"
                              size="large"
                              placeholder="Nhập họ và tên"
                              class="form-control form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Email</label>
                <!--end::Label-->
                <el-form-item prop="email">
                    <!--begin::Input-->
                    <el-input v-model="localForm.email"
                              size="large"
                              placeholder="Nhập địa chỉ email"
                              class="form-control form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fw-bold fs-6 mb-2">Phone</label>
                <!--end::Label-->
                <!--begin::Input-->
                <el-input v-model="localForm.phone"
                          size="large"
                          placeholder="Nhập số điện thoại"
                          class="form-control form-control-solid mb-3 mb-lg-0"
                />
                <!--end::Input-->
            </div>
            <!--end::Input group-->

            <div class="fv-row mb-7" v-if="formAction === 'update'">
                <!--begin::Label-->
                <label class="fw-bold fs-6 mb-2">Đổi mật khẩu</label>
                <el-form-item prop="_isChangePassword">
                    <el-switch v-model="localForm._isChangePassword" />
                </el-form-item>
            </div>

            <!--begin::Input group-->
            <div class="fv-row mb-7" v-if="localForm._isChangePassword">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Mật khẩu</label>
                <!--end::Label-->
                <el-form-item prop="password">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.password"
                        class="form-control form-control-solid mb-3 mb-lg-0"
                        type="password"
                        size="large"
                        placeholder="Nhập mật khẩu"
                        show-password
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Discard</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Submit
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {ref, watch, reactive} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object,
    formAction: String
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);
const ruleFormRef = ref(null);

let localForm = ref(props.form);

// begin: validation
const rules = reactive({
    name: [
        { required: true, message: 'Họ và tên không được để trống', trigger: 'blur' },
        { min: 3, max: 255, message: 'Length should be 3 to 5', trigger: 'blur' },
    ],
    email: [
        { required: true, message: 'Bạn chưa nhập địa chỉ Email', trigger: 'blur' },
        { type: 'email', message: 'Địa chỉ email chưa đúng', trigger: ['blur', 'change'] },
    ],
    password: [
        { required: true, message: 'Mật khẩu không được để trống', trigger: 'blur' },
        { min: 6, message: 'Mật khẩu nên có tối thiểu 6 ký tự', trigger: 'blur' },
    ],
})

// end: validation

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = async () => {
    await ruleFormRef.value.validate((valid) => {
        if (valid) {
            console.log('Form submitted successfully!');
            emit('onSubmit', props.form);
            localDialogFormVisible.value = false;
        } else {
            // console.log('Validation failed');
            return false;
        }
    });
};

watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
    emit('update:dialogFormVisible', newValue);
});
watch(() => localDialogFormVisible.value, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});


</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
<style>
.el-input--large .el-input__wrapper {
    width: 100% !important;
}
</style>
