<script setup>
import ChatSideBar from "@/Pages/Chats/Partials/ChatSideBar.vue";
import ChatMessenger from "@/Pages/Chats/Partials/ChatMessenger.vue";

const props = defineProps({
    users: {
        type: Array,
        default: []
    },
    messages: {
        type: Array,
        default: []
    },
})
</script>

<template>

    <!--begin::Container-->
    <div id="kt_content_container" class="container-fluid mb-15">
        <!--begin::Layout-->
        <div class="d-flex flex-column flex-lg-row">
            <!--begin::Sidebar-->
            <ChatSideBar :users="users"/>
            <!--end::Sidebar-->

            <!--begin::Content-->
            <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
                <!--begin::Messenger-->
                <ChatMessenger :messages="messages"/>
                <!--end::Messenger-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Layout-->
    </div>
    <!--end::Container-->

</template>
