<script setup>

import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, useForm} from '@inertiajs/vue3'
import {computed, reactive, ref} from 'vue'
import {Delete, View} from '@element-plus/icons-vue'
import Toolbar from "@/Components/Toolbar.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";

const props = defineProps({
    cateName: String,
    items: Object,
    paginate: Object,
    type: String,
    keyword: String,
    player_id: Number,
    player_search: String,
    routeIndex: String,
    routeDestroy: String,
    routeDestroys: String,
    feedbackTypes: Object
})

const disabled = ref(false)
let loading = ref(false)
let keyTable = ref(1)

const breadcrumbs = [
    {text: 'Danh sách phản hồi'}
];

const handleBulkDelete = () => {
    if (selectedCount.value === 0) {
        showMessage('<PERSON>ui lòng chọn ít nhất một phản hồi để xóa', 'warning');
        return;
    }

    isMultiDeleteType.value = true
    itemsDelete = selectedItems.value
    dialogVisible.value = true
}

const bulkDeleteButton = computed(() => ({
    name: `Xóa ${selectedCount.value} phản hồi đã chọn`,
    className: 'btn-danger',
    click: handleBulkDelete
}));

const selectedCount = ref(0);
const tableRef = ref(null);
const actions = computed(() => {
    const actionButtons = [
        {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: reloadData}
    ];

    if (selectedCount.value > 0) {
        actionButtons.push(bulkDeleteButton.value);
    }

    return actionButtons;
});

const dialogVisible = ref(false)
let itemsDelete = reactive({})
let isMultiDeleteType = ref(false)

let objectSearch = reactive({
    page: props.page,
    per_page: 10,
    type: props.type || '',
    keyword: props.keyword || '',
    player_id: props.player_id || 0,
    player_search: props.player_search || '',
})

// Player search autocomplete
const playerSearchLoading = ref(false)
const playerOptions = ref([])

const searchPlayers = async (query) => {
    if (!query || query.length < 2) {
        playerOptions.value = []
        return
    }

    playerSearchLoading.value = true
    try {
        const response = await axios.get(route('cms.feedbacks.search-players'), {
            params: {query}
        })
        playerOptions.value = response.data
    } catch (error) {
        console.error('Error searching players:', error)
        playerOptions.value = []
    } finally {
        playerSearchLoading.value = false
    }
}

const handlePlayerSelect = (value) => {
    objectSearch.player_search = value
    filterPlayerSearch()
}

const deleteItem = (item, isMulti) => {
    isMultiDeleteType.value = isMulti
    dialogVisible.value = true
    itemsDelete = item
}

const deleteItemAction = () => {
    loading.value = true

    const idsArr = Array.isArray(itemsDelete) ? itemsDelete : [itemsDelete.id];

    if (isMultiDeleteType.value) {
        const formPost = useForm({
            ids: idsArr
        })
        formPost.post(route(props.routeDestroys), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        const deleteForm = useForm({})
        deleteForm.delete(route(props.routeDestroy, itemsDelete), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    loading.value = false
    dialogVisible.value = false
    itemsDelete = {};
    selectedCount.value = 0
    selectedItems.value = []
}

const selectedItems = ref([]);

const isAllSelected = computed(() => {
    return props.items.data.length > 0 && selectedItems.value.length === props.items.data.length;
});

const isIndeterminate = computed(() => {
    return selectedItems.value.length > 0 && selectedItems.value.length < props.items.data.length;
});

const handleSelectAll = (val) => {
    selectedItems.value = val ? props.items.data.map(item => item.id) : [];
    selectedCount.value = selectedItems.value.length
}

const handleSelect = (id) => {
    const index = selectedItems.value.indexOf(id);
    if (index === -1) {
        selectedItems.value.push(id);
    } else {
        selectedItems.value.splice(index, 1);
    }
    selectedCount.value = selectedItems.value.length
}

// Loading & reload data
const reloadData = () => {
    loading.value = true
    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    const reloadForm = useForm({})
    reloadForm.get(route(props.routeIndex) + '?' + searchParams, {
        onSuccess: () => {
            loading.value = false
        }
    })
}

const handlePerPageChange = (value) => {
    objectSearch.per_page = value;
    objectSearch.page = 1;
    reloadData();
}

const handleCurrentChange = (val) => {
    objectSearch.page = val
    reloadData();
}

const filterType = (value) => {
    objectSearch.type = value;
    reloadData();
}

const filterKeyword = () => {
    objectSearch.page = 1;
    reloadData();
}

const filterPlayerId = () => {
    objectSearch.page = 1;
    reloadData();
}

const filterPlayerSearch = () => {
    objectSearch.page = 1;
    reloadData();
}

const fetchItemById = async (id) => {
    loading.value = true;
    try {
        const url = route('cms.feedbacks.show', id);
        const response = await axios.get(`${url}`);
        if (response.data.success) {
            return response.data.data;
        } else {
            return null;
        }
    } catch (error) {
        console.error('Error fetching feedback:', error);
        return null;
    } finally {
        loading.value = false;
    }
};

// Modal for viewing feedback details
const dialogDetailVisible = ref(false)
const selectedFeedback = ref(null)

const handleViewItem = async (item) => {
    const dataInfo = await fetchItemById(item.id)
    if (dataInfo) {
        selectedFeedback.value = dataInfo
        dialogDetailVisible.value = true
    }
}

defineExpose({
    get selectedItems() {
        return selectedItems.value;
    },
    handleBulkDelete
});

</script>

<template>
    <Head :title="cateName"/>

    <MainLayout>
        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid mb-15">
            <div class="card">
                <div class="card-body">
                    <!-- Search filters -->
                    <div class="row g-5 mb-5">
                        <div class="col-md-3">
                            <el-input
                                v-model="objectSearch.keyword"
                                placeholder="Tìm kiếm theo nội dung..."
                                @keyup.enter="filterKeyword"
                                clearable
                                size="large"
                            >
                                <template #append>
                                    <el-button @click="filterKeyword">Tìm</el-button>
                                </template>
                            </el-input>
                        </div>
                        <div class="col-md-3">
                            <el-select
                                v-model="objectSearch.player_search"
                                placeholder="Tìm theo tên người chơi..."
                                filterable
                                remote
                                reserve-keyword
                                :remote-method="searchPlayers"
                                :loading="playerSearchLoading"
                                @change="handlePlayerSelect"
                                clearable
                                size="large"
                            >
                                <el-option
                                    v-for="player in playerOptions"
                                    :key="player.id"
                                    :label="player.name"
                                    :value="player.name"
                                />
                            </el-select>
                        </div>
                    </div>

                    <div class="row g-5">
                        <div class="col-xl-12">
                            <div class="row">
                                <div class="col-md-12 col-xl-12">
                                    <el-table :key="keyTable" v-loading="loading" :data="items.data" ref="tableRef"
                                              style="width: 100%">
                                        <el-table-column label="" prop="name" width="50">
                                            <template #header>
                                                <el-checkbox
                                                    :model-value="isAllSelected"
                                                    :indeterminate="isIndeterminate"
                                                    @change="handleSelectAll"
                                                />
                                            </template>
                                            <template #default="scope">
                                                <el-checkbox
                                                    :model-value="selectedItems.includes(scope.row.id)"
                                                    @change="handleSelect(scope.row.id)"
                                                />
                                            </template>
                                        </el-table-column>

                                        <!--
                                        <el-table-column type="expand">
                                            <template #default="props">
                                                <div style="padding-left: 360px;">
                                                    <p><strong>Nội dung:</strong> {{ props.row?.message ?? '' }}</p>
                                                    <div v-if="props.row.attachments && props.row.attachments.length > 0">
                                                        <strong>File đính kèm:</strong>
                                                        <ul>
                                                            <li v-for="attachment in props.row.attachments" :key="attachment.id">
                                                                <a :href="attachment.file_path" target="_blank">{{ attachment.file_path }}</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        -->

                                        <el-table-column label="ID" prop="id" sortable width="80"/>

                                        <el-table-column
                                            label="Loại" prop="type" width="200"
                                        >
                                            <template #header>
                                                <el-select
                                                    v-model="objectSearch.type"
                                                    placeholder="Loại"
                                                    size="large"
                                                    @change="filterType"
                                                    clearable
                                                >
                                                    <el-option
                                                        v-for="(label, value) in feedbackTypes"
                                                        :key="value"
                                                        :label="label"
                                                        :value="value"
                                                    />
                                                </el-select>
                                            </template>

                                            <template #default="scope">
                                                <el-tag :type="scope.row.type === 'GAME_BUGS' ? 'danger' : 'primary'">
                                                    {{ scope.row.typeName }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Người gửi" prop="sender_name" sortable width="200">
                                            <template #default="scope">
                                                <span>{{ scope.row.sender_name }}</span>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Nội dung" prop="message" sortable>
                                            <template #default="scope">
                                                <div class="text-truncate" style="max-width: 300px;">
                                                    {{ scope.row.message }}
                                                </div>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Ngày tạo" prop="created_at" sortable width="180">
                                            <template #default="scope">
                                                <span>{{
                                                        new Date(scope.row.created_at).toLocaleString('vi-VN')
                                                    }}</span>
                                            </template>
                                        </el-table-column>

                                        <el-table-column align="right" label="Hành động" width="150">
                                            <template #default="scope">
                                                <el-tooltip
                                                    class="box-item"
                                                    content="Xem chi tiết"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button :icon="View"
                                                               @click.stop.prevent="handleViewItem(scope.row)"
                                                               type="primary"></el-button>
                                                </el-tooltip>

                                                <el-tooltip
                                                    class="box-item"
                                                    content="Xóa"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Delete"
                                                        type="danger"
                                                        @click.stop.prevent="deleteItem(scope.row, false)"
                                                    ></el-button>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container overflow-hidden">
                    <div class="row">
                        <div class="d-flex flex-center mb-5">
                            <el-pagination :page-sizes="[10, 20, 50, 100, 200]"
                                           :current-page="paginate.data.current_page"
                                           :page-size="paginate.data.per_page"
                                           :total="paginate.data.total"
                                           background
                                           layout="total, sizes, prev, pager, next"
                                           @size-change="handlePerPageChange"
                                           @current-change="handleCurrentChange"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete confirmation dialog -->
        <el-dialog
            v-model="dialogVisible"
            title="Xác nhận xóa"
            width="500"
            align-center
        >
            <span>Bạn có chắc chắn muốn xóa {{ isMultiDeleteType ? 'các phản hồi' : 'phản hồi này' }}?</span>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">Hủy</el-button>
                    <el-button type="primary" @click="deleteItemAction">Xác nhận</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- Detail view dialog -->
        <el-dialog
            v-model="dialogDetailVisible"
            title="Chi tiết phản hồi"
            width="800"
            align-center
        >
            <div v-if="selectedFeedback">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="ID">{{ selectedFeedback.id }}</el-descriptions-item>
                    <el-descriptions-item label="Loại">
                        <el-tag :type="selectedFeedback.type === 'GAME_BUGS' ? 'danger' : 'primary'">
                            {{ selectedFeedback.typeName }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="Người gửi">{{ selectedFeedback.sender_name }}</el-descriptions-item>
                    <el-descriptions-item label="Ngày tạo">
                        {{ new Date(selectedFeedback.created_at).toLocaleString('vi-VN') }}
                    </el-descriptions-item>
                    <el-descriptions-item label="Nội dung" :span="2">
                        <div style="white-space: pre-wrap;">{{ selectedFeedback.message }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="selectedFeedback.attachments && selectedFeedback.attachments.length > 0"
                                          label="File đính kèm" :span="2">
                        <div v-for="attachment in selectedFeedback.attachments" :key="attachment.id" class="mb-2">
                            <a :href="attachment.file_path" target="_blank" class="text-primary">
                                {{ attachment.file_path }}
                            </a>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogDetailVisible = false">Đóng</el-button>
                </div>
            </template>
        </el-dialog>
    </MainLayout>
</template>

<style scoped>
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
