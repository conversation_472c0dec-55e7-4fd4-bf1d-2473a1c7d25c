<script setup>

import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, router, useForm} from '@inertiajs/vue3'
import {computed, reactive, ref} from 'vue'
import {CopyDocument, Delete, Edit} from '@element-plus/icons-vue'
import Toolbar from "@/Components/Toolbar.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import FormDialog from "@/Pages/Faq/Partials/FormDialog.vue";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";

const props = defineProps({
    cateName: String,
    statusArr: Array,
    // promotedArr: Array,
    items: Object,
    paginate: Object,
    // item: Object,
    // success: String,
    type: String,
    routeIndex: String,
    routeCreate: String,
    routeEdit: String,
    routeUpdate: String,
    routeDestroy: String,
    routeDestroys: String,
    faqTypes: Object
})

// const search = ref('')

const disabled = ref(false)

let form = useForm({
    type: props.type || '',
    question: '',
    answer: '',
    status: 1
})
const resetForm = () => {
    form.type = props.type
    form.question = ''
    form.answer = ''
    form.status = 1
}

let loading = ref(false)
let keyTable = ref(1)

// const message = computed(() => usePage().props.flash.message)

/*watchEffect(() => {
    if (message.value) {
        keyTable.value += 1
        ElMessage({
            message: message.value,
            type: 'success'
        })
    }
})*/

const breadcrumbs = [
    {text: 'Danh sách'}
];

const handleBulkDelete = () => {
    if (selectedCount.value === 0) {
        showMessage('Vui lòng chọn ít nhất một bài viết để xóa', 'warning');
        return;
    }

    // const selectedIds = tableRef.value?.selectedItems || [];
    // console.log('selectedIds: ', selectedIds)

    // dialogTitle.value = `Bạn có chắc chắn muốn xóa ${selectedCount.value} bài viết đã chọn?`;
    // deleteDialogKey.value += 1
    // deleteDialogVisible.value = true
    isMultiDeleteType.value = true
    itemsDelete = selectedItems.value
    // singleItemName.value = '';
    console.log('itemsDelete: ', itemsDelete)
    dialogVisible.value = true
}

const bulkDeleteButton = computed(() => ({
    name: `Xóa ${selectedCount.value} bài viết đã chọn`,
    className: 'btn-danger',
    click: handleBulkDelete
}));

const selectedCount = ref(0);
const tableRef = ref(null);
const actions = computed(() => {
    const actionButtons = [
        {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: reloadData},
        {
            // type: 'back',
            name: 'Thêm mới',
            className: 'btn-primary',
            icon: IconAdd,
            click: addItem
            // href: route('cms.articles.create')
        }
    ];

    // tạm đóng không cho sử dụng chức năng xóa
    /*if (selectedCount.value > 0) {
        actionButtons.push(bulkDeleteButton.value);
    }*/

    return actionButtons;
});

let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
let actionForm = ref('add')

const incrementDialogKey = () => {
    dialogKey.value += 1
}
const addItem = () => {
    console.log('add Faqs')
    resetForm()
    titleModal.value = 'Thêm Mới'
    dialogFormVisible.value = true
    actionForm.value = 'add'
    incrementDialogKey()
}

// Create submit forms at setup level
const submitForm = useForm({})

const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data, ' -> actionForm: ', actionForm.value)

    if (actionForm.value === 'add') {
        console.log('add new user')
        // Reset and populate the form
        submitForm.reset()
        Object.assign(submitForm, data)

        submitForm.post(route('cms.faqs.store'), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    } else {
        console.log('update: ', data)
        let url = route('cms.faqs.update', data)

        // Reset and populate the form with PUT method
        submitForm.reset()
        Object.assign(submitForm, {_method: 'PUT', ...data})

        submitForm.put(url, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    }
}


const dialogVisible = ref(false)
// let dataDelete = ref(null)
let itemsDelete = reactive({})
let isMultiDeleteType = ref(false)

let objectSearch = reactive({
    page: props.currentPage,
    sort: props.sort,
    // keyword: props.keyword,
    per_page: props.perPage,
    type: props.type || '',
    // is_published: '',
    // promoted: '',
})

const deleteItem = (item, isMulti) => {
    console.log('deleteItem >> isMulti: ', isMulti, ' => item: ', item)
    isMultiDeleteType.value = isMulti
    dialogVisible.value = true
    itemsDelete = item
}

const deleteItemAction = () => {
    loading.value = true

    // const idsArr = Array.isArray(itemsDelete) ? itemsDelete.map(item => item.id) : [itemsDelete.id];
    const idsArr = Array.isArray(itemsDelete) ? itemsDelete : [itemsDelete.id];

    if (isMultiDeleteType.value) {
        console.log('deleteItemAction >> deleteSelected: ', idsArr)
        const formPost = useForm({
            ids: idsArr
        })
        formPost.post(route(props.routeDestroys), {
            onSuccess: (res) => {
                console.log('deleteItemAction >> onSuccess: ', res)
                // clearIds(totalItems)
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        console.log('deleteItemAction >> itemsDelete.value: ', itemsDelete.id)
        const deleteForm = useForm({})
        deleteForm.delete(route(props.routeDestroy, itemsDelete), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    loading.value = false
    dialogVisible.value = false
    itemsDelete = {};
    selectedCount.value = 0
    selectedItems.value = []
}

const selectedItems = ref([]);

const isAllSelected = computed(() => {
    return props.items.data.length > 0 && selectedItems.value.length === props.items.data.length;
});

const isIndeterminate = computed(() => {
    return selectedItems.value.length > 0 && selectedItems.value.length < props.items.data.length;
});

const handleSelectAll = (val) => {
    selectedItems.value = val ? props.items.data.map(item => item.id) : [];
    // emit('selected-count', selectedItems.value.length);
    selectedCount.value = selectedItems.value.length
}

const handleSelect = (id) => {
    console.log('handleSelect >> id: ', id)
    const index = selectedItems.value.indexOf(id);
    if (index === -1) {
        selectedItems.value.push(id);
    } else {
        selectedItems.value.splice(index, 1);
    }
    // emit('selected-count', selectedItems.value.length);
    selectedCount.value = selectedItems.value.length
}


// Loading & reload data
// ========================================================================
const reloadData = () => {
    loading.value = true
    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    const reloadForm = useForm({})
    reloadForm.get(route(props.routeIndex) + '?' + searchParams, {
        onSuccess: () => {
            loading.value = false
        }
    })
}
const changeStatusV2 = (item, val) => {
    console.log('changeStatusV2 >> val: ', val, ' => item: ', item)
    const statusForm = useForm({
        type: item.type,
        question: item.question,
        answer: item.answer,
        status: val ? 1 : 0
    })

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    let url = route(props.routeUpdate, item.id ?? 0);
    if (searchParams) {
        url += '?' + searchParams
    }
    console.log('changeStatusV2 >> url: ', url, ' -> searchParams: ', searchParams)
    statusForm.patch(url, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (res) => {
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

/*const changePromoted = (item, idx) => {
    console.log('changePromoted: ', item, ' => idx: ', idx)
    form = useForm({
        title: item.title,
        // type: item.type,
        promoted: idx ? 1 : 0
    })

    form.patch(route(props.routeUpdate, item))
}*/

/*const perPageOptions = [
    {value: 5, label: '5'},
    {value: 10, label: '10'},
    {value: 20, label: '20'},
    {value: 50, label: '50'},
    {value: 100, label: '100'},
];*/

const handlePerPageChange = (value) => {
    objectSearch.per_page = value;
    objectSearch.page = 1; // Reset to page 1 when changing items per page

    /*const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();

    router.get(route('cms.faqs.index') + '?' + searchParams, {}, {
        preserveState: true,
        preserveScroll: false,
        onSuccess: () => {
            showMessage('Đã thay đổi số lượng hiển thị!', 'success');
        }
    });*/

    reloadData();
}

const handleCurrentChange = (val) => {
    console.log(`current page: ${val} >> objectSearch: ${JSON.stringify(props.meta.current_page)}`)
    // console.log(`current page: ${val} >> meta: ${JSON.stringify(props.meta)}`)
    // Store the current page in local state
    objectSearch.page = val
    // Update the local currentPage ref
    objectSearch.value = val
    // Importantly, make sure to pass the current perPage value, not the default
    reloadData();
}

const filterType = (value) => {
    console.log('filterType: ', value)
    // return row.tag === value
    objectSearch.type = value;
    reloadData();
}

const fetchItemById = async (id) => {
    loading.value = true;
    try {
        const url = route('cms.faqs.show', id);
        const response = await axios.get(`${url}`);
        console.log('fetchItemById: ', response.data.data);
        if (response.data.success) {
            return response.data.data;
        } else {
            return null;
        }
    } catch (error) {
        console.error('Error fetching item faq:', error);
        // showMessage('Failed to load game replay logs', 'error');
        return null;
    } finally {
        loading.value = false;
    }
};

const handleEditItem = async (item) => {
    // console.log('handleEditItem: ', item)
    // router.get(route(props.routeEdit, item))
    const dataInfo = await fetchItemById(item.id)
    // console.log('dataInfo: ', dataInfo)
    if (dataInfo) {
        form.id = dataInfo.id;
        form.type = dataInfo.type;
        form.question = dataInfo.question;
        form.answer = dataInfo.answer;
        form.status = dataInfo.status;
    }

    titleModal.value = 'Cập nhật'
    dialogFormVisible.value = true
    actionForm.value = 'update'
    incrementDialogKey()
}

const handleDuplicate = (id) => {
    return router.get(route('cms.faqs.duplicate', id), {}, {
        onSuccess: () => {
            showMessage('Nhân bản bài viết thành công', 'success');
            // ElMessage.success('Nhân bản bài viết thành công');
        },
        onError: () => {
            showMessage('Có lỗi xảy ra khi nhân bản bài viết', 'error');
            // ElMessage.error('Có lỗi xảy ra khi nhân bản bài viết');
        }
    });
};

defineExpose({
    get selectedItems() {
        return selectedItems.value;
    },
    handleBulkDelete
});

</script>
<style scoped>
.min-width-cate-cus {
    min-width: 110px !important;
}
</style>
<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid mb-15">
            <div class="card">
                <div class="card-body">
                    <div class="row g-5">
                        <div class="col-xl-12">
                            <div class="row">
                                <div class="col-md-12 col-xl-12">
                                    <el-table :key="keyTable" v-loading="loading" :data="items.data" ref="tableRef"
                                              style="width: 100%">
                                        <el-table-column label="" prop="name" width="50">
                                            <template #header>
                                                <el-checkbox
                                                    :model-value="isAllSelected"
                                                    :indeterminate="isIndeterminate"
                                                    @change="handleSelectAll"
                                                />
                                            </template>
                                            <template #default="scope">
                                                <el-checkbox
                                                    :model-value="selectedItems.includes(scope.row.id)"
                                                    @change="handleSelect(scope.row.id)"
                                                />
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="#" width="50">
                                            <template #default="scope">
                                                {{ scope.$index + 1 }}.
                                            </template>
                                        </el-table-column>

                                        <el-table-column type="expand">
                                            <template #default="props">
                                                <div>
                                                    <p style="padding-left: 410px;"><strong>Trả lời:</strong> {{
                                                            props.row?.answer ?? ''
                                                        }}</p>
                                                </div>
                                            </template>
                                        </el-table-column>

                                        <!--
                                        <el-table-column label="Id" prop="id" sortable width="70"/>
                                        -->

                                        <el-table-column
                                            label="Loại" prop="type" width="250"
                                        >
                                            <template #header>
                                                <el-select
                                                    v-model="objectSearch.type"
                                                    placeholder="Loại"
                                                    size="large"
                                                    @change="filterType"
                                                    clearable
                                                >
                                                    <el-option
                                                        v-for="(label, value) in faqTypes"
                                                        :key="value"
                                                        :label="label"
                                                        :value="value"
                                                    />
                                                </el-select>
                                            </template>

                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.typeName }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Câu hỏi" prop="question" sortable>
                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    @click.prevent="handleEditItem(scope.row)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.question }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Trạng thái" prop="status" sortable
                                                         width="180">
                                            <template #default="scope">
                                                <el-switch
                                                    v-model="scope.row.isStatus"
                                                    active-text="Hiện"
                                                    class="ml-2"
                                                    inactive-text="Ẩn"
                                                    inline-prompt
                                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                                                    @change="changeStatusV2(scope.row, $event)"
                                                />
                                            </template>
                                        </el-table-column>
                                        <!--
                                        <el-table-column label="Nổi bật" prop="promoted" sortable
                                                         width="180">
                                            <template #default="scope">
                                                <el-switch
                                                    v-model="scope.row.isPromoted"
                                                    active-text="Y"
                                                    class="ml-2"
                                                    inactive-text="N"
                                                    inline-prompt
                                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                                                    @change="changePromoted(scope.row, $event)"
                                                />
                                            </template>
                                        </el-table-column>
                                        -->
                                        <el-table-column align="right" label="Hành động"
                                                         width="220">
                                            <template #default="scope">
                                                <el-tooltip
                                                    class="box-item"
                                                    content="Nhân bản"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button :icon="CopyDocument"
                                                               @click.stop.prevent="handleDuplicate(scope.row.id)"
                                                               type="info"
                                                    ></el-button>
                                                </el-tooltip>

                                                <el-tooltip
                                                    class="box-item"
                                                    content="cập nhật"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button :icon="Edit"
                                                               @click.stop.prevent="handleEditItem(scope.row)"
                                                               type="primary"></el-button>
                                                </el-tooltip>

                                                <el-tooltip
                                                    class="box-item"
                                                    content="xóa"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Delete"
                                                        type="danger"
                                                        @click.stop.prevent="deleteItem(scope.row, false)"
                                                    ></el-button>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container overflow-hidden">
                    <div class="row">

                        <div class="d-flex flex-center mb-5">
                            <el-pagination :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                                           :current-page="paginate.data.current_page"
                                           :page-size="paginate.data.per_page"
                                           :total="paginate.data.total"
                                           background
                                           layout="total, sizes, prev, pager, next"
                                           @size-change="handlePerPageChange"
                                           @current-change="handleCurrentChange"
                            />
                        </div>

                        <!--
                        <Pagination :pagination="items.meta"/>
                        -->
                        <!--
                        <div class="pagination-wrapper mt-4">
                            <div class="per-page-selector">
                                <span class="me-2">Hiển thị</span>
                                <el-select
                                    v-model="objectSearch.per_page"
                                    class="me-2 w-75px"
                                    @change="handlePerPageChange"
                                >
                                    <el-option
                                        v-for="option in perPageOptions"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>
                                <span>dòng mỗi trang</span>
                            </div>

                            <div v-if="paginate.total > 0" class="pagination-controls">
                                <PaginationInfo
                                    :from="paginate.from"
                                    :to="paginate.to"
                                    :total="paginate.total"
                                />
                                <Pagination :links="paginate.links"/>
                            </div>
                            <div v-else class="text-muted">
                                <span>Không có bản ghi nào</span>
                            </div>
                        </div>
                        -->
                    </div> <!-- end .row -->
                </div>
            </div>
        </div>

        <!--begin::Modal - Add/Update Item-->
        <FormDialog
            :key="dialogKey"
            :dialogFormVisible="dialogFormVisible"
            :form="form"
            :form-action="actionForm"
            :titleModal="titleModal"
            :options="faqTypes"
            @onSubmit="handleSubmit"
            @update:dialogFormVisible="dialogFormVisible = $event"
        />
        <!--end::Modal - Add/Update Item-->

        <el-dialog
            v-model="dialogVisible"
            title="Thông báo"
            width="500"
        >
            <span>Bạn có chắc chắn muốn xóa không ?</span>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">Thoát</el-button>
                    <el-button type="primary" @click="deleteItemAction">
                        Đồng ý
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </MainLayout>
</template>
