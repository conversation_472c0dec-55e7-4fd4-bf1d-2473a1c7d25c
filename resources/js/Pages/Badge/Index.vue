<script setup>

import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, useForm} from '@inertiajs/vue3'
import {computed, reactive, ref} from 'vue'
import {Edit} from '@element-plus/icons-vue'
import Toolbar from "@/Components/Toolbar.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import FormDialog from "@/Pages/Badge/Partials/FormDialog.vue";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";

const props = defineProps({
    cateName: String,
    items: Object,
    paginate: Object,
    perPage: Number,
    page: Number,
    type: String,
    routeIndex: String,
    routeCreate: String,
    routeStore: String,
    routeEdit: String,
    routeUpdate: String,
    routeDestroy: String,
    routeDestroys: String,
    routeShow: String,
    routeDuplicate: String,
    badgeTypes: Object,
    badgeCategories: Object,
    badgeRewardTypes: Object,
    badgeConditions: Object,
})

const disabled = ref(false)

let form = useForm({
    id: 0,
    type: props.type || '',
    name: '',
    code: '',
    description: '',
    condition_json: {},
    reward: '',
    category: '',
    icon_url: '',
    rewards: []
})

const resetForm = () => {
    form.id = 0
    form.type = props.type || ''
    form.name = ''
    form.code = ''
    form.description = ''
    form.condition_json = {}
    form.reward = ''
    form.category = ''
    form.icon_url = ''
    form.rewards = []
}

let loading = ref(false)
let keyTable = ref(1)

const breadcrumbs = [
    {text: 'Danh sách'}
];

const handleBulkDelete = () => {
    if (selectedCount.value === 0) {
        showMessage('Vui lòng chọn ít nhất một thành tích để xóa', 'warning');
        return;
    }

    isMultiDeleteType.value = true
    itemsDelete = selectedItems.value
    console.log('itemsDelete: ', itemsDelete)
    dialogVisible.value = true
}

const bulkDeleteButton = computed(() => ({
    name: `Xóa ${selectedCount.value} thành tích đã chọn`,
    className: 'btn-danger',
    click: handleBulkDelete
}));

const selectedCount = ref(0);
const tableRef = ref(null);
const actions = computed(() => {
    const actionButtons = [
        {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: reloadData},
        /*{
            name: 'Thêm mới',
            className: 'btn-primary',
            icon: IconAdd,
            click: addItem
        }*/
    ];

    // tạm đóng không cho sử dụng chức năng xóa
    /*if (selectedCount.value > 0) {
        actionButtons.push(bulkDeleteButton.value);
    }*/

    return actionButtons;
});

let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
let actionForm = ref('add')

const incrementDialogKey = () => {
    dialogKey.value += 1
}
const addItem = () => {
    console.log('add Badge')
    resetForm()
    titleModal.value = 'Thêm Mới Thành Tích'
    dialogFormVisible.value = true
    actionForm.value = 'add'
    incrementDialogKey()
}

// Create submit forms at setup level
const submitForm = useForm({})

const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data, ' -> actionForm: ', actionForm.value)

    if (actionForm.value === 'add') {
        console.log('add new badge')
        // Reset and populate the form
        submitForm.reset()
        Object.assign(submitForm, data)

        submitForm.post(route(props.routeStore), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    } else {
        console.log('update badge: ', data)
        let url = route(props.routeUpdate, data.id)

        // Reset and populate the form with PUT method
        submitForm.reset()
        Object.assign(submitForm, {_method: 'PUT', ...data})

        submitForm.put(url, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    }
}


const dialogVisible = ref(false)
// let dataDelete = ref(null)
let itemsDelete = reactive({})
let isMultiDeleteType = ref(false)

let objectSearch = reactive({
    page: props.page,
    per_page: props.perPage,
    type: props.type || '',
})

const deleteItem = (item, isMulti) => {
    console.log('deleteItem >> isMulti: ', isMulti, ' => item: ', item)
    isMultiDeleteType.value = isMulti
    dialogVisible.value = true
    itemsDelete = item
}

const deleteItemAction = () => {
    loading.value = true

    // const idsArr = Array.isArray(itemsDelete) ? itemsDelete.map(item => item.id) : [itemsDelete.id];
    const idsArr = Array.isArray(itemsDelete) ? itemsDelete : [itemsDelete.id];

    if (isMultiDeleteType.value) {
        console.log('deleteItemAction >> deleteSelected: ', idsArr)
        const formPost = useForm({
            ids: idsArr
        })
        formPost.post(route(props.routeDestroys), {
            onSuccess: (res) => {
                console.log('deleteItemAction >> onSuccess: ', res)
                // clearIds(totalItems)
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        console.log('deleteItemAction >> itemsDelete.value: ', itemsDelete.id)
        const deleteForm = useForm({})
        deleteForm.delete(route(props.routeDestroy, itemsDelete), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    loading.value = false
    dialogVisible.value = false
    itemsDelete = {};
    selectedCount.value = 0
    selectedItems.value = []
}

const selectedItems = ref([]);

const isAllSelected = computed(() => {
    return props.items.data.length > 0 && selectedItems.value.length === props.items.data.length;
});

const isIndeterminate = computed(() => {
    return selectedItems.value.length > 0 && selectedItems.value.length < props.items.data.length;
});

const handleSelectAll = (val) => {
    selectedItems.value = val ? props.items.data.map(item => item.id) : [];
    // emit('selected-count', selectedItems.value.length);
    selectedCount.value = selectedItems.value.length
}

const handleSelect = (id) => {
    console.log('handleSelect >> id: ', id)
    const index = selectedItems.value.indexOf(id);
    if (index === -1) {
        selectedItems.value.push(id);
    } else {
        selectedItems.value.splice(index, 1);
    }
    // emit('selected-count', selectedItems.value.length);
    selectedCount.value = selectedItems.value.length
}


// Loading & reload data
// ========================================================================
const reloadData = () => {
    loading.value = true
    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    const reloadForm = useForm({})
    reloadForm.get(route(props.routeIndex) + '?' + searchParams, {
        onSuccess: () => {
            loading.value = false
        }
    })
}
const changeStatusV2 = (item, val) => {
    console.log('changeStatusV2 >> val: ', val, ' => item: ', item)
    const statusForm = useForm({
        type: item.type,
        question: item.question,
        answer: item.answer,
        status: val ? 1 : 0
    })

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    let url = route(props.routeUpdate, item.id ?? 0);
    if (searchParams) {
        url += '?' + searchParams
    }
    console.log('changeStatusV2 >> url: ', url, ' -> searchParams: ', searchParams)
    statusForm.patch(url, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (res) => {
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

const handlePerPageChange = (value) => {
    objectSearch.per_page = value;
    objectSearch.page = 1; // Reset to page 1 when changing items per page

    /*const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();

    router.get(route('cms.faqs.index') + '?' + searchParams, {}, {
        preserveState: true,
        preserveScroll: false,
        onSuccess: () => {
            showMessage('Đã thay đổi số lượng hiển thị!', 'success');
        }
    });*/

    reloadData();
}

const handleCurrentChange = (val) => {
    console.log(`current page: ${val} >> objectSearch: ${JSON.stringify(props.paginate.current_page)}`)
    // console.log(`current page: ${val} >> meta: ${JSON.stringify(props.meta)}`)
    // Store the current page in local state
    objectSearch.page = val
    // Update the local currentPage ref
    // objectSearch.value = val
    // Importantly, make sure to pass the current perPage value, not the default
    reloadData();
}

const filterType = (value) => {
    console.log('filterType: ', value)
    // return row.tag === value
    objectSearch.type = value;
    reloadData();
}

const fetchItemById = async (id) => {
    loading.value = true;
    try {
        const url = route(props.routeShow, id);
        const response = await axios.get(`${url}`);
        console.log('fetchItemById: ', response.data.data);
        if (response.data.success) {
            return response.data.data;
        } else {
            return null;
        }
    } catch (error) {
        console.error('Error fetching badge item:', error);
        showMessage('Có lỗi xảy ra khi lấy dữ liệu', 'error');
        return null;
    } finally {
        loading.value = false;
    }
};

const handleEditItem = async (item) => {
    const dataInfo = await fetchItemById(item.id)
    if (dataInfo) {
        form.id = dataInfo.id;
        form.type = dataInfo.type;
        form.name = dataInfo.name;
        form.code = dataInfo.code;
        form.description = dataInfo.description;
        form.condition_json = dataInfo.condition_json || {};
        form.reward = dataInfo.reward || '';
        form.category = dataInfo.category;
        form.icon_url = dataInfo.icon_url || '';
        form.rewards = dataInfo.badge_rewards || [];
    }

    titleModal.value = 'Cập Nhật Thành Tích'
    dialogFormVisible.value = true
    actionForm.value = 'edit'
    incrementDialogKey()
}

const handleDuplicateItem = (item) => {
    const duplicateForm = useForm({})
    duplicateForm.post(route(props.routeDuplicate, item.id), {
        onSuccess: (res) => {
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

defineExpose({
    get selectedItems() {
        return selectedItems.value;
    },
    handleBulkDelete
});

</script>
<style scoped>
.min-width-cate-cus {
    min-width: 110px !important;
}
</style>
<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid mb-15">
            <div class="card">
                <div class="card-body">
                    <div class="row g-5">
                        <div class="col-xl-12">
                            <div class="row">
                                <div class="col-md-12 col-xl-12">
                                    <el-table
                                        :key="keyTable" v-loading="loading" :data="items.data" ref="tableRef"
                                        style="width: 100%"
                                    >
                                        <el-table-column label="" prop="name" width="50">
                                            <template #header>
                                                <el-checkbox
                                                    :model-value="isAllSelected"
                                                    :indeterminate="isIndeterminate"
                                                    @change="handleSelectAll"
                                                />
                                            </template>
                                            <template #default="scope">
                                                <el-checkbox
                                                    :model-value="selectedItems.includes(scope.row.id)"
                                                    @change="handleSelect(scope.row.id)"
                                                />
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="#" width="50">
                                            <template #default="scope">
                                                {{ scope.$index + 1 }}.
                                            </template>
                                        </el-table-column>

                                        <el-table-column
                                            label="Loại" prop="type" width="180"
                                        >
                                            <template #header>
                                                <el-select
                                                    v-model="objectSearch.type"
                                                    placeholder="Loại"
                                                    size="large"
                                                    @change="filterType"
                                                    clearable
                                                >
                                                    <el-option
                                                        v-for="(label, value) in badgeTypes"
                                                        :key="value"
                                                        :label="label"
                                                        :value="value"
                                                    />
                                                </el-select>
                                            </template>

                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.typeName }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Icon" prop="icon_url" width="110">
                                            <template #default="scope">
                                                <el-image style="width: 80px; height: 80px" :src="scope.row.icon_url"
                                                          fit="contain"/>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Tên thành tích" prop="name" sortable>
                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    @click.prevent="handleEditItem(scope.row)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.name }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Mô tả" prop="description">
                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    @click.prevent="handleEditItem(scope.row)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.description }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column align="right" label="Hành động" width="200">
                                            <template #default="scope">
                                                <!--
                                                <el-tooltip
                                                    class="box-item"
                                                    content="Nhân bản"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="CopyDocument"
                                                        @click.stop.prevent="handleDuplicateItem(scope.row)"
                                                        type="info"
                                                    ></el-button>
                                                </el-tooltip>
                                                -->
                                                <el-tooltip
                                                    class="box-item"
                                                    content="cập nhật"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Edit"
                                                        @click.stop.prevent="handleEditItem(scope.row)"
                                                        type="primary"></el-button>
                                                </el-tooltip>
                                                <!--
                                                <el-tooltip
                                                    class="box-item"
                                                    content="xóa"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Delete"
                                                        type="danger"
                                                        class=""
                                                        @click.stop.prevent="deleteItem(scope.row, false)"
                                                    ></el-button>
                                                </el-tooltip>
                                                -->
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container overflow-hidden">
                    <div class="row">

                        <div class="d-flex flex-center mb-5">
                            <el-pagination
                                :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                                :current-page="paginate.data.current_page"
                                :page-size="paginate.data.per_page"
                                :total="paginate.data.total"
                                background
                                layout="total, sizes, prev, pager, next"
                                @size-change="handlePerPageChange"
                                @current-change="handleCurrentChange"
                            />
                        </div>
                    </div> <!-- end .row -->
                </div>
            </div>
        </div>

        <!--begin::Modal - Add/Update Item-->
        <FormDialog
            :key="dialogKey"
            :dialogFormVisible="dialogFormVisible"
            :form="form"
            :form-action="actionForm"
            :titleModal="titleModal"
            :badgeTypes="badgeTypes"
            :badgeCategories="badgeCategories"
            :badgeRewardTypes="badgeRewardTypes"
            :badge-conditions="badgeConditions"
            @onSubmit="handleSubmit"
            @update:dialogFormVisible="dialogFormVisible = $event"
        />
        <!--end::Modal - Add/Update Item-->

        <el-dialog
            v-model="dialogVisible"
            title="Thông báo"
            width="500"
        >
            <span>Bạn có chắc chắn muốn xóa không ?</span>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">Thoát</el-button>
                    <el-button type="primary" @click="deleteItemAction">
                        Đồng ý
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </MainLayout>
</template>
