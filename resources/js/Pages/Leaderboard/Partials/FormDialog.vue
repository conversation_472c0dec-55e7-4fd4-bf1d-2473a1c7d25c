<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal"
               style="margin-top: 5% !important;"
               width="50%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

        <el-form :model="localForm"
                 @submit.prevent="handleSubmit"
                 ref="ruleFormRef"
                 :rules="rules"
                 class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7"
                 style="margin-top: 0 !important;padding-top: 0 !important;"
        >
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Chọn <PERSON></label>
                <!--end::Label-->
                <el-form-item prop="type">
                    <!--
                    <el-input v-model="localForm.type"
                              size="large"
                              placeholder="Nhập họ và tên"
                              class="form-control form-control-solid mb-3 mb-lg-0"
                    />
                    -->
                    <el-select
                        v-model="localForm.type"
                        placeholder="Select"
                        size="large"
                    >
                        <el-option
                            v-for="(label, value) in options"
                            :key="value"
                            :label="label"
                            :value="value"
                        />
                    </el-select>

                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Câu hỏi</label>
                <!--end::Label-->
                <el-form-item prop="question">
                    <!--begin::Input-->
                    <el-input v-model="localForm.question"
                              type="textarea"
                              size="large"
                              :rows="4"
                              placeholder="Nhập câu hỏi"
                              class="form-control form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Trả lời</label>
                <!--end::Label-->
                <el-form-item prop="answer">
                    <!--begin::Input-->
                    <el-input v-model="localForm.answer"
                              type="textarea"
                              size="large"
                              :rows="4"
                              placeholder="Nhập nội dung trả lời ..."
                              class="form-control form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->


        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Hủy</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Lưu
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {reactive, ref, watch, computed} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object,
    formAction: String,
    options: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const ruleFormRef = ref(null);

// Use ref for dialog visibility to avoid recursive updates
const localDialogFormVisible = ref(false);

// Watch for dialogFormVisible prop changes
watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
}, { immediate: true });

// Watch for local dialog changes and emit to parent
watch(localDialogFormVisible, (newValue) => {
    if (newValue !== props.dialogFormVisible) {
        emit('update:dialogFormVisible', newValue);
    }
});

// Create a local copy of the form data
const localForm = reactive({
    type: '',
    question: '',
    answer: '',
    status: 1
});

// Watch for form prop changes and update local form
watch(() => props.form, (newForm) => {
    if (newForm) {
        Object.assign(localForm, newForm);
    }
}, { immediate: true, deep: true });

// begin: validation
const rules = reactive({
    question: [
        {required: true, message: 'Câu hỏi không được để trống', trigger: 'blur'},
        {min: 3, max: 255, message: 'Length should be 3 to 255', trigger: 'blur'},
    ],
    answer: [
        {required: true, message: 'Câu trả lời không được để trống', trigger: 'blur'},
    ],
    type: [
        {required: true, message: 'Loại câu hỏi không được để trống', trigger: 'blur'},
    ],
})

// end: validation

const handleSubmit = async () => {
    if (!ruleFormRef.value) return;

    try {
        const isValid = await ruleFormRef.value.validate();
        if (isValid) {
            console.log('Form submitted successfully!');
            emit('onSubmit', localForm);
        }
    } catch (error) {
        console.log('Validation failed:', error);
        return false;
    }
};

</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
<style>
.el-input--large .el-input__wrapper {
    width: 100% !important;
}
</style>
