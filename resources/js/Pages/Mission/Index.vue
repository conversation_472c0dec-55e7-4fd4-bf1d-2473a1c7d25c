<script setup>

import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, router, useForm} from '@inertiajs/vue3'
import {computed, reactive, ref} from 'vue'
import {Edit} from '@element-plus/icons-vue'
import Toolbar from "@/Components/Toolbar.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import FormDialog from "@/Pages/Mission/Partials/FormDialog.vue";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";

const props = defineProps({
    cateName: String,
    statusArr: Array,
    items: Object,
    paginate: Object,
    perPage: Number,
    type: String,
    faqTypes: Object,
    missionConditionsTypes: Object,
    missionRewardTypes: Object,
    routeIndex: String,
    routeCreate: String,
    routeStore: String,
    routeEdit: String,
    routeUpdate: String,
    routeDestroy: String,
    routeDestroys: String,
    routeShow: String,
    routeDuplicate: String
})

let form = useForm({
    type: props.type || '',
    code: '',
    name: '',
    description: '',
    condition_json: {},
    reward_json: {},
    conditions: [],
    rewards: [],
    repeatable: 0,
    is_active: 1,
    start_time: '',
    end_time: '',
    created_at: '',
    updated_at: ''
})
const resetForm = () => {
    form.id = 0
    form.type = props.type
    form.code = ''
    form.name = ''
    form.description = ''
    form.condition_json = ''
    form.reward_json = ''
    form.conditions = []
    form.rewards = []
    form.repeatable = 0
    form.start_time = ''
    form.end_time = ''
    form.created_at = ''
    form.updated_at = ''
    form.is_active = 1
}

let loading = ref(false)
let keyTable = ref(1)

const breadcrumbs = [
    {text: 'Danh sách'}
];

const handleBulkDelete = () => {
    if (selectedCount.value === 0) {
        showMessage('Vui lòng chọn ít nhất một bài viết để xóa', 'warning');
        return;
    }

    isMultiDeleteType.value = true
    itemsDelete = selectedItems.value
    // console.log('itemsDelete: ', itemsDelete)
    dialogVisible.value = true
}

const bulkDeleteButton = computed(() => ({
    name: `Xóa ${selectedCount.value} bài viết đã chọn`,
    className: 'btn-danger',
    click: handleBulkDelete
}));

const selectedCount = ref(0);
const tableRef = ref(null);
const actions = computed(() => {
    const actionButtons = [
        {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: reloadData},
        /*{
            name: 'Thêm mới',
            className: 'btn-primary',
            icon: IconAdd,
            click: addItem
        }*/
    ];

    // tạm đóng không cho sử dụng chức năng xóa
    // if (selectedCount.value > 0) {
    //     actionButtons.push(bulkDeleteButton.value);
    // }

    return actionButtons;
});

let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
let actionForm = ref('add')

const incrementDialogKey = () => {
    dialogKey.value += 1
}
const addItem = () => {
    console.log('addItem ')
    resetForm()
    titleModal.value = 'Thêm Mới'
    dialogFormVisible.value = true
    actionForm.value = 'add'
    incrementDialogKey()
}

// Create submit forms at setup level
const submitForm = useForm({})

const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data, ' -> actionForm: ', actionForm.value)

    if (actionForm.value === 'add') {
        console.log('[handleSubmit] add new user')
        // Reset and populate the form
        // submitForm.reset()
        Object.assign(submitForm, data)
        console.log('[handleSubmit] submitForm: ', submitForm)
        submitForm.post(route(props.routeStore), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    } else {
        console.log('[handleSubmit] update: ', data)
        let url = route(props.routeUpdate, data)

        // Reset and populate the form with PUT method
        submitForm.reset()
        Object.assign(submitForm, {_method: 'PUT', ...data})

        submitForm.put(url, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
                resetForm()
            },
            onError: (error) => {
                console.log('handleSubmit >> error: ', error)
            }
        })
    }
}


const dialogVisible = ref(false)
// let dataDelete = ref(null)
let itemsDelete = reactive({})
let isMultiDeleteType = ref(false)

let objectSearch = reactive({
    page: props.currentPage,
    sort: props.sort,
    // keyword: props.keyword,
    per_page: props.perPage,
    type: props.type || '',
})

const deleteItem = (item, isMulti) => {
    console.log('deleteItem >> isMulti: ', isMulti, ' => item: ', item)
    isMultiDeleteType.value = isMulti
    dialogVisible.value = true
    itemsDelete = item
}

const deleteItemAction = () => {
    loading.value = true

    const idsArr = Array.isArray(itemsDelete) ? itemsDelete : [itemsDelete.id];

    if (isMultiDeleteType.value) {
        console.log('deleteItemAction >> deleteSelected: ', idsArr)
        const formPost = useForm({
            ids: idsArr
        })
        formPost.post(route(props.routeDestroys), {
            onSuccess: (res) => {
                console.log('deleteItemAction >> onSuccess: ', res)
                // clearIds(totalItems)
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        console.log('deleteItemAction >> itemsDelete.value: ', itemsDelete.id)
        const deleteForm = useForm({})
        deleteForm.delete(route(props.routeDestroy, itemsDelete), {
            onSuccess: (res) => {
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    loading.value = false
    dialogVisible.value = false
    itemsDelete = {};
    selectedCount.value = 0
    selectedItems.value = []
}

const selectedItems = ref([]);

const isAllSelected = computed(() => {
    return props.items.data.length > 0 && selectedItems.value.length === props.items.data.length;
});

const isIndeterminate = computed(() => {
    return selectedItems.value.length > 0 && selectedItems.value.length < props.items.data.length;
});

const handleSelectAll = (val) => {
    // console.log('handleSelectAll >> val: ', val)
    selectedItems.value = val ? props.items.data.map(item => item.id) : [];
    // console.log('handleSelectAll >> selectedItems.value: ', selectedItems.value)
    selectedCount.value = selectedItems.value.length
    // emit('selected-count', selectedItems.value.length);
}

const handleSelect = (id) => {
    console.log('handleSelect >> id: ', id)
    const index = selectedItems.value.indexOf(id);
    if (index === -1) {
        selectedItems.value.push(id);
    } else {
        selectedItems.value.splice(index, 1);
    }
    // emit('selected-count', selectedItems.value.length);
    selectedCount.value = selectedItems.value.length
}


// Loading & reload data
// ========================================================================
const reloadData = () => {
    loading.value = true
    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    const reloadForm = useForm({})
    reloadForm.get(route(props.routeIndex) + '?' + searchParams, {
        onSuccess: () => {
            loading.value = false
        }
    })
}
const changeStatus = (item, val) => {
    console.log('changeStatus >> val: ', val, ' => item: ', item)
    const statusForm = useForm({
        ...item,
        condition_json: item.conditions.reduce((acc, condition) => {
            if (condition.key && condition.value) {
                acc[condition.key] = parseInt(condition.value);
            }
            return acc;
        }, {}),
        reward_json: item.rewards.reduce((acc, reward) => {
            if (reward.key && reward.value) {
                acc[reward.key] = parseInt(reward.value);
            }
            return acc;
        }, {}),
        is_active: val ? 1 : 0
    })

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();
    let url = route(props.routeUpdate, item.id ?? 0);
    if (searchParams) {
        url += '?' + searchParams
    }
    console.log('changeStatus >> url: ', url, ' -> searchParams: ', searchParams)
    statusForm.patch(url, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (res) => {
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

const handlePerPageChange = (value) => {
    objectSearch.per_page = value;
    objectSearch.page = 1; // Reset to page 1 when changing items per page
    reloadData();
}

const handleCurrentChange = (val) => {
    console.log(`current page: ${val} >> objectSearch: ${JSON.stringify(props.paginate.current_page)}`)
    // Store the current page in local state
    objectSearch.page = val
    // Update the local currentPage ref
    // objectSearch.value = val
    // Importantly, make sure to pass the current perPage value, not the default
    reloadData();
}

const filterType = (value) => {
    console.log('filterType: ', value)
    // return row.tag === value
    objectSearch.type = value;
    reloadData();
}

const fetchItemById = async (id) => {
    loading.value = true;
    try {
        const url = route(props.routeShow, id);
        const response = await axios.get(`${url}`);
        console.log('fetchItemById: ', response.data.data);
        if (response.data.success) {
            return response.data.data;
        } else {
            return null;
        }
    } catch (error) {
        console.error('Error fetching item faq:', error);
        showMessage('Có lỗi xảy ra khi lấy dữ liệu', 'error');
        return null;
    } finally {
        loading.value = false;
    }
};

const handleEditItem = async (item) => {
    // console.log('handleEditItem: ', item)
    // router.get(route(props.routeEdit, item))
    const dataInfo = await fetchItemById(item.id)
    // console.log('dataInfo: ', dataInfo)
    if (dataInfo) {
        form.id = dataInfo.id;
        form.type = dataInfo.type;
        form.name = dataInfo.name;
        form.code = dataInfo.code;
        form.description = dataInfo.description;
        // form.condition_json = dataInfo.condition_json;
        form.conditions = dataInfo.conditions;
        // form.reward_json = dataInfo.reward_json;
        form.rewards = dataInfo.rewards;
        form.repeatable = dataInfo.repeatable;
        form.is_active = dataInfo.is_active;
        form.start_time = dataInfo.start_time;
        form.end_time = dataInfo.end_time;

        titleModal.value = 'Cập nhật'
        dialogFormVisible.value = true
        actionForm.value = 'update'
        incrementDialogKey()
    }
}

const handleDuplicate = (id) => {
    return router.get(route(props.routeDuplicate, id), {}, {
        onSuccess: (res) => {
            // console.log("res: ", res)
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
            // showMessage('Nhân bản item thành công', 'success');
            // ElMessage.success('Nhân bản bài viết thành công');
        },
        onError: () => {
            showMessage('Có lỗi xảy ra khi nhân bản item', 'error');
        }
    });
};

defineExpose({
    get selectedItems() {
        return selectedItems.value;
    },
    handleBulkDelete
});

</script>
<style scoped>
.min-width-cate-cus {
    min-width: 110px !important;
}
</style>
<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid mb-15">
            <div class="card">
                <div class="card-body">
                    <div class="row g-5">
                        <div class="col-xl-12">
                            <div class="row">
                                <div class="col-md-12 col-xl-12">
                                    <el-table :key="keyTable" v-loading="loading" :data="items.data" ref="tableRef"
                                              style="width: 100%">
                                        <el-table-column label="" prop="name" width="50">
                                            <template #header>
                                                <el-checkbox
                                                    :model-value="isAllSelected"
                                                    :indeterminate="isIndeterminate"
                                                    @change="handleSelectAll"
                                                />
                                            </template>
                                            <template #default="scope">
                                                <el-checkbox
                                                    :model-value="selectedItems.includes(scope.row.id)"
                                                    @change="handleSelect(scope.row.id)"
                                                />
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="#" width="50">
                                            <template #default="scope">
                                                {{ scope.$index + 1 }}.
                                            </template>
                                        </el-table-column>

                                        <!--
                                        <el-table-column type="expand">
                                            <template #default="props">
                                                <div>
                                                    <p style="padding-left: 360px;"><strong>Trả lời:</strong> {{
                                                            props.row?.answer ?? ''
                                                        }}</p>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        -->

                                        <!--
                                        <el-table-column label="Id" prop="id" sortable width="70"/>
                                        -->

                                        <el-table-column
                                            label="Loại" prop="type" width="250"
                                        >
                                            <template #header>
                                                <el-select
                                                    v-model="objectSearch.type"
                                                    placeholder="Loại"
                                                    size="large"
                                                    @change="filterType"
                                                    clearable
                                                >
                                                    <el-option
                                                        v-for="(label, value) in faqTypes"
                                                        :key="value"
                                                        :label="label"
                                                        :value="value"
                                                    />
                                                </el-select>
                                            </template>

                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.typeName }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Tên nhiệm vụ" prop="name" sortable>
                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    @click.prevent="handleEditItem(scope.row)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.name }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column label="Mô tả" prop="description">
                                            <template #default="scope">
                                                <a
                                                    href="javascript:void(0)"
                                                    @click.prevent="handleEditItem(scope.row)"
                                                    class="text-reset"
                                                >
                                                    {{ scope.row.description }}
                                                </a>
                                            </template>
                                        </el-table-column>

                                        <el-table-column
                                            label="Trạng thái"
                                            prop="status"
                                            sortable
                                            width="180"
                                        >
                                            <template #default="scope">
                                                <el-switch
                                                    v-model="scope.row.isStatus"
                                                    active-text="Hiện"
                                                    class="ml-2"
                                                    inactive-text="Ẩn"
                                                    inline-prompt
                                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                                                    @change="changeStatus(scope.row, $event)"
                                                />
                                            </template>
                                        </el-table-column>

                                        <el-table-column
                                            align="right"
                                            label="Hành động"
                                            width="220"
                                        >
                                            <template #default="scope">
                                                <!--
                                                <el-tooltip
                                                    class="box-item"
                                                    content="Nhân bản"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="CopyDocument"
                                                        @click.stop.prevent="handleDuplicate(scope.row.id)"
                                                        type="info"
                                                    ></el-button>
                                                </el-tooltip>
                                                -->

                                                <el-tooltip
                                                    class="box-item"
                                                    content="cập nhật"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Edit"
                                                        @click.stop.prevent="handleEditItem(scope.row)"
                                                        type="primary"
                                                    ></el-button>
                                                </el-tooltip>

                                                <!--
                                                <el-tooltip
                                                    class="box-item"
                                                    content="xóa"
                                                    effect="dark"
                                                    placement="top"
                                                >
                                                    <el-button
                                                        :icon="Delete"
                                                        type="danger"
                                                        @click.stop.prevent="deleteItem(scope.row, false)"
                                                    ></el-button>
                                                </el-tooltip>
                                                -->
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container overflow-hidden">
                    <div class="row">

                        <div class="d-flex flex-center mb-5">
                            <el-pagination
                                :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                                :current-page="paginate.data.current_page"
                                :page-size="paginate.data.per_page"
                                :total="paginate.data.total"
                                background
                                layout="total, sizes, prev, pager, next"
                                @size-change="handlePerPageChange"
                                @current-change="handleCurrentChange"
                            />
                        </div>
                    </div> <!-- end .row -->
                </div>
            </div>
        </div>

        <!--begin::Modal - Add/Update Item-->
        <FormDialog
            :key="dialogKey"
            :dialogFormVisible="dialogFormVisible"
            :form="form"
            :form-action="actionForm"
            :titleModal="titleModal"
            :options="faqTypes"
            :missionConditionsTypes="missionConditionsTypes"
            :missionRewardTypes="missionRewardTypes"
            @onSubmit="handleSubmit"
            @update:dialogFormVisible="dialogFormVisible = $event"
        />
        <!--end::Modal - Add/Update Item-->

        <el-dialog
            v-model="dialogVisible"
            title="Thông báo"
            width="500"
        >
            <span>Bạn có chắc chắn muốn xóa không ?</span>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">Thoát</el-button>
                    <el-button type="primary" @click="deleteItemAction">
                        Đồng ý
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </MainLayout>
</template>
