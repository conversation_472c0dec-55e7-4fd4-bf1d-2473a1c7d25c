<template>
    <el-dialog
        v-model="localDialogFormVisible"
        :title="titleModal"
        style="margin-top: 5% !important;"
        width="50%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

        <el-form
            :model="localForm"
            @submit.prevent="handleSubmit"
            ref="ruleFormRef"
            :rules="rules"
            class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7"
            style="margin-top: 0 !important;padding-top: 0 !important;"
        >

            <!--                        <pre>
                                    {{ localForm }}
                                        </pre>-->
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Chọn Loại</label>
                <!--end::Label-->
                <el-form-item prop="type">
                    <el-select
                        v-model="localForm.type"
                        placeholder="Select"
                        size="large"
                    >
                        <el-option
                            v-for="(label, value) in options"
                            :key="value"
                            :label="label"
                            :value="value"
                        />
                    </el-select>

                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Nhập tên nhiệm vụ</label>
                <!--end::Label-->
                <el-form-item prop="name">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.name"
                        size="large"
                        :rows="4"
                        placeholder="Nhập tên nhiệm vụ"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fw-bold fs-6 mb-2">Mô tả</label>
                <!--end::Label-->
                <el-form-item prop="description">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.description"
                        type="textarea"
                        size="large"
                        :rows="4"
                        placeholder="Nhập nội dung mô tả ..."
                        class="form-control22 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Condition JSON Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Điều kiện</label>
                <!--end::Label-->

                <div v-for="(condition, index) in localForm.conditions" :key="index"
                     class="d-flex align-items-center mb-3">
                    <el-select
                        v-model="condition.key"
                        placeholder="Chọn điều kiện"
                        size="large"
                        class="me-3"
                        style="width: 200px;"
                    >
                        <!--
                        <el-option label="Unique Tables Played" value="unique_tables_played"/>
                        <el-option label="Games Won" value="games_won"/>
                        <el-option label="Total Hands" value="total_hands"/>
                        <el-option label="Chips Won" value="chips_won"/>
                        -->
                        <el-option
                            v-for="(label, value) in missionConditionsTypes"
                            :key="value"
                            :label="label"
                            :value="value"
                        />
                    </el-select>


                    <el-input
                        v-model="condition.value"
                        size="large"
                        placeholder="Nhập giá trị"
                        type="number"
                        class="me-3 w-100"
                        style="flex: 1;width: 500px;"
                    />


                    <el-button
                        type="primary"
                        size="large"
                        @click="addCondition"
                        class="me-2"
                    >
                        +
                    </el-button>
                    <el-button
                        type="danger"
                        size="large"
                        @click="removeCondition(index)"
                        :disabled="localForm.conditions.length === 1"
                    >
                        -
                    </el-button>


                </div>

            </div>
            <!--end::Condition JSON Input group-->

            <!--begin::Reward JSON Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Phần thưởng</label>
                <!--end::Label-->
                <div v-for="(reward, index) in localForm.rewards" :key="index"
                     class="d-flex align-items-center mb-3">
                    <el-select
                        v-model="reward.key"
                        placeholder="Chọn phần thưởng"
                        size="large"
                        class="me-3"
                        style="width: 200px;"
                    >
                        <!--
                        <el-option label="Experience" value="exp"/>
                        <el-option label="Coins" value="coins"/>
                        <el-option label="Gems" value="gems"/>
                        <el-option label="Items" value="items"/>
                        -->
                        <el-option
                            v-for="(label, value) in missionRewardTypes"
                            :key="value"
                            :label="label"
                            :value="value"
                        />
                    </el-select>
                    <el-input
                        v-model="reward.value"
                        size="large"
                        placeholder="Nhập giá trị"
                        type="number"
                        class="me-3"
                        style="flex: 1;"
                    />
                    <el-button
                        type="primary"
                        size="large"
                        @click="addReward"
                        class="me-2"
                    >
                        +
                    </el-button>
                    <el-button
                        type="danger"
                        size="large"
                        @click="removeReward(index)"
                        :disabled="localForm.rewards.length === 1"
                    >
                        -
                    </el-button>
                </div>
            </div>
            <!--end::Reward JSON Input group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Hủy</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Lưu
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {reactive, ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object,
    formAction: String,
    options: Object,
    missionConditionsTypes: Object,
    missionRewardTypes: Object,
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const ruleFormRef = ref(null);

// Use ref for dialog visibility to avoid recursive updates
const localDialogFormVisible = ref(false);

// Watch for dialogFormVisible prop changes
watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
}, {immediate: true});

// Watch for local dialog changes and emit to parent
watch(localDialogFormVisible, (newValue) => {
    if (newValue !== props.dialogFormVisible) {
        emit('update:dialogFormVisible', newValue);
    }
});

// Create a local copy of the form data
const localForm = reactive({
    type: '',
    name: '',
    code: '',
    description: '',
    repeatable: 0,
    is_active: 1,
    conditions: [{key: '', value: ''}],
    rewards: [{key: '', value: ''}]
});

// Watch for form prop changes and update local form
watch(() => props.form, (newForm) => {
    if (newForm) {
        Object.assign(localForm, newForm);
        // console.log("localForm: ", localForm);

        // if (localForm.condition_json && localForm.condition_json !== '{}') {
        //     const conditionJson = JSON.parse(localForm.condition_json);
        //     console.log("conditionJson: ", conditionJson);
        //     localForm.conditions = Object.entries(conditionJson).map(([key, value]) => ({
        //         key,
        //         value: value.toString()
        //     }));
        // }
        //
        // if (localForm.reward_json && localForm.reward_json !== '{}') {
        //     const rewardJson = JSON.parse(localForm.reward_json);
        //     console.log("rewardJson: ", rewardJson);
        //     localForm.rewards = Object.entries(rewardJson).map(([key, value]) => ({
        //         key,
        //         value: value.toString()
        //     }));
        // }

        // Initialize arrays if they don't exist
        if (!localForm.conditions || localForm.conditions.length === 0) {
            localForm.conditions = [{key: '', value: ''}];
            // console.log("localForm.conditions: ", localForm.conditions);
        }

        if (!localForm.rewards || localForm.rewards.length === 0) {
            localForm.rewards = [{key: '', value: ''}];
        }
    }
}, {immediate: true, deep: true});

// begin: validation
const rules = reactive({
    name: [
        {required: true, message: 'Tên nhiệm vụ không được để trống', trigger: 'blur'},
        {min: 3, max: 255, message: 'Length should be 3 to 255', trigger: 'blur'},
    ],
    // answer: [
    //     {required: true, message: 'Câu trả lời không được để trống', trigger: 'blur'},
    // ],
    type: [
        {required: true, message: 'Loại nhiệm vụ không được để trống', trigger: 'blur'},
    ],
    // conditions: [
    //     {required: true, message: 'Điều kiện không được để trống', trigger: 'blur'},
    //     {min: 1, max: 255, message: 'Length should be 1 to 255', trigger: 'blur'},
    // ],
    // rewards: [
    //     {required: true, message: 'Phần thưởng không được để trống', trigger: 'blur'},
    //     {min: 1, max: 255, message: 'Length should be 1 to 255', trigger: 'blur'},
    // ],
})

// end: validation

const handleSubmit = async () => {
    if (!ruleFormRef.value) return;

    try {
        const isValid = await ruleFormRef.value.validate();
        if (isValid) {
            console.log('Form submitted successfully!');

            // Convert arrays to JSON objects
            const formData = {
                ...localForm,
                condition_json: localForm.conditions.reduce((acc, condition) => {
                    if (condition.key && condition.value) {
                        acc[condition.key] = parseInt(condition.value);
                    }
                    return acc;
                }, {}),
                reward_json: localForm.rewards.reduce((acc, reward) => {
                    if (reward.key && reward.value) {
                        acc[reward.key] = parseInt(reward.value);
                    }
                    return acc;
                }, {})
            };

            emit('onSubmit', formData);
        }
    } catch (error) {
        console.log('Validation failed:', error);
        return false;
    }
};

const addCondition = () => {
    localForm.conditions.push({key: '', value: ''});
};

const removeCondition = (index) => {
    localForm.conditions.splice(index, 1);
};

const addReward = () => {
    localForm.rewards.push({key: '', value: ''});
};

const removeReward = (index) => {
    localForm.rewards.splice(index, 1);
};

</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
<style>
.el-input--large .el-input__wrapper {
    width: 100% !important;
}
</style>
