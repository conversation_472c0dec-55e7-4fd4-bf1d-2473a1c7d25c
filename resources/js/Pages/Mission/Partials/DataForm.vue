<script setup>

import InputError from '@/Components/InputError.vue'
import { ref } from 'vue'
import { Back } from '@element-plus/icons-vue'
import { Link } from '@inertiajs/vue3'

const props = defineProps({
    form: Object,
    statusArr: Array,
    // promotedArr: Array,
    page: Number,
    // type: String,
    routeIndex: String
})

const outerVisible = ref(false)
const testValue = ref(1)

const emit = defineEmits(['cb:changeStateSubmitBtn'])
const handleChangeStatesAction = (newState) => {
    // statesAction.value = newState
    // console.log('handleChangeStatesAction >> statesAction: ', statesAction.value)
    emit('cb:changeStateSubmitBtn', newState)
}

</script>

<template>
    <div class="row row-cards">
        <div class="col-lg-12">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 col-xl-12">
                                    <div class="mb-3 row">
                                        <label class="col-3 col-form-label required">Nhập câu hỏi</label>
                                        <div class="col">
                                            <el-input v-model="form.title"
                                                      placeholder="Nhập câu hỏi ..."
                                                      size="large" />
                                            <InputError :message="form.errors.title" />
                                        </div>
                                    </div>
                                    <div class="mb-3 row">
                                        <label class="col-3 col-form-label">Nhập câu trả lời</label>
                                        <div class="col">
                                            <el-input v-model="form.detail"
                                                      placeholder="Nhập câu trả lời ..."
                                                      size="large"
                                                      type="textarea"
                                            />
                                            <InputError :message="form.errors.detail" />
                                        </div>
                                    </div>

                                    <div class="mb-3 row">
                                        <label class="col-3 col-form-label">Trạng thái</label>
                                        <div class="col">
                                            <el-select v-model="form.status" class="w-full"
                                                       placeholder="Select"
                                                       size="large">
                                                <el-option
                                                    v-for="(item, idx) in statusArr"
                                                    :key="idx"
                                                    :label="item"
                                                    :value="idx"
                                                />
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="col-12">
            <div class="card">
                <div class="card-footer text-end">
                    <div class="d-flex">
                        <Link :href="route(routeIndex) + '?page=' + page">
                            <el-button :icon="Back" plain size="large" type="warning">Quay lại</el-button>
                        </Link>
                        <el-button :disabled="form.processing" class="ms-auto" native-type="submit"
                                   size="large"
                                   type="success"
                                   @click="handleChangeStatesAction('save')"
                        >
                            <i aria-hidden="true" class="fa fa-floppy-o"></i>&nbsp;
                            Lưu
                        </el-button>
                        <el-button :processing="form.processing" class="mx-2" native-type="submit" plain size="large"
                                   type="primary"
                                   @click="handleChangeStatesAction('create')"
                        >
                            <i aria-hidden="true" class="fa fa-floppy-o"></i> &nbsp;
                            Lưu & thoát
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
