<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head} from '@inertiajs/vue3'

import DataTable from "@/Pages/Articles/Partials/DataTable.vue";
import IconFilter from "@/Components/Icons/IconFilter.vue";
import Toolbar from "@/Components/Toolbar.vue";
import UserDialog from "@/Pages/Users/<USER>/UserDialog.vue";
import {reactive, ref} from "vue";
import {showMessage} from "@/Helpers/messageHelper.js";

const props = defineProps({
    cateName: String,
    // statusArr: Array,
    items: Array,
    // roles: Array,
    // querySearch: Object,
    // roleId: Number
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},*/
    // {text: 'Users'},
    {text: props.cateName + ' List'}
];

const actions = [
    /*{name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    {name: 'Create', className: 'btn-sm btn-primary'}*/
];

// Begin: Add User
// =====================================================================================================================
let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})
const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}
const addUser = () => {
    console.log('addUser')
    titleModal.value = 'Add User'
    dialogFormVisible.value = true
    incrementDialogKey()

    form.name = ''
    form.region = ''
    form.date1 = ''
    form.date2 = ''
    form.delivery = false
    form.type = []
    form.resource = ''
    form.desc = ''
}
// =====================================================================================================================
// End: Add User

</script>

<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!--begin::Post-->
        <DataTable :data="items" @cb:onAddUser="addUser" :cateName="cateName"/>
        <!--end::Post-->

        <!--begin::Modal - Add User-->
        <UserDialog
            :key="dialogKey"
            :dialogFormVisible="dialogFormVisible"
            :form="form"
            :titleModal="titleModal"
            @onSubmit="handleSubmit"
            @update:dialogFormVisible="dialogFormVisible = $event"
        />
        <!--end::Modal - Add User-->

    </MainLayout>
</template>
