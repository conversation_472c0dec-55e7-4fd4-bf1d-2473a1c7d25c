<script setup>
import ThirdButton from "@/Components/ThirdButton.vue";
import IconExport from "@/Components/Icons/IconExport.vue";
import IconFilter from "@/Components/Icons/IconFilter.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import DropdownButton from "@/Components/DropdownButton.vue";
import {ElMessage} from 'element-plus'
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {reactive, ref} from "vue";
import DeleteDialog from "@/Components/DeleteDialog.vue";

const props = defineProps({
    type: {
        type: String,
        default: 'users'
    },
    roleId: {
        type: Number,
        default: 0
    },
    loading: Boolean,
    data: Array,
    meta: Object,
    statusArr: Array
})

const emit = defineEmits(['cb:onAddUser']);

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemDelete = reactive({})
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemDelete: ', itemDelete)
    deleteDialogVisible.value = false
}
// =====================================================================================================================
// End: delete

const ActionCbHandler = (item) => {
    console.log('UserTable >> ActionCbHandler >> item: ', item)
    const {action, row} = item
    // Handle the command and row here
    switch (action) {
        case 'edit':
            console.log('UserTable >> ActionCbHandler >> edit >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            window.location.href = route('cms.users.show', {id: row.id});
            break
        case 'delete':
            console.log('UserTable >> ActionCbHandler >> delete >> row: ', row)
            // ElMessage(`click on item ${action} ${row?.name}`)
            deleteDialogKey.value += 1
            deleteDialogVisible.value = true
            itemDelete = row
            break
        default:
            console.log('UserTable >> ActionCbHandler >> default >> row: ', row)
            ElMessage(`click on item ${action} ${row?.name}`)
            break
    }
}

// Begin: Search
// =====================================================================================================================
const searchQuery = ref('');
const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Thực hiện hành động tìm kiếm ở đây
};
// =====================================================================================================================
// End: Search

// Begin: Add User
// =====================================================================================================================
/*let titleModal = ref('Add User')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})*/
/*const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}*/
const addUser = () => {
    console.log('addUser')
    /*
    titleModal.value = 'Add User'
    dialogFormVisible.value = true
    incrementDialogKey()

    form.name = ''
    form.region = ''
    form.date1 = ''
    form.date2 = ''
    form.delivery = false
    form.type = []
    form.resource = ''
    form.desc = ''*/
    emit('cb:onAddUser', true);
}
// =====================================================================================================================
// End: Add User

</script>

<style scoped>

</style>

<template>
    <div id="kt_post" class="post d-flex flex-column-fluid">
        <!--begin::Container-->
        <div id="kt_content_container" class="container">
            <!--begin::Card-->
            <div class="card">
                <!--begin::Card header-->
                <div class="card-header border-0 pt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <IconSearch/>
                            </span>
                            <input v-model="searchQuery"
                                   class="form-control form-control-solid w-250px ps-14"
                                   data-kt-user-table-filter="search" placeholder="Search ..."
                                   type="text"
                                   @keyup.enter="search"
                            />
                        </div>
                        <!--end::Search-->
                    </div>
                    <!--begin::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Toolbar-->
                        <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                            <!--begin::Filter-->
                            <ThirdButton class-name="btn-light-primary me-3" name="Filter">
                                <IconFilter/>
                            </ThirdButton>
                            <!--end::Filter-->
                            <!--begin::Export-->
                            <ThirdButton class-name="btn-light-primary me-3" name="Export">
                                <IconExport/>
                            </ThirdButton>
                            <!--end::Export-->
                            <!--begin::Add user-->
                            <ThirdButton class-name="btn-primary"
                                         name="Thêm mới"
                                         @click.prevent="addUser"
                            >
                                <IconAdd/>
                            </ThirdButton>
                            <!--end::Add user-->
                        </div>
                        <!--end::Toolbar-->
                        <!--begin::Group actions-->
                        <div class="d-flex justify-content-end align-items-center d-none"
                             data-kt-user-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-user-table-select="selected_count"></span>Selected
                            </div>
                            <button class="btn btn-danger" data-kt-user-table-select="delete_selected"
                                    type="button">Delete Selected
                            </button>
                        </div>
                        <!--end::Group actions-->
                    </div>
                    <!--end::Card toolbar-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <table id="kt_table_users" class="table align-middle table-row-dashed fs-6 gy-5">
                        <!--begin::Table head-->
                        <thead>
                        <!--begin::Table row-->
                        <tr class="text-start text-muted fw-bolder fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">
                                <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                    <input class="form-check-input" data-kt-check="true"
                                           data-kt-check-target="#kt_table_users .form-check-input"
                                           type="checkbox" value="1"/>
                                </div>
                            </th>
                            <th class="min-w-125px">Ảnh</th>
                            <th class="min-w-125px">Tiêu đề</th>
                            <th class="min-w-125px">Đề mục</th>
                            <th class="min-w-125px">Ngày tạo</th>
                            <th class="text-end min-w-100px">Actions</th>
                        </tr>
                        <!--end::Table row-->
                        </thead>
                        <!--end::Table head-->
                        <!--begin::Table body-->
                        <tbody class="text-gray-600 fw-bold">
                        <!--begin::Table row-->
                        <tr v-for="row in data" :key="row.id">
                            <!--begin::Checkbox-->
                            <td>
                                <div class="form-check form-check-sm form-check-custom form-check-solid">
                                    <input class="form-check-input" type="checkbox" value="1"/>
                                </div>
                            </td>
                            <!--end::Checkbox-->
                            <!--begin::User=-->
                            <td class="d-flex align-items-center">
                                <!--begin:: Avatar -->
                                <div class="symbol symbol-circle symbol-50px overflow-hidden">
                                    <a :href="route('cms.users.show', row?.id ?? 0)">
                                        <div v-if="row.avatar" class="symbol-label">
                                            <img :alt="row?.name ?? ''" :src="row?.avatar"
                                                 class="w-100"/>
                                        </div>
                                        <div v-else class="symbol-label fs-3 bg-light-danger text-danger">M</div>
                                    </a>
                                </div>
                                <!--end::Avatar-->
                            </td>
                            <!--end::User=-->
                            <!--begin::Role=-->
                            <td>{{ row?.title ?? '' }}</td>
                            <!--end::Role=-->
                            <!--begin::Last login=-->
                            <td>
                                <div class="badge badge-light fw-bolder">{{ row?.category ?? '' }}</div>
                            </td>
                            <!--end::Last login=-->
                            <!--begin::Joined-->
                            <td>{{ row?.created_at ?? '' }}</td>
                            <!--begin::Joined-->
                            <!--begin::Action=-->
                            <td class="text-end">
                                <DropdownButton :row="row" @cb:action="ActionCbHandler">
                                    <template #default>
                                        <el-dropdown-menu>
                                            <el-dropdown-item command="edit">Edit</el-dropdown-item>
                                            <el-dropdown-item command="delete">Delete</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </DropdownButton>
                            </td>
                            <!--end::Action=-->
                        </tr>
                        <!--end::Table row-->
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Container-->

        <!--begin::Modal - Delete Role-->
        <DeleteDialog
            :key="deleteDialogKey"
            :itemName="itemDelete?.name ?? ''"
            :visible="deleteDialogVisible"
            @delete="handleDeleteAction"
            @update:visible="deleteDialogVisible = $event"
        />
        <!--end::Modal - Delete Role-->

    </div>
</template>
