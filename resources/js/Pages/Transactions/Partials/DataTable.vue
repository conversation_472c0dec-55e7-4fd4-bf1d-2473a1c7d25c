<script setup>
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {ElMessage, ElTable} from 'element-plus'
import {computed, reactive, ref, watch} from "vue";
import {Link, usePage} from "@inertiajs/vue3";
import {QuestionFilled, Search} from '@element-plus/icons-vue'

const props = defineProps({
    cateName: String,
    data: Array,
    total: Number,
    currentPage: Number,
    perPage: Number,
    loading: {
        type: Boolean,
        default: true
    },
    keyword: String,
    defaultSort: Object
})

const localCurrentPage = ref(props.currentPage)
const localPerPage = ref(props.perPage)

const emit = defineEmits(['cb:onChangePage', 'cb:onChangePerPage', 'cb:onSearch', 'cb:onSort']);

// const roleClassMapping = {
//     'Administrator': 'badge-light-primary',
//     'Developer': 'badge-light-danger',
//     'Analyst': 'badge-light-success',
//     'Support': 'badge-light-info',
//     'Trial': 'badge-light-warning'
// };

const errors = computed(() => usePage().props.errors)

// check error and display message
watch(
    () => errors.value,
    () => {
        Object.entries(errors.value).forEach(([key, value]) => {
            // console.log(`${key}: ${value}`);
            ElMessage({
                message: `${key}: ${value}`,
                type: 'error'
            })
        });
    }
)

const dialogFormVisible = ref(false)
let titleModal = ref('Thêm mới ' + props.cateName)
let dialogKey = ref(1)
let formAction = ref('add')
const incrementDialogKey = () => {
    dialogKey.value += 1
}


// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemsDelete = reactive({})
let singleItemName = ref('')
const selectedRows = ref([]);

const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
};
// =====================================================================================================================
// End: delete

// Begin: Search
// =====================================================================================================================
const searchQuery = ref(props.keyword);
// const filteredData = ref([...props.data]);

const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Thực hiện hành động tìm kiếm ở đây
    emit('cb:onSearch', searchQuery.value);
};

// =====================================================================================================================
// End: Search

const handleSizeChange = (val) => {
    console.log(`${val} items per page`)
    emit('cb:onChangePerPage', val);
}
const handleCurrentChange = (val) => {
    console.log(`current page: ${val}`)
    emit('cb:onChangePage', val);
}

const handleSortChange = (value) => {
    console.log('handleSortChange >> value: ', value, ' -> value: ', value.prop, ' -> value: ', value.order)
    const sortKey = value.prop
    let sortValue = value.order

    // descending
    if (value.order === 'ascending') {
        sortValue = 'asc'
    } else {
        sortValue = 'desc'
    }

    // emit('handleSortChange', sortKey + ':' + sortValue)
    emit('cb:onSort', {'sort': sortKey + ':' + sortValue})
}
</script>

<style>
.el-table .cell, .el-scrollbar {
    padding: 0 !important;
}

.el-table__header {
    padding: 0 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    background-color: #b5b5c3 !important;
}

.el-dialog__headerbtn {
    top: 13px !important;
}
</style>

<template>

    <!--begin::Card-->
    <div class="card card-flush mb-15">
        <!--begin::Card header-->
        <div class="card-header mt-6">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 me-5">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <IconSearch/>
                            </span>
                    <!--
                    <input v-model="searchQuery"
                           class="form-control form-control-solid w-250px ps-15"
                           data-kt-permissions-table-filter="search"
                           :placeholder="'Tìm kiếm ' + props.cateName"
                           type="text"
                           @keyup.enter="search"
                    />
                    -->
                    <el-input
                        style="width: 300px"
                        v-model="searchQuery"
                        :placeholder="'Tìm kiếm ' + props.cateName"
                        clearable
                        size="large"
                        @keyup.enter="search"
                        :prefix-icon="Search"
                        @clear="search"
                    />
                </div>
                <!--end::Search-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <!--begin::Filter-->
                <!--
                <ThirdButton class-name="btn-light-primary me-3" name="Filter">
                    <IconFilter/>
                </ThirdButton>
                -->
                <!--end::Filter-->

                <!--begin::Button-->
                <!--
                <ThirdButton class="ml-5"
                             class-name="btn-primary ml-5"
                             :name="'Thêm mới'"
                             @click="addItem"
                             v-if="selectedRows.length <= 0"
                >
                    <span class="svg-icon svg-icon-3">
                        <IconAdd2/>
                    </span>
                </ThirdButton>
                -->
                <!--end::Button-->

                <!--begin::Button-->
                <!--
                <div class="fw-bolder me-5" v-if="selectedRows.length > 0">
                    <span class="me-2">{{ selectedRows.length }}</span>Selected
                </div>
                <button v-if="selectedRows.length > 0"
                        type="button"
                        class="btn btn-danger"
                        @click.prevent="deleteItem(selectedRows, 2)"
                >Delete Selected</button>
                -->
                <!--end::Button-->

            </div>
            <!--end::Card toolbar-->

        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">
            <!--begin::Table-->
            <!--                    <pre>
                                    {{ data }}
                                </pre>-->
            <!--                    {{ defaultSort }}-->
            <el-table :data="data" class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
                      @selection-change="handleSelectionChange"
                      v-loading="loading"
                      @sort-change="handleSortChange"
                      row-key="id"
                      style="width: 100%; margin-left: 0; padding-left: 0;"
            >
                <!--
                <el-table-column type="selection" width="55" />
                -->

                <el-table-column label="Id" width="50" sortable prop="id">

                    <template #default="scope">{{ scope.row.id }}</template>
                </el-table-column>

                <el-table-column label="Thời gian" width="180" sortable prop="transDate">
                    <template #default="scope">{{ scope.row.transDate }}</template>
                </el-table-column>

                <el-table-column label="Tên người nhận" min-width="180">
                    <template #default="scope">
                        <Link
                            :href="route('cms.users.showByUserId', scope.row?.debitUid ?? 0)"
                            class="text-reset22"
                        >
                            {{ scope.row.debitName }}
                        </Link>
                    </template>
                </el-table-column>

                <el-table-column label="Hành động" width="120">
                    <template #header>
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            content="CASHIN: Cập nhật tiền, TRANSFER: Chuyển tiền, PAYMENT: Thanh toán, EXCHANGE: Đổi tiền"
                            placement="top"
                        >
                            <span>Hành động &nbsp;</span>
                        </el-tooltip>
                        <el-icon>
                            <QuestionFilled/>
                        </el-icon>
                    </template>
                    <template #default="scope">
                        <el-tag
                            key="primary"
                            effect="dark"
                        >

                            {{ scope.row?.systemNote }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="Số tiền" min-width="100" sortable prop="amount">
                    <template #default="scope">
                        {{ scope.row?.amount.toLocaleString('de-DE') }}
                    </template>
                </el-table-column>

                <el-table-column label="Mô tả" min-width="200">
                    <template #default="scope">{{ scope.row.description }}</template>
                </el-table-column>

                <!--
                <el-table-column label="Trạng thái" min-width="110">
                    <template #default="scope">
                        <el-switch
                            v-model="scope.row.isStatus"
                            active-text="Hoạt động"
                            class="ml-2"
                            inactive-text="Khóa"
                            inline-prompt
                            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                            @change="changeStatus(scope.row, $event)"
                        />
                    </template>
                </el-table-column>
                -->
            </el-table>
            <!--end::Table-->
        </div>
        <!--end::Card body-->

        <!--begin::Pagination-->
        <div class="container overflow-hidden">
            <div class="row">
                <div class="d-flex flex-center mb-5">
                    <el-pagination
                        :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                        :current-page="localCurrentPage"
                        :page-size="localPerPage"
                        :total="total"
                        background
                        layout="total, sizes, prev, pager, next"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
        <!--        <div v-if="data.length > 0"
                     class="d-flex justify-content-between align-items-center flex-wrap mb-5">
                    <div class="d-flex flex-wrap py-2 mr-3">
                        <el-pagination
                            :page-sizes="[10, 20, 50, 100, 200, 300]"
                            v-model:current-page="localCurrentPage"
                            v-model:page-size="localPerPage"
                            :total="total"
                            background layout="total, sizes, prev, pager, next"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>-->
        <!--end::Pagination-->

    </div>
    <!--end::Card-->

</template>
