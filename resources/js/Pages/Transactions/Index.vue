<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, useForm, usePage} from '@inertiajs/vue3'
import IconFilter from "@/Components/Icons/IconFilter.vue";
import Toolbar from "@/Components/Toolbar.vue";
import {computed, reactive, ref, watchEffect} from "vue";
import DataTable from "@/Pages/Transactions/Partials/DataTable.vue";

const props = defineProps({
    cateName: String,
    // statusArr: Array,
    items: Array,
    // roles: Array,
    // querySearch: Object,
    total: Number,
    currentPage: Number,
    perPage: Number,
    keyword: String,
    sortData: String
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},*/
    // {text: 'Users'},
    // {text: props.cateName + ' List'}
];

const actions = [
    {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    /*{name: 'Create', className: 'btn-sm btn-primary'}*/
];

const currentSort = computed(() => usePage().props.sortData ?? 'id:desc')

const defaultSort = reactive({
    prop: 'id',
    order: 'descending'
})

watchEffect(() => {
    if (currentSort.value) {
        defaultSort.prop = currentSort.value.split(':')[0]
        defaultSort.order = currentSort.value.split(':')[1] === 'desc' ? 'descending' : 'ascending'
    }
})

// const sortReq = ref(props.sortData ?? null)

// Begin: Add User
// =====================================================================================================================
let titleModal = ref('Add User')
let dialogKey = ref(1)
let loadingContainer = ref(false)
const dialogFormVisible = ref(false)
/*const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})*/
/*const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}*/
const incrementDialogKey = () => {
    dialogKey.value += 1
}
/*const addUser = () => {
    console.log('addUser')
    titleModal.value = 'Add User'
    dialogFormVisible.value = true
    incrementDialogKey()

    form.name = ''
    form.region = ''
    form.date1 = ''
    form.date2 = ''
    form.delivery = false
    form.type = []
    form.resource = ''
    form.desc = ''
}*/
// =====================================================================================================================
// End: Add User

let objectSearch = reactive({
    page: props.currentPage,
    per_page: props.perPage,
    s: props.keyword ?? null,
    sort: currentSort.value // 'id:desc',
})


let searchParams = computed(() => {
    return new URLSearchParams(Object.fromEntries(Object.entries(objectSearch).filter(([_, v]) => v != null))).toString();
})

const fetchData = () => {
    console.log('[Transactions >> Index] fetchData >> objectSearch: ', objectSearch)
    loadingContainer.value = true
    // const searchParams = new URLSearchParams(objectSearch).toString();

    let form = useForm({})
    form.get(route('cms.transactions.index') + '?' + searchParams.value, {
        onSuccess: () => {
            loadingContainer.value = false
        }
    })
}

const handleOnChangePage = (page) => {
    console.log('[Transactions >> Index] handleOnChangePage >> page: ', page)
    objectSearch.page = page
    fetchData()
}

const handleOnChangePerPage = (perPage) => {
    console.log('[Transactions >> Index] handleOnChangePerPage >> perPage: ', perPage)
    objectSearch.per_page = perPage
    fetchData()
}

const handleSearch = (keyword) => {
    console.log('[Transactions >> Index] handleSearch >> keyword: ', keyword)
    objectSearch.s = keyword
    fetchData()
}

const handleSortChange = (value) => {
    console.log('[Transactions >> Index] handleSortChange: >> value: ', value)
    // sortReq.value = value.sort
    objectSearch.sort = value.sort
    fetchData()
}

</script>

<template>
    <Head title="Logs Giao dịch"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->
        
        <div class="container-fluid mb-15">
            <DataTable
                :data="items" :cateName="cateName" :total="total" :current-page="currentPage" :per-page="perPage"
                :loading="loadingContainer"
                :keyword="keyword"
                :default-sort="defaultSort"
                @cb:onChangePage="handleOnChangePage"
                @cb:onChangePerPage="handleOnChangePerPage"
                @cb:onSearch="handleSearch"
                @cb:onSort="handleSortChange"
            />
        </div>

    </MainLayout>
</template>
