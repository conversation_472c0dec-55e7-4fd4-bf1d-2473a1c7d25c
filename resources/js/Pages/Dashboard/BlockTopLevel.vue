<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'

const topPlayers = ref([])
const loading = ref(true)

const fetchTopPlayers = async () => {
    try {
        loading.value = true
        const response = await axios.get('/api/dashboard/top-players-level')
        if (response.data.status === 'success') {
            topPlayers.value = response.data.data
        }
    } catch (error) {
        console.error('Error fetching top players by level:', error)
    } finally {
        loading.value = false
    }
}

const getAvatarUrl = (avatar) => {
    return avatar || 'templates/dashboard/assets/media/avatars/blank.png'
}

onMounted(() => {
    fetchTopPlayers()
})
</script>
<template>
    <!--begin::List Widget 2-->
    <div class="card card-xl-stretch mb-xl-8">
        <!--begin::Header-->
        <div class="card-header border-0">
            <h3 class="card-title fw-bolder text-dark">Top Level</h3>
            <div class="card-toolbar">
                <!--begin::Menu-->
                <button
                    class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                    data-kt-menu-flip="top-end"
                    data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                    type="button">
                    <!--begin::Svg Icon | path: icons/duotone/Layout/Layout-4-blocks-2.svg-->
                    <span class="svg-icon svg-icon-2">
                        <svg height="24px"
                             version="1.1"
                             viewBox="0 0 24 24" width="24px"
                             xmlns="http://www.w3.org/2000/svg">
                            <g fill="none" fill-rule="evenodd" stroke="none"
                               stroke-width="1">
                                <rect fill="#000000" height="5" rx="1" width="5"
                                      x="5"
                                      y="5"/>
                                <rect fill="#000000" height="5" opacity="0.3" rx="1"
                                      width="5"
                                      x="14" y="5"/>
                                <rect fill="#000000" height="5" opacity="0.3" rx="1"
                                      width="5"
                                      x="5" y="14"/>
                                <rect fill="#000000" height="5" opacity="0.3" rx="1"
                                      width="5"
                                      x="14" y="14"/>
                            </g>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </button>
                <!--begin::Menu 2-->
                <div
                    class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px"
                    data-kt-menu="true">
                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                        <div class="menu-content fs-6 text-dark fw-bolder px-3 py-4">
                            Quick Actions
                        </div>
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu separator-->
                    <div class="separator mb-3 opacity-75"></div>
                    <!--end::Menu separator-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                        <a class="menu-link px-3" href="#" @click="fetchTopPlayers">Refresh Data</a>
                    </div>
                    <!--end::Menu item-->
                </div>
                <!--end::Menu 2-->
                <!--end::Menu-->
            </div>
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body pt-2">
            <div v-if="loading" class="text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div v-else>
                <!--begin::Item-->
                <div v-for="(player, index) in topPlayers" :key="player.id"
                     class="d-flex align-items-center"
                     :class="{ 'mb-7': index < topPlayers.length - 1 }">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-50px me-5">
                        <img :alt="player.display_name || player.nick_name" class=""
                             :src="getAvatarUrl(player.avatar)"/>
                    </div>
                    <!--end::Avatar-->
                    <!--begin::Text-->
                    <div class="flex-grow-1">
                        <a class="text-dark fw-bolder text-hover-primary fs-6" href="#">
                            {{ player.display_name || player.nick_name }}
                        </a>
                        <span class="text-muted d-block fw-bold">
                            Level {{ player.level }}
                        </span>
                    </div>
                    <!--end::Text-->
                </div>
                <!--end::Item-->
            </div>
        </div>
        <!--end::Body-->
    </div>
    <!--end::List Widget 2-->
</template>
