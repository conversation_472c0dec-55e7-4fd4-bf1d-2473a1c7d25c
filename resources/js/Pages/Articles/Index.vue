<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, router, useForm} from '@inertiajs/vue3'
import Toolbar from "@/Components/Toolbar.vue";
import DataTable from "@/Pages/Articles/Partials/DataTable.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import {computed, onMounted, reactive, ref} from 'vue';
import IconRefresh from "@/Components/Icons/IconRefresh.vue";
import DeleteDialog from "@/Components/DeleteDialog.vue";
import IconFilter from "@/Components/Icons/IconFilter.vue";
import IconSearch from "@/Components/Icons/IconSearch.vue";

const props = defineProps({
    cateName: String,
    articles: Object,
    articleType: Object,
    sort: String,
    keyword: {
        type: String,
        default: ''
    },
    currentPage: Number,
    perPage: {
        type: Number,
        default: 10
    },
    type: {
        type: String,
        default: ''
    },
    is_published: {
        type: [Number, String, null],
        default: null
    },
    promoted: {
        type: [Number, String, null],
        default: null
    },
    paginate: Object,
});

const breadcrumbs = [
    {text: 'Danh sách'}
];

let deleteDialogKey = ref(1)
let deleteDialogVisible = ref(false)
let itemsDelete = reactive({})
let singleItemName = ref('')
let dialogTitle = ref('Xóa bài viết')

const selectedCount = ref(0);
const tableRef = ref(null);
const searchVisible = ref(false);
const searchForm = useForm({
    keyword: props.keyword || '',
});

const handleBulkDelete = () => {
    if (selectedCount.value === 0) {
        showMessage('Vui lòng chọn ít nhất một bài viết để xóa', 'warning');
        return;
    }

    const selectedIds = tableRef.value?.selectedItems || [];

    dialogTitle.value = `Bạn có chắc chắn muốn xóa ${selectedCount.value} bài viết đã chọn?`;
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemsDelete = selectedIds
    singleItemName.value = '';
}

const bulkDeleteButton = computed(() => ({
    name: `Xóa ${selectedCount.value} bài viết đã chọn`,
    className: 'btn-danger',
    click: handleBulkDelete
}));

let objectSearch = reactive({
    page: props.currentPage,
    sort: props.sort,
    keyword: props.keyword,
    per_page: props.perPage,
    type: '',
    is_published: '',
    promoted: '',
})

const handleReload = () => {
    objectSearch.sort = props.sort
    const searchParams = new URLSearchParams(Object.fromEntries(Object.entries(objectSearch).filter(([_, v]) => v != null))).toString();

    const form = useForm({})
    form.get(route('cms.articles.index') + '?' + searchParams, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            showMessage('Làm mới danh sách thành công !', 'success');
        }
    })
}

const toggleSearch = () => {
    searchVisible.value = !searchVisible.value;
}

const handleSearch = () => {
    objectSearch.keyword = searchForm.keyword;
    objectSearch.page = 1; // Reset to first page on new search

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();

    router.get(route('cms.articles.index') + '?' + searchParams, {}, {
        preserveState: true,
        preserveScroll: false,
        onSuccess: () => {
            showMessage('Tìm kiếm thành công!', 'success');
        }
    });
}

const clearSearch = () => {
    searchForm.keyword = '';
    objectSearch.keyword = '';
    objectSearch.type = '';
    objectSearch.is_published = '';
    objectSearch.promoted = '';
    handleSearch();
}

const actions = computed(() => {
    const actionButtons = [
        {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: handleReload},
        {name: 'Tìm kiếm', className: 'btn-light-primary fw-bolder', icon: IconFilter, click: toggleSearch},
        {
            type: 'back',
            name: 'Thêm mới',
            className: 'btn-primary',
            icon: IconAdd,
            href: route('cms.articles.create')
        }
    ];

    if (selectedCount.value > 0) {
        actionButtons.push(bulkDeleteButton.value);
    }

    return actionButtons;
});


const deleteArticle = (item) => {
    dialogTitle.value = 'Bạn có chắc chắn muốn xóa bài viết này?';
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemsDelete = item
    singleItemName.value = item.title ?? '';
};


const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemsDelete: ', itemsDelete)

    // Check if we're deleting a single item or multiple items
    if (Array.isArray(itemsDelete)) {
        // const ids = itemsDelete.map(item => item.id);
        const ids = itemsDelete;
        console.log('handleDeleteAction >> ids: ', ids)
        if (ids.length > 0) {
            // Call API to delete multiple users
            router.delete(route('cms.articles.bulk-destroy'), {
                data: {ids: ids},
                onSuccess: () => {
                    showMessage('Xóa bài viết thành công!', 'success');
                    // Reset selection
                    // selectedRows.value = [];
                },
                onError: (errors) => {
                    showMessage('Có lỗi xảy ra khi xóa bài viết: ' + Object.values(errors).join(', '), 'error');
                }
            });
        }
    } else {
        // Deleting a single user
        if (itemsDelete?.id) {
            router.delete(route('cms.articles.destroy', {id: itemsDelete.id}), {
                onSuccess: () => {
                    showMessage('Xóa bài viết thành công !', 'success');
                },
                onError: (errors) => {
                    showMessage('Có lỗi xảy ra khi xóa bài viết: ' + Object.values(errors).join(', '), 'error');
                }
            });
        }
    }

    // Close delete dialog
    deleteDialogVisible.value = false;

    // Reset data
    singleItemName.value = '';
    itemsDelete = {};
}

const perPageOptions = [
    {value: 5, label: '5'},
    {value: 10, label: '10'},
    {value: 20, label: '20'},
    {value: 50, label: '50'},
    {value: 100, label: '100'},
];

const handlePerPageChange = (value) => {
    objectSearch.per_page = value;
    objectSearch.page = 1; // Reset to page 1 when changing items per page

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();

    router.get(route('cms.articles.index') + '?' + searchParams, {}, {
        preserveState: true,
        preserveScroll: false,
        onSuccess: () => {
            showMessage('Đã thay đổi số lượng hiển thị!', 'success');
        }
    });
}

const handleCurrentChange = (page) => {
    objectSearch.page = page;

    const searchParams = new URLSearchParams(
        Object.fromEntries(
            Object.entries(objectSearch).filter(([_, v]) => v != null && v !== '')
        )
    ).toString();

    router.get(route('cms.articles.index') + '?' + searchParams, {}, {
        preserveState: true,
        preserveScroll: false,
        onSuccess: () => {
            showMessage(`Chuyển đến trang ${page}!`, 'success');
        }
    });
}

onMounted(() => {
    // Set search form keyword
    searchForm.keyword = props.keyword || '';
    objectSearch.keyword = props.keyword || '';

    // Set pagination
    objectSearch.per_page = props.perPage;

    // Set filters
    objectSearch.type = props.type || '';
    objectSearch.is_published = props.is_published !== null ? props.is_published : '';
    objectSearch.promoted = props.promoted !== null ? props.promoted : '';
});
</script>

<style scoped>
.btn-group-sm > .btn, .btn-sm {
    padding: unset !important;
}

.search-container {
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
    border-radius: 0.475rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
}

.search-container.hidden {
    max-height: 0;
    margin-bottom: 0;
    padding: 0;
    opacity: 0;
}

.search-container.visible {
    max-height: 280px;
    padding: 1rem;
    opacity: 1;
}

.search-form {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-buttons {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.advanced-filters {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 150px;
}

.filter-label {
    margin-bottom: 0.25rem;
    font-size: 1.05rem;
    color: #5E6278;
    font-weight: 500;
}

.filter-select {
    width: 100%;
}

.form-control {
    height: 42px;
}

.search-btn {
    min-width: 120px;
}

.clear-btn {
    background-color: #eee;
    border-color: #ddd;
    color: #555;
}

.clear-btn:hover {
    background-color: #e7e7e7;
    color: #333;
}

.close-btn {
    border-color: #e4e6ef;
    color: #7E8299;
    min-width: 80px;
}

.close-btn:hover {
    background-color: #F5F8FA;
    color: #5E6278;
}

.w-75px {
    width: 75px !important;
}

.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.per-page-selector {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.pagination-controls {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

@media (max-width: 767px) {
    .pagination-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .per-page-selector span {
        font-size: 0.9rem;
    }

    .pagination-controls {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .search-form {
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-container.visible {
        max-height: 520px;
    }

    .search-btn {
        width: auto;
    }

    .filter-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-item {
        min-width: 100%;
    }

    .search-buttons .justify-content-between {
        flex-direction: column;
        gap: 0.75rem;
    }

    .search-buttons .d-flex {
        width: 100%;
    }

    .search-buttons .btn {
        flex: 1;
    }

    .close-btn {
        width: 100%;
    }
}

:deep(.el-select) {
    --el-select-input-focus-border-color: #009ef7;
}

:deep(.el-input__wrapper) {
    background-color: #f5f8fa;
    box-shadow: none;
    border: 1px solid #e4e6ef;
}

:deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #009ef7;
}

:deep(.el-select-dropdown__item.selected) {
    color: #009ef7;
}
</style>

<template>
    <Head :title="cateName"/>

    <MainLayout>
        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid mb-15">
            <!-- Search Container -->
            <div :class="['search-container', searchVisible ? 'visible' : 'hidden']">
                <!-- Search Input -->
                <div class="search-form">
                    <div class="w-100">
                        <input
                            v-model="searchForm.keyword"
                            type="text"
                            class="form-control"
                            placeholder="Nhập từ khóa tìm kiếm..."
                            @keyup.enter="handleSearch"
                        />
                    </div>
                </div>

                <!-- Advanced filter options -->
                <div class="advanced-filters mt-3">
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">Loại</label>
                            <el-select
                                v-model="objectSearch.type"
                                clearable
                                size="large"
                                placeholder="Tất cả"
                                class="filter-select"
                                @change="handleSearch">
                                <el-option
                                    v-for="(label, key) in articleType"
                                    :key="key"
                                    :label="label"
                                    :value="key"
                                />
                            </el-select>
                        </div>

                        <div class="filter-item">
                            <label class="filter-label">Trạng thái</label>
                            <el-select
                                v-model="objectSearch.is_published"
                                clearable
                                placeholder="Tất cả"
                                size="large"
                                class="filter-select"
                                @change="handleSearch">
                                <el-option
                                    label="Hiển thị"
                                    :value="1"
                                />
                                <el-option
                                    label="Tạm ẩn"
                                    :value="0"
                                />
                            </el-select>
                        </div>

                        <div class="filter-item">
                            <label class="filter-label">Nổi bật</label>
                            <el-select
                                v-model="objectSearch.promoted"
                                clearable
                                placeholder="Tất cả"
                                size="large"
                                class="filter-select"
                                @change="handleSearch">
                                <el-option
                                    label="Nổi bật"
                                    :value="1"
                                />
                                <el-option
                                    label="Thường"
                                    :value="0"
                                />
                            </el-select>
                        </div>
                    </div>
                </div>

                <!-- Search and Clear Buttons -->
                <div class="search-buttons mt-3">
                    <div class="d-flex gap-2 justify-content-between">
                        <div class="d-flex gap-2">
                            <button @click="handleSearch" class="btn btn-primary search-btn">
                                <span class="me-2">Tìm kiếm</span>
                                <IconSearch/>
                            </button>
                            <button
                                v-if="searchForm.keyword || objectSearch.type || objectSearch.is_published !== '' || objectSearch.promoted !== ''"
                                @click="clearSearch"
                                class="btn clear-btn">
                                Xóa bộ lọc
                            </button>
                        </div>

                        <button @click="toggleSearch" class="btn btn-light-secondary close-btn">
                            <span>Đóng</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <DataTable
                        ref="tableRef"
                        :articles="articles"
                        :articleType="articleType"
                        @delete="deleteArticle"
                        @selected-count="(count) => selectedCount = count"
                    />

                    <div class="container overflow-hidden">
                        <div class="row">

                            <div class="d-flex flex-center mb-5">
                                <el-pagination
                                    :page-sizes="[2, 10, 20, 50, 100, 200, 300]"
                                    :current-page="paginate.data.current_page"
                                    :page-size="paginate.data.per_page"
                                    :total="paginate.data.total"
                                    background
                                    layout="total, sizes, prev, pager, next"
                                    @size-change="handlePerPageChange"
                                    @current-change="handleCurrentChange"
                                />
                            </div>
                        </div> <!-- end .row -->
                    </div>

                    <!--
                    <div class="pagination-wrapper mt-4">
                        <div class="per-page-selector">
                            <span class="me-2">Hiển thị</span>
                            <el-select
                                v-model="objectSearch.per_page"
                                class="me-2 w-75px"
                                @change="handlePerPageChange"
                            >
                                <el-option
                                    v-for="option in perPageOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                            <span>dòng mỗi trang</span>
                        </div>

                        <div v-if="articles.total > 0" class="pagination-controls">
                            <PaginationInfo
                                :from="articles.from"
                                :to="articles.to"
                                :total="articles.total"
                            />
                            <Pagination :links="articles.links"/>
                        </div>
                        <div v-else class="text-muted">
                            <span>Không có bản ghi nào</span>
                        </div>
                    </div>
                    -->

                    <!--begin::Modal - Delete -->
                    <DeleteDialog
                        :key="deleteDialogKey"
                        :itemName="singleItemName ?? ''"
                        :visible="deleteDialogVisible"
                        :dialog-content="dialogTitle"
                        btn-agree-name="Đồng ý, Xóa"
                        btn-cancel-name="Hủy thao tác"
                        dialog-header="Thông báo"
                        @delete="handleDeleteAction"
                        @update:visible="deleteDialogVisible = $event"
                    />
                    <!--end::Modal - Delete -->

                </div>
            </div>
        </div>
    </MainLayout>
</template>
