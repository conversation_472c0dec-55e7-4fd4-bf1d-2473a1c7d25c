<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import { Head } from '@inertiajs/vue3'
import { ref } from 'vue'
import Toolbar from "@/Components/Toolbar.vue";
import IconBack from "@/Components/Icons/IconBack.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";
import { showMessage } from "@/Helpers/messageHelper.js";
import DataForm from './Partials/DataForm.vue'

const props = defineProps({
    cateName: String,
    articleType: Object,
    page: Number,
})

const breadcrumbs = [
    {text: props.cateName}
];

const loading = ref(false)
const formRef = ref(null)

const actions = [
    {type: 'back', name: 'Quay lại', className: 'btn-light btn-light-primary', icon: IconBack, href: '/articles'},
    {name: 'Thêm mới', className: 'btn-primary', icon: IconAdd, click: () => {
        if (formRef.value) {
            formRef.value.handleSubmit()
        }
    }}
];
</script>

<template>
    <Head :title="cateName" />

    <MainLayout>
        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <DataForm
            ref="formRef"
            :article-type="articleType"
            :article="{
                type: 'NEWS',
                title: '',
                content: '',
                is_published: false,
                promoted: false
            }"
        />
    </MainLayout>
</template>
