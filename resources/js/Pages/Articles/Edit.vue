<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import { Head } from '@inertiajs/vue3'
import { ref } from 'vue'
import Toolbar from "@/Components/Toolbar.vue";
import IconBack from "@/Components/Icons/IconBack.vue";
import IconEdit from "@/Components/Icons/IconEdit4.vue";
import DataForm from './Partials/DataForm.vue'

const props = defineProps({
    cateName: String,
    article: Object,
    articleType: Object,
    page: Number,
})

const breadcrumbs = [
    // { text: 'Bài viết', href: route('cms.articles.index') },
    {text: props.article.title ?? ''}
];

const loading = ref(false)
const formRef = ref(null)

const actions = [
    {type: 'back', name: 'Quay lại', className: 'btn-light btn-light-primary', icon: IconBack, href: '/articles'},
    {name: '<PERSON><PERSON><PERSON> nhật', className: 'btn-primary', icon: IconEdit, click: () => {
        if (formRef.value) {
            formRef.value.handleSubmit()
        }
    }}
];
</script>

<template>
    <Head :title="cateName" />

    <MainLayout>
        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <DataForm
            ref="formRef"
            :article-type="articleType"
            :article="article"
            :is-edit="true"
        />
    </MainLayout>
</template>
