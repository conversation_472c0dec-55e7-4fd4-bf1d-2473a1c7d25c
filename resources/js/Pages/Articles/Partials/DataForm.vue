<script setup>
import { useForm } from '@inertiajs/vue3'
import { ref, onMounted, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import TextInputSecond from "@/Components/TextInputSecond.vue";
import { ElMessage, ElUpload } from "element-plus";
import { ElSwitch, ElSelect, ElOption } from 'element-plus'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import axios from 'axios'

const props = defineProps({
    articleType: {
        type: Object,
        required: true
    },
    article: {
        type: Object,
        default: () => ({
            type: 'NEWS',
            title: '',
            content: '',
            is_published: false,
            promoted: false,
            thumbnail: null
        })
    },
    isEdit: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['submit'])

const imageUrl = ref(props.article.thumbnail || '')
const formRef = ref(null)
const uploadRef = ref(null)
const uploadedImageId = ref(null)
const uploading = ref(false)

const form = useForm({
    type: props.article.type,
    title: props.article.title,
    content: props.article.content,
    is_published: Boolean(props.article.is_published),
    promoted: Boolean(props.article.promoted),
    thumbnail_url: props.article.thumbnail || '',
    _method: props.isEdit ? 'put' : 'post',
})

onMounted(() => {
    formRef.value = form
    // Set initial thumbnail URL if exists
    if (props.article.thumbnail) {
        imageUrl.value = getThumbnailUrl(props.article.thumbnail)
        form.thumbnail_url = props.article.thumbnail
    }
})

// Configure axios with auth token
const getAuthToken = () => {
    // Method 1: Try to get from meta tags (Laravel default)
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')

    // Method 2: Try to get from cookie (Sanctum)
    const getCookie = (name) => {
        const value = `; ${document.cookie}`
        const parts = value.split(`; ${name}=`)
        if (parts.length === 2) return parts.pop().split(';').shift()
    }

    // Method 3: Try various token sources
    return localStorage.getItem('auth_token') ||
           localStorage.getItem('token') ||
           sessionStorage.getItem('auth_token') ||
           sessionStorage.getItem('token') ||
           getCookie('laravel_token') ||
           getCookie('sanctum_token') ||
           csrfToken
}

const beforeUpload = (file) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp', 'image/bmp']
    if (!allowedTypes.includes(file.type)) {
        ElMessage.error('Hình ảnh phải là định dạng JPG, PNG, GIF, WEBP, BMP!')
        return false
    }

    // Validate file size (10MB as per API)
    if (file.size / 1024 / 1024 > 10) {
        ElMessage.error('Kích thước hình ảnh không được vượt quá 10MB!')
        return false
    }

    return true
}

const handleUploadRequest = async (options) => {
    const { file } = options

    if (!beforeUpload(file)) {
        return
    }

    uploading.value = true

    try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('description', `Article thumbnail - ${file.name}`)

        // Use session authentication instead of Bearer token
        const headers = {
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }

        // Add CSRF token for Laravel session auth
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken
        }

        console.log('Upload headers:', headers) // Debug log

        // Use withCredentials for session auth
        const response = await axios.post('/uploads/single', formData, {
            headers,
            withCredentials: true,
            onUploadProgress: (progressEvent) => {
                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                // Could emit progress event here if needed
            }
        })

        if (response.data.success && response.data.images && response.data.images.length > 0) {
            const uploadedImage = response.data.images[0]
            uploadedImageId.value = uploadedImage.id

            // Use absolute_path for display
            imageUrl.value = uploadedImage.absolute_path

            // Store URL in form for submission
            form.thumbnail_url = uploadedImage.absolute_path

            ElMessage.success('Upload hình ảnh thành công!')
        } else {
            throw new Error('Invalid response format')
        }
    } catch (error) {
        console.error('Upload error:', error)

        if (error.response) {
            const errorMessage = error.response.data?.message || 'Có lỗi xảy ra khi upload hình ảnh'
            ElMessage.error(`${errorMessage} (${error.response.status})`)

            // If unauthorized, show specific message
            if (error.response.status === 401) {
                ElMessage.error('Không có quyền truy cập. Vui lòng đăng nhập lại.')
            }
        } else {
            ElMessage.error('Không thể kết nối đến server. Vui lòng thử lại sau.')
        }
    } finally {
        uploading.value = false
    }
}

const removeImage = async () => {
    try {
        // If we have uploaded image ID, delete it from server
        if (uploadedImageId.value) {
            const headers = {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            }

            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            if (csrfToken) {
                headers['X-CSRF-TOKEN'] = csrfToken
            }

            await axios.delete(`/uploads/file/${uploadedImageId.value}`, {
                headers,
                withCredentials: true
            })
        }

        // Clear local state
        imageUrl.value = ''
        form.thumbnail_url = ''
        uploadedImageId.value = null

        ElMessage.success('Đã xóa hình ảnh!')
    } catch (error) {
        console.error('Delete error:', error)

        // Clear local state even if delete request fails
        imageUrl.value = ''
        form.thumbnail_url = ''
        uploadedImageId.value = null

        ElMessage.warning('Đã xóa hình ảnh khỏi form (có thể còn trên server)')
    }
}

const handleSubmit = () => {
    form.post(props.isEdit
        ? route('cms.articles.update', props.article.id)
        : route('cms.articles.store'), {
        onSuccess: () => {
            ElMessage.success(props.isEdit ? 'Cập nhật thành công!' : 'Thêm mới thành công!')
        },
        onError: (errors) => {
            ElMessage.error('Có lỗi xảy ra. Vui lòng thử lại sau.')
            console.error(errors)
        }
    })
}

const getThumbnailUrl = (thumbnail) => {
    if (!thumbnail) return null;

    // If the path is a URL, return it directly
    if (thumbnail.startsWith('http')) {
        return thumbnail;
    }

    // If MinIO URL is available in the environment, construct the full URL
    const minioUrl = import.meta.env.VITE_MINIO_URL || 'http://minio.pokee.local:8088';

    // Check if thumbnail is a relative path for MinIO
    if (thumbnail.startsWith('articles/')) {
        return `${minioUrl}/pokee-storage/${thumbnail}`;
    }

    // Default case
    return thumbnail;
};

defineExpose({
    form: formRef,
    handleSubmit
})
</script>

<template>
    <div class="post d-flex flex-column-fluid" id="kt_post">
        <div id="kt_content_container" class="container-fluid">
            <div class="d-flex flex-column flex-lg-row">
                <!--begin::Content-->
                <div class="flex-lg-row-fluid me-lg-15 order-2 order-lg-1 mb-10 mb-lg-0">
                    <form @submit.prevent="handleSubmit">
                        <div class="card card-flush pt-3 mb-5 mb-lg-10">
                            <div class="card-body pt-0">
                                <!--begin::Type-->
                                <div class="row g-9 mb-8">
                                    <div class="col-md-4 fv-row">
                                        <label class="required fw-bolder fs-6 mb-2">Loại bài viết</label>
                                        <el-select
                                            v-model="form.type"
                                            class="w-100"
                                            placeholder="Chọn loại bài viết"
                                            size="large"
                                        >
                                            <el-option
                                                v-for="(label, value) in articleType"
                                                :key="value"
                                                :label="label"
                                                :value="value"
                                            />
                                        </el-select>
                                    </div>
                                    <div class="col-md-4 fv-row">
                                        <label class="fs-6 fw-bolder mb-2">Trạng thái</label>
                                        <div class="d-flex align-items-center">
                                            <el-switch
                                                v-model="form.is_published"
                                                size="large"
                                                class="me-2"
                                                active-text="Hiển thị"
                                                inactive-text="Tạm ẩn"
                                                :active-value="true"
                                                :inactive-value="false"
                                                @change="(val) => form.is_published = Boolean(val)"
                                            />
                                            <span class="text-muted">{{ form.is_published ? 'Hiển thị' : 'Tạm ẩn' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 fv-row">
                                        <label class="fs-6 fw-bolder mb-2">Nổi bật</label>
                                        <div class="d-flex align-items-center">
                                            <el-switch
                                                v-model="form.promoted"
                                                size="large"
                                                class="me-2"
                                                active-text="Nổi bật"
                                                inactive-text="Thường"
                                                :active-value="true"
                                                :inactive-value="false"
                                                @change="(val) => form.promoted = Boolean(val)"
                                            />
                                            <span class="text-muted">{{ form.promoted ? 'Nổi bật' : 'Thường' }}</span>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Type-->

                                <!--begin::Title-->
                                <div class="d-flex flex-column mb-10 fv-row">
                                    <div class="fs-5 fw-bolder form-label mb-3">Tiêu đề</div>
                                    <TextInputSecond
                                        v-model="form.title"
                                        :size="'large'"
                                        id="title"
                                        autocomplete="username"
                                        autofocus
                                        class="form-control2 form-control-solid2 rounded-3"
                                        required
                                        type="text"
                                        placeholder="Nhập tiêu đề ..."
                                    />
                                </div>
                                <!--end::Title-->

                                <!--begin::Content-->
                                <div class="d-flex flex-column mb-10 fv-row">
                                    <div class="fs-5 fw-bolder form-label mb-3">Nội dung</div>
                                    <QuillEditor
                                        v-model:content="form.content"
                                        contentType="html"
                                        theme="snow"
                                        toolbar="full"
                                        :options="{
                                            modules: {
                                                toolbar: [
                                                    ['bold', 'italic', 'underline', 'strike'],
                                                    ['blockquote', 'code-block'],
                                                    [{ 'header': 1 }, { 'header': 2 }],
                                                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                                    [{ 'script': 'sub'}, { 'script': 'super' }],
                                                    [{ 'indent': '-1'}, { 'indent': '+1' }],
                                                    [{ 'direction': 'rtl' }],
                                                    [{ 'size': ['small', false, 'large', 'huge'] }],
                                                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                                                    [{ 'color': [] }, { 'background': [] }],
                                                    [{ 'font': [] }],
                                                    [{ 'align': [] }],
                                                    ['clean']
                                                ]
                                            }
                                        }"
                                    />
                                </div>
                                <!--end::Content-->
                            </div>
                        </div>
                    </form>
                </div>
                <!--end::Content-->

                <!--begin::Sidebar-->
                <div class="flex-column flex-lg-row-auto w-100 w-lg-250px w-xl-300px mb-10 order-1 order-lg-2">
                    <!--begin::Image-->
                    <div class="card card-flush pt-3 mb-0 mt-0">
                        <div class="card-body pt-0 fs-6">
                            <div class="mb-3 mt-2">
                                <h5>Hình ảnh</h5>
                                <div class="text-muted fs-7">Kích thước tối đa: 10MB. Định dạng: JPG, PNG, GIF, WEBP, BMP</div>
                            </div>

                            <div class="separator separator-dashed mb-7"></div>

                            <div class="mb-5">
                                <!-- Upload Area -->
                                <div v-if="!imageUrl" class="upload-area">
                                    <el-upload
                                        ref="uploadRef"
                                        class="upload-demo"
                                        :http-request="handleUploadRequest"
                                        :before-upload="beforeUpload"
                                        :show-file-list="false"
                                        :disabled="uploading"
                                        accept=".png,.jpg,.jpeg,.gif,.webp,.bmp"
                                        drag
                                    >
                                        <div class="upload-placeholder">
                                            <i v-if="!uploading" class="bi bi-cloud-upload fs-1 text-muted"></i>
                                            <i v-else class="bi bi-arrow-repeat fs-1 text-primary rotating"></i>
                                            <div class="text-muted mt-2">
                                                {{ uploading ? 'Đang upload...' : 'Kéo thả hoặc nhấp để chọn hình ảnh' }}
                                            </div>
                                        </div>
                                    </el-upload>
                                </div>

                                <!-- Preview Area -->
                                <div v-else class="image-preview">
                                    <div class="position-relative">
                                        <img
                                            :src="imageUrl"
                                            class="uploaded-image"
                                            alt="Uploaded thumbnail"
                                        />
                                        <button
                                            type="button"
                                            class="btn btn-icon btn-sm btn-danger position-absolute top-0 end-0 m-1"
                                            @click="removeImage"
                                            :disabled="uploading"
                                        >
                                            <i class="bi bi-x fs-4"></i>
                                        </button>
                                    </div>

                                    <!-- Replace Button -->
                                    <el-upload
                                        ref="uploadRef"
                                        class="mt-3"
                                        :http-request="handleUploadRequest"
                                        :before-upload="beforeUpload"
                                        :show-file-list="false"
                                        :disabled="uploading"
                                        accept=".png,.jpg,.jpeg,.gif,.webp,.bmp"
                                    >
                                        <button
                                            type="button"
                                            class="btn btn-light btn-sm w-100"
                                            :disabled="uploading"
                                        >
                                            <i class="bi bi-arrow-clockwise me-1"></i>
                                            {{ uploading ? 'Đang upload...' : 'Thay đổi hình ảnh' }}
                                        </button>
                                    </el-upload>
                                </div>
                            </div>

                            <!-- Current Image (for edit mode) -->
                            <div v-if="props.article?.thumbnail && !imageUrl" class="mb-2">
                                <div class="fs-6 fw-bold mb-2">Hình ảnh hiện tại</div>
                                <img
                                    :src="getThumbnailUrl(props.article.thumbnail)"
                                    class="current-image border rounded"
                                    alt="Current thumbnail"
                                />
                            </div>
                        </div>
                    </div>
                    <!--end::Image-->
                </div>
                <!--end::Sidebar-->
            </div>
        </div>
    </div>
</template>

<style scoped>
:deep(.el-select) {
    width: 100%;
}

:deep(.el-select .el-input__wrapper) {
    background-color: #f5f8fa;
    box-shadow: none;
    border: 1px solid #e4e6ef;
}

:deep(.el-select .el-input__wrapper:hover) {
    border-color: #009ef7;
}

:deep(.el-select .el-input__wrapper.is-focus) {
    border-color: #009ef7;
    box-shadow: 0 0 0 2px rgba(0, 158, 247, 0.2);
}

:deep(.ql-container) {
    min-height: 200px;
    background-color: #f5f8fa;
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
}

:deep(.ql-toolbar) {
    background-color: #f5f8fa;
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem 0.475rem 0 0;
}

:deep(.ql-editor) {
    min-height: 200px;
    font-size: 1rem;
    line-height: 1.5;
    color: #3f4254;
}

:deep(.ql-editor.ql-empty:before) {
    color: #a1a5b7;
    font-style: normal;
}

:deep(.el-switch) {
    --el-switch-on-color: #50cd89;
    --el-switch-off-color: #d8d6d1;
}

:deep(.el-switch.is-checked) {
    --el-switch-on-color: #50cd89;
}

:deep(.el-switch.is-checked .el-switch__core) {
    border-color: var(--el-switch-on-color);
    background-color: var(--el-switch-on-color);
}

/* Upload Styles */
.upload-area {
    border: 2px dashed #e4e6ef;
    border-radius: 0.475rem;
    background-color: #f5f8fa;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #009ef7;
    background-color: #f8faff;
}

.upload-placeholder {
    text-align: center;
    padding: 2rem 1rem;
}

:deep(.el-upload-dragger) {
    background-color: transparent !important;
    border: none !important;
    border-radius: 0.475rem;
    width: 100%;
}

:deep(.el-upload-dragger:hover) {
    border-color: transparent !important;
}

.image-preview {
    text-align: center;
}

.uploaded-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 0.475rem;
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
}

.current-image {
    max-width: 100%;
    max-height: 200px;
}

.rotating {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

:deep(.el-upload) {
    width: 100%;
}

:deep(.el-upload .el-upload-dragger) {
    width: 100%;
    height: auto;
    min-height: 120px;
}
</style>
