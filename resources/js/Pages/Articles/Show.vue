<template>
    <Head :title="cateName" />

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <div class="container-fluid">

            <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h2 class="fs-2x fw-bold mb-4">{{ article.title }}</h2>
                                    <div class="mb-4">
                                        <span class="badge badge-light-info me-2">{{ article.type }}</span>
                                        <span :class="['badge me-2', article.is_published ? 'badge-light-success' : 'badge-light-warning']">
                                            {{ article.is_published ? 'Hiển thị' : 'Tạm ẩn' }}
                                        </span>
                                        <span :class="['badge', article.promoted ? 'badge-light-danger' : 'badge-light-secondary']">
                                            {{ article.promoted ? 'Nổi bật' : 'Thường' }}
                                        </span>
                                    </div>
                                    <div class="content" v-html="article.content"></div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card card-flush">
                                        <div class="card-header">
                                            <h3 class="card-title align-items-start flex-column">
                                                <span class="card-label fw-bold text-dark">Thông tin bài viết</span>
                                            </h3>
                                        </div>
                                        <div class="card-body pt-0">
                                            <div class="d-flex flex-column gap-7 gap-lg-10">
                                                <div class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Ngày xuất bản</div>
                                                    <div class="text-gray-600">{{ article.published_at || 'Chưa xuất bản' }}</div>
                                                </div>

                                                <div class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Ngày tạo</div>
                                                    <div class="text-gray-600">{{ article.created_at }}</div>
                                                </div>

                                                <div class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Ngày cập nhật</div>
                                                    <div class="text-gray-600">{{ article.updated_at }}</div>
                                                </div>

                                                <div v-if="article.thumbnail" class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Hình ảnh</div>
                                                    <img :src="article.thumbnail"
                                                         :alt="article.title"
                                                         class="rounded-3 w-100">
                                                </div>

                                                <div v-if="article.meta_title" class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Meta Title</div>
                                                    <div class="text-gray-600">{{ article.meta_title }}</div>
                                                </div>

                                                <div v-if="article.meta_description" class="d-flex flex-column gap-2">
                                                    <div class="fw-bold">Meta Description</div>
                                                    <div class="text-gray-600">{{ article.meta_description }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

        </div>

    </MainLayout>
</template>

<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, Link} from '@inertiajs/vue3';
import { ref } from 'vue';
import Toolbar from "@/Components/Toolbar.vue";
import IconBack from "@/Components/Icons/IconBack.vue";

const props = defineProps({
    cateName: String,
    article: Object
});

const loading = ref(false);
const action = ref(null);

/*const actions = [
    {
        label: 'Quay lại',
        icon: ArrowLeft,
        type: 'secondary',
        onClick: () => window.history.back()
    }
];*/
const actions = [
    {
        type: 'back', name: 'Quay lại', className: 'btn-light btn-light-primary', icon: IconBack, href: '/articles'
    }
];
const breadcrumbs = [
    // { text: props.cateName }
];

</script>

<style scoped>
.content {
    line-height: 1.6;
    color: #3f4254;
}

.content :deep(h1) {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.content :deep(h2) {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.content :deep(p) {
    margin-bottom: 1rem;
}

.content :deep(img) {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
}

.content :deep(ul), .content :deep(ol) {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.content :deep(blockquote) {
    border-left: 4px solid #e4e6ef;
    padding-left: 1rem;
    margin: 1rem 0;
    color: #7e8299;
}
</style>
