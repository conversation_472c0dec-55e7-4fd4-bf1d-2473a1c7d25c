<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal" style="margin-top: 5%; !important;" width="40%">
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>
        <el-form :model="form" class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7">

            <!--begin::Notice-->
            <!--begin::Notice-->
            <div
                class="notice d-flex bg-light-primary rounded border-primary border border-dashed mb-9 p-6">
                <!--begin::Icon-->
                <!--begin::Svg Icon | path: icons/duotone/Code/Warning-1-circle.svg-->
                <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
															<svg height="24px" version="1.1"
                                                                 viewBox="0 0 24 24" width="24px"
                                                                 xmlns="http://www.w3.org/2000/svg">
																<circle cx="12" cy="12" fill="#000000" opacity="0.3"
                                                                        r="10"/>
																<rect fill="#000000" height="8" rx="1" width="2" x="11"
                                                                      y="7"/>
																<rect fill="#000000" height="2" rx="1" width="2" x="11"
                                                                      y="16"/>
															</svg>
														</span>
                <!--end::Svg Icon-->
                <!--end::Icon-->
                <!--begin::Wrapper-->
                <div class="d-flex flex-stack flex-grow-1">
                    <!--begin::Content-->
                    <div class="fw-bold">
                        <div class="fs-6 text-gray-700">Please note that reducing a user role
                            rank, that user will lose all priviledges that was assigned to the
                            previous role.
                        </div>
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Notice-->
            <!--end::Notice-->
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="fs-6 fw-bold form-label mb-5">
                    <span class="required">Select a user role</span>
                </label>
                <!--end::Label-->
                <!--begin::Input row-->
                <div class="d-flex">
                    <!--begin::Radio-->
                    <div class="form-check form-check-custom form-check-solid">
                        <!--begin::Input-->
                        <input id="kt_modal_update_role_option_0" checked='checked'
                               class="form-check-input me-3"
                               name="user_role" type="radio" value="0"/>
                        <!--end::Input-->
                        <!--begin::Label-->
                        <label class="form-check-label" for="kt_modal_update_role_option_0">
                            <div class="fw-bolder text-gray-800">Administrator</div>
                            <div class="text-gray-600">Best for business owners and company
                                administrators
                            </div>
                        </label>
                        <!--end::Label-->
                    </div>
                    <!--end::Radio-->
                </div>
                <!--end::Input row-->
                <div class='separator separator-dashed my-5'></div>
                <!--begin::Input row-->
                <div class="d-flex">
                    <!--begin::Radio-->
                    <div class="form-check form-check-custom form-check-solid">
                        <!--begin::Input-->
                        <input id="kt_modal_update_role_option_1" class="form-check-input me-3"
                               name="user_role"
                               type="radio" value="1"/>
                        <!--end::Input-->
                        <!--begin::Label-->
                        <label class="form-check-label" for="kt_modal_update_role_option_1">
                            <div class="fw-bolder text-gray-800">Developer</div>
                            <div class="text-gray-600">Best for developers or people primarily
                                using the API
                            </div>
                        </label>
                        <!--end::Label-->
                    </div>
                    <!--end::Radio-->
                </div>
                <!--end::Input row-->
                <div class='separator separator-dashed my-5'></div>
                <!--begin::Input row-->
                <div class="d-flex">
                    <!--begin::Radio-->
                    <div class="form-check form-check-custom form-check-solid">
                        <!--begin::Input-->
                        <input id="kt_modal_update_role_option_2" class="form-check-input me-3"
                               name="user_role"
                               type="radio" value="2"/>
                        <!--end::Input-->
                        <!--begin::Label-->
                        <label class="form-check-label" for="kt_modal_update_role_option_2">
                            <div class="fw-bolder text-gray-800">Analyst</div>
                            <div class="text-gray-600">Best for people who need full access to
                                analytics data, but don't need to update business settings
                            </div>
                        </label>
                        <!--end::Label-->
                    </div>
                    <!--end::Radio-->
                </div>
                <!--end::Input row-->
                <div class='separator separator-dashed my-5'></div>
                <!--begin::Input row-->
                <div class="d-flex">
                    <!--begin::Radio-->
                    <div class="form-check form-check-custom form-check-solid">
                        <!--begin::Input-->
                        <input id="kt_modal_update_role_option_3" class="form-check-input me-3"
                               name="user_role"
                               type="radio" value="3"/>
                        <!--end::Input-->
                        <!--begin::Label-->
                        <label class="form-check-label" for="kt_modal_update_role_option_3">
                            <div class="fw-bolder text-gray-800">Support</div>
                            <div class="text-gray-600">Best for employees who regularly refund
                                payments and respond to disputes
                            </div>
                        </label>
                        <!--end::Label-->
                    </div>
                    <!--end::Radio-->
                </div>
                <!--end::Input row-->
                <div class='separator separator-dashed my-5'></div>
                <!--begin::Input row-->
                <div class="d-flex">
                    <!--begin::Radio-->
                    <div class="form-check form-check-custom form-check-solid">
                        <!--begin::Input-->
                        <input id="kt_modal_update_role_option_4" class="form-check-input me-3"
                               name="user_role"
                               type="radio" value="4"/>
                        <!--end::Input-->
                        <!--begin::Label-->
                        <label class="form-check-label" for="kt_modal_update_role_option_4">
                            <div class="fw-bolder text-gray-800">Trial</div>
                            <div class="text-gray-600">Best for people who need to preview
                                content data, but don't need to make any updates
                            </div>
                        </label>
                        <!--end::Label-->
                    </div>
                    <!--end::Radio-->
                </div>
                <!--end::Input row-->
            </div>
            <!--end::Input group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Discard</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Submit
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = () => {
    emit('onSubmit', props.form);
    localDialogFormVisible.value = false;
};
</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
