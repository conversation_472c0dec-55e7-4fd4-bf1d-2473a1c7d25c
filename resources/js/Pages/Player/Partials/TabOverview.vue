<script setup>
import BlockProfile from "@/Pages/Player/Partials/TabOverview/BlockProfile.vue";
import BlockAccount from "@/Pages/Player/Partials/TabOverview/BlockAccount.vue";

const props = defineProps({
    userInfo: Object,
    playerInfo: Object,
    accountList: Array,
})


const emit = defineEmits([
    'cb:changePass',
    // 'cb:changeRole',
    'cb:changeEmail',
    'cb:onAddOrMinusValue',
    // 'cb:onUpdateProfile'
]);

const handleBtnChangePass = () => {
    console.log('handleBtnChangePass')
    emit('cb:changePass');
}

/*const handleBtnChangeRole = () => {
    console.log('handleBtnChangeRole')
    emit('cb:changeRole');
}*/

const handleBtnChangeEmail = () => {
    console.log('handleBtnChangeEmail')
    emit('cb:changeEmail');
}

const handleCbOnAddOrMinusValue = (data) => {
    console.log('handleCbOnAddOrMinusValue >> data: ', data)
    emit('cb:onAddOrMinusValue', data);
}

/*const handleOnUpdateProfile = (item) => {
    console.log('handleOnUpdateProfile >> item: ', item)
    emit('cb:onUpdateProfile', item);
}*/

</script>

<template>
    <div id="kt_user_view_overview_tab" class="tab-pane fade show active" role="tabpanel">

        <!--begin::Danh sách tài khoản-->
        <BlockAccount
            :account-list="accountList"
            @cb:onAddOrMinusValue="handleCbOnAddOrMinusValue"
        />
        <!--end::Danh sách tài khoản-->

        <!--        playerInfo: {{ playerInfo }} <br/>
                userInfo: {{ userInfo }}-->

        <!--begin::Card-->
        <!-- @cb:onUpdateProfile="handleOnUpdateProfile" -->
        <BlockProfile
            :player-info="playerInfo"
            :user-info="userInfo"
            @cb:changePass="handleBtnChangePass"
            @cb:changeEmail="handleBtnChangeEmail"
        />
        <!--end::Card-->

        <!--begin::Card-->
        <!--
        <BlockSessions/>
        -->
        <!--end::Card-->

    </div>
</template>
