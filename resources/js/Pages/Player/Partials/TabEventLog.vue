<script setup>
</script>

<template>
    <div id="kt_user_view_overview_events_and_logs_tab" class="tab-pane fade" role="tabpanel">

        <!--begin::Card-->
        <div class="card pt-4 mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header border-0">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>Emails</h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Button-->
                    <button class="btn btn-sm btn-light-primary" type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Files/Download.svg-->
                        <span class="svg-icon svg-icon-3">
                            <svg height="24px"
                                 version="1.1"
                                 viewBox="0 0 24 24" width="24px"
                                 xmlns="http://www.w3.org/2000/svg"
                                 xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g fill="none" fill-rule="evenodd" stroke="none"
                                   stroke-width="1">
                                    <rect height="24" width="24" x="0" y="0"/>
                                    <path
                                        d="M2,13 C2,12.5 2.5,12 3,12 C3.5,12 4,12.5 4,13 C4,13.3333333 4,15 4,18 C4,19.1045695 4.8954305,20 6,20 L18,20 C19.1045695,20 20,19.1045695 20,18 L20,13 C20,12.4477153 20.4477153,12 21,12 C21.5522847,12 22,12.4477153 22,13 L22,18 C22,20.209139 20.209139,22 18,22 L6,22 C3.790861,22 2,20.209139 2,18 C2,15 2,13.3333333 2,13 Z"
                                        fill="#000000" fill-rule="nonzero"
                                        opacity="0.3"/>
                                    <rect fill="#000000" height="14"
                                          opacity="0.3"
                                          rx="1"
                                          transform="translate(12.000000, 8.000000) rotate(-180.000000) translate(-12.000000, -8.000000)"
                                          width="2" x="11"
                                          y="1"/>
                                    <path
                                        d="M7.70710678,15.7071068 C7.31658249,16.0976311 6.68341751,16.0976311 6.29289322,15.7071068 C5.90236893,15.3165825 5.90236893,14.6834175 6.29289322,14.2928932 L11.2928932,9.29289322 C11.6689749,8.91681153 12.2736364,8.90091039 12.6689647,9.25670585 L17.6689647,13.7567059 C18.0794748,14.1261649 18.1127532,14.7584547 17.7432941,15.1689647 C17.3738351,15.5794748 16.7415453,15.6127532 16.3310353,15.2432941 L12.0362375,11.3779761 L7.70710678,15.7071068 Z"
                                        fill="#000000" fill-rule="nonzero"
                                        transform="translate(12.000004, 12.499999) rotate(-180.000000) translate(-12.000004, -12.499999)"/>
                                </g>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->Download Report
                    </button>
                    <!--end::Button-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body py-0">
                <!--begin::Table-->
                <table id="kt_table_customers_events"
                       class="table align-middle table-row-dashed fs-6 text-gray-600 fw-bold gy-5">
                    <!--begin::Table body-->
                    <tbody>
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">
                            <a class="text-gray-600 text-hover-primary me-1" href="#">Brian Cox</a>has made payment to
                            <a class="fw-bolder text-gray-900 text-hover-primary" href="#">#OLP-45690</a></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">20 Jun 2021, 11:05 am</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">
                            <a class="text-gray-600 text-hover-primary me-1" href="#">Max Smith</a>has made payment to
                            <a class="fw-bolder text-gray-900 text-hover-primary" href="#">#SDK-45670</a></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">10 Nov 2021, 11:30 am</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">Invoice
                            <a class="fw-bolder text-gray-900 text-hover-primary me-1" href="#">#LOP-45640</a>has been
                            <span class="badge badge-light-danger">Declined</span></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">05 May 2021, 9:23 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">
                            <a class="text-gray-600 text-hover-primary me-1" href="#">Sean Bean</a>has made payment to
                            <a class="fw-bolder text-gray-900 text-hover-primary" href="#">#XRS-45670</a></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">24 Jun 2021, 8:43 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">Invoice
                            <a class="fw-bolder text-gray-900 text-hover-primary me-1" href="#">#LOP-45640</a>has been
                            <span class="badge badge-light-danger">Declined</span></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">20 Jun 2021, 8:43 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">Invoice
                            <a class="fw-bolder text-gray-900 text-hover-primary me-1" href="#">#SEP-45656</a>status has
                            changed from
                            <span class="badge badge-light-warning me-1">Pending</span>to
                            <span class="badge badge-light-info">In Progress</span></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">20 Dec 2021, 6:05 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">Invoice
                            <a class="fw-bolder text-gray-900 text-hover-primary me-1" href="#">#LOP-45640</a>has been
                            <span class="badge badge-light-danger">Declined</span></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">20 Jun 2021, 2:40 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">Invoice
                            <a class="fw-bolder text-gray-900 text-hover-primary me-1" href="#">#KIO-45656</a>status has
                            changed from
                            <span class="badge badge-light-succees me-1">In Transit</span>to
                            <span class="badge badge-light-success">Approved</span></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">10 Nov 2021, 6:05 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">
                            <a class="text-gray-600 text-hover-primary me-1" href="#">Melody Macy</a>has made payment to
                            <a class="fw-bolder text-gray-900 text-hover-primary" href="#">#XRS-45670</a></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">20 Jun 2021, 6:05 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    <!--begin::Table row-->
                    <tr>
                        <!--begin::Event=-->
                        <td class="min-w-400px">
                            <a class="text-gray-600 text-hover-primary me-1" href="#">Emma Smith</a>has made payment to
                            <a class="fw-bolder text-gray-900 text-hover-primary" href="#">#XRS-45670</a></td>
                        <!--end::Event=-->
                        <!--begin::Timestamp=-->
                        <td class="pe-0 text-gray-600 text-end min-w-200px">10 Nov 2021, 5:30 pm</td>
                        <!--end::Timestamp=-->
                    </tr>
                    <!--end::Table row-->
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->

        <!--begin::Tasks-->
        <div class="card card-flush mb-6 mb-xl-9">
            <!--begin::Card header-->
            <div class="card-header mt-6">
                <!--begin::Card title-->
                <div class="card-title flex-column">
                    <h2 class="mb-1">Tin nhắn hệ thống</h2>
                    <div class="fs-6 fw-bold text-muted">Total 25 tasks in backlog</div>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <!--
                <div class="card-toolbar">
                    <button class="btn btn-light-primary btn-sm" data-bs-target="#kt_modal_add_task"
                            data-bs-toggle="modal"
                            type="button">
                        <span class="svg-icon svg-icon-3">
                            <IconAdd3/>
                        </span>
                        Add Task
                    </button>
                </div>
                -->
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column">
                <!--begin::Item-->
                <div class="d-flex align-items-center position-relative mb-7">
                    <!--begin::Label-->
                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>
                    <!--end::Label-->
                    <!--begin::Details-->
                    <div class="fw-bold ms-5">
                        <a class="fs-5 fw-bolder text-dark text-hover-primary" href="#">Create FureStibe branding
                            logo</a>
                        <!--begin::Info-->
                        <div class="fs-7 text-muted">Due in 1 day
                            <a href="#">Karina Clark</a></div>
                        <!--end::Info-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Menu-->
                    <button class="btn btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                            data-kt-menu-flip="top-end"
                            data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                            type="button">
                        <!--begin::Svg Icon-->
                        <span class="svg-icon svg-icon-3">
                            <svg fill="none" height="24"
                                 viewBox="0 0 24 24" width="24"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M2 6.5C2 4.01472 4.01472 2 6.5 2H17.5C19.9853 2 22 4.01472 22 6.5V6.5C22 8.98528 19.9853 11 17.5 11H6.5C4.01472 11 2 8.98528 2 6.5V6.5Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path
                                    d="M20 6.5C20 7.88071 18.8807 9 17.5 9C16.1193 9 15 7.88071 15 6.5C15 5.11929 16.1193 4 17.5 4C18.8807 4 20 5.11929 20 6.5Z"
                                    fill="#12131A"/>
                                <path
                                    d="M2 17.5C2 15.0147 4.01472 13 6.5 13H17.5C19.9853 13 22 15.0147 22 17.5V17.5C22 19.9853 19.9853 22 17.5 22H6.5C4.01472 22 2 19.9853 2 17.5V17.5Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path
                                    d="M9 17.5C9 18.8807 7.88071 20 6.5 20C5.11929 20 4 18.8807 4 17.5C4 16.1193 5.11929 15 6.5 15C7.88071 15 9 16.1193 9 17.5Z"
                                    fill="#12131A"/>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Task menu-->
                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true"
                         data-kt-menu-id="kt-users-tasks">
                        <!--begin::Header-->
                        <div class="px-7 py-5">
                            <div class="fs-5 text-dark fw-bolder">Update Status</div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Menu separator-->
                        <div class="separator border-gray-200"></div>
                        <!--end::Menu separator-->
                        <!--begin::Form-->
                        <form class="form px-7 py-5" data-kt-menu-id="kt-users-tasks-form">
                            <!--begin::Input group-->
                            <div class="fv-row mb-10">
                                <!--begin::Label-->
                                <label class="form-label fs-6 fw-bold">Status:</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <select class="form-select form-select-solid" data-allow-clear="true"
                                        data-hide-search="true"
                                        data-kt-select2="true" data-placeholder="Select option"
                                        name="task_status">
                                    <option></option>
                                    <option value="1">Approved</option>
                                    <option value="2">Pending</option>
                                    <option value="3">In Process</option>
                                    <option value="4">Rejected</option>
                                </select>
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Actions-->
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-light btn-active-light-primary me-2"
                                        data-kt-users-update-task-status="reset"
                                        type="button">Reset
                                </button>
                                <button class="btn btn-sm btn-primary" data-kt-users-update-task-status="submit"
                                        type="submit">
                                    <span class="indicator-label">Apply</span>
                                    <span class="indicator-progress">Please wait...
																			<span
                                                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            <!--end::Actions-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Task menu-->
                    <!--end::Menu-->
                </div>
                <!--end::Item-->
                <!--begin::Item-->
                <div class="d-flex align-items-center position-relative mb-7">
                    <!--begin::Label-->
                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>
                    <!--end::Label-->
                    <!--begin::Details-->
                    <div class="fw-bold ms-5">
                        <a class="fs-5 fw-bolder text-dark text-hover-primary" href="#">Schedule a meeting with FireBear
                            CTO John</a>
                        <!--begin::Info-->
                        <div class="fs-7 text-muted">Due in 3 days
                            <a href="#">Rober Doe</a></div>
                        <!--end::Info-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Menu-->
                    <button class="btn btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                            data-kt-menu-flip="top-end"
                            data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                            type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Settings-02.svg-->
                        <span class="svg-icon svg-icon-3">
                            <svg fill="none" height="24"
                                 viewBox="0 0 24 24" width="24"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M2 6.5C2 4.01472 4.01472 2 6.5 2H17.5C19.9853 2 22 4.01472 22 6.5V6.5C22 8.98528 19.9853 11 17.5 11H6.5C4.01472 11 2 8.98528 2 6.5V6.5Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path
                                    d="M20 6.5C20 7.88071 18.8807 9 17.5 9C16.1193 9 15 7.88071 15 6.5C15 5.11929 16.1193 4 17.5 4C18.8807 4 20 5.11929 20 6.5Z"
                                    fill="#12131A"/>
                                <path
                                    d="M2 17.5C2 15.0147 4.01472 13 6.5 13H17.5C19.9853 13 22 15.0147 22 17.5V17.5C22 19.9853 19.9853 22 17.5 22H6.5C4.01472 22 2 19.9853 2 17.5V17.5Z"
                                    fill="#12131A"
                                    opacity="0.25"/>
                                <path
                                    d="M9 17.5C9 18.8807 7.88071 20 6.5 20C5.11929 20 4 18.8807 4 17.5C4 16.1193 5.11929 15 6.5 15C7.88071 15 9 16.1193 9 17.5Z"
                                    fill="#12131A"/>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Task menu-->
                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true"
                         data-kt-menu-id="kt-users-tasks">
                        <!--begin::Header-->
                        <div class="px-7 py-5">
                            <div class="fs-5 text-dark fw-bolder">Update Status</div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Menu separator-->
                        <div class="separator border-gray-200"></div>
                        <!--end::Menu separator-->
                        <!--begin::Form-->
                        <form class="form px-7 py-5" data-kt-menu-id="kt-users-tasks-form">
                            <!--begin::Input group-->
                            <div class="fv-row mb-10">
                                <!--begin::Label-->
                                <label class="form-label fs-6 fw-bold">Status:</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <select class="form-select form-select-solid" data-allow-clear="true"
                                        data-hide-search="true"
                                        data-kt-select2="true" data-placeholder="Select option"
                                        name="task_status">
                                    <option></option>
                                    <option value="1">Approved</option>
                                    <option value="2">Pending</option>
                                    <option value="3">In Process</option>
                                    <option value="4">Rejected</option>
                                </select>
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Actions-->
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-light btn-active-light-primary me-2"
                                        data-kt-users-update-task-status="reset"
                                        type="button">Reset
                                </button>
                                <button class="btn btn-sm btn-primary" data-kt-users-update-task-status="submit"
                                        type="submit">
                                    <span class="indicator-label">Apply</span>
                                    <span class="indicator-progress">Please wait...
																			<span
                                                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            <!--end::Actions-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Task menu-->
                    <!--end::Menu-->
                </div>
                <!--end::Item-->
                <!--begin::Item-->
                <div class="d-flex align-items-center position-relative mb-7">
                    <!--begin::Label-->
                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>
                    <!--end::Label-->
                    <!--begin::Details-->
                    <div class="fw-bold ms-5">
                        <a class="fs-5 fw-bolder text-dark text-hover-primary" href="#">9 Degree Project Estimation</a>
                        <!--begin::Info-->
                        <div class="fs-7 text-muted">Due in 1 week
                            <a href="#">Neil Owen</a></div>
                        <!--end::Info-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Menu-->
                    <button class="btn btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                            data-kt-menu-flip="top-end"
                            data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                            type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Settings-02.svg-->
                        <span class="svg-icon svg-icon-3">
																	<svg fill="none" height="24"
                                                                         viewBox="0 0 24 24" width="24"
                                                                         xmlns="http://www.w3.org/2000/svg">
																		<path
                                                                            d="M2 6.5C2 4.01472 4.01472 2 6.5 2H17.5C19.9853 2 22 4.01472 22 6.5V6.5C22 8.98528 19.9853 11 17.5 11H6.5C4.01472 11 2 8.98528 2 6.5V6.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M20 6.5C20 7.88071 18.8807 9 17.5 9C16.1193 9 15 7.88071 15 6.5C15 5.11929 16.1193 4 17.5 4C18.8807 4 20 5.11929 20 6.5Z"
                                                                            fill="#12131A"/>
																		<path
                                                                            d="M2 17.5C2 15.0147 4.01472 13 6.5 13H17.5C19.9853 13 22 15.0147 22 17.5V17.5C22 19.9853 19.9853 22 17.5 22H6.5C4.01472 22 2 19.9853 2 17.5V17.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M9 17.5C9 18.8807 7.88071 20 6.5 20C5.11929 20 4 18.8807 4 17.5C4 16.1193 5.11929 15 6.5 15C7.88071 15 9 16.1193 9 17.5Z"
                                                                            fill="#12131A"/>
																	</svg>
																</span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Task menu-->
                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true"
                         data-kt-menu-id="kt-users-tasks">
                        <!--begin::Header-->
                        <div class="px-7 py-5">
                            <div class="fs-5 text-dark fw-bolder">Update Status</div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Menu separator-->
                        <div class="separator border-gray-200"></div>
                        <!--end::Menu separator-->
                        <!--begin::Form-->
                        <form class="form px-7 py-5" data-kt-menu-id="kt-users-tasks-form">
                            <!--begin::Input group-->
                            <div class="fv-row mb-10">
                                <!--begin::Label-->
                                <label class="form-label fs-6 fw-bold">Status:</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <select class="form-select form-select-solid" data-allow-clear="true"
                                        data-hide-search="true"
                                        data-kt-select2="true" data-placeholder="Select option"
                                        name="task_status">
                                    <option></option>
                                    <option value="1">Approved</option>
                                    <option value="2">Pending</option>
                                    <option value="3">In Process</option>
                                    <option value="4">Rejected</option>
                                </select>
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Actions-->
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-light btn-active-light-primary me-2"
                                        data-kt-users-update-task-status="reset"
                                        type="button">Reset
                                </button>
                                <button class="btn btn-sm btn-primary" data-kt-users-update-task-status="submit"
                                        type="submit">
                                    <span class="indicator-label">Apply</span>
                                    <span class="indicator-progress">Please wait...
																			<span
                                                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            <!--end::Actions-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Task menu-->
                    <!--end::Menu-->
                </div>
                <!--end::Item-->
                <!--begin::Item-->
                <div class="d-flex align-items-center position-relative mb-7">
                    <!--begin::Label-->
                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>
                    <!--end::Label-->
                    <!--begin::Details-->
                    <div class="fw-bold ms-5">
                        <a class="fs-5 fw-bolder text-dark text-hover-primary" href="#">Dashboard UI &amp; UX for Leafr
                            CRM</a>
                        <!--begin::Info-->
                        <div class="fs-7 text-muted">Due in 1 week
                            <a href="#">Olivia Wild</a></div>
                        <!--end::Info-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Menu-->
                    <button class="btn btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                            data-kt-menu-flip="top-end"
                            data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                            type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Settings-02.svg-->
                        <span class="svg-icon svg-icon-3">
																	<svg fill="none" height="24"
                                                                         viewBox="0 0 24 24" width="24"
                                                                         xmlns="http://www.w3.org/2000/svg">
																		<path
                                                                            d="M2 6.5C2 4.01472 4.01472 2 6.5 2H17.5C19.9853 2 22 4.01472 22 6.5V6.5C22 8.98528 19.9853 11 17.5 11H6.5C4.01472 11 2 8.98528 2 6.5V6.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M20 6.5C20 7.88071 18.8807 9 17.5 9C16.1193 9 15 7.88071 15 6.5C15 5.11929 16.1193 4 17.5 4C18.8807 4 20 5.11929 20 6.5Z"
                                                                            fill="#12131A"/>
																		<path
                                                                            d="M2 17.5C2 15.0147 4.01472 13 6.5 13H17.5C19.9853 13 22 15.0147 22 17.5V17.5C22 19.9853 19.9853 22 17.5 22H6.5C4.01472 22 2 19.9853 2 17.5V17.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M9 17.5C9 18.8807 7.88071 20 6.5 20C5.11929 20 4 18.8807 4 17.5C4 16.1193 5.11929 15 6.5 15C7.88071 15 9 16.1193 9 17.5Z"
                                                                            fill="#12131A"/>
																	</svg>
																</span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Task menu-->
                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true"
                         data-kt-menu-id="kt-users-tasks">
                        <!--begin::Header-->
                        <div class="px-7 py-5">
                            <div class="fs-5 text-dark fw-bolder">Update Status</div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Menu separator-->
                        <div class="separator border-gray-200"></div>
                        <!--end::Menu separator-->
                        <!--begin::Form-->
                        <form class="form px-7 py-5" data-kt-menu-id="kt-users-tasks-form">
                            <!--begin::Input group-->
                            <div class="fv-row mb-10">
                                <!--begin::Label-->
                                <label class="form-label fs-6 fw-bold">Status:</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <select class="form-select form-select-solid" data-allow-clear="true"
                                        data-hide-search="true"
                                        data-kt-select2="true" data-placeholder="Select option"
                                        name="task_status">
                                    <option></option>
                                    <option value="1">Approved</option>
                                    <option value="2">Pending</option>
                                    <option value="3">In Process</option>
                                    <option value="4">Rejected</option>
                                </select>
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Actions-->
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-light btn-active-light-primary me-2"
                                        data-kt-users-update-task-status="reset"
                                        type="button">Reset
                                </button>
                                <button class="btn btn-sm btn-primary" data-kt-users-update-task-status="submit"
                                        type="submit">
                                    <span class="indicator-label">Apply</span>
                                    <span class="indicator-progress">Please wait...
																			<span
                                                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            <!--end::Actions-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Task menu-->
                    <!--end::Menu-->
                </div>
                <!--end::Item-->
                <!--begin::Item-->
                <div class="d-flex align-items-center position-relative">
                    <!--begin::Label-->
                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>
                    <!--end::Label-->
                    <!--begin::Details-->
                    <div class="fw-bold ms-5">
                        <a class="fs-5 fw-bolder text-dark text-hover-primary" href="#">Mivy App R&amp;D, Meeting with
                            clients</a>
                        <!--begin::Info-->
                        <div class="fs-7 text-muted">Due in 2 weeks
                            <a href="#">Sean Bean</a></div>
                        <!--end::Info-->
                    </div>
                    <!--end::Details-->
                    <!--begin::Menu-->
                    <button class="btn btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                            data-kt-menu-flip="top-end"
                            data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
                            type="button">
                        <!--begin::Svg Icon | path: icons/duotone/Interface/Settings-02.svg-->
                        <span class="svg-icon svg-icon-3">
																	<svg fill="none" height="24"
                                                                         viewBox="0 0 24 24" width="24"
                                                                         xmlns="http://www.w3.org/2000/svg">
																		<path
                                                                            d="M2 6.5C2 4.01472 4.01472 2 6.5 2H17.5C19.9853 2 22 4.01472 22 6.5V6.5C22 8.98528 19.9853 11 17.5 11H6.5C4.01472 11 2 8.98528 2 6.5V6.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M20 6.5C20 7.88071 18.8807 9 17.5 9C16.1193 9 15 7.88071 15 6.5C15 5.11929 16.1193 4 17.5 4C18.8807 4 20 5.11929 20 6.5Z"
                                                                            fill="#12131A"/>
																		<path
                                                                            d="M2 17.5C2 15.0147 4.01472 13 6.5 13H17.5C19.9853 13 22 15.0147 22 17.5V17.5C22 19.9853 19.9853 22 17.5 22H6.5C4.01472 22 2 19.9853 2 17.5V17.5Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M9 17.5C9 18.8807 7.88071 20 6.5 20C5.11929 20 4 18.8807 4 17.5C4 16.1193 5.11929 15 6.5 15C7.88071 15 9 16.1193 9 17.5Z"
                                                                            fill="#12131A"/>
																	</svg>
																</span>
                        <!--end::Svg Icon-->
                    </button>
                    <!--begin::Task menu-->
                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true"
                         data-kt-menu-id="kt-users-tasks">
                        <!--begin::Header-->
                        <div class="px-7 py-5">
                            <div class="fs-5 text-dark fw-bolder">Update Status</div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Menu separator-->
                        <div class="separator border-gray-200"></div>
                        <!--end::Menu separator-->
                        <!--begin::Form-->
                        <form class="form px-7 py-5" data-kt-menu-id="kt-users-tasks-form">
                            <!--begin::Input group-->
                            <div class="fv-row mb-10">
                                <!--begin::Label-->
                                <label class="form-label fs-6 fw-bold">Status:</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <select class="form-select form-select-solid" data-allow-clear="true"
                                        data-hide-search="true"
                                        data-kt-select2="true" data-placeholder="Select option"
                                        name="task_status">
                                    <option></option>
                                    <option value="1">Approved</option>
                                    <option value="2">Pending</option>
                                    <option value="3">In Process</option>
                                    <option value="4">Rejected</option>
                                </select>
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Actions-->
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-light btn-active-light-primary me-2"
                                        data-kt-users-update-task-status="reset"
                                        type="button">Reset
                                </button>
                                <button class="btn btn-sm btn-primary" data-kt-users-update-task-status="submit"
                                        type="submit">
                                    <span class="indicator-label">Apply</span>
                                    <span class="indicator-progress">Please wait...
																			<span
                                                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            <!--end::Actions-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Task menu-->
                    <!--end::Menu-->
                </div>
                <!--end::Item-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Tasks-->
    </div>
</template>
