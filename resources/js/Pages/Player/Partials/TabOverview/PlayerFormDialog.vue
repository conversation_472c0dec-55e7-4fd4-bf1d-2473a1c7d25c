<template>
    <el-dialog
        v-model="localDialogFormVisible" :title="titleModal"
        style="margin-top: 5% !important;"
        width="50%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

        <el-form
            :model="localForm"
            @submit.prevent="handleSubmit"
            ref="ruleFormRef"
            :rules="rules"
            class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7"
            style="margin-top: 0 !important;padding-top: 0 !important;"
        >
            
            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Nick Name</label>
                <!--end::Label-->
                <el-form-item prop="nick_name">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.nick_name"
                        size="large"
                        placeholder="Nhập nick name"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Display Name</label>
                <!--end::Label-->
                <el-form-item prop="display_name">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.display_name"
                        size="large"
                        placeholder="Nhập tên hiển thị"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group - Level and EXP row-->
            <div class="row mb-7">
                <div class="col-6">
                    <div class="fv-row">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Level</label>
                        <!--end::Label-->
                        <el-form-item prop="level">
                            <!--begin::Input-->
                            <el-input-number
                                v-model="localForm.level"
                                size="large"
                                :min="0"
                                :max="100"
                                placeholder="Nhập level"
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                                style="width: 100%"
                            />
                            <!--end::Input-->
                        </el-form-item>
                    </div>
                </div>
                <div class="col-6">
                    <div class="fv-row">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">EXP</label>
                        <!--end::Label-->
                        <el-form-item prop="exp">
                            <!--begin::Input-->
                            <el-input-number
                                v-model="localForm.exp"
                                size="large"
                                :min="0"
                                placeholder="Nhập exp"
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                                style="width: 100%"
                            />
                            <!--end::Input-->
                        </el-form-item>
                    </div>
                </div>
            </div>
            <!--end::Input group - Level and EXP row-->

            <!--begin::Input group - VIP Point and Login Type row-->
            <div class="row mb-7">
                <div class="col-6">
                    <div class="fv-row">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">VIP Point</label>
                        <!--end::Label-->
                        <el-form-item prop="vip_point">
                            <!--begin::Input-->
                            <el-input-number
                                v-model="localForm.vip_point"
                                size="large"
                                :min="0"
                                placeholder="Nhập VIP point"
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                                style="width: 100%"
                            />
                            <!--end::Input-->
                        </el-form-item>
                    </div>
                </div>
                <div class="col-6">
                    <div class="fv-row">
                        <label class="fw-bold fs-6 mb-2">Giới tính</label>
                        <el-form-item prop="type">
                            <el-select
                                v-model="localForm.gender"
                                size="large"
                                placeholder="Chọn loại tài khoản"
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="(label, value) in genderTypes"
                                    :key="value"
                                    :label="label"
                                    :value="value"
                                />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <!--
                <div class="col-6">
                    <div class="fv-row">
                        <label class="fw-bold fs-6 mb-2">Login Type</label>
                        <el-form-item prop="type">
                            <el-select
                                v-model="localForm.type"
                                size="large"
                                placeholder="Chọn loại tài khoản"
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="(label, value) in playerLoginTypes"
                                    :key="value"
                                    :label="label"
                                    :value="value"
                                />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                -->
            </div>
            <!--end::Input group - VIP Point and Login Type row-->

            <!--begin::Input group-->
            <!--
            <div class="fv-row mb-7">
                <label class="fw-bold fs-6 mb-2">Avatar</label>
                <el-form-item prop="avatar">
                    <el-input-number
                        v-model="localForm.avatar"
                        size="large"
                        :min="1"
                        :max="10"
                        placeholder="Chọn avatar (1-10)"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                        style="width: 100%"
                    />
                </el-form-item>
            </div>
            -->
            <!--end::Input group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Hủy</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Cập nhật
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {reactive, ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object,
    formAction: String,
    playerLoginTypes: Object,
    genderTypes: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);
const ruleFormRef = ref(null);

let localForm = ref({
    id: props.form?.id || null,
    nick_name: props.form?.nick_name || '',
    display_name: props.form?.display_name || '',
    level: props.form?.level || 0,
    exp: props.form?.exp || 0,
    vip_point: props.form?.vip_point || 0,
    avatar: props.form?.avatar || 1,
    type: props.form?.type || 0,
    gender: props.form?.gender || 'OTHER',
});

// begin: validation
const rules = reactive({
    nick_name: [
        {required: true, message: 'Nick name không được để trống', trigger: 'blur'},
        {min: 3, max: 50, message: 'Nick name từ 3-50 ký tự', trigger: 'blur'},
    ],
    display_name: [
        {max: 100, message: 'Tên hiển thị tối đa 100 ký tự', trigger: 'blur'},
    ],
    level: [
        {required: true, message: 'Level không được để trống', trigger: 'blur'},
        {type: 'number', min: 0, max: 100, message: 'Level từ 0-100', trigger: 'blur'},
    ],
    exp: [
        {required: true, message: 'EXP không được để trống', trigger: 'blur'},
        {type: 'number', min: 0, message: 'EXP phải >= 0', trigger: 'blur'},
    ],
    vip_point: [
        {required: true, message: 'VIP Point không được để trống', trigger: 'blur'},
        {type: 'number', min: 0, message: 'VIP Point phải >= 0', trigger: 'blur'},
    ],
    /*avatar: [
        {required: true, message: 'Avatar không được để trống', trigger: 'blur'},
        {type: 'number', min: 1, max: 10, message: 'Avatar từ 1-10', trigger: 'blur'},
    ],*/
    type: [
        {required: true, message: 'Type không được để trống', trigger: 'blur'},
        // {type: 'number', min: 0, max: 2, message: 'Type từ 0-2', trigger: 'blur'},
    ],
})

// end: validation

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

watch(() => props.form, (newForm) => {
    if (newForm) {
        localForm.value = {
            id: newForm.id || null,
            nick_name: newForm.nick_name || '',
            display_name: newForm.display_name || '',
            level: newForm.level || 0,
            exp: newForm.exp || 0,
            vip_point: newForm.vip_point || 0,
            avatar: newForm.avatar || 1,
            type: newForm.type || 0,
        };
    }
}, {deep: true});

const handleSubmit = async () => {
    await ruleFormRef.value.validate((valid) => {
        if (valid) {
            console.log('Form submitted successfully!', localForm.value);
            emit('onSubmit', localForm.value);
            localDialogFormVisible.value = false;
        } else {
            return false;
        }
    });
};

watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
    emit('update:dialogFormVisible', newValue);
});

watch(() => localDialogFormVisible.value, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
<style>
.el-input--large .el-input__wrapper {
    width: 100% !important;
}
</style>
