<script setup>
import {Edit} from "@element-plus/icons-vue";

const props = defineProps({
    userInfo: Object,
    playerInfo: Object,
    accountList: Array,
})

const emit = defineEmits([
    'cb:changePass',
    // 'cb:changeRole',
    'cb:changeEmail',
    // 'cb:onUpdateProfile'
]);

const handleBtnChangePass = () => {
    console.log('handleBtnChangePass >> userInfo: ', props.userInfo)
    emit('cb:changePass');
}

/*const handleBtnChangeRole = () => {
    console.log('handleBtnChangeRole')
    // emit('cb:changeRole');
    emit('cb:onUpdateProfile');
}*/

const handleBtnChangeEmail = () => {
    console.log('handleBtnChangeEmail')
    emit('cb:changeEmail');
}

</script>

<template>
    <!--begin::Card-->
    <div class="card pt-4 mb-6 mb-xl-9">
        <!--begin::Card header-->
        <div class="card-header border-0">
            <!--begin::Card title-->
            <div class="card-title">
                <h2>Thông tin cá nhân</h2>
            </div>
            <!--end::Card title-->
        </div>
        <!--end::Card header-->

        <!-- userInfo: {{ userInfo }} <br/> -->

        <!--begin::Card body-->
        <div class="card-body pt-0 pb-5">
            <!--begin::Table wrapper-->
            <div class="table-responsive">
                <!--begin::Table-->
                <table id="kt_table_users_login_session" class="table align-middle table-row-dashed gy-5">
                    <!--begin::Table body-->
                    <tbody class="fs-6 fw-bold text-gray-600">
                    <tr>
                        <td>Email</td>
                        <td>{{ userInfo?.email ?? '' }}</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeEmail"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                            <el-tooltip
                                class="box-item"
                                content="cập nhật"
                                effect="dark"
                                placement="top"
                            >
                                <el-button
                                    :icon="Edit"
                                    @click.prevent="handleBtnChangeEmail"
                                    type="primary"
                                >
                                </el-button>
                            </el-tooltip>
                        </td>
                    </tr>
                    <tr>
                        <td>Password</td>
                        <td>******</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangePass"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                            <el-tooltip
                                class="box-item"
                                content="cập nhật"
                                effect="dark"
                                placement="top"
                            >
                                <el-button
                                    :icon="Edit"
                                    @click.prevent="handleBtnChangePass"
                                    type="primary"></el-button>
                            </el-tooltip>
                        </td>
                    </tr>
                    <!--
                    <tr>
                        <td>Role</td>
                        <td>Administrator</td>
                        <td class="text-end">
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeRole"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                        </td>
                    </tr>
                    -->
                    <tr>
                        <td>Giới tính</td>
                        <td>{{ userInfo?.gender ?? '' }}</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeRole"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                        </td>
                    </tr>
                    <tr>
                        <td>Login Type</td>
                        <td>{{ userInfo?.loginType ?? '' }}</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeRole"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                        </td>
                    </tr>
                    <tr>
                        <td>Ngày tham gia</td>
                        <td>{{ userInfo?.createdAt ?? '' }}</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeRole"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                        </td>
                    </tr>
                    <tr>
                        <td>Đăng nhập lần cuối</td>
                        <td>{{ userInfo?.lastLogin ?? '' }}</td>
                        <td class="text-end">
                            <!--
                            <ThirdButton
                                class-name="btn-icon btn-active-light-primary w-30px h-30px ms-auto"
                                @click.prevent="handleBtnChangeRole"
                            >
                                <span class="svg-icon svg-icon-3">
                                    <IconEdit2/>
                                </span>
                            </ThirdButton>
                            -->
                        </td>
                    </tr>
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->
            </div>
            <!--end::Table wrapper-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Card-->
</template>
