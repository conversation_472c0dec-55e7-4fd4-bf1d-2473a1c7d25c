<template>
    <el-dialog
        v-model="localDialogFormVisible"
        :title="titleModal"
        style="margin-top: 5% !important;"
        width="50%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

        <el-form
            :model="localForm"
            @submit.prevent="handleSubmit"
            ref="ruleFormRef"
            :rules="rules"
            class="mt-1 modal-body scroll-y"
            style="margin-top: 0 !important;padding-top: 0 !important;"
        >

            <!--            form: {{ form }} <br/>
                        appId: {{ accountId }} <br/>
                        localForm: {{ localForm }}-->

            <!--begin::Input group - Account Selection-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Chọn tài khoản</label>
                <!--end::Label-->
                <el-form-item prop="accountId">
                    <!--begin::Select-->
                    <el-select
                        v-model="localForm.accountId"
                        size="large"
                        placeholder="Chọn tài khoản cần cộng/trừ"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="account in accountList"
                            :key="account.id"
                            :label="`${account.appName} (${account.fullName}) - Balance: ${account.balance}`"
                            :value="account.id"
                        />
                    </el-select>
                    <!--end::Select-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group - Amount-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">
                    Số tiền {{ formAction === 'add' ? 'cộng' : 'trừ' }}
                </label>
                <!--end::Label-->
                <el-form-item prop="amount">
                    <!--begin::Input-->
                    <el-input
                        v-model.number="localForm.amount"
                        size="large"
                        type="number"
                        :min="0"
                        :step="1000"
                        placeholder="Nhập số tiền"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Input group - Reason-->
            <div class="fv-row mb-7">
                <!--begin::Label-->
                <label class="required fw-bold fs-6 mb-2">Lý do</label>
                <!--end::Label-->
                <el-form-item prop="reason">
                    <!--begin::Input-->
                    <el-input
                        v-model="localForm.reason"
                        size="large"
                        type="textarea"
                        :rows="3"
                        placeholder="Nhập lý do cộng/trừ tiền (tùy chọn)"
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                    <!--end::Input-->
                </el-form-item>
            </div>
            <!--end::Input group-->

            <!--begin::Info group-->
            <div class="fv-row mb-7" v-if="selectedAccount">
                <div class="notice d-flex bg-light-info rounded border-info border border-dashed p-6">
                    <i class="ki-duotone ki-information fs-2tx text-info me-4">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    <div class="d-flex flex-stack flex-grow-1">
                        <div class="fw-semibold">
                            <h4 class="text-gray-900 fw-bold">Thông tin tài khoản</h4>
                            <div class="fs-6 text-gray-700">
                                <strong>App:</strong> {{ selectedAccount.appName }} (ID: {{
                                    selectedAccount.appId
                                }})<br>
                                <strong>Tên:</strong> {{ selectedAccount.fullName }} ({{ selectedAccount.name }})<br>
                                <strong>Balance hiện tại:</strong> {{ selectedAccount.balance.toLocaleString() }}<br>
                                <strong>Balance sau {{ formAction === 'add' ? 'cộng' : 'trừ' }}: </strong>
                                <span :class="newBalance >= 0 ? 'text-success' : 'text-danger'">
                                    {{ newBalance.toLocaleString() }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Info group-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Hủy</el-button>
                <el-button
                    size="large"
                    :type="formAction === 'add' ? 'success' : 'danger'"
                    @click.prevent="handleSubmit"
                >
                    {{ formAction === 'add' ? 'Cộng tiền' : 'Trừ tiền' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {computed, reactive, ref, watch} from 'vue';
import {ElMessage} from 'element-plus';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object,
    formAction: String,
    accountList: Array,
    accountId: Number,
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);
const ruleFormRef = ref(null);

let localForm = ref({
    accountId: props.accountId || null, // null,
    amount: 0,
    reason: '',
    type: props.formAction
});

// Find selected account info
const selectedAccount = computed(() => {
    if (!localForm.value.accountId || !props.accountList) return null;
    return props.accountList.find(account => account.id === localForm.value.accountId);
});

// Calculate new balance
const newBalance = computed(() => {
    if (!selectedAccount.value || !localForm.value.amount) return selectedAccount.value?.balance || 0;

    const currentBalance = selectedAccount.value.balance;
    const amount = localForm.value.amount;

    return props.formAction === 'add'
        ? currentBalance + amount
        : currentBalance - amount;
});

// begin: validation
const rules = reactive({
    accountId: [
        {required: true, message: 'Vui lòng chọn tài khoản', trigger: 'change'},
    ],
    amount: [
        {required: true, message: 'Vui lòng nhập số tiền', trigger: 'blur'},
        {type: 'number', min: 1, message: 'Số tiền phải lớn hơn 0', trigger: 'blur'},
    ],
    reason: [
        {required: true, message: 'Vui lòng nhập lý do', trigger: 'blur'}
    ],
});

// end: validation

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = async () => {
    await ruleFormRef.value.validate((valid) => {
        if (valid) {
            // Validate minimum balance for minus operation
            if (props.formAction === 'minus' && newBalance.value < 0) {
                ElMessage.error('Số dư không đủ để thực hiện giao dịch');
                return false;
            }

            const submitData = {
                ...localForm.value,
                selectedAccount: selectedAccount.value,
                newBalance: newBalance.value
            };

            console.log('Form submitted successfully!', submitData);
            emit('onSubmit', submitData);
            localDialogFormVisible.value = false;
        } else {
            return false;
        }
    });
};

watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
    if (newValue) {
        // Reset form when dialog opens
        localForm.value = {
            accountId: null,
            amount: 0,
            reason: '',
            type: props.formAction
        };
    }
});

watch(() => localDialogFormVisible.value, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

watch(() => props.formAction, (newValue) => {
    localForm.value.type = newValue;
});

</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
<style>
.el-input--large .el-input__wrapper {
    width: 100% !important;
}
</style>
