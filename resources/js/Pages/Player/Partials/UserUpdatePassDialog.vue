<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal" style="margin-top: 5% !important;" width="40%">
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>
        <el-form
            :model="passwordForm"
            ref="ruleFormRef"
            :rules="rules"
            class="mt-1 modal-body scroll-y mxxxx-5 mx-xl-153333 myyyy-7"
        >

            <!--begin::Input group=-->
            <!--
            <div class="fv-row mb-10">
                <label class="required form-label fs-6 mb-2">Current Password</label>
                <input
                    v-model="passwordForm.current_password"
                    autocomplete="off"
                    class="form-control form-control-lg form-control-solid"
                    name="current_password"
                    placeholder=""s
                    type="password"
                />
            </div>
            -->
            <!--end::Input group=-->
            <!--begin::Input group-->
            <div class="mb-10 fv-row" data-kt-password-meter="true">
                <!--begin::Wrapper-->
                <div class="mb-1">
                    <!--begin::Label-->
                    <label class="required form-label fw-bold fs-6 mb-2">Mật khẩu mới</label>
                    <!--end::Label-->
                    <!--begin::Input wrapper-->
                    <div class="position-relative mb-3">
                        <!--
                        <input
                            v-model="passwordForm.new_password"
                            autocomplete="off"
                            class="form-control form-control-lg form-control-solid"
                            name="new_password"
                            placeholder=""
                            type="password"
                        />
                        <span
                            class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2"
                            data-kt-password-meter-control="visibility">
                            <i class="bi bi-eye-slash fs-2"></i>
                            <i class="bi bi-eye fs-2 d-none"></i>
                        </span>
                        -->
                        <el-form-item prop="new_password">
                            <el-input
                                v-model="passwordForm.new_password"
                                type="password"
                                size="large"
                                placeholder="Nhập mật khẩu mới"
                                show-password
                                class="form-control2 form-control-solid mb-3 mb-lg-0"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input wrapper-->
                    <!--begin::Meter-->
                    <!--
                    <div class="d-flex align-items-center mb-3"
                         data-kt-password-meter-control="highlight">
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                        <div
                            class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                    </div>
                    -->
                    <!--end::Meter-->
                </div>
                <!--end::Wrapper-->
                <!--begin::Hint-->
                <!--
                <div class="text-muted">Use 8 or more characters with a mix of letters, numbers
                    &amp; symbols.
                </div>
                -->
                <!--end::Hint-->
            </div>
            <!--end::Input group=-->
            <!--begin::Input group=-->
            <div class="fv-row mb-10">
                <label class="required form-label fw-bold fs-6 mb-2">Nhập lại mật khẩu</label>
                <!--
                <input
                    v-model="passwordForm.confirm_password"
                    autocomplete="off"
                    class="form-control form-control-lg form-control-solid"
                    name="confirm_password"
                    placeholder=""
                    type="password"
                />
                -->
                <el-form-item prop="confirm_password">
                    <el-input
                        v-model="passwordForm.confirm_password"
                        type="password"
                        size="large"
                        placeholder="Nhập lại mật khẩu"
                        show-password
                        class="form-control2 form-control-solid mb-3 mb-lg-0"
                    />
                </el-form-item>
            </div>
            <!--end::Input group=-->

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Hủy</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Cập nhật
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {reactive, ref, watch} from 'vue';

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    form: Object
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);
const ruleFormRef = ref(null);

const passwordForm = reactive({
    // current_password: '',
    new_password: '',
    confirm_password: ''
});

// Custom validator for confirm password
const validateConfirmPassword = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('Vui lòng nhập lại mật khẩu'));
    } else if (value !== passwordForm.new_password) {
        callback(new Error('Mật khẩu nhập lại không khớp'));
    } else {
        callback();
    }
};

// begin: validation
const rules = reactive({
    new_password: [
        {required: true, message: 'Mật khẩu mới không được để trống', trigger: 'blur'},
        {min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự', trigger: 'blur'},
        {max: 255, message: 'Mật khẩu tối đa 255 ký tự', trigger: 'blur'},
    ],
    confirm_password: [
        {required: true, message: 'Vui lòng nhập lại mật khẩu', trigger: 'blur'},
        {validator: validateConfirmPassword, trigger: 'blur'},
    ],
});
// end: validation

watch(() => props.dialogFormVisible, (newValue) => {
    localDialogFormVisible.value = newValue;
    if (newValue) {
        // Reset form when dialog opens
        // passwordForm.current_password = '';
        passwordForm.new_password = '';
        passwordForm.confirm_password = '';
    }
});

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

const handleSubmit = async () => {
    await ruleFormRef.value.validate((valid) => {
        if (valid) {
            console.log('Form submitted successfully!', passwordForm);
            emit('onSubmit', passwordForm);
            localDialogFormVisible.value = false;
        } else {
            return false;
        }
    });
};
</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
