<script setup>
import MainLayout from '@/Layouts/MainLayout.vue';
import {Head, router} from '@inertiajs/vue3'
import Toolbar from "@/Components/Toolbar.vue";
import UserDialog from "@/Pages/Users/<USER>/UserDialog.vue";
import UserEditDialog from "@/Pages/Users/<USER>/UserEditDialog.vue";
import {reactive, ref} from "vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import DataTable from "@/Pages/Users/<USER>/DataTable.vue";
import IconRefresh from "@/Components/Icons/IconRefresh.vue";
import IconAdd from "@/Components/Icons/IconAdd.vue";

const props = defineProps({
    cateName: String,
    statusArr: Array,
    items: Object,
    paginate: Object,
    roles: Array,
    queryParams: Object,
    roleId: Number,
    totalCount: Number,
    totalPages: Number,
    /*keyword: {
        type: String,
        default: ''
    }*/
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},*/
    // {text: 'Users'},
    // {text: 'Users List'}
];


// Begin: Add User
// =====================================================================================================================
let titleModal = ref('Thêm mới')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    username: '',
    email: '',
    password: '',
    first_name: '',
    last_name: '',
})

const handleSubmit = (data) => {
    console.log('handleSubmit >> data: ', data)

    // Prepare user data for API call
    const userData = {
        username: data.username,
        email: data.email,
        password: data.password,
        first_name: data.first_name,
        last_name: data.last_name,
    }

    // Call to api users.store using user data
    router.post(route('cms.players.store'), userData, {
        preserveState: true,
        preserveScroll: true,
        onSuccess: (res) => {
            dialogFormVisible.value = false
            console.log('User created successfully:', res);
            showMessage('Thêm mới user thành công!', 'success');
        },
        onError: (errors) => {
            dialogFormVisible.value = false
            console.error('Error creating user:', errors);
            showMessage('Lỗi khi tạo user: ' + Object.values(errors).join(', '), 'error');
        }
    })
}

// Begin: Edit User
// =====================================================================================================================
let editTitleModal = ref('Cập nhật người dùng')
let editDialogKey = ref(1)
const editDialogFormVisible = ref(false)
const currentUserId = ref(0)
const editForm = reactive({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
})

const editUser = (userData) => {
    console.log('editUser', userData)
    editTitleModal.value = 'Cập nhật người dùng'
    editDialogFormVisible.value = true
    editDialogKey.value += 1

    // Call to api users.show using userData.uid
    /* router.get(route('user.show', { id: userData.uid }), {
        preserveState: true,
        preserveScroll: true,
        only: ['user'],
        onSuccess: (res) => {
            console.log('User data:', res.user);
        },
        onError: (errors) => {
            console.error('Error fetching user:', errors);
        }
    }) */

    // Set the user ID - form data will be loaded via API
    currentUserId.value = userData.uid || 0
}

const handleEditSubmit = (data) => {
    console.log('handleEditSubmit >> data: ', data)

    // Call to api users.update using updated user data
    router.put(route('cms.users.update', {id: currentUserId.value}), data, {
        preserveState: true,
        preserveScroll: true,
        onSuccess: (res) => {
            editDialogFormVisible.value = false
            console.log('User updated successfully:', res);
            showMessage('Cập nhật người dùng thành công!', 'success');
        },
        onError: (errors) => {
            console.error('Error updating user:', errors);
            showMessage('Lỗi khi cập nhật người dùng: ' + Object.values(errors).join(', '), 'error');
        }
    })
}
// =====================================================================================================================
// End: Edit User

const incrementDialogKey = () => {
    dialogKey.value += 1
}

const addUser = () => {
    console.log('addUser')
    // Navigate to create page instead of opening modal
    router.get(route('cms.players.create'), {
        preserveState: false,
        preserveScroll: false,
    })
}
// =====================================================================================================================
// End: Add User

// Create an object to track search/filter parameters
const objectSearch = reactive({
    page: props.queryParams?.page || 1,
    per_page: props.queryParams?.per_page || 10,
    s: props.queryParams?.s || null,
    // roleId: props.queryParams?.roleId || 0,
    // status: props.queryParams?.status || -1,
    // sort: props.queryParams?.sort || 'id:desc'
})

// Function to fetch data with current search parameters
const fetchData = () => {
    console.log('[Users >> Index] fetchData >> objectSearch: ', objectSearch)
    //write for me function by my objectSearch has 3 properties: page, per_page, s. but s = '', so i need function not included in the query string
    const params = Object.fromEntries(Object.entries(objectSearch).filter(([key, value]) => value !== '' && value !== null));
    console.log('[Users >> Index] fetchData >> params: ', params)

    router.get(
        route(route().current()),
        {
            ...params,
            // ...objectSearch,
            // Calculate firstResult and maxResult for backend pagination
            // first_result: (objectSearch.page - 1) * objectSearch.per_page,
            // max_result: objectSearch.per_page
        },
        {
            preserveState: true,
            preserveScroll: true,
            only: ['items', 'totalCount', 'totalPages']
        }
    )
}

const handlePageChange = (page) => {
    console.log('[Users >> Index] handlePageChange >> page: ', page)
    objectSearch.page = page
    fetchData()
}

const handlePerPageChange = (perPage) => {
    console.log('[Users >> Index] handlePageChange >> perPage: ', perPage)
    objectSearch.per_page = perPage
    fetchData()
}

const handleMetaChange = (meta) => {
    console.log('[Users >> Index] handleMetaChange >> meta: ', meta)
    objectSearch.page = meta.page
    objectSearch.per_page = meta.per_page
    objectSearch.s = meta.s
    fetchData();
}

const actions = [
    // {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    {name: 'Reload', className: 'btn-light-primary fw-bolder', icon: IconRefresh, click: fetchData},
    // {name: 'Thêm mới', className: 'btn-sm btn-primary'}
    {name: 'Thêm mới', className: 'btn-primary', icon: IconAdd, click: addUser}
];

</script>

<template>
    <Head title="users"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!--begin::Post-->
        <!--
        <UserTable :data="items.data" @cb:onAddUser="addUser"/>
        -->
        <div class="container-fluid mb-15">

            <DataTable
                :data="items.data"
                :meta="paginate.data"
                @cb:onAddUser="addUser"
                @cb:onEditUser="editUser"
                :cateName="cateName"
                :totalCount="totalCount"
                :totalPages="totalPages"
                @cb:meta-change="handleMetaChange"
                @cb:onPageChange="handlePageChange"
                @cb:onPerPageChange="handlePerPageChange"
            />

            <!--end::Post-->

            <!--begin::Modal - Add User-->
            <UserDialog
                :key="dialogKey"
                :dialogFormVisible="dialogFormVisible"
                :form="form"
                :titleModal="titleModal"
                @onSubmit="handleSubmit"
                @update:dialogFormVisible="dialogFormVisible = $event"
            />
            <!--end::Modal - Add User-->

            <!--begin::Modal - Edit User-->
            <UserEditDialog
                :key="editDialogKey"
                :dialogFormVisible="editDialogFormVisible"
                :form="editForm"
                :titleModal="editTitleModal"
                :userId="currentUserId"
                @onSubmit="handleEditSubmit"
                @update:dialogFormVisible="editDialogFormVisible = $event"
            />
            <!--end::Modal - Edit User-->
        </div>

    </MainLayout>
</template>
