<script setup>
import MainLayout from '@/Layouts/MainLayout.vue'
import {Head} from '@inertiajs/vue3'
import {reactive, ref} from 'vue'
import IconBack from "@/Components/Icons/IconBack.vue";
import Toolbar from "@/Components/Toolbar.vue";
import {showMessage} from "@/Helpers/messageHelper.js";
import ShowNavbar from "@/Pages/Player/Partials/ShowNavbar.vue";
import TabTransaction from "@/Pages/Player/Partials/TabTransaction.vue";

const props = defineProps({
    cateName: String,
    item: Object,
    userInfo: Object,
    playerInfo: Object,
    userBalance: Object,
    page: Number,
    routeIndex: String,
    roleId: Number,
    orderStatusObj: Object,
    tab: String,
    playerId: Number, // user id
    routerUpdateUserAddress: String,
    products: Object,
    accountList: Array,
    page: Number,
    perPage: Number,
})

const breadcrumbs = [
    /*{text: 'Home', href: '../../demo1/dist/index.html'},
    {text: 'User Management'},
    {text: 'Users'},*/
    // {text: 'View User'}
];

const actions = [
    // {name: 'Filter', className: 'btn-sm btn-flex btn-light btn-active-primary fw-bolder', icon: IconFilter},
    // {name: 'Create', className: 'btn-sm btn-primary'},
    {
        type: 'back',
        name: 'Quay lại',
        className: 'btn-sm2 btn-flex2 btn-light btn-light-primary fw-bolder2',
        icon: IconBack,
        href: '/players'
    },
];

// Begin: Update User
// =====================================================================================================================
let titleModal = ref('Update Player Details')
let dialogKey = ref(1)
const dialogFormVisible = ref(false)
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})
const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    showMessage('Submit success.');
}
const incrementDialogKey = () => {
    dialogKey.value += 1
}

const editUser = row => {
    // console.log('updatePermission >> row: ', row)
    // titleModal.value = 'Update Role'
    dialogFormVisible.value = true
    form.name = "" // row.name
    incrementDialogKey()
}

// =====================================================================================================================
// End: Update User

// Begin: Update Email
// =====================================================================================================================
let dialogKeyEmail = ref(1)
const dialogFormVisibleEmail = ref(false)
const handleClickBtnChangeEmail = () => {
    dialogFormVisibleEmail.value = true
    dialogKeyEmail.value += 1
}
const handleSubmitUpdateEmail = (data) => {
    console.log('handleSubmitUpdateEmail >>  data: ', data)
    showMessage('Submit change email success.');
}
// =====================================================================================================================
// End: Update Email

// Begin: Update Role
// =====================================================================================================================
let dialogKeyRole = ref(1)
const dialogFormVisibleRole = ref(false)
const handleClickBtnChangeRole = () => {
    dialogFormVisibleRole.value = true
    dialogKeyRole.value += 1
}
const handleSubmitUpdateRole = (data) => {
    console.log('handleSubmitUpdateRole >>  data: ', data)
    showMessage('Submit change role success.');
}
// =====================================================================================================================
// End: Update Role

// Begin: Update Password
// =====================================================================================================================
let dialogKeyPass = ref(1)
const dialogFormVisiblePass = ref(false)
const handleClickBtnChangePass = () => {
    dialogFormVisiblePass.value = true
    dialogKeyPass.value += 1
}
const handleSubmitUpdatePass = (data) => {
    console.log('handleSubmitUpdatePass >>  data: ', data)
    showMessage('Submit change pass success.');
}
// =====================================================================================================================
// End: Update Password

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemDelete = reactive({})
const deleteUser = row => {
    console.log('deletePermission >> row: ', row)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemDelete = row
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemDelete: ', itemDelete)
    deleteDialogVisible.value = false
}
// =====================================================================================================================
// End: delete

</script>

<template>
    <Head :title="cateName"/>

    <MainLayout>

        <!--begin::Toolbar-->
        <Toolbar :actions="actions" :breadcrumbs="breadcrumbs" :title="cateName"/>
        <!--end::Toolbar-->

        <!--begin::Container-->
        <div id="kt_content_container" class="container-fluid mb-15">

            <!--begin::Navbar-->
            <ShowNavbar :player-info="playerInfo" :user-info="userInfo" :tab="tab"/>
            <!--end::Navbar-->

            <!--begin::Toolbar-->
            <!--
            <div class="d-flex flex-wrap flex-stack mb-6">

                <h3 class="fw-bolder my-2">My Projects
                    <span class="fs-6 text-gray-400 fw-bold ms-1">Active</span></h3>

                <div class="d-flex flex-wrap my-2">
                    <div class="me-4">
                        <select name="status" data-control="select2" data-hide-search="true"
                                class="form-select form-select-sm form-select-white w-125px">
                            <option value="Active" selected="selected">Active</option>
                            <option value="Approved">In Progress</option>
                            <option value="Declined">To Do</option>
                            <option value="In Progress">Completed</option>
                        </select>

                    </div>
                    <a href="#" class="btn btn-primary btn-sm" data-bs-toggle="modal"
                       data-bs-target="#kt_modal_create_project">New Project</a>
                </div>
            </div>
            -->
            <!--end::Toolbar-->

            <!--begin::Row-->
            <div class="row g-5 g-xxl-8">
                <div class="col-xl-12">
                    <!--begin:::Tab logs giao dịch -->
                    <TabTransaction :userId="playerId" :current-page="page" :per-page="perPage"/>
                    <!--end:::Tab logs giao dịch -->
                </div>

            </div>
            <!--end::Row-->

        </div>
        <!--end::Container-->

    </MainLayout>
</template>
