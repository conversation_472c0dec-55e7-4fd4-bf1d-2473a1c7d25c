<script setup>
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {ElMessage, ElTable} from 'element-plus'
import {computed, reactive, ref, watch} from "vue";
import ThirdButton from "@/Components/ThirdButton.vue";
import IconAdd2 from "@/Components/Icons/IconAdd2.vue";
import PermissionDialog from "@/Components/PermissionDialog.vue";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import IconDelete from "@/Components/Icons/IconDelete.vue";
import IconEdit from "@/Components/Icons/IconEdit.vue";
import {useForm, usePage} from "@inertiajs/vue3";
import {showMessage} from "@/Helpers/messageHelper.js";

const props = defineProps({
    /*type: {
        type: String,
        default: 'users'
    },
    roleId: {
        type: Number,
        default: 0
    },
    loading: Boolean,*/
    data: Array,
    // meta: Object,
    // statusArr: Array
})

const roleClassMapping = {
    'Administrator': 'badge-light-primary',
    'Developer': 'badge-light-danger',
    'Analyst': 'badge-light-success',
    'Support': 'badge-light-info',
    'Trial': 'badge-light-warning'
};

const errors = computed(() => usePage().props.errors)

// check error and display message
watch(
    () => errors.value,
    () => {
        Object.entries(errors.value).forEach(([key, value]) => {
            // console.log(`${key}: ${value}`);
            ElMessage({
                message: `${key}: ${value}`,
                type: 'error'
            })
        });
    }
)


/*const tableData = [
    {
        date: '2016-05-04',
        name: 'Aleyna Kutzner',
        address: 'Lohrbergstr. 86c, Süd Lilli, Saarland',
    },
    {
        date: '2016-05-03',
        name: 'Helen Jacobi',
        address: '760 A Street, South Frankfield, Illinois',
    },
    {
        date: '2016-05-02',
        name: 'Brandon Deckert',
        address: 'Arnold-Ohletz-Str. 41a, Alt Malinascheid, Thüringen',
    },
    {
        date: '2016-05-01',
        name: 'Margie Smith',
        address: '23618 Windsor Drive, West Ricardoview, Idaho',
    },
]*/

const dialogFormVisible = ref(false)
// const formLabelWidth = '140px'
const form = useForm({
    name: '',
    description: '',
    status: 1
})
let titleModal = ref('Add a Permission')
let dialogKey = ref(1)

const incrementDialogKey = () => {
    dialogKey.value += 1
}

const addPermission = () => {
    console.log('addPermission')
    titleModal.value = 'Add a Permission'
    dialogFormVisible.value = true
    incrementDialogKey()

    form.name = ''
    form.description = ''
    form.status = 1
}

const updatePermission = row => {
    console.log('updatePermission >> row: ', row)
    titleModal.value = 'Update Permission'
    dialogFormVisible.value = true
    form.name = row.name
    incrementDialogKey()
}

const handleSubmit = (data) => {
    console.log('handleSubmit >>  data: ', data)
    // dialogFormVisible.value = false
    form.post(route('cms.permissions.store'), {
        preserveScroll: true,
        onSuccess: (res) => {
            dialogFormVisible.value = false
            const message = res?.props?.flash?.message ?? ''
            const codeType = res?.props?.flash?.codeType ?? 'success'
            showMessage(message, codeType);
        }
    })
}

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemDelete = reactive({})
const deletePermission = row => {
    console.log('deletePermission >> row: ', row)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemDelete = row
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemDelete: ', itemDelete)
    deleteDialogVisible.value = false
}
// =====================================================================================================================
// End: delete

// Begin: Search
// =====================================================================================================================
const searchQuery = ref('');
const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Thực hiện hành động tìm kiếm ở đây
};
// =====================================================================================================================
// End: Search

</script>

<style>
.el-table .cell, .el-scrollbar {
    padding: 0 !important;
}

.el-table__header {
    padding: 0 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    background-color: #b5b5c3 !important;
}

/*.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}*/

.el-dialog__headerbtn {
    top: 13px !important;
}
</style>

<template>
    <div id="kt_post" class="post d-flex flex-column-fluid">
        <!--begin::Container-->
        <div id="kt_content_container" class="container">
            <!--begin::Card-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header mt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1 me-5">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <IconSearch/>
                            </span>
                            <input v-model="searchQuery"
                                   class="form-control form-control-solid w-250px ps-15"
                                   data-kt-permissions-table-filter="search"
                                   placeholder="Search Permissions"
                                   type="text"
                                   @keyup.enter="search"
                            />
                        </div>
                        <!--end::Search-->
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Button-->
                        <ThirdButton class="ml-5" class-name="btn-light-primary ml-5" name="Add Permission"
                                     @click="addPermission">
                            <span class="svg-icon svg-icon-3">
                                <IconAdd2/>
                            </span>
                        </ThirdButton>
                        <!--end::Button-->
                    </div>
                    <!--end::Card toolbar-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <el-table :data="data" class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
                              style="width: 100%; margin-left: 0; padding-left: 0;">
                        <el-table-column label="Name" min-width="120">
                            <template #default="scope">{{ scope.row.name }}</template>
                        </el-table-column>
                        <el-table-column label="Assigned to" min-width="210">
                            <template #default="scope">
                                <a v-for="role in scope.row?.assigned_to" :key="role"
                                   :class="['badge', 'fs-7', 'm2222-1', roleClassMapping[role]]"
                                   href="#">{{ role }}</a>
                            </template>
                        </el-table-column>
                        <el-table-column label="Created Date" property="created_date"/>
                        <!--begin::Action=-->
                        <el-table-column align="right" label="Actions">
                            <template #default="scope">
                                <!--begin::Update-->
                                <ThirdButton
                                    class="btn btn-icon btn-active-light-primary w-30px h-30px me-3"
                                    @click.prevent="updatePermission(scope.row)"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit/>
                                    </span>
                                </ThirdButton>
                                <!--end::Update-->
                                <!--begin::Delete-->
                                <ThirdButton class="btn btn-icon btn-active-light-primary w-30px h-30px"
                                             @click.prevent="deletePermission(scope.row)">
                                    <span class="svg-icon svg-icon-3">
                                        <IconDelete/>
                                    </span>
                                </ThirdButton>
                                <!--end::Delete-->
                            </template>
                        </el-table-column>
                        <!--end::Action=-->
                    </el-table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
                <!--begin::Pagination-->
                <div class="d-flex flex-center mb-5">
                    <el-pagination :page-sizes="[10, 20, 50, 100, 200, 300]"
                                   :total="1000" background layout="total, sizes, prev, pager, next, jumper"/>
                </div>
                <!--end::Pagination-->
            </div>
            <!--end::Card-->

            <!--begin::Modals-->
            <!--begin::Modal - Add/Update permissions-->
            <PermissionDialog
                :key="dialogKey"
                :dialogFormVisible="dialogFormVisible"
                :form="form"
                :titleModal="titleModal"
                @onSubmit="handleSubmit"
                @update:dialogFormVisible="dialogFormVisible = $event"
            />
            <!--end::Modal - Add/Update permissions-->

            <!--begin::Modal - Delete permissions-->
            <DeleteDialog
                :key="deleteDialogKey"
                :itemName="itemDelete?.name ?? ''"
                :visible="deleteDialogVisible"
                @delete="handleDeleteAction"
                @update:visible="deleteDialogVisible = $event"
            />
            <!--end::Modal - Delete permissions-->
            <!--end::Modals-->
        </div>
        <!--end::Container-->
    </div>
</template>
