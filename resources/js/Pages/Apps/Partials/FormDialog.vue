<template>
    <el-dialog v-model="localDialogFormVisible" :title="titleModal" width="50%">
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

<!--        <pre>
            {{ form }}
        </pre>-->
        <el-form :model="form" class="mt-1 modal-body scroll-y mx-5 mx-xl-15 myyyy-7" style="max-height: 600px;">
            <div class="fv-row mb-7">
                <label class="fs-6 fw-bold form-label mb-2">
                    <span class="required">Tên app</span>
                    <el-tooltip
                        class="box-item"
                        content="Tên app không được đề trống"
                        effect="dark"
                        placement="top"
                    >
                        <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </el-tooltip>
                </label>
                <input v-model="form.appName" class="form-control form-control-solid" name="appName"
                       placeholder="Nhập tên app ..."/>
            </div>

            <div class="fv-row mb-7">
                <label class="fs-6 fw-bold form-label mb-2">
                    <span>Ghi chú</span>
                </label>
                <!--
                <input v-model="form.notes" class="form-control form-control-solid" name="notes"
                       placeholder="Nhập ghi chú ..."/>
                -->
                <textarea v-model="form.notes" rows="3" class="form-control form-control-solid" name="notes" placeholder="Nhập ghi chú ..."></textarea>
            </div>

            <div class="row g-9 mb-5">
                <!--begin::Col-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Trạng thái</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <!--
                    <input v-model="form.transfer" class="form-control form-control-solid" placeholder="" name="transfer" />
                    -->
                    <!--
                    <el-select
                        v-model="form.status"
                        clearable
                        placeholder="Select"
                        size="large"
                        class="form-control form-control-solid"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    -->
                    <el-switch
                        v-model="form.isStatus"
                        class="form-control form-control-solid mb-5"
                        size="large"
                        inline-prompt
                        active-text="Hoạt động"
                        inactive-text="Khóa"
                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                    />
                    <!--end::Input-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Transfer</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <input v-model="form.transfer" class="form-control form-control-solid" placeholder="" name="transfer" />
                    <!--end::Input-->
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-md-4 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Transfer Cost</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <input v-model="form.transferCost" class="form-control form-control-solid" placeholder="" name="transferCost" />
                    <!--end::Input-->
                </div>
                <!--end::Col-->
            </div>

            <div class="row g-9 mb-5">
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Cost Type</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <input v-model="form.costType" class="form-control form-control-solid" placeholder="" name="costType" />
                    <!--end::Input-->
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Unit</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <input v-model="form.unit" class="form-control form-control-solid" placeholder="" name="unit" />
                    <!--end::Input-->
                </div>
                <!--end::Col-->
            </div>

            <div class="row g-9 mb-5">
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">Parent App</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <!--
                    <input v-model="form.pid" class="form-control form-control-solid" placeholder="" name="pid" />
                    -->
                    <el-select
                        v-model="form.pid"
                        clearable
                        placeholder="Select"
                        class="form-control form-control-solid"
                        size="large"
                    >
                        <el-option
                            v-for="item in apps"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <!--end::Input-->
                </div>
                <!--end::Col-->
                <!--begin::Col-->
                <div class="col-md-6 fv-row">
                    <!--begin::Label-->
                    <label class="fs-5 fw-bold mb-2">External App</label>
                    <!--end::Label-->
                    <!--begin::Input-->
                    <!--
                    <input v-model="form.ext" class="form-control form-control-solid" placeholder="" name="unit" />
                    -->
                    <el-select
                        v-model="form.ext"
                        clearable
                        placeholder="Select"
                        class="form-control form-control-solid"
                        size="large"
                    >
                        <el-option
                            v-for="item in apps"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <!--end::Input-->
                </div>
                <!--end::Col-->
            </div>

        </el-form>
        <template #footer>
            <div class="dialog-footer d-flex flex-center">
                <el-button size="large" @click="localDialogFormVisible = false">Discard</el-button>
                <el-button size="large" type="primary" @click.prevent="handleSubmit">
                    Submit
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {reactive, ref, watch} from 'vue';
import {ElMessage} from "element-plus";

const props = defineProps({
    dialogFormVisible: Boolean,
    titleModal: String,
    actionForm: String,
    form: Object,
    apps: Array
});

const emit = defineEmits(['update:dialogFormVisible', 'onSubmit']);
const localDialogFormVisible = ref(props.dialogFormVisible);

watch(localDialogFormVisible, (newValue) => {
    emit('update:dialogFormVisible', newValue);
});

// watch(props.apps, (newValue) => {
//
// });

const handleSubmit = () => {
    emit('onSubmit', {'data': props.form, 'action': props.actionForm});
    localDialogFormVisible.value = false;
};

// const options = [
//     {
//         value: 'Option1',
//         label: 'Option1',
//     },
//     {
//         value: 'Option2',
//         label: 'Option2',
//     },
//     {
//         value: 'Option3',
//         label: 'Option3',
//     },
//     {
//         value: 'Option4',
//         label: 'Option4',
//     },
//     {
//         value: 'Option5',
//         label: 'Option5',
//     },
// ]

</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-left: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eff2f5;
}
</style>
