<script setup>
import IconSearch from "@/Components/Icons/IconSearch.vue";
import {ElMessage, ElTable} from 'element-plus'
import {computed, reactive, ref, watch} from "vue";
import ThirdButton from "@/Components/ThirdButton.vue";
import IconAdd2 from "@/Components/Icons/IconAdd2.vue";
import FormDialog from "@/Pages/Apps/Partials/FormDialog.vue";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import IconDelete from "@/Components/Icons/IconDelete.vue";
import {useForm, usePage} from "@inertiajs/vue3";
import {showMessage} from "@/Helpers/messageHelper.js";
import IconEdit3 from "@/Components/Icons/IconEdit3.vue";

const props = defineProps({
    cateName: String,
    data: Array,
    // meta: Object,
    // statusArr: Array
})

const roleClassMapping = {
    'Administrator': 'badge-light-primary',
    'Developer': 'badge-light-danger',
    'Analyst': 'badge-light-success',
    'Support': 'badge-light-info',
    'Trial': 'badge-light-warning'
};

const errors = computed(() => usePage().props.errors)

// check error and display message
watch(
    () => errors.value,
    () => {
        Object.entries(errors.value).forEach(([key, value]) => {
            // console.log(`${key}: ${value}`);
            ElMessage({
                message: `${key}: ${value}`,
                type: 'error'
            })
        });
    }
)

const dialogFormVisible = ref(false)
// const formLabelWidth = '140px'
let form = useForm({
    appName: '',
    notes: '',
    status: 1,
    isStatus: true,
    transfer: 0,
    transferCost: 0,
    costType: 0,
    systemAcc: '-1',
    unit: 'VNĐ',
    pid: 0,
    ext: 0
})
let titleModal = ref('Thêm mới ' + props.cateName)
let dialogKey = ref(1)
let formAction = ref('add')
const incrementDialogKey = () => {
    dialogKey.value += 1
}

const resetForm = () => {
    // form.reset()
    form.appName = ''
    form.notes = ''
    form.status = 1
    form.isStatus = true
    form.transfer = 0
    form.transferCost = 0
    form.costType = 0
    form.systemAcc = '-1'
    form.unit = 'VNĐ'
    form.pid = 0
    form.ext = 0
}

const addItem = () => {
    console.log('addItem')
    formAction.value = 'add'
    titleModal.value = 'Thêm mới ' + props.cateName
    dialogFormVisible.value = true
    incrementDialogKey()

    // form.name = ''
    // form.description = ''
    // form.status = 1
}

const updateItem = row => {
    console.log('updateItem >> row: ', row)
    formAction.value = 'update'
    titleModal.value = 'Cập nhật: ' + row.appName
    dialogFormVisible.value = true
    form.name = row.name
    form = row
    form.pid = parseInt(row.pid) > 0 ? row.pid.toString() : row.pid
    form.ext = parseInt(row.ext) > 0 ? row.ext.toString() : row.ext
    incrementDialogKey()
}

const handleSubmit = (response) => {
    console.log('handleSubmit >>  response: ', response)

    const {data, action} = response
    console.log('handleSubmit >>  action: ', action)
    console.log('handleSubmit >>  data: ', data)

    if (data.isStatus) {
        data.status = 1
    } else {
        data.status = 0
    }
    data.pid = Number(data.pid)

    if (action === 'update') {
        // update
        // data.id = form.id
        const form = useForm(data)
        form.patch(route('cms.apps.update', data), {
            preserveScroll: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    } else {
        // save
        form.post(route('cms.apps.store'), {
            preserveScroll: true,
            onSuccess: (res) => {
                dialogFormVisible.value = false
                const message = res?.props?.flash?.message ?? ''
                const codeType = res?.props?.flash?.codeType ?? 'success'
                showMessage(message, codeType);
            }
        })
    }
    // reset data form
    resetForm()
}

// Begin: delete
// =====================================================================================================================
let deleteDialogKey = ref(1)
const deleteDialogVisible = ref(false)
let itemsDelete = reactive({})
let singleItemName = ref('')
const selectedRows = ref([]);

const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
};

const deleteItem = (row, type) => {
    console.log('deleteItem >> row: ', row, ' -> type: ', type)
    deleteDialogKey.value += 1
    deleteDialogVisible.value = true
    itemsDelete = row
    if (type === 1) {
        singleItemName.value = row.title
    } else {
        singleItemName.value = ''
    }
}
const handleDeleteAction = () => {
    console.log('handleDeleteAction >> itemsDelete: ', itemsDelete)
    const ids = itemsDelete.map(item => item.id);
    console.log('ids: ', ids)
    deleteDialogVisible.value = false

    // reset data
    // ---------------------------------------------------------------------------
    selectedRows.value = []
    singleItemName.value = ''
    itemsDelete = {}
}
// =====================================================================================================================
// End: delete

// Begin: Search
// =====================================================================================================================
const searchQuery = ref('');
const filteredData = ref([...props.data]);

/*const search = () => {
    console.log('Searching for:', searchQuery.value);
    // Thực hiện hành động tìm kiếm ở đây
};*/
const filterData = () => {
    filteredData.value = props.data.filter(item =>
        item.appName.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
};
// =====================================================================================================================
// End: Search

const changeStatus = (item, val) => {
    console.log('changeStatus: ', item, ' => val: ', val)
    // const form = useForm({
    //     name: item.name,
    //     // email: item.email,
    //     // status: item.status
    //     status: val ? 1 : 0
    // })
    //
    // form.patch(route('cms.roles.update', item))
}

// Watch the searchQuery to update filteredData
watch(searchQuery, filterData);

// Initial filter
filterData();

</script>

<style>
.el-table .cell, .el-scrollbar {
    padding: 0 !important;
}

.el-table__header {
    padding: 0 !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    background-color: #b5b5c3 !important;
}

.el-dialog__headerbtn {
    top: 13px !important;
}
</style>

<template>
    <div id="kt_post" class="post d-flex flex-column-fluid mb-10" style="min-height: 500px;">
        <!--begin::Container-->
        <div id="kt_content_container" class="container">
            <!--begin::Card-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header mt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1 me-5">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <IconSearch/>
                            </span>
                            <input v-model="searchQuery"
                                   class="form-control form-control-solid w-250px ps-15"
                                   data-kt-permissions-table-filter="search"
                                   :placeholder="'Tìm kiếm ' + props.cateName"
                                   type="text"
                                   @keyup.enter="filterData"
                            />
                        </div>
                        <!--end::Search-->
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Button-->
                        <ThirdButton class="ml-5"
                                     class-name="btn-primary ml-5"
                                     :name="'Thêm mới'"
                                     @click="addItem"
                                     v-if="selectedRows.length <= 0"
                        >
                            <span class="svg-icon svg-icon-3">
                                <IconAdd2/>
                            </span>
                        </ThirdButton>
                        <!--end::Button-->

                        <!--begin::Button-->
                        <div class="fw-bolder me-5" v-if="selectedRows.length > 0">
                            <span class="me-2">{{ selectedRows.length }}</span>Selected
                        </div>
                        <button v-if="selectedRows.length > 0"
                                type="button"
                                class="btn btn-danger"
                                @click.prevent="deleteItem(selectedRows, 2)"
                        >Delete Selected</button>
                        <!--end::Button-->

                    </div>
                    <!--end::Card toolbar-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--
                    <pre>
                        {{ data }}
                    </pre>
                    -->
                    <!--begin::Table-->
                    <el-table :data="filteredData" class="table align-middle table-row-dashed fs-6 gy-5 mb-0"
                              @selection-change="handleSelectionChange"
                              style="width: 100%; margin-left: 0; padding-left: 0;">
                        <el-table-column type="selection" width="55" />

                        <el-table-column label="Id" width="50">
                            <template #default="scope">{{ scope.row.id }}</template>
                        </el-table-column>

                        <el-table-column label="Tên App" min-width="200">
                            <template #default="scope">{{ scope.row.appName }}</template>
                        </el-table-column>

                        <el-table-column label="Ghi chú" min-width="200">
                            <template #default="scope">{{ scope.row.notes }}</template>
                        </el-table-column>

                        <el-table-column label="Trạng thái" min-width="110">
                            <template #default="scope">
                                <!--
                                <div class="badge badge-light fw-bolder">{{ scope.row?.status ?? 0 }}</div>
                                <span class="badge badge-light-success fs-7 fw-bolder">Active</span>
                                -->

                                <el-switch
                                    v-model="scope.row.isStatus"
                                    active-text="Hoạt động"
                                    class="ml-2"
                                    inactive-text="Khóa"
                                    inline-prompt
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #DCDFE6"
                                    @change="changeStatus(scope.row, $event)"
                                />

                            </template>
                        </el-table-column>

                        <!--begin::Action=-->
                        <el-table-column align="right" label="Hành động">
                            <template #default="scope">
                                <!--begin::Update-->
                                <ThirdButton
                                    class-name="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1"
                                    @click.prevent="updateItem(scope.row)"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconEdit3 />
                                    </span>
                                </ThirdButton>
                                <!--end::Update-->
                                <!--begin::Delete-->
                                <ThirdButton class-name="btn btn-icon btn-bg-light btn-active-color-primary btn-sm"
                                             @click.prevent="deleteItem(scope.row, 1)"
                                >
                                    <span class="svg-icon svg-icon-3">
                                        <IconDelete/>
                                    </span>
                                </ThirdButton>
                                <!--end::Delete-->
                            </template>
                        </el-table-column>
                        <!--end::Action=-->
                    </el-table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->

            <!--begin::Modals-->
            <!--begin::Modal - Add/Update apps-->
            <FormDialog
                :key="dialogKey"
                :dialogFormVisible="dialogFormVisible"
                :action-form="formAction"
                :form="form"
                :apps="data"
                :titleModal="titleModal"
                @onSubmit="handleSubmit"
                @update:dialogFormVisible="dialogFormVisible = $event"
            />
            <!--end::Modal - Add/Update apps-->

            <!--begin::Modal - Delete apps-->
            <DeleteDialog
                :key="deleteDialogKey"
                :itemName="singleItemName ?? ''"
                :visible="deleteDialogVisible"
                @delete="handleDeleteAction"
                @update:visible="deleteDialogVisible = $event"
            />
            <!--end::Modal - Delete apps-->
            <!--end::Modals-->
        </div>
        <!--end::Container-->
    </div>
</template>
