<template>
    <el-dialog
        v-model="localDialogFormVisible" :title="titleModal"
        style="margin-top: 5% !important;"
        width="60%"
    >
        <template #header>
            <div class="custom-dialog-header">
                <h2>{{ titleModal }}</h2>
            </div>
        </template>

        <el-form
            :model="localForm"
            @submit.prevent="handleSubmit"
            ref="ruleFormRef"
            :rules="rules"
            class="mt-1 modal-body scroll-y mx-5 my-7"
            style="margin-top: 0 !important;padding-top: 0 !important;"
        >
            <div class="row">
                <div class="col-md-6">
                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="required fw-bold fs-6 mb-2">Loại Shop</label>
                        <!--end::Label-->
                        <el-form-item prop="type">
                            <el-select
                                v-model="localForm.type"
                                placeholder="Chọn loại shop"
                                size="large"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="(label, value) in options"
                                    :key="value"
                                    :label="label"
                                    :value="value"
                                />
                            </el-select>
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Category</label>
                        <!--end::Label-->
                        <el-form-item prop="category">
                            <el-input
                                v-model="localForm.category"
                                size="large"
                                placeholder="Nhập category (ví dụ: SKIN, AVATAR cho PROPS)"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="required fw-bold fs-6 mb-2">Tên Item</label>
                        <!--end::Label-->
                        <el-form-item prop="item_name">
                            <el-input
                                v-model="localForm.item_name"
                                size="large"
                                placeholder="Nhập tên item"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Item Value</label>
                        <!--end::Label-->
                        <el-form-item prop="item_value">
                            <el-input
                                v-model="localForm.item_value"
                                size="large"
                                placeholder="Nhập giá trị item"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Item ID</label>
                        <!--end::Label-->
                        <el-form-item prop="item_id">
                            <el-input-number
                                v-model="localForm.item_id"
                                size="large"
                                :min="0"
                                placeholder="Nhập ID item"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="required fw-bold fs-6 mb-2">Giá</label>
                        <!--end::Label-->
                        <el-form-item prop="price">
                            <el-input-number
                                v-model="localForm.price"
                                size="large"
                                :min="0"
                                placeholder="Nhập giá"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Đơn vị tiền tệ</label>
                        <!--end::Label-->
                        <el-form-item prop="currency">
                            <el-select
                                v-model="localForm.currency"
                                placeholder="Chọn đơn vị tiền tệ"
                                size="large"
                                style="width: 100%"
                            >
                                <el-option label="CHIP" value="CHIP" />
                                <el-option label="DIAMOND" value="DIAMOND" />
                                <el-option label="COIN" value="COIN" />
                                <el-option label="USD" value="USD" />
                                <el-option label="VND" value="VND" />
                            </el-select>
                        </el-form-item>
                    </div>
                    <!--end::Input group-->
                </div>

                <div class="col-md-6">
                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Mô tả</label>
                        <!--end::Label-->
                        <el-form-item prop="description">
                            <el-input
                                v-model="localForm.description"
                                type="textarea"
                                size="large"
                                :rows="3"
                                placeholder="Nhập mô tả item"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Giới hạn số lượng (-1 là không giới hạn)</label>
                        <!--end::Label-->
                        <el-form-item prop="item_limit">
                            <el-input-number
                                v-model="localForm.item_limit"
                                size="large"
                                :min="-1"
                                placeholder="-1 = không giới hạn"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Thời gian hiển thị (phút)</label>
                        <!--end::Label-->
                        <el-form-item prop="available_time">
                            <el-input-number
                                v-model="localForm.available_time"
                                size="large"
                                :min="-1"
                                placeholder="-1 = luôn hiển thị"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Icon URL</label>
                        <!--end::Label-->
                        <el-form-item prop="icon">
                            <el-input
                                v-model="localForm.icon"
                                size="large"
                                placeholder="Nhập URL icon"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Ghi chú</label>
                        <!--end::Label-->
                        <el-form-item prop="note">
                            <el-input
                                v-model="localForm.note"
                                type="textarea"
                                size="large"
                                :rows="2"
                                placeholder="Nhập ghi chú"
                                class="form-control2 form-control-solid"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Thứ tự hiển thị</label>
                        <!--end::Label-->
                        <el-form-item prop="order_index">
                            <el-input-number
                                v-model="localForm.order_index"
                                size="large"
                                :min="0"
                                placeholder="Thứ tự sắp xếp"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <div class="row">
                            <div class="col-6">
                                <label class="fw-bold fs-6 mb-2 d-block">Trạng thái quà tặng</label>
                                <el-form-item prop="gift_status">
                                    <el-switch
                                        v-model="localForm.gift_status"
                                        active-text="Có"
                                        inactive-text="Không"
                                        :active-value="1"
                                        :inactive-value="0"
                                        inline-prompt
                                    />
                                </el-form-item>
                            </div>
                            <div class="col-6">
                                <label class="fw-bold fs-6 mb-2 d-block">Đang Sale</label>
                                <el-form-item prop="is_sale">
                                    <el-switch
                                        v-model="localForm.is_sale"
                                        active-text="Có"
                                        inactive-text="Không"
                                        :active-value="1"
                                        :inactive-value="0"
                                        inline-prompt
                                    />
                                </el-form-item>
                            </div>
                        </div>
                    </div>
                    <!--end::Input group-->

                    <!--begin::Input group-->
                    <div class="fv-row mb-7">
                        <!--begin::Label-->
                        <label class="fw-bold fs-6 mb-2">Trạng thái hiển thị</label>
                        <!--end::Label-->
                        <el-form-item prop="status">
                            <el-switch
                                v-model="localForm.status"
                                active-text="Hiện"
                                inactive-text="Ẩn"
                                :active-value="1"
                                :inactive-value="0"
                                inline-prompt
                            />
                        </el-form-item>
                    </div>
                    <!--end::Input group-->
                </div>
            </div>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelForm" size="large">Hủy</el-button>
                <el-button type="primary" @click="handleSubmit" size="large" :loading="loading">
                    {{ formAction === 'add' ? 'Thêm mới' : 'Cập nhật' }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import {computed, ref, watch} from 'vue'

const props = defineProps({
    dialogFormVisible: Boolean,
    form: Object,
    formAction: String,
    titleModal: String,
    options: Object
})

const emit = defineEmits(['onSubmit', 'update:dialogFormVisible'])

const localDialogFormVisible = computed({
    get() {
        return props.dialogFormVisible
    },
    set(value) {
        emit('update:dialogFormVisible', value)
    }
})

const localForm = ref({...props.form})
const loading = ref(false)
const ruleFormRef = ref()

// Watch for form changes from parent
watch(() => props.form, (newForm) => {
    localForm.value = {...newForm}
}, {deep: true})

const rules = {
    type: [{required: true, message: 'Vui lòng chọn loại shop', trigger: 'change'}],
    item_name: [{required: true, message: 'Vui lòng nhập tên item', trigger: 'blur'}],
    price: [{required: true, message: 'Vui lòng nhập giá', trigger: 'blur'}]
}

const handleSubmit = async () => {
    if (!ruleFormRef.value) return

    await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            loading.value = true
            emit('onSubmit', localForm.value)
            setTimeout(() => {
                loading.value = false
            }, 1000)
        } else {
            console.log('error submit!', fields)
        }
    })
}

const cancelForm = () => {
    localDialogFormVisible.value = false
}
</script>

<style scoped>
.custom-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}
</style>
