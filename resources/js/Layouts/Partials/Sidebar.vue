<!-- Sidebar.vue -->
<script setup>
import {Link} from "@inertiajs/vue3";
import {ref} from "vue";

/*const isOpen = ref(false);

const toggleSidebar = () => {
    isOpen.value = !isOpen.value;
};*/

const isMenuOpen = ref(false);

const toggleMenu = () => {
    console.log('hi there');
    isMenuOpen.value = !isMenuOpen.value;
};

</script>

<template>
    <div class="aside-menu flex-column-fluid">
        <!--begin::Aside Menu-->
        <div id="kt_aside_menu_wrapper" class="hover-scroll-overlay-y my-5 my-lg-5" data-kt-scroll="true"
             data-kt-scroll-activate="{default: false, lg: true}"
             data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer"
             data-kt-scroll-height="auto"
             data-kt-scroll-offset="0" data-kt-scroll-wrappers="#kt_aside_menu">
            <!--begin::Menu-->
            <div
                id="#kt_aside_menu"
                class="menu menu-column menu-title-gray-800 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500"
                data-kt-menu="true">
                <div class="menu-item">
                    <div class="menu-content pb-2">
                        <span class="menu-section text-muted text-uppercase fs-8 ls-1">Management</span>
                    </div>
                </div>
                <div class="menu-item">
                    <Link :class="{'active': route().current('dashboard')}" :href="route('dashboard')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-tachometer" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Dashboard</span>
                    </Link>
                </div>
                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.players.*')}" :href="route('cms.players.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-users" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Người chơi</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.partners.*')}" :href="route('cms.partners.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-handshake-o" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Đại lý</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.transactions.*')}"
                          :href="route('cms.transactions.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-money" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Logs Giao dịch</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.replays.*')}" :href="route('cms.replays.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-play" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Logs Ván Đánh</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.articles.*')}" :href="route('cms.articles.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-file" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Bài viết</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.chats.*')}" :href="route('cms.chats.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-comments-o" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Chat</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.notifications.*')}"
                          :href="route('cms.notifications.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-bell" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Thông báo</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.faqs.*')}"
                          :href="route('cms.faqs.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-question-circle" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Hỗ trợ</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.shops.*')}"
                          :href="route('cms.shops.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-shopping-cart" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Cửa hàng</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.leaderboards.*')}"
                          :href="route('cms.leaderboards.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-bar-chart" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Bảng xếp hạng</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.missions.*')}"
                          :href="route('cms.missions.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-calendar" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Nhiệm vụ</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.badges.*')}"
                          :href="route('cms.badges.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-certificate" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Thành tích</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.feedbacks.*')}"
                          :href="route('cms.feedbacks.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-comments" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Feedback</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <div class="menu-content pb-2">
                        <span class="menu-section text-muted text-uppercase fs-8 ls-1">Systems</span>
                    </div>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.apps.*')}" :href="route('cms.apps.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-rocket" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Applications</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.admin.*')}" :href="route('cms.admin.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-users" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Admin</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.roles.*')}" :href="route('cms.roles.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-tasks" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Roles</span>
                    </Link>
                </div>
                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.permissions.*')}"
                          :href="route('cms.permissions.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-universal-access" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Permissions</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.activities.*')}" :href="route('cms.activities.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-desktop" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Logs hành động</span>
                    </Link>
                </div>

                <div class="menu-item">
                    <Link :class="{'active': route().current('cms.settings.*')}" :href="route('cms.settings.index')"
                          class="menu-link">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <i class="fa fa-cogs" aria-hidden="true"></i>
                            </span>
                        </span>
                        <span class="menu-title">Cài đặt</span>
                    </Link>
                </div>

                <!--
                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                    <span class="menu-link" @click="toggleMenu">
                        <span class="menu-icon">
                            <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24" />
                                        <path d="M4,9.67471899 L10.880262,13.6470401 C10.9543486,13.689814 11.0320333,13.7207107 11.1111111,13.740321 L11.1111111,21.4444444 L4.49070127,17.526473 C4.18655139,17.3464765 4,17.0193034 4,16.6658832 L4,9.67471899 Z M20,9.56911707 L20,16.6658832 C20,17.0193034 19.8134486,17.3464765 19.5092987,17.526473 L12.8888889,21.4444444 L12.8888889,13.6728275 C12.9050191,13.6647696 12.9210067,13.6561758 12.9368301,13.6470401 L20,9.56911707 Z" fill="#000000" />
                                        <path d="M4.21611835,7.74669402 C4.30015839,7.64056877 4.40623188,7.55087574 4.5299008,7.48500698 L11.5299008,3.75665466 C11.8237589,3.60013944 12.1762411,3.60013944 12.4700992,3.75665466 L19.4700992,7.48500698 C19.5654307,7.53578262 19.6503066,7.60071528 19.7226939,7.67641889 L12.0479413,12.1074394 C11.9974761,12.1365754 11.9509488,12.1699127 11.9085461,12.2067543 C11.8661433,12.1699127 11.819616,12.1365754 11.7691509,12.1074394 L4.21611835,7.74669402 Z" fill="#000000" opacity="0.3" />
                                    </g>
                                </svg>
                            </span>
                        </span>
                        <span class="menu-title">Cài đặt</span>
                        <span class="menu-arrow"></span>
                    </span>
                    <div class="menu-active-bg" v-if="isMenuOpen">
                        <div class="menu-item sub-menu-item">
                            <a class="menu-link" href="#">
                                <span class="menu-bullet">
                                    <span class="bullet bullet-dot"></span>
                                </span>
                                <span class="menu-title">Cài đặt chung</span>
                            </a>
                        </div>
                        <div class="menu-item sub-menu-item">
                            <Link :class="{'active': route().current('cms.apps.*')}" :href="route('cms.apps.index')"
                                  class="menu-link">

                                <span class="menu-bullet">
                                    <span class="bullet bullet-dot"></span>
                                </span>
                                <span class="menu-title">Danh sách apps</span>
                            </Link>
                        </div>
                    </div>
                </div>
                -->

            </div>
            <!--end::Menu-->
        </div>
        <!--end::Aside Menu-->
    </div>
</template>

<style scoped>
/* Add any additional styles if needed */
.sub-menu-item {
    /*padding: 0.5rem 1rem;*/
    padding-left: 1rem;
}
</style>
