<script setup>
import {onMounted} from 'vue';
// import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap';
import {Link} from '@inertiajs/vue3';

/*const initializeDropdowns = () => {
    const specificElement = document.querySelector('#kt_header_user_menu_toggle2');
    if (specificElement) {
        console.log("vao day roi nhe")
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.map((dropdownToggleEl) => new bootstrap.Dropdown(dropdownToggleEl));
    }
};*/
const initializeDropdown = () => {
    const dropdownToggle = document.querySelector('[data-bs-toggle="dropdown"]');
    if (dropdownToggle) {
        dropdownToggle.addEventListener('click', (event) => {
            event.preventDefault();
            const dropdown = new bootstrap.Dropdown(dropdownToggle);
            dropdown.show();
        });
    }
};

/*const toggleDropdowns = () => {
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.forEach((dropdownToggleEl) => {
        dropdownToggleEl.addEventListener('click', (event) => {
            event.preventDefault();
            const dropdown = new bootstrap.Dropdown(dropdownToggleEl);
            dropdown.toggle();
            dropdownToggleEl.classList.remove('dropdown-toggle');
        });
    });
};*/


onMounted(() => {
    initializeDropdown();

    // Lắng nghe sự kiện click trên các liên kết
    /*document.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', () => {
            initializeDropdowns();
        });
    });*/

    // toggleDropdowns();
});
</script>

<template>
    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1">
        <!--begin::Navbar-->
        <div id="kt_header_nav" class="d-flex align-items-stretch">
            <!--begin::Menu wrapper-->
            <div class="header-menu align-items-stretch" data-kt-drawer="true"
                 data-kt-drawer-activate="{default: true, lg: false}"
                 data-kt-drawer-direction="end" data-kt-drawer-name="header-menu"
                 data-kt-drawer-overlay="true"
                 data-kt-drawer-toggle="#kt_header_menu_mobile_toggle"
                 data-kt-drawer-width="{default:'200px', '300px': '250px'}" data-kt-swapper="true"
                 data-kt-swapper-mode="prepend"
                 data-kt-swapper-parent="{default: '#kt_body', lg: '#kt_header_nav'}">
                <!--begin::Menu-->
                <div
                    id="#kt_header_menu"
                    class="menu menu-lg-rounded menu-column menu-lg-row menu-state-bg menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-400 fw-bold my-5 my-lg-0 align-items-stretch"
                    data-kt-menu="true">
                    <div class="menu-item me-lg-1">
                        <a class="menu-link active py-3" href="/">
                            <span class="menu-title">Dashboard</span>
                        </a>
                    </div>

                    <div class="menu-item me-lg-1">
                        <a class="menu-link py-3" href="https://top.pokee.club" target="_blank">
                            <span class="menu-title">Apps</span>
                        </a>
                    </div>
                </div>
                <!--end::Menu-->
            </div>
            <!--end::Menu wrapper-->
        </div>
        <!--end::Navbar-->
        <!--begin::Topbar-->
        <div class="d-flex align-items-stretch flex-shrink-0">
            <!--begin::Toolbar wrapper-->
            <div class="d-flex align-items-stretch flex-shrink-0">
                <!--begin::Search-->
                <div class="d-flex align-items-stretch ms-1 ms-lg-3">
                    <!--begin::Search-->
                    <div id="kt_header_search" class="d-flex align-items-stretch"
                         data-kt-menu-flip="bottom" data-kt-menu-overflow="false"
                         data-kt-menu-permanent="true" data-kt-menu-placement="bottom-end"
                         data-kt-menu-trigger="auto" data-kt-search-enter="enter"
                         data-kt-search-keypress="true" data-kt-search-layout="menu"
                         data-kt-search-min-length="2">
                        <!--begin::Search toggle-->
                        <div id="kt_header_search_toggle" class="d-flex align-items-center"
                             data-kt-search-element="toggle">
                            <div
                                class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px">
                                <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
                                <span class="svg-icon svg-icon-1">
                                    <svg height="24px"
                                            version="1.1"
                                            viewBox="0 0 24 24" width="24px"
                                            xmlns="http://www.w3.org/2000/svg">
                                        <g fill="none" fill-rule="evenodd" stroke="none"
                                            stroke-width="1">
                                            <rect height="24" width="24" x="0" y="0"/>
                                            <path
                                                d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                fill="#000000" fill-rule="nonzero"
                                                opacity="0.3"/>
                                            <path
                                                d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                fill="#000000" fill-rule="nonzero"/>
                                        </g>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                            </div>
                        </div>
                        <!--end::Search toggle-->
                        <!--begin::Menu-->
                        <div class="menu menu-sub menu-sub-dropdown p-7 w-325px w-md-375px"
                             data-kt-search-element="content">
                            <!--begin::Wrapper-->
                            <div data-kt-search-element="wrapper">
                                <!--begin::Form-->
                                <form autocomplete="off"
                                      class="w-100 position-relative mb-3"
                                      data-kt-search-element="form">
                                    <!--begin::Icon-->
                                    <!--begin::Svg Icon | path: icons/duotone/General/Search.svg-->
                                    <span
                                        class="svg-icon svg-icon-2 svg-icon-lg-1 svg-icon-gray-500 position-absolute top-50 translate-middle-y ms-0">
																<svg height="24px"
                                                                     version="1.1" viewBox="0 0 24 24" width="24px"
                                                                     xmlns="http://www.w3.org/2000/svg">
																	<g fill="none" fill-rule="evenodd" stroke="none"
                                                                       stroke-width="1">
																		<rect height="24" width="24" x="0" y="0"/>
																		<path
                                                                            d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z"
                                                                            fill="#000000" fill-rule="nonzero"
                                                                            opacity="0.3"/>
																		<path
                                                                            d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z"
                                                                            fill="#000000" fill-rule="nonzero"/>
																	</g>
																</svg>
															</span>
                                    <!--end::Svg Icon-->
                                    <!--end::Icon-->
                                    <!--begin::Input-->
                                    <input class="form-control form-control-flush ps-10"
                                           data-kt-search-element="input"
                                           name="search" placeholder="Search..." type="text"
                                           value=""/>
                                    <!--end::Input-->
                                    <!--begin::Spinner-->
                                    <span
                                        class="position-absolute top-50 end-0 translate-middle-y lh-0 d-none me-1"
                                        data-kt-search-element="spinner">
																<span
                                                                    class="spinner-border h-15px w-15px align-middle text-gray-400"></span>
															</span>
                                    <!--end::Spinner-->
                                    <!--begin::Reset-->
                                    <span
                                        class="btn btn-flush btn-active-color-primary position-absolute top-50 end-0 translate-middle-y lh-0 d-none"
                                        data-kt-search-element="clear">
																<!--begin::Svg Icon | path: icons/duotone/Navigation/Close.svg-->
																<span class="svg-icon svg-icon-2 svg-icon-lg-1 me-0">
																	<svg height="24px"
                                                                         version="1.1" viewBox="0 0 24 24" width="24px"
                                                                         xmlns="http://www.w3.org/2000/svg">
																		<g fill="#000000"
                                                                           transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)">
																			<rect fill="#000000" height="2" rx="1"
                                                                                  width="16"
                                                                                  x="0" y="7"/>
																			<rect fill="#000000" height="2"
                                                                                  opacity="0.5"
                                                                                  rx="1"
                                                                                  transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                                                                  width="16" x="0"
                                                                                  y="7"/>
																		</g>
																	</svg>
																</span>
                                        <!--end::Svg Icon-->
															</span>
                                    <!--end::Reset-->
                                    <!--begin::Toolbar-->
                                    <div class="position-absolute top-50 end-0 translate-middle-y"
                                         data-kt-search-element="toolbar">
                                        <!--begin::Preferences toggle-->
                                        <div
                                            class="btn btn-icon w-20px btn-sm btn-active-color-primary me-1"
                                            data-bs-toggle="tooltip"
                                            data-kt-search-element="preferences-show"
                                            title="Show search preferences">
                                            <!--begin::Svg Icon | path: icons/duotone/Code/Settings4.svg-->
                                            <span class="svg-icon svg-icon-1">
																		<svg height="24px"
                                                                             version="1.1" viewBox="0 0 24 24"
                                                                             width="24px"
                                                                             xmlns="http://www.w3.org/2000/svg">
																			<path
                                                                                d="M18.6225,9.75 L18.75,9.75 C19.9926407,9.75 21,10.7573593 21,12 C21,13.2426407 19.9926407,14.25 18.75,14.25 L18.6854912,14.249994 C18.4911876,14.250769 18.3158978,14.366855 18.2393549,14.5454486 C18.1556809,14.7351461 18.1942911,14.948087 18.3278301,15.0846699 L18.372535,15.129375 C18.7950334,15.5514036 19.03243,16.1240792 19.03243,16.72125 C19.03243,17.3184208 18.7950334,17.8910964 18.373125,18.312535 C17.9510964,18.7350334 17.3784208,18.97243 16.78125,18.97243 C16.1840792,18.97243 15.6114036,18.7350334 15.1896699,18.3128301 L15.1505513,18.2736469 C15.008087,18.1342911 14.7951461,18.0956809 14.6054486,18.1793549 C14.426855,18.2558978 14.310769,18.4311876 14.31,18.6225 L14.31,18.75 C14.31,19.9926407 13.3026407,21 12.06,21 C10.8173593,21 9.81,19.9926407 9.81,18.75 C9.80552409,18.4999185 9.67898539,18.3229986 9.44717599,18.2361469 C9.26485393,18.1556809 9.05191298,18.1942911 8.91533009,18.3278301 L8.870625,18.372535 C8.44859642,18.7950334 7.87592081,19.03243 7.27875,19.03243 C6.68157919,19.03243 6.10890358,18.7950334 5.68746499,18.373125 C5.26496665,17.9510964 5.02757002,17.3784208 5.02757002,16.78125 C5.02757002,16.1840792 5.26496665,15.6114036 5.68716991,15.1896699 L5.72635306,15.1505513 C5.86570889,15.008087 5.90431906,14.7951461 5.82064513,14.6054486 C5.74410223,14.426855 5.56881236,14.310769 5.3775,14.31 L5.25,14.31 C4.00735931,14.31 3,13.3026407 3,12.06 C3,10.8173593 4.00735931,9.81 5.25,9.81 C5.50008154,9.80552409 5.67700139,9.67898539 5.76385306,9.44717599 C5.84431906,9.26485393 5.80570889,9.05191298 5.67216991,8.91533009 L5.62746499,8.870625 C5.20496665,8.44859642 4.96757002,7.87592081 4.96757002,7.27875 C4.96757002,6.68157919 5.20496665,6.10890358 5.626875,5.68746499 C6.04890358,5.26496665 6.62157919,5.02757002 7.21875,5.02757002 C7.81592081,5.02757002 8.38859642,5.26496665 8.81033009,5.68716991 L8.84944872,5.72635306 C8.99191298,5.86570889 9.20485393,5.90431906 9.38717599,5.82385306 L9.49484664,5.80114977 C9.65041313,5.71688974 9.7492905,5.55401473 9.75,5.3775 L9.75,5.25 C9.75,4.00735931 10.7573593,3 12,3 C13.2426407,3 14.25,4.00735931 14.25,5.25 L14.249994,5.31450877 C14.250769,5.50881236 14.366855,5.68410223 14.552824,5.76385306 C14.7351461,5.84431906 14.948087,5.80570889 15.0846699,5.67216991 L15.129375,5.62746499 C15.5514036,5.20496665 16.1240792,4.96757002 16.72125,4.96757002 C17.3184208,4.96757002 17.8910964,5.20496665 18.312535,5.626875 C18.7350334,6.04890358 18.97243,6.62157919 18.97243,7.21875 C18.97243,7.81592081 18.7350334,8.38859642 18.3128301,8.81033009 L18.2736469,8.84944872 C18.1342911,8.99191298 18.0956809,9.20485393 18.1761469,9.38717599 L18.1988502,9.49484664 C18.2831103,9.65041313 18.4459853,9.7492905 18.6225,9.75 Z"
                                                                                fill="#000000" fill-rule="nonzero"
                                                                                opacity="0.3"/>
																			<path
                                                                                d="M12,15 C13.6568542,15 15,13.6568542 15,12 C15,10.3431458 13.6568542,9 12,9 C10.3431458,9 9,10.3431458 9,12 C9,13.6568542 10.3431458,15 12,15 Z"
                                                                                fill="#000000"/>
																		</svg>
																	</span>
                                            <!--end::Svg Icon-->
                                        </div>
                                        <!--end::Preferences toggle-->
                                        <!--begin::Advanced search toggle-->
                                        <div
                                            class="btn btn-icon w-20px btn-sm btn-active-color-primary"
                                            data-bs-toggle="tooltip"
                                            data-kt-search-element="advanced-options-form-show"
                                            title="Show more search options">
                                            <!--begin::Svg Icon | path: icons/duotone/Navigation/Angle-down.svg-->
                                            <span class="svg-icon svg-icon-2">
																		<svg height="24px"
                                                                             version="1.1" viewBox="0 0 24 24"
                                                                             width="24px"
                                                                             xmlns="http://www.w3.org/2000/svg">
																			<g fill="none" fill-rule="evenodd"
                                                                               stroke="none" stroke-width="1">
																				<polygon points="0 0 24 0 24 24 0 24"/>
																				<path
                                                                                    d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z"
                                                                                    fill="#000000" fill-rule="nonzero"
                                                                                    transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"/>
																			</g>
																		</svg>
																	</span>
                                            <!--end::Svg Icon-->
                                        </div>
                                        <!--end::Advanced search toggle-->
                                    </div>
                                    <!--end::Toolbar-->
                                </form>
                                <!--end::Form-->
                                <!--begin::Separator-->
                                <div class="separator border-gray-200 mb-6"></div>
                                <!--end::Separator-->
                                <!--begin::Recently viewed-->
                                <div class="d-none" data-kt-search-element="results">
                                    <!--begin::Items-->
                                    <div class="scroll-y mh-200px mh-lg-350px">
                                        <!--begin::Category title-->
                                        <h3 class="fs-5 text-muted m-0 pb-5"
                                            data-kt-search-element="category-title">Users</h3>
                                        <!--end::Category title-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
                                                <img alt=""
                                                     src="templates/dashboard/assets/media/avatars/150-1.jpg"/>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Karina Clark</span>
                                                <span class="fs-7 fw-bold text-muted">Marketing Manager</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
                                                <img alt=""
                                                     src="templates/dashboard/assets/media/avatars/150-3.jpg"/>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Olivia Bold</span>
                                                <span class="fs-7 fw-bold text-muted">Software Engineer</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
                                                <img alt=""
                                                     src="templates/dashboard/assets/media/avatars/150-8.jpg"/>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Ana Clark</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">UI/UX Designer</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
                                                <img alt=""
                                                     src="templates/dashboard/assets/media/avatars/150-11.jpg"/>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Nick Pitola</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">Art Director</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
                                                <img alt=""
                                                     src="templates/dashboard/assets/media/avatars/150-12.jpg"/>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Edward Kulnic</span>
                                                <span class="fs-7 fw-bold text-muted">System Administrator</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Category title-->
                                        <h3 class="fs-5 text-muted m-0 pt-5 pb-5"
                                            data-kt-search-element="category-title">Customers</h3>
                                        <!--end::Category title-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<img alt=""
                                                                                 class="w-20px h-20px"
                                                                                 src="templates/dashboard/assets/media/svg/brand-logos/volicity-9.svg"/>
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">Company Rbranding</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">UI Design</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<img alt=""
                                                                                 class="w-20px h-20px"
                                                                                 src="templates/dashboard/assets/media/svg/brand-logos/tvit.svg"/>
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                                    <span
                                                                        class="fs-6 fw-bold">Company Re-branding</span>
                                                <span class="fs-7 fw-bold text-muted">Web Development</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<img alt=""
                                                                                 class="w-20px h-20px"
                                                                                 src="templates/dashboard/assets/media/svg/misc/infography.svg"/>
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                                    <span
                                                                        class="fs-6 fw-bold">Business Analytics App</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">Administration</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<img alt=""
                                                                                 class="w-20px h-20px"
                                                                                 src="templates/dashboard/assets/media/svg/brand-logos/leaf.svg"/>
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                <span class="fs-6 fw-bold">EcoLeaf App Launch</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">Marketing</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<img alt=""
                                                                                 class="w-20px h-20px"
                                                                                 src="templates/dashboard/assets/media/svg/brand-logos/tower.svg"/>
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div
                                                class="d-flex flex-column justify-content-start fw-bold">
                                                                    <span
                                                                        class="fs-6 fw-bold">Tower Group Website</span>
                                                <span
                                                    class="fs-7 fw-bold text-muted">Google Adwords</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Category title-->
                                        <h3 class="fs-5 text-muted m-0 pt-5 pb-5"
                                            data-kt-search-element="category-title">Projects</h3>
                                        <!--end::Category title-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Communication/Clipboard-list.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg height="24px"
                                                                                     version="1.1" viewBox="0 0 24 24"
                                                                                     width="24px"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M8,3 L8,3.5 C8,4.32842712 8.67157288,5 9.5,5 L14.5,5 C15.3284271,5 16,4.32842712 16,3.5 L16,3 L18,3 C19.1045695,3 20,3.8954305 20,5 L20,21 C20,22.1045695 19.1045695,23 18,23 L6,23 C4.8954305,23 4,22.1045695 4,21 L4,5 C4,3.8954305 4.8954305,3 6,3 L8,3 Z"
                                                                                        fill="#000000" opacity="0.3"/>
																					<path
                                                                                        d="M11,2 C11,1.44771525 11.4477153,1 12,1 C12.5522847,1 13,1.44771525 13,2 L14.5,2 C14.7761424,2 15,2.22385763 15,2.5 L15,3.5 C15,3.77614237 14.7761424,4 14.5,4 L9.5,4 C9.22385763,4 9,3.77614237 9,3.5 L9,2.5 C9,2.22385763 9.22385763,2 9.5,2 L11,2 Z"
                                                                                        fill="#000000"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="7"
                                                                                          x="10" y="9"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="2"
                                                                                          x="7" y="9"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="2"
                                                                                          x="7" y="13"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="7"
                                                                                          x="10" y="13"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="2"
                                                                                          x="7" y="17"/>
																					<rect fill="#000000" height="2"
                                                                                          opacity="0.3" rx="1" width="7"
                                                                                          x="10" y="17"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <span class="fs-6 fw-bold">Si-Fi Project by AU Themes</span>
                                                <span class="fs-7 fw-bold text-muted">#45670</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Media/Equalizer.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg height="24px"
                                                                                     version="1.1" viewBox="0 0 24 24"
                                                                                     width="24px"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<g fill="none" fill-rule="evenodd"
                                                                                       stroke="none" stroke-width="1">
																						<rect height="24" width="24"
                                                                                              x="0"
                                                                                              y="0"/>
																						<rect fill="#000000"
                                                                                              height="16" opacity="0.3"
                                                                                              rx="1.5"
                                                                                              width="3" x="13"
                                                                                              y="4"/>
																						<rect fill="#000000" height="11"
                                                                                              rx="1.5"
                                                                                              width="3" x="8"
                                                                                              y="9"/>
																						<rect fill="#000000" height="9"
                                                                                              rx="1.5" width="3"
                                                                                              x="18" y="11"/>
																						<rect fill="#000000" height="7"
                                                                                              rx="1.5" width="3"
                                                                                              x="3" y="13"/>
																					</g>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <span class="fs-6 fw-bold">Shopix Mobile App Planning</span>
                                                <span class="fs-7 fw-bold text-muted">#45690</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Communication/Group-chat.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg height="24px"
                                                                                     version="1.1" viewBox="0 0 24 24"
                                                                                     width="24px"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M16,15.6315789 L16,12 C16,10.3431458 14.6568542,9 13,9 L6.16183229,9 L6.16183229,5.52631579 C6.16183229,4.13107011 7.29290239,3 8.68814808,3 L20.4776218,3 C21.8728674,3 23.0039375,4.13107011 23.0039375,5.52631579 L23.0039375,13.1052632 L23.0206157,17.786793 C23.0215995,18.0629336 22.7985408,18.2875874 22.5224001,18.2885711 C22.3891754,18.2890457 22.2612702,18.2363324 22.1670655,18.1421277 L19.6565168,15.6315789 L16,15.6315789 Z"
                                                                                        fill="#000000"/>
																					<path
                                                                                        d="M1.98505595,18 L1.98505595,13 C1.98505595,11.8954305 2.88048645,11 3.98505595,11 L11.9850559,11 C13.0896254,11 13.9850559,11.8954305 13.9850559,13 L13.9850559,18 C13.9850559,19.1045695 13.0896254,20 11.9850559,20 L4.10078614,20 L2.85693427,21.1905292 C2.65744295,21.3814685 2.34093638,21.3745358 2.14999706,21.1750444 C2.06092565,21.0819836 2.01120804,20.958136 2.01120804,20.8293182 L2.01120804,18.32426 C1.99400175,18.2187196 1.98505595,18.1104045 1.98505595,18 Z M6.5,14 C6.22385763,14 6,14.2238576 6,14.5 C6,14.7761424 6.22385763,15 6.5,15 L11.5,15 C11.7761424,15 12,14.7761424 12,14.5 C12,14.2238576 11.7761424,14 11.5,14 L6.5,14 Z M9.5,16 C9.22385763,16 9,16.2238576 9,16.5 C9,16.7761424 9.22385763,17 9.5,17 L11.5,17 C11.7761424,17 12,16.7761424 12,16.5 C12,16.2238576 11.7761424,16 11.5,16 L9.5,16 Z"
                                                                                        fill="#000000" opacity="0.3"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <span class="fs-6 fw-bold">Finance Monitoring SAAS Discussion</span>
                                                <span class="fs-7 fw-bold text-muted">#21090</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <a class="d-flex text-dark text-hover-primary align-items-center mb-5"
                                           href="#">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/General/User.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg height="24px"
                                                                                     version="1.1" viewBox="0 0 24 24"
                                                                                     width="24px"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<g fill="none" fill-rule="evenodd"
                                                                                       stroke="none" stroke-width="1">
																						<polygon
                                                                                            points="0 0 24 0 24 24 0 24"/>
																						<path
                                                                                            d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z"
                                                                                            fill="#000000"
                                                                                            fill-rule="nonzero"
                                                                                            opacity="0.3"/>
																						<path
                                                                                            d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z"
                                                                                            fill="#000000"
                                                                                            fill-rule="nonzero"/>
																					</g>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <span class="fs-6 fw-bold">Dashboard Analitics Launch</span>
                                                <span class="fs-7 fw-bold text-muted">#34560</span>
                                            </div>
                                            <!--end::Title-->
                                        </a>
                                        <!--end::Item-->
                                    </div>
                                    <!--end::Items-->
                                </div>
                                <!--end::Recently viewed-->
                                <!--begin::Recently viewed-->
                                <div class="mb-4" data-kt-search-element="main">
                                    <!--begin::Heading-->
                                    <div class="d-flex flex-stack fw-bold mb-4">
                                        <!--begin::Label-->
                                        <span class="text-muted fs-6 me-2">Recently Searched:</span>
                                        <!--end::Label-->
                                    </div>
                                    <!--end::Heading-->
                                    <!--begin::Items-->
                                    <div class="scroll-y mh-200px mh-lg-325px">
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Monitor.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<g opacity="0.25">
																						<path
                                                                                            d="M2 15C2 16.6569 3.34315 18 5 18L19 18C20.6569 18 22 16.6569 22 15V5C22 3.34315 20.6569 2 19 2H5C3.34315 2 2 3.34315 2 5V15Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M8 20C7.44772 20 7 20.4477 7 21C7 21.5523 7.44772 22 8 22H16C16.5523 22 17 21.5523 17 21C17 20.4477 16.5523 20 16 20H8Z"
                                                                                            fill="#12131A"/>
																					</g>
																					<path
                                                                                        d="M7 6C6.44772 6 6 6.44772 6 7C6 7.55228 6.44772 8 7 8H14C14.5523 8 15 7.55228 15 7C15 6.44772 14.5523 6 14 6H7Z"
                                                                                        fill="#12131A"/>
																					<path
                                                                                        d="M7 10C6.44772 10 6 10.4477 6 11C6 11.5523 6.44772 12 7 12H9C9.55229 12 10 11.5523 10 11C10 10.4477 9.55229 10 9 10H7Z"
                                                                                        fill="#12131A"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">BoomApp
                                                    by Keenthemes</a>
                                                <span class="fs-7 text-muted fw-bold">#45789</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Scatter-Up.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<g opacity="0.25">
																						<path
                                                                                            d="M20 13C20.5523 13 21 12.5523 21 12C21 11.4477 20.5523 11 20 11C19.4477 11 19 11.4477 19 12C19 12.5523 19.4477 13 20 13Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M20 17C20.5523 17 21 16.5523 21 16C21 15.4477 20.5523 15 20 15C19.4477 15 19 15.4477 19 16C19 16.5523 19.4477 17 20 17Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M20 21C20.5523 21 21 20.5523 21 20C21 19.4477 20.5523 19 20 19C19.4477 19 19 19.4477 19 20C19 20.5523 19.4477 21 20 21Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M12 17C12.5523 17 13 16.5523 13 16C13 15.4477 12.5523 15 12 15C11.4477 15 11 15.4477 11 16C11 16.5523 11.4477 17 12 17Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M12 21C12.5523 21 13 20.5523 13 20C13 19.4477 12.5523 19 12 19C11.4477 19 11 19.4477 11 20C11 20.5523 11.4477 21 12 21Z"
                                                                                            fill="#12131A"/>
																						<path
                                                                                            d="M4 21C4.55228 21 5 20.5523 5 20C5 19.4477 4.55228 19 4 19C3.44772 19 3 19.4477 3 20C3 20.5523 3.44772 21 4 21Z"
                                                                                            fill="#12131A"/>
																					</g>
																					<path
                                                                                        d="M17 6C17 7.65685 18.3431 9 20 9C21.6569 9 23 7.65685 23 6C23 4.34315 21.6569 3 20 3C18.3431 3 17 4.34315 17 6Z"
                                                                                        fill="#12131A"/>
																					<path
                                                                                        d="M9 10C9 11.6569 10.3431 13 12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10Z"
                                                                                        fill="#12131A"/>
																					<path
                                                                                        d="M4 17C2.34315 17 1 15.6569 1 14C1 12.3431 2.34315 11 4 11C5.65685 11 7 12.3431 7 14C7 15.6569 5.65685 17 4 17Z"
                                                                                        fill="#12131A"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">"Kept
                                                    API Project Meeting</a>
                                                <span class="fs-7 text-muted fw-bold">#84050</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Doughnut.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path clip-rule="evenodd"
                                                                                          d="M11 4.25769C11 3.07501 9.9663 2.13515 8.84397 2.50814C4.86766 3.82961 2 7.57987 2 11.9999C2 13.6101 2.38057 15.1314 3.05667 16.4788C3.58731 17.5363 4.98303 17.6028 5.81966 16.7662L5.91302 16.6728C6.60358 15.9823 6.65613 14.9011 6.3341 13.9791C6.11766 13.3594 6 12.6934 6 11.9999C6 9.62064 7.38488 7.56483 9.39252 6.59458C10.2721 6.16952 11 5.36732 11 4.39046V4.25769ZM16.4787 20.9434C17.5362 20.4127 17.6027 19.017 16.7661 18.1804L16.6727 18.087C15.9822 17.3964 14.901 17.3439 13.979 17.6659C13.3594 17.8823 12.6934 17.9999 12 17.9999C11.3066 17.9999 10.6406 17.8823 10.021 17.6659C9.09899 17.3439 8.01784 17.3964 7.3273 18.087L7.23392 18.1804C6.39728 19.017 6.4638 20.4127 7.52133 20.9434C8.86866 21.6194 10.3899 21.9999 12 21.9999C13.6101 21.9999 15.1313 21.6194 16.4787 20.9434Z"
                                                                                          fill="#12131A"
                                                                                          fill-rule="evenodd"
                                                                                          opacity="0.25"/>
																					<path clip-rule="evenodd"
                                                                                          d="M13 4.39046C13 5.36732 13.7279 6.16952 14.6075 6.59458C16.6151 7.56483 18 9.62064 18 11.9999C18 12.6934 17.8823 13.3594 17.6659 13.9791C17.3439 14.9011 17.3964 15.9823 18.087 16.6728L18.1803 16.7662C19.017 17.6028 20.4127 17.5363 20.9433 16.4788C21.6194 15.1314 22 13.6101 22 11.9999C22 7.57987 19.1323 3.82961 15.156 2.50814C14.0337 2.13515 13 3.07501 13 4.25769V4.39046Z"
                                                                                          fill="#12131A"
                                                                                          fill-rule="evenodd"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">"KPI
                                                    Monitoring App Launch</a>
                                                <span class="fs-7 text-muted fw-bold">#84250</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Stacked-Area-Down.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M2 13.8857C2 13.1875 2.69737 12.7042 3.35112 12.9493L8.14677 14.7477C8.64016 14.9327 9.17357 14.9845 9.69334 14.8979L14.6354 14.0742C14.8087 14.0453 14.9865 14.0626 15.151 14.1243L21.3511 16.4493C21.7414 16.5957 22 16.9688 22 17.3857V20C22 21.1046 21.1046 22 20 22H4C2.89543 22 2 21.1046 2 20V13.8857Z"
                                                                                        fill="#12131A"
                                                                                        opacity="0.25"/>
																					<path
                                                                                        d="M2 4.13517C2 2.4395 3.97771 1.51318 5.28037 2.59873L8.93565 5.64479C9.1593 5.83117 9.45306 5.91083 9.74023 5.86296L14.0555 5.14376C14.8073 5.01846 15.5786 5.18401 16.2128 5.60679L20.6641 8.57435C21.4987 9.13074 22 10.0674 22 11.0705V13.1138C22 13.812 21.3026 14.2953 20.6489 14.0501L15.8532 12.2518C15.3598 12.0667 14.8264 12.0149 14.3067 12.1016L9.36454 12.9253C9.19129 12.9541 9.01348 12.9369 8.84902 12.8752L2.64888 10.5501C2.25857 10.4038 2 10.0307 2 9.61381V4.13517Z"
                                                                                        fill="#12131A"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">Project
                                                    Reference FAQ</a>
                                                <span class="fs-7 text-muted fw-bold">#67945</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Envelope.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M1 6C1 4.34315 2.34315 3 4 3H20C21.6569 3 23 4.34315 23 6V18C23 19.6569 21.6569 21 20 21H4C2.34315 21 1 19.6569 1 18V6Z"
                                                                                        fill="#191213"
                                                                                        opacity="0.25"/>
																					<path clip-rule="evenodd"
                                                                                          d="M5.23177 7.35984C5.58534 6.93556 6.2159 6.87824 6.64018 7.2318L11.3598 11.1648C11.7307 11.4739 12.2693 11.4739 12.6402 11.1648L17.3598 7.2318C17.7841 6.87824 18.4147 6.93556 18.7682 7.35984C19.1218 7.78412 19.0645 8.41468 18.6402 8.76825L13.9205 12.7013C12.808 13.6284 11.192 13.6284 10.0794 12.7013L5.35981 8.76825C4.93553 8.41468 4.87821 7.78412 5.23177 7.35984Z"
                                                                                          fill="#121319"
                                                                                          fill-rule="evenodd"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">"FitPro
                                                    App Development</a>
                                                <span class="fs-7 text-muted fw-bold">#84250</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Bank.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M4 10H8V17H10V10H14V17H16V10H20V17C21.1046 17 22 17.8954 22 19V20C22 21.1046 21.1046 22 20 22H4C2.89543 22 2 21.1046 2 20V19C2 17.8954 2.89543 17 4 17V10Z"
                                                                                        fill="#12131A"
                                                                                        opacity="0.25"/>
																					<path
                                                                                        d="M2 7.35405C2 6.53624 2.4979 5.80083 3.25722 5.4971L11.2572 2.2971C11.734 2.10637 12.266 2.10637 12.7428 2.2971L20.7428 5.4971C21.5021 5.80083 22 6.53624 22 7.35405V7.99999C22 9.10456 21.1046 9.99999 20 9.99999H4C2.89543 9.99999 2 9.10456 2 7.99999V7.35405Z"
                                                                                        fill="#12131A"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">Shopix
                                                    Mobile App</a>
                                                <span class="fs-7 text-muted fw-bold">#45690</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <div class="d-flex align-items-center mb-5">
                                            <!--begin::Symbol-->
                                            <div class="symbol symbol-40px me-4">
																		<span class="symbol-label bg-light">
																			<!--begin::Svg Icon | path: icons/duotone/Interface/Line-03-Up.svg-->
																			<span
                                                                                class="svg-icon svg-icon-2 svg-icon-primary">
																				<svg fill="none"
                                                                                     height="24" viewBox="0 0 24 24"
                                                                                     width="24"
                                                                                     xmlns="http://www.w3.org/2000/svg">
																					<path
                                                                                        d="M1 5C1 3.89543 1.89543 3 3 3H21C22.1046 3 23 3.89543 23 5V19C23 20.1046 22.1046 21 21 21H3C1.89543 21 1 20.1046 1 19V5Z"
                                                                                        fill="#12131A"
                                                                                        opacity="0.25"/>
																					<path clip-rule="evenodd"
                                                                                          d="M20.8682 6.49631C21.1422 6.01679 20.9756 5.40594 20.4961 5.13193C20.0166 4.85792 19.4058 5.02451 19.1317 5.50403L15.8834 11.1886C15.6612 11.5775 15.2073 11.7712 14.7727 11.6626L9.71238 10.3975C8.40847 10.0715 7.04688 10.6526 6.38005 11.8195L3.13174 17.504C2.85773 17.9835 3.02433 18.5944 3.50385 18.8684C3.98337 19.1424 4.59422 18.9758 4.86823 18.4963L8.11653 12.8118C8.33881 12.4228 8.79268 12.2291 9.22731 12.3378L14.2876 13.6028C15.5915 13.9288 16.9531 13.3478 17.6199 12.1808L20.8682 6.49631Z"
                                                                                          fill="#12131A"
                                                                                          fill-rule="evenodd"/>
																				</svg>
																			</span>
                                                                            <!--end::Svg Icon-->
																		</span>
                                            </div>
                                            <!--end::Symbol-->
                                            <!--begin::Title-->
                                            <div class="d-flex flex-column">
                                                <a class="fs-6 text-gray-800 text-hover-primary fw-bold"
                                                   href="#">"Landing
                                                    UI Design" Launch</a>
                                                <span class="fs-7 text-muted fw-bold">#24005</span>
                                            </div>
                                            <!--end::Title-->
                                        </div>
                                        <!--end::Item-->
                                    </div>
                                    <!--end::Items-->
                                </div>
                                <!--end::Recently viewed-->
                                <!--begin::Empty-->
                                <div class="text-center d-none" data-kt-search-element="empty">
                                    <!--begin::Icon-->
                                    <div class="pt-10 pb-10">
                                        <!--begin::Svg Icon | path: icons/duotone/Interface/File-Search.svg-->
                                        <span class="svg-icon svg-icon-4x opacity-50">
																	<svg fill="none" height="24"
                                                                         viewBox="0 0 24 24" width="24"
                                                                         xmlns="http://www.w3.org/2000/svg">
																		<path
                                                                            d="M3 4C3 2.34315 4.34315 1 6 1H15.7574C16.553 1 17.3161 1.31607 17.8787 1.87868L20.1213 4.12132C20.6839 4.68393 21 5.44699 21 6.24264V20C21 21.6569 19.6569 23 18 23H6C4.34315 23 3 21.6569 3 20V4Z"
                                                                            fill="#12131A"
                                                                            opacity="0.25"/>
																		<path
                                                                            d="M15 1.89181C15 1.39927 15.3993 1 15.8918 1V1C16.6014 1 17.2819 1.28187 17.7836 1.78361L20.2164 4.21639C20.7181 4.71813 21 5.39863 21 6.10819V6.10819C21 6.60073 20.6007 7 20.1082 7H16C15.4477 7 15 6.55228 15 6V1.89181Z"
                                                                            fill="#12131A"/>
																		<path clip-rule="evenodd"
                                                                              d="M13.032 15.4462C12.4365 15.7981 11.7418 16 11 16C8.79086 16 7 14.2091 7 12C7 9.79086 8.79086 8 11 8C13.2091 8 15 9.79086 15 12C15 12.7418 14.7981 13.4365 14.4462 14.032L16.7072 16.293C17.0977 16.6835 17.0977 17.3167 16.7072 17.7072C16.3167 18.0977 15.6835 18.0977 15.293 17.7072L13.032 15.4462ZM13 12C13 13.1046 12.1046 14 11 14C9.89543 14 9 13.1046 9 12C9 10.8954 9.89543 10 11 10C12.1046 10 13 10.8954 13 12Z"
                                                                              fill="#12131A"
                                                                              fill-rule="evenodd"/>
																	</svg>
																</span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <!--end::Icon-->
                                    <!--begin::Message-->
                                    <div class="pb-15 fw-bold">
                                        <h3 class="text-gray-600 fs-5 mb-2">No result found</h3>
                                        <div class="text-muted fs-7">Please try again with a
                                            different query
                                        </div>
                                    </div>
                                    <!--end::Message-->
                                </div>
                                <!--end::Empty-->
                            </div>
                            <!--end::Wrapper-->
                            <!--begin::Preferences-->
                            <form class="pt-1 d-none"
                                  data-kt-search-element="advanced-options-form">
                                <!--begin::Heading-->
                                <h3 class="fw-bold text-dark mb-7">Advanced Search</h3>
                                <!--end::Heading-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <input class="form-control form-control-sm form-control-solid"
                                           name="query"
                                           placeholder="Contains the word" type="text"/>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <!--begin::Radio group-->
                                    <div class="nav-group nav-group-fluid">
                                        <!--begin::Option-->
                                        <label>
                                            <input checked="checked" class="btn-check" name="type"
                                                   type="radio" value="has"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary">All</span>
                                        </label>
                                        <!--end::Option-->
                                        <!--begin::Option-->
                                        <label>
                                            <input class="btn-check" name="type" type="radio"
                                                   value="users"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary px-4">Users</span>
                                        </label>
                                        <!--end::Option-->
                                        <!--begin::Option-->
                                        <label>
                                            <input class="btn-check" name="type" type="radio"
                                                   value="orders"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary px-4">Orders</span>
                                        </label>
                                        <!--end::Option-->
                                        <!--begin::Option-->
                                        <label>
                                            <input class="btn-check" name="type" type="radio"
                                                   value="projects"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary px-4">Projects</span>
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Radio group-->
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <input class="form-control form-control-sm form-control-solid"
                                           name="assignedto"
                                           placeholder="Assigned to"
                                           type="text" value=""/>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <input class="form-control form-control-sm form-control-solid"
                                           name="collaborators"
                                           placeholder="Collaborators"
                                           type="text" value=""/>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <!--begin::Radio group-->
                                    <div class="nav-group nav-group-fluid">
                                        <!--begin::Option-->
                                        <label>
                                            <input checked="checked" class="btn-check"
                                                   name="attachment"
                                                   type="radio" value="has"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary">Has attachment</span>
                                        </label>
                                        <!--end::Option-->
                                        <!--begin::Option-->
                                        <label>
                                            <input class="btn-check" name="attachment" type="radio"
                                                   value="any"/>
                                            <span
                                                class="btn btn-sm btn-color-muted btn-active btn-active-primary px-4">Any</span>
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Radio group-->
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <select aria-label="Select a Timezone"
                                            class="form-select form-select-sm form-select-solid"
                                            data-control="select2" data-placeholder="date_period"
                                            name="timezone">
                                        <option value="next">Within the next</option>
                                        <option value="last">Within the last</option>
                                        <option value="between">Between</option>
                                        <option value="on">On</option>
                                    </select>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="row mb-8">
                                    <!--begin::Col-->
                                    <div class="col-6">
                                        <input
                                            class="form-control form-control-sm form-control-solid"
                                            name="date_number"
                                            placeholder="Lenght"
                                            type="number" value=""/>
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-6">
                                        <select aria-label="Select a Timezone"
                                                class="form-select form-select-sm form-select-solid"
                                                data-control="select2" data-placeholder="Period"
                                                name="date_typer">
                                            <option value="days">Days</option>
                                            <option value="weeks">Weeks</option>
                                            <option value="months">Months</option>
                                            <option value="years">Years</option>
                                        </select>
                                    </div>
                                    <!--end::Col-->
                                </div>
                                <!--end::Input group-->
                                <!--begin::Actions-->
                                <div class="d-flex justify-content-end">
                                    <button
                                        class="btn btn-sm btn-light fw-bolder btn-active-light-primary me-2"
                                        data-kt-search-element="advanced-options-form-cancel"
                                        type="reset">
                                        Cancel
                                    </button>
                                    <a class="btn btn-sm fw-bolder btn-primary"
                                       data-kt-search-element="advanced-options-form-search"
                                       href="../../demo1/dist/pages/search/horizontal.html">Search</a>
                                </div>
                                <!--end::Actions-->
                            </form>
                            <!--end::Preferences-->
                            <!--begin::Preferences-->
                            <form class="pt-1 d-none" data-kt-search-element="preferences">
                                <!--begin::Heading-->
                                <h3 class="fw-bold text-dark mb-7">Search Preferences</h3>
                                <!--end::Heading-->
                                <!--begin::Input group-->
                                <div class="pb-4 border-bottom">
                                    <label
                                        class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack">
                                                            <span
                                                                class="form-check-label text-gray-700 fs-6 fw-bold ms-0 me-2">Projects</span>
                                        <input checked="checked" class="form-check-input"
                                               type="checkbox"
                                               value="1"/>
                                    </label>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="py-4 border-bottom">
                                    <label
                                        class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack">
                                                            <span
                                                                class="form-check-label text-gray-700 fs-6 fw-bold ms-0 me-2">Targets</span>
                                        <input checked="checked" class="form-check-input"
                                               type="checkbox"
                                               value="1"/>
                                    </label>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="py-4 border-bottom">
                                    <label
                                        class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack">
                                                            <span
                                                                class="form-check-label text-gray-700 fs-6 fw-bold ms-0 me-2">Affiliate Programs</span>
                                        <input class="form-check-input" type="checkbox" value="1"/>
                                    </label>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="py-4 border-bottom">
                                    <label
                                        class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack">
                                                            <span
                                                                class="form-check-label text-gray-700 fs-6 fw-bold ms-0 me-2">Referrals</span>
                                        <input checked="checked" class="form-check-input"
                                               type="checkbox"
                                               value="1"/>
                                    </label>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="py-4 border-bottom">
                                    <label
                                        class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack">
                                                            <span
                                                                class="form-check-label text-gray-700 fs-6 fw-bold ms-0 me-2">Users</span>
                                        <input class="form-check-input" type="checkbox" value="1"/>
                                    </label>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Actions-->
                                <div class="d-flex justify-content-end pt-7">
                                    <button
                                        class="btn btn-sm btn-light fw-bolder btn-active-light-primary me-2"
                                        data-kt-search-element="preferences-dismiss"
                                        type="reset">Cancel
                                    </button>
                                    <button class="btn btn-sm fw-bolder btn-primary" type="submit">
                                        Save Changes
                                    </button>
                                </div>
                                <!--end::Actions-->
                            </form>
                            <!--end::Preferences-->
                        </div>
                        <!--end::Menu-->
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Search-->
                <!--begin::Activities-->
                <div class="d-flex align-items-center ms-1 ms-lg-3">
                    <!--begin::Drawer toggle-->
                    <div
                        id="kt_activities_toggle"
                        class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px">
                        <!--begin::Svg Icon | path: icons/duotone/Media/Equalizer.svg-->
                        <span class="svg-icon svg-icon-1">
													<svg height="24px"
                                                         version="1.1"
                                                         viewBox="0 0 24 24" width="24px"
                                                         xmlns="http://www.w3.org/2000/svg">
														<g fill="none" fill-rule="evenodd" stroke="none"
                                                           stroke-width="1">
															<rect height="24" width="24" x="0" y="0"/>
															<rect fill="#000000" height="16" opacity="0.3" rx="1.5"
                                                                  width="3"
                                                                  x="13" y="4"/>
															<rect fill="#000000" height="11" rx="1.5" width="3" x="8"
                                                                  y="9"/>
															<rect fill="#000000" height="9" rx="1.5" width="3" x="18"
                                                                  y="11"/>
															<rect fill="#000000" height="7" rx="1.5" width="3" x="3"
                                                                  y="13"/>
														</g>
													</svg>
												</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Drawer toggle-->
                </div>
                <!--end::Activities-->

                <!--begin::Chat-->
                <div class="d-flex align-items-center ms-1 ms-lg-3">
                    <!--begin::Menu wrapper-->
                    <div
                        id="kt_drawer_chat_toggle"
                        class="btn btn-icon btn-active-light-primary position-relative w-30px h-30px w-md-40px h-md-40px">
                        <!--begin::Svg Icon | path: icons/duotone/Communication/Group-chat.svg-->
                        <span class="svg-icon svg-icon-1">
													<svg height="24px" version="1.1" viewBox="0 0 24 24"
                                                         width="24px" xmlns="http://www.w3.org/2000/svg">
														<path
                                                            d="M16,15.6315789 L16,12 C16,10.3431458 14.6568542,9 13,9 L6.16183229,9 L6.16183229,5.52631579 C6.16183229,4.13107011 7.29290239,3 8.68814808,3 L20.4776218,3 C21.8728674,3 23.0039375,4.13107011 23.0039375,5.52631579 L23.0039375,13.1052632 L23.0206157,17.786793 C23.0215995,18.0629336 22.7985408,18.2875874 22.5224001,18.2885711 C22.3891754,18.2890457 22.2612702,18.2363324 22.1670655,18.1421277 L19.6565168,15.6315789 L16,15.6315789 Z"
                                                            fill="#000000"/>
														<path
                                                            d="M1.98505595,18 L1.98505595,13 C1.98505595,11.8954305 2.88048645,11 3.98505595,11 L11.9850559,11 C13.0896254,11 13.9850559,11.8954305 13.9850559,13 L13.9850559,18 C13.9850559,19.1045695 13.0896254,20 11.9850559,20 L4.10078614,20 L2.85693427,21.1905292 C2.65744295,21.3814685 2.34093638,21.3745358 2.14999706,21.1750444 C2.06092565,21.0819836 2.01120804,20.958136 2.01120804,20.8293182 L2.01120804,18.32426 C1.99400175,18.2187196 1.98505595,18.1104045 1.98505595,18 Z M6.5,14 C6.22385763,14 6,14.2238576 6,14.5 C6,14.7761424 6.22385763,15 6.5,15 L11.5,15 C11.7761424,15 12,14.7761424 12,14.5 C12,14.2238576 11.7761424,14 11.5,14 L6.5,14 Z M9.5,16 C9.22385763,16 9,16.2238576 9,16.5 C9,16.7761424 9.22385763,17 9.5,17 L11.5,17 C11.7761424,17 12,16.7761424 12,16.5 C12,16.2238576 11.7761424,16 11.5,16 L9.5,16 Z"
                                                            fill="#000000" opacity="0.3"/>
													</svg>
												</span>
                        <!--end::Svg Icon-->
                        <span
                            class="bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink"></span>
                    </div>
                    <!--end::Menu wrapper-->
                </div>
                <!--end::Chat-->


                <!--begin::User-->
                <div id="kt_header_user_menu_toggle2" class="d-flex align-items-center ms-1 ms-lg-3">
                    <!--begin::Menu wrapper-->
                    <div aria-expanded="false"
                         class="cursor-pointer symbol symbol-30px symbol-md-40px" data-bs-toggle="dropdown">
                        <img alt="metronic2222" src="templates/dashboard/assets/media/avatars/150-27.jpg"/>
                    </div>
                    <!--begin::Menu-->
                    <div
                        class="dropdown-menu menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-primary fw-bold py-4 fs-6 w-275px">
                        <!--begin::Menu item-->
                        <div class="menu-item px-3">
                            <div class="menu-content d-flex align-items-center px-3">
                                <!--begin::Avatar-->
                                <div class="symbol symbol-50px me-5">
                                    <img alt="Logo" src="templates/dashboard/assets/media/avatars/150-27.jpg"/>
                                </div>
                                <!--end::Avatar-->
                                <!--begin::Username-->
                                <div class="d-flex flex-column">
                                    <div class="fw-bolder d-flex align-items-center fs-5">{{ $page.props.auth.user.name }}
                                        <span class="badge badge-light-success fw-bolder fs-8 px-2 py-1 ms-2">Admin</span>
                                    </div>
                                    <a class="fw-bold text-muted text-hover-primary fs-7" href="#">{{ $page.props.auth.user.email }}</a>
                                </div>
                                <!--end::Username-->
                            </div>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu separator-->
                        <div class="separator my-2"></div>
                        <!--end::Menu separator-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <a class="menu-link px-5" href="/">My Profile</a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu separator-->
                        <div class="separator my-2"></div>
                        <!--end::Menu separator-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5 my-1">
                            <a class="menu-link px-5" href="/">Account Settings</a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu separator-->
                        <div class="separator my-2"></div>
                        <!--end::Menu separator-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <!--
                            <a class="menu-link px-5" href="#">Sign Out</a>
                            -->
                            <Link :href="route('cms.logout')" as="button" class="menu-link px-5 cus-btn-signout"
                                  method="post">Sign
                                Out
                            </Link>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu separator-->
                        <!--
                        <div class="separator my-2"></div>
                        -->
                        <!--end::Menu separator-->
                    </div>
                    <!--end::Menu-->
                    <!--end::Menu wrapper-->
                </div>
                <!--end::User-->

                <!--begin::Heaeder menu toggle-->
                <div class="d-flex align-items-center d-lg-none ms-2 me-n3"
                     title="Show header menu">
                    <div
                        id="kt_header_menu_mobile_toggle"
                        class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px">
                        <!--begin::Svg Icon | path: icons/duotone/Text/Toggle-Right.svg-->
                        <span class="svg-icon svg-icon-1">
                            <svg height="24px"
                                 version="1.1"
                                 viewBox="0 0 24 24" width="24px"
                                 xmlns="http://www.w3.org/2000/svg">
                                <g fill="none" fill-rule="evenodd" stroke="none"
                                   stroke-width="1">
                                    <rect height="24" width="24" x="0" y="0"/>
                                    <path clip-rule="evenodd"
                                          d="M22 11.5C22 12.3284 21.3284 13 20.5 13H3.5C2.6716 13 2 12.3284 2 11.5C2 10.6716 2.6716 10 3.5 10H20.5C21.3284 10 22 10.6716 22 11.5Z"
                                          fill="black"
                                          fill-rule="evenodd"/>
                                    <path clip-rule="evenodd"
                                          d="M14.5 20C15.3284 20 16 19.3284 16 18.5C16 17.6716 15.3284 17 14.5 17H3.5C2.6716 17 2 17.6716 2 18.5C2 19.3284 2.6716 20 3.5 20H14.5ZM8.5 6C9.3284 6 10 5.32843 10 4.5C10 3.67157 9.3284 3 8.5 3H3.5C2.6716 3 2 3.67157 2 4.5C2 5.32843 2.6716 6 3.5 6H8.5Z"
                                          fill="black"
                                          fill-rule="evenodd"
                                          opacity="0.5"/>
                                </g>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                </div>
                <!--end::Heaeder menu toggle-->
            </div>
            <!--end::Toolbar wrapper-->
        </div>
        <!--end::Topbar-->
    </div>
</template>

<style scoped>
.cus-btn-signout {
    cursor: pointer;
    background-color: unset !important;
    border: unset !important;
    font-weight: 500;
}
</style>

