<script setup>
import {loadScript} from '../Utils/loadScript';
import {onMounted} from "vue";

onMounted(async () => {
    try {
        await loadScript('../../../../templates/dashboard/assets/plugins/global/plugins.bundle.js', {defer: 'true'});
        await loadScript('../../../../templates/dashboard/assets/js/scripts.bundle.js', {defer: 'true'});
        // await loadScript('../../../../templates/dashboard/assets/js/custom/authentication/sign-in/general.js', {defer: 'true'});
    } catch (error) {
        console.error(error);
    }
});
</script>

<template>
    <!--begin::Main-->
    <div class="d-flex flex-column flex-root" style="height: 100vh;">
        <!--begin::Authentication - Sign-in -->
        <div class="d-flex flex-column flex-lg-row flex-column-fluid">
            <!--begin::Aside-->
            <div class="d-flex flex-column flex-lg-row-auto w-xl-600px positon-xl-relative"
                 style="background-color: #7ad474">
                <!--begin::Wrapper-->
                <div class="d-flex flex-column position-xl-fixed top-0 bottom-0 w-xl-600px scroll-y">
                    <!--begin::Content-->
                    <div class="d-flex flex-row-fluid flex-column text-center p-10 pt-lg-20">
                        <!--begin::Logo-->
                        <a class="py-9" href="/">
                            <img alt="Logo" class="h-70px" src="templates/dashboard/assets/media/logos/top_pokee_logo.png"/>
                        </a>
                        <!--end::Logo-->
                        <!--begin::Title-->
                        <h1 class="fw-bolder fs-2qx pb-5 pb-md-10" style="color: #ffffff;">Welcome to TopPoker</h1>
                        <!--end::Title-->
                        <!--begin::Description-->
                        <p class="fw-bold fs-2" style="color: #ffffff;">Discover Amazing TopPoker
                            <br/>with great build tools</p>
                        <!--end::Description-->
                    </div>
                    <!--end::Content-->
                    <!--begin::Illustration-->
                    <div
                        class="d-flex flex-row-auto bgi-no-repeat bgi-position-x-center bgi-size-contain bgi-position-y-bottom min-h-100px min-h-lg-350px"
                        style="background-image: url(templates/dashboard/assets/media/illustrations/checkout.png)"></div>
                    <!--end::Illustration-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Aside-->
            <!--begin::Body-->
            <slot/>
            <!--end::Body-->
        </div>
        <!--end::Authentication - Sign-in-->
    </div>
    <!--end::Main-->
</template>
