<script setup>
// import {ElMessage} from 'element-plus'
import {ArrowDown} from '@element-plus/icons-vue'

defineProps({
    row: Object,
    // định nghĩa kích thước cho button
    buttonSize: {
        type: String,
        default: 'default'
    },
    buttonName: {
        type: String,
        default: 'Actions'
    },
    isPlain: {
        type: Boolean,
        default: true
    },
    buttonType: {
        type: String,
        default: 'primary'
    }
});

const emit = defineEmits(['cb:action'])

const handleCommand = (action, row) => {
    console.log('DropdownButton >> action: ', action, 'row: ', row)
    // ElMessage(`click on item ${action}`)
    emit('cb:action', {action, row})
}
</script>

<template>
    <el-dropdown @command="(action) => handleCommand(action, row)">
        <el-button :plain="isPlain"
                   :size="buttonSize || 'default'"
                   :type="buttonType"
        >
            {{ buttonName }}
            <el-icon class="el-icon--right">
                <arrow-down/>
            </el-icon>
        </el-button>
        <template #dropdown>
            <!--            <el-dropdown-menu>
                            <el-dropdown-item>Edit</el-dropdown-item>
                            <el-dropdown-item>Delete</el-dropdown-item>
                        </el-dropdown-menu>-->
            <slot/>
        </template>
    </el-dropdown>
</template>
