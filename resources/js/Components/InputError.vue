<script setup>
defineProps({
    message: {
        type: String,
    },
});
</script>

<template>
    <!--    <div v-show="message">
            <p class="text-sm text-red-600 invalid-feedback">
                {{ message }}
            </p>
        </div>-->
    <div v-show="message" class="fv-plugins-message-container invalid-feedback">
        <div data-field="email" data-validator="notEmpty">{{ message }}</div>
    </div>
</template>
