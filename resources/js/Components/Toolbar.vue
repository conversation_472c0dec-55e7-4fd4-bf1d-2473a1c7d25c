<!-- resources/js/Components/Toolbar.vue -->
<script setup>
import ThirdButton from "@/Components/ThirdButton.vue";
import {Link} from '@inertiajs/vue3'
defineProps({
    title: {
        type: String,
        required: true,
    },
    breadcrumbs: {
        type: Array,
        required: true,
    },
    actions: {
        type: Array,
        required: true,
    },
});
</script>

<template>
    <!--begin::Toolbar-->
    <div id="kt_toolbar" class="toolbar">
        <!--begin::Container-->
        <div id="kt_toolbar_container" class="container-fluid d-flex flex-stack">
            <!--begin::Page title-->
            <div class="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
                <!--begin::Title-->
                <h1 class="d-flex align-items-center text-dark fw-bolder fs-3 my-1">{{ title }}</h1>
                <!--end::Title-->
                <!--begin::Separator-->
                <span class="h-20px border-gray-200 border-start mx-4"></span>
                <!--end::Separator-->
                <!--begin::Breadcrumb-->
                <ul class="breadcrumb breadcrumb-separatorless fw-bold fs-7 my-1">
                    <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="breadcrumb-item text-muted">
                        <a v-if="breadcrumb.href" :href="breadcrumb.href"
                           class="text-muted text-hover-primary"> {{ breadcrumb.text }}</a>
                        <span v-else>{{ breadcrumb.text }}</span>
                        <span v-if="index < breadcrumbs.length - 1" class="bullet bg-gray-200 w-5px h-2px"></span>
                    </li>
                </ul>
                <!--end::Breadcrumb-->
            </div>
            <!--end::Page title-->
            <!--begin::Actions-->
            <div class="d-flex align-items-center py-1">
                <div v-for="(action, index) in actions" :key="index" class="me-4">
                    <template v-if="action.type === 'back'">
                        <Link :class="action.className" :href="action.href">
                            <ThirdButton :class-name="action.className" :name="action.name" :click="action.click">
                                <component :is="action.icon" />
                            </ThirdButton>
                        </Link>
                    </template>
                    <template v-else>
                        <ThirdButton :class-name="action.className" :name="action.name" :click="action.click">
                            <component :is="action.icon" class="svg-icon svg-icon-5 svg-icon-gray-500 me-1"/>
                        </ThirdButton>
                    </template>
                </div>
            </div>
            <!--end::Actions-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::Toolbar-->
</template>
