<script setup>
import { onMounted, ref } from 'vue';

const props = defineProps({
    contentClasses: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: 'Please input'
    },
    size: {
        type: String,
        default: 'default'
    }
});

const model = defineModel({
    type: String,
    required: true,
});

const input = ref(null);

/*onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});*/

// defineExpose({ focus: () => input.value.focus() });
</script>

<template>
    <!--
    <input
        class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
        v-model="model"
        ref="input"
    />
    -->
    <el-input v-model="model" :size="props.size" :class="contentClasses" :placeholder="placeholder" />
</template>
