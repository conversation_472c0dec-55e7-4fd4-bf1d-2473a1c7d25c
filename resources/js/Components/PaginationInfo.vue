<template>
    <div class="pagination-info">
        <span class="info-text">Hiển thị từ {{ from }} đến {{ to }} của {{ total }} bản ghi</span>
        <span class="info-text-short">{{ from }}-{{ to }}/{{ total }}</span>
    </div>
</template>

<script setup>
defineProps({
    from: {
        type: Number,
        required: true
    },
    to: {
        type: Number,
        required: true
    },
    total: {
        type: Number,
        required: true
    }
});
</script>

<style scoped>
.pagination-info {
    color: #5E6278;
    font-size: 0.9rem;
    white-space: nowrap;
    margin: 0 1rem;
}

.info-text-short {
    display: none;
}

@media (max-width: 767px) {
    .info-text {
        display: none;
    }

    .info-text-short {
        display: inline;
    }

    .pagination-info {
        margin: 0 0.5rem;
    }
}
</style>
