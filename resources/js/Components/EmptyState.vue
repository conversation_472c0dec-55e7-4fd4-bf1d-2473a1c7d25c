<script setup>
defineProps({
    title: {
        type: String,
        default: 'Không có dữ liệu'
    },
    description: {
        type: String,
        default: '<PERSON>hông tìm thấy bản ghi nào.'
    },
    icon: {
        type: String,
        default: 'fa fa-info-circle'
    }
});
</script>

<template>
    <div class="empty-state d-flex flex-column align-items-center justify-content-center p-10">
        <div class="empty-state-icon mb-5">
            <i :class="[icon, 'fa-5x', 'text-muted']"></i>
        </div>
        <h3 class="fs-2 fw-bold mb-3">{{ title }}</h3>
        <p class="text-muted fs-6 mb-7">{{ description }}</p>
    </div>
</template>

<style scoped>
.empty-state {
    min-height: 250px;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.empty-state-icon {
    opacity: 0.7;
}
</style>
