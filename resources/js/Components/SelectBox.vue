<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';

const props = defineProps({
    items: Array,
    contentClasses: {
        type: String,
        default: '',
    },
    multiple: {
        type: Boolean,
        default: false
    },
    placeholder: {
        type: String,
        default: 'Select'
    },
    size: {
        type: String,
        default: 'large'
    }
});

/*const model = defineModel({
    type: String,
    required: true,
});*/
const model = defineModel({
    type: [String, Array],
    required: true,
});

</script>

<template>
    <el-select
        v-model="model"
        :placeholder="props.placeholder"
        :size="props.size"
        :class="contentClasses"
        :multiple="props.multiple"
    >
        <el-option
            v-for="item in items"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
    </el-select>
</template>
