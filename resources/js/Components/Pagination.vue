<template>
    <div v-if="links.length > 3" class="d-flex justify-content-center">
        <nav aria-label="Page navigation">
            <ul class="pagination">
                <!-- Previous Button - Always Shown -->
                <li class="page-item" :class="{ 'disabled': !links[0].url }">
                    <Link v-if="links[0].url" :href="links[0].url" class="page-link">
                        <span>«</span>
                    </Link>
                    <span v-else class="page-link">«</span>
                </li>

                <!-- Page Links -->
                <li v-for="(link, key) in filteredLinks"
                    :key="key"
                    class="page-item"
                    :class="{ 'active': link.active, 'disabled': !link.url }">
                    <Link v-if="link.url"
                          :href="link.url"
                          class="page-link">
                          <span>{{ formatLabel(link.label) }}</span>
                    </Link>
                    <span v-else
                          class="page-link">
                          {{ formatLabel(link.label) }}
                    </span>
                </li>

                <!-- Next Button - Always Shown -->
                <li class="page-item" :class="{ 'disabled': !links[links.length - 1].url }">
                    <Link v-if="links[links.length - 1].url" :href="links[links.length - 1].url" class="page-link">
                        <span>»</span>
                    </Link>
                    <span v-else class="page-link">»</span>
                </li>
            </ul>
        </nav>
    </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    links: {
        type: Array,
        required: true,
    },
});

const formatLabel = (label) => {
    if (label === '&laquo; Previous' || label === '«' || label === '« Previous') {
        return '«';
    } else if (label === 'Next &raquo;' || label === '»' || label === 'Next »') {
        return '»';
    }
    return label;
};

// Create a filtered version of links for responsive display
// Exclude first and last (Previous/Next) and reduce intermediate links on mobile
const filteredLinks = computed(() => {
    if (props.links.length <= 3) return [];

    // Get inner links (exclude first and last elements which are prev/next)
    const innerLinks = props.links.slice(1, -1);

    // Determine active page and calculate visible page range
    const activeLinkIndex = innerLinks.findIndex(link => link.active);

    // If no active link is found for some reason, show all
    if (activeLinkIndex === -1) return innerLinks;

    // For most cases, show the active link and some context links
    let startIndex = Math.max(0, activeLinkIndex - 1);
    let endIndex = Math.min(innerLinks.length - 1, activeLinkIndex + 1);

    // Try to always show at least 3 page links if available
    while ((endIndex - startIndex + 1) < 3 && (startIndex > 0 || endIndex < innerLinks.length - 1)) {
        if (startIndex > 0) startIndex--;
        else if (endIndex < innerLinks.length - 1) endIndex++;
        else break;
    }

    return innerLinks.slice(startIndex, endIndex + 1);
});
</script>

<style scoped>
.pagination {
    margin-bottom: 0;
    gap: 0.25rem;
}

.page-item {
    margin: 0;
}

.page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
    line-height: 1.25;
    color: #5e6278;
    background-color: #f5f8fa;
    border: 1px solid #eff2f5;
    border-radius: 0.475rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    text-align: center;
    min-width: 2.25rem;
    font-weight: 500;
}

.page-link:hover {
    color: #009ef7;
    background-color: #e9f1f7;
    border-color: #e9f1f7;
}

.page-item.active .page-link {
    z-index: 3;
    color: #ffffff;
    background-color: #009ef7;
    border-color: #009ef7;
    box-shadow: 0 0.1rem 0.7rem rgba(0, 158, 247, 0.3);
}

.page-item.disabled .page-link {
    color: #b5b5c3;
    pointer-events: none;
    background-color: #f5f8fa;
    border-color: #eff2f5;
    opacity: 0.7;
}

@media (hover: hover) {
    .page-link:hover {
        transform: translateY(-1px);
    }
}

@media (max-width: 576px) {
    .page-link {
        padding: 0.4rem 0.65rem;
        font-size: 0.9rem;
        min-width: 2rem;
    }

    .pagination {
        gap: 0.15rem;
    }
}
</style>
