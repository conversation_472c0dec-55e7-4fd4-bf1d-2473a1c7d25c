<template>
    <el-dialog v-model="localVisible" align-center center :title="dialogHeader" width="500">
        <div class="d-flex flex-center mt-10 mb-10">
            <span class="text-center">
              {{ dialogContent }}
            </span>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button size="large" @click="localVisible = false">{{ btnCancelName }}</el-button>
                <el-button size="large" type="danger" @click.prevent="handleDelete">
                    {{ btnAgreeName }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import {ref, watch} from 'vue';

const props = defineProps({
    dialogHeader: {
        type: String,
        default: 'Warning'
    },
    dialogContent: {
        type: String,
        default: 'Are you sure you want to delete ?'
    },
    visible: Boolean,
    btnAgreeName: {
        type: String,
        default: 'Yes, delete!'
    },
    btnCancelName: {
        type: String,
        default: 'No, cancel'
    },
    // itemName: String
});

const emit = defineEmits(['update:visible', 'delete']);
const localVisible = ref(props.visible);

watch(localVisible, (newValue) => {
    emit('update:visible', newValue);
});

const handleDelete = () => {
    emit('delete');
    localVisible.value = false;
};
</script>

<!--<style scoped>
.dialog-footer {
    display: flex;
    justify-content: space-between;
}
</style>-->
