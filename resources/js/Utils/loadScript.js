export function loadScript(src, attributes = {}) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;

        // Apply attributes such as 'defer' or others
        Object.keys(attributes).forEach(key => {
            script.setAttribute(key, attributes[key]);
        });

        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));

        document.head.appendChild(script);
    });
}
