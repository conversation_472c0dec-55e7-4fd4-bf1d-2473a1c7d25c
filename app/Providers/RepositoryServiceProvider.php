<?php

namespace App\Providers;

use App\Models\Badge;
use App\Models\Faq;
use App\Models\Feedback;
use App\Models\LeaderBoard;
use App\Models\Mission;
use App\Models\Permission;
use App\Models\Player;
use App\Models\Role;
use App\Models\Shop;
use App\Models\User;
use App\Repositories\Badge\BadgeRepository;
use App\Repositories\Badge\CacheBadgeDecorator;
use App\Repositories\Badge\EloquentBadgeRepository;
use App\Repositories\Faq\CacheFaqDecorator;
use App\Repositories\Faq\EloquentFaqRepository;
use App\Repositories\Faq\FaqRepository;
use App\Repositories\Feedback\CacheFeedbackDecorator;
use App\Repositories\Feedback\EloquentFeedbackRepository;
use App\Repositories\Feedback\FeedbackRepository;
use App\Repositories\Leaderboard\CacheLeaderboardDecorator;
use App\Repositories\Leaderboard\EloquentLeaderboardRepository;
use App\Repositories\Leaderboard\LeaderboardRepository;
use App\Repositories\Mission\CacheMissionDecorator;
use App\Repositories\Mission\EloquentMissionRepository;
use App\Repositories\Mission\MissionRepository;
use App\Repositories\Permission\CachePermissionRepository;
use App\Repositories\Permission\EloquentPermissionRepository;
use App\Repositories\Permission\PermissionRepository;
use App\Repositories\Player\CachePlayerDecorator;
use App\Repositories\Player\EloquentPlayerRepository;
use App\Repositories\Player\PlayerRepository;
use App\Repositories\Role\CacheRoleDecorator;
use App\Repositories\Role\EloquentRoleRepository;
use App\Repositories\Role\RoleRepository;
use App\Repositories\Shop\CacheShopDecorator;
use App\Repositories\Shop\EloquentShopRepository;
use App\Repositories\Shop\ShopRepository;
use App\Repositories\User\CacheUserDecorator;
use App\Repositories\User\EloquentUserRepository;
use App\Repositories\User\UserRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public final function register(): void
    {
        // Bind UserRepository
        // ====================================================================================================
        $this->app->bind(UserRepository::class, function () {
            $repository = new EloquentUserRepository(new User());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheUserDecorator();
        });

        // Bind RoleRepository
        // ====================================================================================================
        $this->app->bind(RoleRepository::class, function () {
            $repository = new EloquentRoleRepository(new Role());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheRoleDecorator();
        });

        // Bind PermissionRepository
        // ====================================================================================================
        $this->app->bind(PermissionRepository::class, function () {
            $repository = new EloquentPermissionRepository(new Permission());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CachePermissionRepository();
        });

        // Bind PlayerRepository
        // ====================================================================================================
        $this->app->bind(PlayerRepository::class, function () {
            $repository = new EloquentPlayerRepository(new Player());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CachePlayerDecorator();
        });

        // Bind FaqRepository
        // ====================================================================================================
        $this->app->bind(FaqRepository::class, function () {
            $repository = new EloquentFaqRepository(new Faq());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheFaqDecorator();
        });

        // Bind BadgeRepository
        // ====================================================================================================
        $this->app->bind(BadgeRepository::class, function () {
            $repository = new EloquentBadgeRepository(new Badge());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheBadgeDecorator();
        });

        // Bind FeedbackRepository
        // ====================================================================================================
        $this->app->bind(FeedbackRepository::class, function () {
            $repository = new EloquentFeedbackRepository(new Feedback());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheFeedbackDecorator();
        });

        // Bind LeaderboardRepository
        // ====================================================================================================
        $this->app->bind(LeaderboardRepository::class, function () {
            $repository = new EloquentLeaderboardRepository(new LeaderBoard());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheLeaderboardDecorator();
        });

        // Bind MissionRepository
        // ====================================================================================================
        $this->app->bind(MissionRepository::class, function () {
            $repository = new EloquentMissionRepository(new Mission());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheMissionDecorator();
        });

        // Bind ShopRepository
        // ====================================================================================================
        $this->app->bind(ShopRepository::class, function () {
            $repository = new EloquentShopRepository(new Shop());
            if (config('app.cache') === false) {
                return $repository;
            }
            return new CacheShopDecorator($repository);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public final function boot(): void
    {
        //
    }
}
