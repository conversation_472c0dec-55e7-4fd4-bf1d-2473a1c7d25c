<?php

namespace App\Providers;

use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(UrlGenerator $url): void
    {
        $env = config('app.env');
        if ($env === 'development') {
            $url->forceScheme('https');
            $this->app['request']->server->set('HTTPS', 'on');
        }
        Schema::defaultStringLength(191);
    }
}
