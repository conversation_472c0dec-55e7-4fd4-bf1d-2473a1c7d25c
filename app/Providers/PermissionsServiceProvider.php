<?php
/**
 * Provider t<PERSON><PERSON> thêm để check permission của users
 */

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class PermissionsServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot(): void
    {
        $allPermissions = app('config');
        $permissions = $allPermissions['permissions'];

        foreach ($permissions as $name => $listPermissions) {
            foreach ($listPermissions as $action => $actionName) {
                $permissionAction = $name . '.' . $action;
                // Log::debug('minh: ' . $permissionAction);

                Gate::define($permissionAction, function ($user) use ($permissionAction) {
                    return $user->checkPermission($permissionAction);
                });
            }
        }
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register(): void
    {
        //
    }
}
