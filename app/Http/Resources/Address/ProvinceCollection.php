<?php

namespace App\Http\Resources\Address;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *  schema="ProvinceCollection",
 *  title="Province Collection schema for using references",
 * 	@OA\Property(
 *        property="id",
 *        type="integer"
 *    ),
 *  @OA\Property(
 *         property="name",
 *         type="string"
 *    ),
 * 	@OA\Property(
 *        property="code",
 *        type="string"
 *    )
 * )
 */
class ProvinceCollection extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->gso_id,
            'geo_id' => $this->geo_id,
            // 'coordinates' => $this->coordinates,
            // 'coordinates' => json_decode($this->coordinates, true),
            // 'type' => $this->type
        ];
    }
}
