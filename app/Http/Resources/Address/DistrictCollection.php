<?php

namespace App\Http\Resources\Address;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *  schema="DistrictCollection",
 *  title="District Collection schema for using references",
 * 	@OA\Property(
 *        property="id",
 *        type="integer"
 *    ),
 *  @OA\Property(
 *         property="name",
 *         type="string"
 *    ),
 * 	@OA\Property(
 *        property="code",
 *        type="string"
 *    )
 * )
 */
class DistrictCollection extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->gso_id,
        ];
    }
}
