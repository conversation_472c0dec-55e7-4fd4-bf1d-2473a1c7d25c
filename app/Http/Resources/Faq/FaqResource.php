<?php

namespace App\Http\Resources\Faq;

use App\Entities\Status;
use App\Enums\FaqType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="FaqResource",
 *      title="Faq Resource schema for using references",
 * 	    @OA\Property(
 *          property="id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="type",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="question",
 *          type="string"
 *      ),
 * 	    @OA\Property(
 *          property="answer",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="status",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="isStatus",
 *          type="boolean"
 *      )
 * )
 */
class FaqResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'typeName' => FaqType::getKeyValues()[$this->type],
            'question' => $this->question,
            'answer' => $this->answer,
            # 'position' => $this->position ?? 1,
            "status" => $this->status,
            'isStatus' => Status::ACTIVE == $this->status,
            # "promoted" => $this->promoted,
            # 'isPromoted' => Promoted::PROMOTED == $this->promoted,
            /*'created_at' => new DateTimeResource($this->created_at),
            'updated_at' => new DateTimeResource($this->updated_at),*/
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }

    public function jsonOptions(): int
    {
        return JSON_PRETTY_PRINT | JSON_NUMERIC_CHECK;
    }
}
