<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="PaginationResource",
 *      title="Pagination Resource schema for using references",
 * 	    @OA\Property(
 *          property="total",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *         property="per_page",
 *         type="integer"
 *       ),
 *      @OA\Property(
 *         property="current_page",
 *         type="integer"
 *      ),
 * 	    @OA\Property(
 *        property="count",
 *        type="integer"
 *      ),
 *      @OA\Property(
 *         property="from",
 *         type="integer"
 *      ),
 *     @OA\Property(
 *          property="to",
 *          type="integer"
 *       ),
 *     @OA\Property(
 *          property="total_pages",
 *          type="integer"
 *       )
 * )
 */
class PaginationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'total' => $this->total(),
            'per_page' => $this->perPage(),
            'current_page' => $this->currentPage(),
            'count' => $this->count(),
            'from' => $this->firstItem(),
            'to' => $this->lastItem(),
            'total_pages' => $this->lastPage()
        ];
    }
}
