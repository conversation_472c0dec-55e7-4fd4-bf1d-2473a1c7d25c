<?php

namespace App\Http\Resources\Article;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'thumbnail' => $this->s3Endpoint . '/' . $this->thumbnail,
            'type' => $this->type,
            'promoted' => $this->promoted,
            'is_published' => $this->is_published,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }

    public function jsonOptions(): int
    {
        return JSON_PRETTY_PRINT | JSON_NUMERIC_CHECK;
    }
}
