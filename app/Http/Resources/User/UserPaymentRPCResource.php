<?php

namespace App\Http\Resources\User;

use App\Entities\Status;
use App\Http\Resources\DateTimeResource;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;


class UserPaymentRPCResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // $lastLogin = (int)$this->getLastLogin() > 0 ? date('Y-m-d H:i:s', $this->getLastLogin()) : 0;
        $createdAtDate = Carbon::parse($this->getCreatedAt())->format('Y-m-d H:i:s');
        $updatedAtDate = Carbon::parse($this->getUpdatedAt())->format('Y-m-d H:i:s');

        return [
            'id' => $this->getId(),
            'uid' => $this->getUid(),
            'appId' => $this->getAppId(),
            // 'userId' => $this->getUserId(),
            'username' => $this->getName(),
            'fullName' => $this->getFullName(),
            'balance' => $this->getBalance(),
            'status' => $this->getStatus(),
            'isStatus' => Status::ACTIVE === $this->getStatus(),
            'createdAt' => $createdAtDate, // $this->getCreatedAt(),
            'updatedAt' => $updatedAtDate, // $this->getUpdatedAt()
        ];
    }

    /**
     * Customize the response for the resource.
     *
     * @param Request $request
     * @param JsonResponse|JsonResource $response
     * @return void
     */
    /*public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }*/
}
