<?php

namespace App\Http\Resources\Shop;

use App\Entities\Status;
use App\Enums\ShopType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="ShopResource",
 *      title="Faq Resource schema for using references",
 * 	    @OA\Property(
 *          property="id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="type",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="question",
 *          type="string"
 *      ),
 * 	    @OA\Property(
 *          property="answer",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="status",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="isStatus",
 *          type="boolean"
 *      )
 * )
 */
class ShopResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'typeName' => ShopType::getKeyValues()[$this->type],
            'category' => $this->category,
            'item_name' => $this->item_name,
            'item_value' => $this->item_value,
            "item_id" => $this->item_id,
            'price' => $this->price,
            "currency" => $this->currency,
            'description' => $this->description,
            'item_limit' => $this->item_limit,
            'available_time' => $this->available_time,
            'icon' => $this->icon,
            'gift_status' => $this->gift_status,
            'status' => $this->status,
            'isStatus' => Status::ACTIVE == $this->status,
            'is_sale' => $this->is_sale,
            'note' => $this->note,
            'order_index' => $this->order_index,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }

    public function jsonOptions(): int
    {
        return JSON_PRETTY_PRINT | JSON_NUMERIC_CHECK;
    }
}
