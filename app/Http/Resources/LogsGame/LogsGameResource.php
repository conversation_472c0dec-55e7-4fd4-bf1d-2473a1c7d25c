<?php

namespace App\Http\Resources\LogsGame;

// use App\Http\Resources\DateTimeResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="LogsGameResource",
 *      title="LogGame Resource schema for using references",
 *      @OA\Property(
 *          property="id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="game_id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="user_id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="room_id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="table_id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="buy_in",
 *          type="number"
 *      ),
 *      @OA\Property(
 *          property="chips_won",
 *          type="number"
 *      ),
 *      @OA\Property(
 *          property="status",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="created_at",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="updated_at",
 *          type="string"
 *      )
 * )
 */
class LogsGameResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = json_decode($this->data, true);
        $turnId = $data['id'];
        $tid = $data['tid'];
        $dealer = $data['dealer'] ?? 0;
        $smallBlind = $data['smallBlind'];
        $bigBlind = $data['bigBlind'];
        $minPlayers = $data['minPlayers'];
        $maxPlayers = $data['maxPlayers'];
        $minBuyIn = $data['minBuyIn'];
        $maxBuyIn = $data['maxBuyIn'];
        $players = $data['players'];
        $dealerInfo = $players[$dealer] ?? null;
        $gameWinners = $data['gameWinners'];
        // trong cột gameWinners có cột players là danh sách người chơi, trong từng item của players có 1 cột là isWinner = true/false để đánh dấu là người thắng hay thua, viết hàm lấy ra player thắng trong mảng
        /*$playerWinners = array_filter($gameWinners[0]['players'], function ($player) {
            return $player['isWinner'] == true;
        });*/
        $playerWinners = $gameWinners[0]['players'];
        $actions = $data['actions'];
        $game = $data['game'];
        unset($game['betName']);
        unset($game['incrRaise']);
        unset($game['roundName']);
        unset($game['bets']);
        unset($game['roundBets']);
        unset($game['smallBlind']);
        unset($game['bigBlind']);

        $board = $data['board'];
        $pot = $game['pot'] ?? 0;
        $sidePot = $game['sidePot'] ?? 0;
        $blinds = $game['blinds'] ?? 0;
        return [
            'id' => $this->id,
            'turn_id' => $turnId,
            'tid' => $tid,
            'data' => $data,
            'dealer' => $dealer,
            'dealer_info' => $dealerInfo,
            'small_blind' => $smallBlind,
            'big_blind' => $bigBlind,
            'min_players' => $minPlayers,
            'max_players' => $maxPlayers,
            'min_buy_in' => $minBuyIn,
            'max_buy_in' => $maxBuyIn,
            'players' => $players,
            'game_winners' => $gameWinners,
            'players_winner' => $playerWinners,
            'actions' => $actions,
            'game' => $game,
            'board' => $board,
            'pot' => $pot,
            'side_pot' => $sidePot,
            'blinds' => $blinds,
            'created_at' => $this->created_at, // new DateTimeResource($this->created_at),
        ];
    }

    /**
     * Customize the response for the resource.
     *
     * @param Request $request
     * @param JsonResponse|JsonResource $response
     * @return void
     */
    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }
}
