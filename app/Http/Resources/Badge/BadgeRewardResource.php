<?php

namespace App\Http\Resources\Badge;

use App\Enums\BadgeRewardType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BadgeRewardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'badge_id' => $this->badge_id,
            'reward_type' => $this->reward_type,
            'reward_type_name' => BadgeRewardType::getKeyValues()[$this->reward_type] ?? $this->reward_type,
            'reward_value' => $this->reward_value,
            'is_permanent' => $this->is_permanent,
        ];
    }
}
