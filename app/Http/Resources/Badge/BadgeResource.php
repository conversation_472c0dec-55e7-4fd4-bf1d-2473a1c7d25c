<?php

namespace App\Http\Resources\Badge;

use App\Enums\BadgeCategory;
use App\Enums\BadgeType;
use App\Http\Resources\Badge\BadgeRewardResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BadgeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'typeName' => BadgeType::getKeyValues()[$this->type] ?? $this->type,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            "condition_json" => $this->condition_json,
            'reward' => $this->reward,
            "category" => $this->category,
            'categoryName' => BadgeCategory::getKeyValues()[$this->category] ?? $this->category,
            'icon_url' => $this->icon_url,
            'badge_rewards' => BadgeRewardResource::collection($this->whenLoaded('badgeRewards')),
            /*'created_at' => new DateTimeResource($this->created_at),
            'updated_at' => new DateTimeResource($this->updated_at),*/
            'created_at' => $this->created_at
        ];
    }

    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }

    public function jsonOptions(): int
    {
        return JSON_PRETTY_PRINT | JSON_NUMERIC_CHECK;
    }
}
