<?php

namespace App\Http\Resources\Feedback;

use App\Enums\FeedbackType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="FeedbackResource",
 *      title="Feedback Resource schema for using references",
 * 	    @OA\Property(
 *          property="id",
 *          type="integer"
 *      ),
 *      @OA\Property(
 *          property="type",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="player_id",
 *          type="integer"
 *      ),
 * 	    @OA\Property(
 *          property="message",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="attachments",
 *          type="array"
 *      ),
 *      @OA\Property(
 *          property="created_at",
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="updated_at",
 *          type="string"
 *      )
 * )
 */
class FeedbackResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'typeName' => FeedbackType::getKeyValues()[$this->type] ?? $this->type,
            'player_id' => $this->player_id,
            'player' => $this->player ? [
                'id' => $this->player->id,
                'nick_name' => $this->player->nick_name,
                'display_name' => $this->player->display_name,
                'name' => $this->player->display_name ?: $this->player->nick_name
            ] : null,
            'sender_name' => $this->player ? ($this->player->display_name ?: $this->player->nick_name) : 'N/A',
            'message' => $this->message,
            'attachments' => $this->attachments,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function withResponse($request, $response): void
    {
        // Get the original data
        $data = $response->getData(true);

        // Set the response data to be the resource itself, without the 'data' key
        $response->setData($data['data']);
    }

    public function jsonOptions(): int
    {
        return JSON_PRETTY_PRINT | JSON_NUMERIC_CHECK;
    }
}
