<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class MultiAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Try Sanctum authentication first
        if ($this->attemptSanctumAuth($request)) {
            return $next($request);
        }

        // Try external user token authentication
        if ($this->attemptExternalUserTokenAuth($request)) {
            return $next($request);
        }

        // Try API key authentication for system-to-system calls
        if ($this->attemptApiKeyAuth($request)) {
            return $next($request);
        }

        // No valid authentication found
        return response()->json([
            'success' => false,
            'message' => 'Unauthorized. Please provide a valid Bearer token, external user token, or API key.',
            'errors' => ['Authentication required']
        ], 401);
    }

    /**
     * Attempt to authenticate using Sanctum
     */
    private function attemptSanctumAuth(Request $request): bool
    {
        $bearerToken = $request->bearerToken();

        if (!$bearerToken) {
            return false;
        }

        // Try to authenticate with Sanctum
        $guard = Auth::guard('sanctum');

        try {
            if ($guard->check()) {
                $request->setUserResolver(function () use ($guard) {
                    return $guard->user();
                });
                return true;
            }
        } catch (\Exception $e) {
            Log::warning('Sanctum authentication failed', ['error' => $e->getMessage()]);
        }

        return false;
    }

    /**
     * Attempt to authenticate using API key
     */
    private function attemptApiKeyAuth(Request $request): bool
    {
        $apiKey = $request->header('X-API-Key') ?? $request->input('api_key');
        $apiSecret = $request->header('X-API-Secret') ?? $request->input('api_secret');

        if (!$apiKey || !$apiSecret) {
            return false;
        }

        // Validate API key and secret
        $validApiKeys = config('api.external_keys', []);

        if (!isset($validApiKeys[$apiKey]) || $validApiKeys[$apiKey]['secret'] !== $apiSecret) {
            return false;
        }

        // Create a virtual user for external systems
        $externalUser = $this->createExternalUser($validApiKeys[$apiKey]);

        $request->setUserResolver(function () use ($externalUser) {
            return $externalUser;
        });

        // Mark this request as external API request
        $request->attributes->set('is_external_api', true);
        $request->attributes->set('external_system', $validApiKeys[$apiKey]['name']);

        return true;
    }

    /**
     * Attempt to authenticate using external user token from Redis
     */
    private function attemptExternalUserTokenAuth(Request $request): bool
    {
        $externalToken = $request->header('X-External-Token') ?? $request->input('external_token');

        if (!$externalToken) {
            return false;
        }

        try {
            // Get user data from Redis
            $redisKey = "external_user_token:{$externalToken}";
            $userDataJson = Redis::get($redisKey);

            if (!$userDataJson) {
                return false;
            }

            $userData = json_decode($userDataJson, true);

            // Create external user with real game user info
            $externalUser = $this->createExternalUserFromToken($userData);

            $request->setUserResolver(function () use ($externalUser) {
                return $externalUser;
            });

            // Mark this request as external user request (different from system API)
            $request->attributes->set('is_external_user', true);
            // $request->attributes->set('is_external_api', true);
            $request->attributes->set('external_system', $userData['external_system']);
            $request->attributes->set('external_user_info', $userData);

            return true;
        } catch (\Exception $e) {
            Log::warning('External user token authentication failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Create a virtual user object for external systems
     */
    private function createExternalUser(array $apiConfig): User
    {
        $user = new User();
        $user->id = $apiConfig['virtual_user_id'];
        $user->email = $apiConfig['email'];
        $user->name = $apiConfig['name'];
        $user->exists = true;

        return $user;
    }

    /**
     * Create external user from Redis token data
     */
    private function createExternalUserFromToken(array $userData): User
    {
        $user = new User();

        // Use a consistent virtual user ID based on game_uid and system
        // This ensures same external user gets same virtual ID across requests
        $virtualUserId = 900000 + abs(crc32($userData['external_system'] . ':' . $userData['game_uid'])) % 99999;

        // $user->id = $virtualUserId;
        $user->id = $userData['game_uid'] ?? $virtualUserId;
        $user->email = $userData['email'] ?? ($userData['username'] . '@' . strtolower($userData['external_system']) . '.game');
        $user->name = $userData['username'];
        $user->exists = true;

        return $user;
    }
}
