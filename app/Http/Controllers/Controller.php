<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use OpenApi\Annotations as OA;

/**
 * @OA\Info(
 *     version="1.0.0",
 *     title="TopPoker PIP API",
 *     description="API of TopPoker PIP",
 *     @OA\Contact(name="The Developer TopPoker", email="<EMAIL>"),
 *     @OA\License(name="Apache 2.0", url="http://www.apache.org/licenses/LICENSE-2.0.html")
 * )
 *
 * @OA\Server(url=L5_SWAGGER_CONST_HOST, description="TopPoker API")
 * @OA\PathItem(path="/api/v1")
 * @OA\SecurityScheme(
 *     in="header",
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Enter your token",
 *     name="Authorization"
 * )
 *
 * @OA\Tag(
 *      name="User",
 *      description="API Endpoints of User, Quản lý thông tin User"
 * )
 *
 * @OA\Tag(
 *      name="Addresses",
 *      description="API Endpoints of Addresses, Quản lý thông tin về địa chỉ, quận/huyện/xã/phường..."
 * )
 *
 * @OA\Tag(
 *      name="Population",
 *      description="API Endpoints of Population"
 * )
 *
 * @OA\Tag(
 *      name="Auth",
 *      description="API Endpoints of Auth, Quản lý đăng nhập/đăng ký/đăng xuất"
 * )
 *
 */
class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
}
