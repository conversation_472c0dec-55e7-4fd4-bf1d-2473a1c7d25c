<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmailController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function requestEmail(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $email = $request->input('email');
        $user = User::where('email', $email)->first();
        if (!$user) {
            return $this->sendError('User not found', [], 404);
        }
        $token = $user->createToken('auth_token')->plainTextToken;
        $user->sendEmailVerificationNotification();
        return $this->sendResponse(['token' => $token], 'Email sent successfully');
    }

    public function verifyEmail(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required',
        ]);
        $token = $request->input('token');
        $user = User::where('email_verification_token', $token)->first();
        if (!$user) {
            return $this->sendError('Invalid token', [], 400);
        }
        $user->markEmailAsVerified();
        return $this->sendResponse([], 'Email verified successfully');
    }

}
