<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\UploadMultipleRequest;
use App\Http\Requests\UploadSingleRequest;
use App\Services\UploadService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UploadController extends BaseController
{
    protected UploadService $uploadService;

    public function __construct(UploadService $uploadService)
    {
        $this->uploadService = $uploadService;
    }

    /**
     * Get authenticated user from request (handled by middleware)
     */
    protected function getAuthenticatedUser(Request $request)
    {
        return $request->user();
    }

    /**
     * Check if request is from external API
     */
    protected function isExternalApiRequest(Request $request): bool
    {
        return $request->attributes->get('is_external_api', false);
    }

    /**
     * Get external system name
     */
    protected function getExternalSystemName(Request $request): ?string
    {
        return $request->attributes->get('external_system');
    }

    /**
     * Check if request is from external user
     */
    protected function isExternalUserRequest(Request $request): bool
    {
        return $request->attributes->get('is_external_user', false);
    }

    /**
     * Get upload context based on request type
     */
    protected function getUploadContext(Request $request): array
    {
        $context = [];

        if ($this->isExternalApiRequest($request)) {
            $context['type'] = 'external';
            $context['external_user_info'] = [
                'source' => 'api_key',
                'system' => $this->getExternalSystemName($request),
            ];
        } elseif ($this->isExternalUserRequest($request)) {
            $context['type'] = 'external';
            $context['external_user_info'] = $request->attributes->get('external_user_info', []);
        } else {
            $context['type'] = 'admin';
        }

        return $context;
    }

    /**
     * Upload single file
     *
     * @param UploadSingleRequest $request
     * @return JsonResponse
     */
    public function uploadSingle(UploadSingleRequest $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser($request);
            $isExternal = $this->isExternalApiRequest($request);

            // Apply external API limits if needed
            if ($isExternal && !$this->validateExternalUploadLimits($request)) {
                return $this->sendError('Upload limits exceeded for external API', [], 429);
            }
            
            // Get upload context for proper data storage
            $context = $this->getUploadContext($request);
            /*\Log::info('Upload context: ' . json_encode($context));
            \Log::info('user ' . json_encode($user));
            \Log::info('isExternal ' . json_encode($isExternal));*/

            $result = $this->uploadService->uploadSingle(
                $request->validated()['file'],
                $user->id,
                $request->validated()['description'] ?? null,
                $context
            );

            // Add external system info to response if applicable
            if ($isExternal) {
                $result['external_system'] = $this->getExternalSystemName($request);
                $result['upload_source'] = 'external_api';
            } elseif ($this->isExternalUserRequest($request)) {
                $externalInfo = app('request')->attributes->get('external_user_info', []);
                $result['external_system'] = $externalInfo['external_system'] ?? 'Unknown';
                $result['upload_source'] = 'external_user';
                $result['game_user'] = [
                    'game_uid' => $externalInfo['game_uid'] ?? null,
                    'username' => $externalInfo['username'] ?? null,
                ];
            }

            return $this->sendResponse($result, 'File uploaded successfully');
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
            return $this->sendError('Upload failed', $e->getMessage(), 500);
        }
    }

    /**
     * Upload multiple files
     *
     * @param UploadMultipleRequest $request
     * @return JsonResponse
     */
    public function uploadMultiple(UploadMultipleRequest $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser($request);
            $isExternal = $this->isExternalApiRequest($request);

            // Apply external API limits if needed
            if ($isExternal && !$this->validateExternalUploadLimits($request)) {
                return $this->sendError('Upload limits exceeded for external API', [], 429);
            }

            // Get upload context for proper data storage
            $context = $this->getUploadContext($request);

            $result = $this->uploadService->uploadMultiple(
                $request->validated()['files'],
                $user->id,
                $request->validated()['description'] ?? null,
                $context
            );

            // Add external system info to response if applicable
            if ($isExternal) {
                $result['external_system'] = $this->getExternalSystemName($request);
                $result['upload_source'] = 'external_api';
            } elseif ($this->isExternalUserRequest($request)) {
                $externalInfo = app('request')->attributes->get('external_user_info', []);
                $result['external_system'] = $externalInfo['external_system'] ?? 'Unknown';
                $result['upload_source'] = 'external_user';
                $result['game_user'] = [
                    'game_uid' => $externalInfo['game_uid'] ?? null,
                    'username' => $externalInfo['username'] ?? null,
                ];
            }

            return $this->sendResponse($result, 'Files uploaded successfully');
        } catch (\Exception $e) {
            return $this->sendError('Upload failed', $e->getMessage(), 500);
        }
    }

    /**
     * Delete a specific file
     *
     * @param Request $request
     * @param int $imageId
     * @return JsonResponse
     */
    public function deleteFile(Request $request, int $imageId): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser($request);

            $result = $this->uploadService->deleteFile($imageId, $user->id);

            if ($result) {
                return $this->sendResponse([], 'File deleted successfully');
            } else {
                return $this->sendError('File not found or unauthorized', [], 404);
            }
        } catch (\Exception $e) {
            return $this->sendError('Delete failed', $e->getMessage(), 500);
        }
    }

    /**
     * Delete all files for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteAllUserFiles(Request $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser($request);

            $result = $this->uploadService->deleteAllUserFiles($user->id);

            if ($result) {
                return $this->sendResponse([], 'All user files deleted successfully');
            } else {
                return $this->sendError('Delete failed', [], 500);
            }
        } catch (\Exception $e) {
            return $this->sendError('Delete failed', $e->getMessage(), 500);
        }
    }

    /**
     * Get user's uploaded files
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserFiles(Request $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser($request);

            $images = \App\Models\Image::where('user_id', $user->id)
                ->with(['versions', 'userUpload'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $result = [
                'images' => $images->items(),
                'pagination' => [
                    'current_page' => $images->currentPage(),
                    'last_page' => $images->lastPage(),
                    'per_page' => $images->perPage(),
                    'total' => $images->total(),
                ]
            ];

            return $this->sendResponse($result, 'Files retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendError('Retrieval failed', $e->getMessage(), 500);
        }
    }

    /**
     * Validate external API upload limits
     */
    protected function validateExternalUploadLimits(Request $request): bool
    {
        $limits = config('api.external_upload_limits');

        // Check file count for multiple uploads
        if ($request->hasFile('files')) {
            $fileCount = count($request->file('files'));
            if ($fileCount > $limits['max_files_per_request']) {
                return false;
            }
        }

        // Additional rate limiting and daily upload checks can be implemented here
        // For now, basic validation passes
        return true;
    }
}
