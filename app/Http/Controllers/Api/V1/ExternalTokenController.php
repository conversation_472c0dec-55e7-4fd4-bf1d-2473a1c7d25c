<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ExternalTokenController extends BaseController
{
    /**
     * Create external user token for game systems
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createToken(Request $request): JsonResponse
    {
        try {
            // Validate input
            $validator = Validator::make($request->all(), [
                'game_uid' => 'required|integer|min:1',
                'username' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'avatar_url' => 'nullable|string|max:500',
                'level' => 'nullable|integer|min:1',
                'expires_in' => 'nullable|integer|min:300|max:86400', // 5 minutes to 24 hours
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation failed', $validator->errors()->toArray(), 422);
            }

            $validated = $validator->validated();
            $expiresIn = $validated['expires_in'] ?? 3600; // Default 1 hour

            // Generate unique token
            $token = 'ext_' . Str::random(32) . '_' . time();

            // Get external system info from middleware
            $externalSystem = $request->attributes->get('external_system', 'Unknown');

            // Prepare user data to store in Redis
            $userData = [
                'game_uid' => $validated['game_uid'],
                'username' => $validated['username'],
                'email' => $validated['email'] ?? null,
                'avatar_url' => $validated['avatar_url'] ?? null,
                'level' => $validated['level'] ?? 1,
                'external_system' => $externalSystem,
                'created_at' => now()->toISOString(),
                'expires_at' => now()->addSeconds($expiresIn)->toISOString(),
            ];

            // Store in Redis with expiration
            $redisKey = "external_user_token:{$token}";
            Redis::setex($redisKey, $expiresIn, json_encode($userData));

            // Also create reverse lookup for game_uid to token
            $gameUidKey = "external_user_uid:{$externalSystem}:{$validated['game_uid']}";
            Redis::setex($gameUidKey, $expiresIn, $token);

            return $this->sendResponse([
                'token' => $token,
                'expires_in' => $expiresIn,
                'expires_at' => now()->addSeconds($expiresIn)->toISOString(),
                'user_info' => [
                    'game_uid' => $validated['game_uid'],
                    'username' => $validated['username'],
                    'external_system' => $externalSystem,
                ]
            ], 'External user token created successfully');
        } catch (\Exception $e) {
            return $this->sendError('Token creation failed', $e->getMessage(), 500);
        }
    }

    /**
     * Refresh external user token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function refreshToken(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string',
                'expires_in' => 'nullable|integer|min:300|max:86400',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation failed', $validator->errors()->toArray(), 422);
            }

            $token = $request->input('token');
            $expiresIn = $request->input('expires_in', 3600);

            // Get current user data from Redis
            $redisKey = "external_user_token:{$token}";
            $userDataJson = Redis::get($redisKey);

            if (!$userDataJson) {
                return $this->sendError('Token not found or expired', [], 404);
            }

            $userData = json_decode($userDataJson, true);

            // Update expiration time
            $userData['expires_at'] = now()->addSeconds($expiresIn)->toISOString();

            // Extend token expiration in Redis
            Redis::setex($redisKey, $expiresIn, json_encode($userData));

            // Also extend reverse lookup
            $gameUidKey = "external_user_uid:{$userData['external_system']}:{$userData['game_uid']}";
            Redis::setex($gameUidKey, $expiresIn, $token);

            return $this->sendResponse([
                'token' => $token,
                'expires_in' => $expiresIn,
                'expires_at' => now()->addSeconds($expiresIn)->toISOString(),
            ], 'Token refreshed successfully');
        } catch (\Exception $e) {
            return $this->sendError('Token refresh failed', $e->getMessage(), 500);
        }
    }

    /**
     * Revoke external user token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeToken(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation failed', $validator->errors()->toArray(), 422);
            }

            $token = $request->input('token');
            $redisKey = "external_user_token:{$token}";

            // Get user data before deletion for cleanup
            $userDataJson = Redis::get($redisKey);
            if ($userDataJson) {
                $userData = json_decode($userDataJson, true);

                // Remove reverse lookup
                $gameUidKey = "external_user_uid:{$userData['external_system']}:{$userData['game_uid']}";
                Redis::del($gameUidKey);
            }

            // Remove token
            $deleted = Redis::del($redisKey);

            if ($deleted) {
                return $this->sendResponse([], 'Token revoked successfully');
            } else {
                return $this->sendError('Token not found', [], 404);
            }
        } catch (\Exception $e) {
            return $this->sendError('Token revocation failed', $e->getMessage(), 500);
        }
    }

    /**
     * Get external user info by token (for debugging)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTokenInfo(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation failed', $validator->errors()->toArray(), 422);
            }

            $token = $request->input('token');
            $redisKey = "external_user_token:{$token}";
            $userDataJson = Redis::get($redisKey);

            if (!$userDataJson) {
                return $this->sendError('Token not found or expired', [], 404);
            }

            $userData = json_decode($userDataJson, true);

            return $this->sendResponse([
                'token' => $token,
                'user_info' => $userData,
                'ttl' => Redis::ttl($redisKey),
            ], 'Token info retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendError('Failed to get token info', $e->getMessage(), 500);
        }
    }
}
