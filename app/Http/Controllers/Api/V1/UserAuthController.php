<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Auth\ApiLoginRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use OpenApi\Annotations as OA;

// use App\Models\Role;


class UserAuthController extends BaseController
{
    private string $keyAppToken = 'TopPokerAuthToken';

    /**
     * Register
     * @OA\Post (
     *     path="/api/v1/auth/register",
     *     operationId="authRegister",
     *     summary="Đăng ký user",
     *     description="Đăng ký user",
     *     tags={"Auth"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(
     *                      type="object",
     *                      @OA\Property(
     *                          property="name",
     *                          type="string"
     *                      ),
     *                      @OA\Property(
     *                          property="phone",
     *                          type="number"
     *                      ),
     *                      @OA\Property(
     *                          property="email",
     *                          type="string"
     *                      ),
     *                      @OA\Property(
     *                          property="password",
     *                          type="string"
     *                      ),
     *                      @OA\Property(
     *                           property="c_password",
     *                           type="string"
     *                       )
     *                 ),
     *                 example={
     *                     "name":"Harry Pham",
     *                     "phone":"123456789",
     *                     "email": "<EMAIL>",
     *                     "password":"123456",
     *                     "c_password":"123456",
     *                }
     *             )
     *         )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Success",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="true"),
     *              @OA\Property(property="message", type="string", example="User register successfully !"),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="token", type="string", example="1|SHmA0f6zBGbP0g341Z6ylpYKgafo1OmbYsog0gsm80d0f5c4"),
     *                  @OA\Property(property="user", type="object",
     *                      @OA\Property(property="id", type="number", example=1),
     *                      @OA\Property(property="name", type="string", example="Minh Pham"),
     *                      @OA\Property(property="phone", type="string", example="0972342078"),
     *                      @OA\Property(property="updated_at", type="string", example="2024-04-28 06:06:17"),
     *                      @OA\Property(property="created_at", type="string", example="2024-04-28 06:06:17"),
     *                  ),
     *              ),
     *          )
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Validation error",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="false"),
     *              @OA\Property(property="message", type="string", example="Validation error"),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="phone", type="array", collectionFormat="multi",
     *                      @OA\Items(
     *                          type="string",
     *                          example="The phone has already been taken.",
     *                      )
     *                  ),
     *              ),
     *          )
     *      )
     * )
     */
    public function register(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'string', 'unique:users,email'],
            'phone' => ['required', 'numeric', 'digits:10', 'unique:users,phone'],
            'password' => ['required', 'min:8'], // 'required',
            'c_password' => ['required', 'same:password'], // 'required|same:password',
        ], [
            'phone.unique' => 'Số điện thoại đã tồn tại !',
            'email.unique' => 'Email đã đăng ký !',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors(), 422);
        }

        // $input['password'] = bcrypt($input['password']);
        $input['password'] = Hash::make($input['password']);
        $user = User::create($input);
        // $user->roles()->sync(Role::where('slug', RoleName::MEMBER->value)->first());

        $success['token'] = $user->createToken($user->name . '-' . $this->keyAppToken)->plainTextToken;
        $success['name'] = $user->name;
        $success['user'] = $user;

        return $this->sendResponse($success, 'User register successfully !');
    }

    /**
     * Login
     * @OA\Post (
     *     path="/api/v1/auth/login",
     *     operationId="authLogin",
     *     tags={"Auth"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  type="object",
     *                  ref="#/components/schemas/ApiLoginRequest",
     *              )
     *         )
     *     ),
     *     @OA\Response(
     *          response=200,
     *          description="Valid credentials",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="true"),
     *              @OA\Property(property="message", type="string", example="User login successfully."),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="token", type="string", example="6|8x8YsorvYcOLc52lUYWAjg6mkum98SNB1gjgBUyt4f0c11fe"),
     *                  @OA\Property(property="user", type="object",
     *                      ref="#/components/schemas/UserResource",
     *                  ),
     *              ),
     *          )
     *     ),
     *     @OA\Response(
     *           response=422,
     *           description="Validation error",
     *           @OA\JsonContent(
     *               @OA\Property(property="success", type="boolean", example="false"),
     *               @OA\Property(property="message", type="string", example="Validation error"),
     *               @OA\Property(property="data", type="object",
     *                   @OA\Property(property="phone", type="array", collectionFormat="multi",
     *                       @OA\Items(
     *                           type="string",
     *                           example="The phone field must be 10 digits.",
     *                       )
     *                   ),
     *               ),
     *           )
     *     ),
     *     @OA\Response(
     *          response=403,
     *          description="Invalid credentials",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="false"),
     *              @OA\Property(property="message", type="string", example="Invalid credentials."),
     *              @OA\Property(property="data", type="object", example={}),
     *              example={
     *                  "success": false,
     *                  "message": "Unauthorised.",
     *                  "data": {
     *                      "error": "Invalid credentials"
     *                  }
     *              },
     *      ),
     *     )
     * )
     */
    // public function login(Request $request): JsonResponse
    public function login(ApiLoginRequest $request): JsonResponse
    {
        $user = User::where('email', $request->email)->first();
        if (is_null($user)) {
            return $this->sendError('User không tồn tại', ['error' => 'User không tồn tại'], 404);
        }

        // if (!$user || bcrypt($request->password) !== $user->password) {
        if (!$user || !Hash::check($request->password, $user->password)) {
            $msg = 'Thông tin tài khoản không chính xác';
            return $this->sendError($msg, ['error' => $msg], 401);
        }

        if (Auth::attempt(['email' => $request->email, 'password' => $request->password])) {
            $user = Auth::user();
            $success['token'] = $user->createToken($user->name . '-' . $this->keyAppToken)->plainTextToken;
            $success['user'] = $user;

            return $this->sendResponse($success, 'User login successfully.');
        } else {
            return $this->sendError('Invalid credentials.', ['error' => 'Invalid credentials'], 403);
        }
    }

    /**
     * Logout
     * @OA\Post (
     *      path="/api/v1/auth/logout",
     *      operationId="authLogout",
     *      security={{"bearerAuth":{}}},
     *      tags={"Auth"},
     *      summary="logout user",
     *      description="Logout user",
     *      @OA\Response(
     *          response=200,
     *          description="Success",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="true"),
     *              @OA\Property(property="message", type="string", example="User is logged out successfully."),
     *              @OA\Property(property="data", type="object", example={}),
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *          @OA\JsonContent(
     *              @OA\Property(property="success", type="boolean", example="false"),
     *              @OA\Property(property="message", type="string", example="Unauthenticated"),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="error", type="string", example="Unauthenticated"),
     *              ),
     *          )
     *      )
     * )
     */
    public function logout(): JsonResponse
    {
        auth()->user()->tokens()->delete();

        return $this->sendResponse([], 'User is logged out successfully.');
    }
}
