<?php

namespace App\Http\Controllers\Dash;

use App\Http\Resources\Feedback\FeedbackResource;
use App\Http\Resources\PaginationResource;
use App\Repositories\Feedback\FeedbackRepository;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FeedbackController extends BaseController
{
    private FeedbackRepository $repository;

    private string $routeIndex = 'cms.feedbacks.index';
    private string $routeCreate = 'cms.feedbacks.create';
    private string $routeStore = 'cms.feedbacks.store';
    private string $routeEdit = 'cms.feedbacks.edit';
    private string $routeUpdate = 'cms.feedbacks.update';
    private string $routeDestroy = 'cms.feedbacks.destroy';
    private string $routeDestroys = 'cms.feedbacks.destroys';

    public function __construct(FeedbackRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    public function index(Request $request): Response
    {
        $perPage = (int)$request->input('per_page', $this->limit);
        $page = (int)$request->input('page', 1);
        $type = $request->input('type', null);

        $titleSite = 'Hỗ trợ';

        $items = $this->repository->filterItems([
            'per_page' => $perPage,
            'page' => $page,
            'type' => $type,
        ])->withQueryString();

        $items = FeedbackResource::collection($items);
        $paginate = new PaginationResource($items);

        return Inertia::render('Feedback/Index', [
            'cateName' => $titleSite,
            'items' => $items,
            'paginate' => $paginate,
            'perPage' => $perPage,
            'page' => $page,
            'type' => $type,
            'routeIndex' => $this->routeIndex,
            'routeCreate' => $this->routeCreate,
            'routeEdit' => $this->routeEdit,
            'routeUpdate' => $this->routeUpdate,
            'routeDestroy' => $this->routeDestroy,
            'routeDestroys' => $this->routeDestroys,
            // 'faqTypes' => FaqType::getKeyValues()
        ]);
    }
}
