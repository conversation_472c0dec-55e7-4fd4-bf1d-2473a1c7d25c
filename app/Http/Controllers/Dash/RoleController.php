<?php

namespace App\Http\Controllers\Dash;

use App\Entities\Status;
use App\Http\Requests\Role\RoleRequest;
use App\Http\Resources\Role\RoleResource;
use App\Models\Role;
use App\Repositories\Role\RoleRepository;
use App\Services\Dash\RoleService;
use App\Services\Dash\UserService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

// use App\Http\Requests\Category\DestroysCategoryRequest;
// use App\Http\Requests\Role\RoleRequest;
// use App\Http\Resources\Role\RoleResource;

class RoleController extends BaseController
{
    // private RoleService $roleService;
    private Status $statues;
    // private UserService $userService;

    private RoleRepository $roleRepository;

    public function __construct(
        // RoleService $roleService,
        Status $status,
        // UserService $userService,
        RoleRepository $roleRepository
    )
    {
        parent::__construct();
        // $this->roleService = $roleService;
        $this->statues = $status;
        // $this->userService = $userService;
        $this->roleRepository = $roleRepository;
    }

    public function index(Request $request): Response
    {
        // $this->authorize('roles.index');

        // $page = (int)$request->input('page', 1);
        // $sortReq = $request->input('sort', 'id:desc');
        // $items = Role::orderBy('id', 'desc')->paginate(10);
        // $res = $this->roleService->searchItems(10, $sortReq);
        $res = [];
        // $items = RoleResource::collection($res);
        $items = [];
        $roles = [
            [
                'id' => 1,
                'title' => 'Administrator',
                'total_users' => 5,
                'permissions' => [
                    'All Admin Controls',
                    'View and Edit Financial Summaries',
                    'Enabled Bulk Reports',
                    'View and Edit Payouts',
                    'View and Edit Disputes',
                    'and 7 more...'
                ]
            ],
            [
                'id' => 2,
                'title' => 'Developer',
                'total_users' => 14,
                'permissions' => [
                    'Some Admin Controls',
                    'View Financial Summaries only',
                    'View and Edit API Controls',
                    'View Payouts only',
                    'View and Edit Disputes',
                    'and 3 more...'
                ]
            ],
            [
                'id' => 3,
                'title' => 'Analyst',
                'total_users' => 4,
                'permissions' => [
                    'No Admin Controls',
                    'View and Edit Financial Summaries',
                    'Enabled Bulk Reports',
                    'View Payouts only',
                    'View Disputes only',
                    'and 2 more...'
                ]
            ],
            [
                'id' => 4,
                'title' => 'Support',
                'total_users' => 23,
                'permissions' => [
                    'No Admin Controls',
                    'View Financial Summaries only',
                    'View Payouts only',
                    'View and Edit Disputes',
                    'Response to Customer Feedback'
                ]
            ],
            [
                'id' => 5,
                'title' => 'Trial',
                'total_users' => 546,
                'permissions' => [
                    'No Admin Controls',
                    'View Financial Summaries only',
                    'View Bulk Reports only',
                    'View Payouts only',
                    'View Disputes only'
                ]
            ]
        ];

        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $sortReq = $request->input('sort', 'id:desc');

        // $res = $this->roleRepository->paginate($perPage, ['*'], $page);
        $res = $this->roleRepository->all();
        // $items = $res->items();

        $roles = RoleResource::collection($res);

        // dd($roles);

        return Inertia::render('Role/Index', [
            'cateName' => 'Nhóm và Quyền',
            'items' => $roles,
            'statusArr' => $this->statues->lists(),
            'sortRequest' => $sortReq,
            'page' => $page,
        ]);
    }

    public function store(RoleRequest $request): RedirectResponse
    {
        // $this->authorize('roles.store');

        // $validated = $request->validated();

        /*unset($validated['password']);
        $validated['password'] = bcrypt($request->password);

        $fileData = $this->processFileUploadFromRequest($request);
        $validated['image_id'] = $fileData['file_id'];*/

        $input = $this->processDataInput($request);
        // $statesAction = $request->input('statesAction', 'create');

        Role::create($input);

        /*if ($statesAction === 'create') {
            return to_route('cms.roles.index')->with('message', $this->msg_created_success);
        }*/

        return to_route('cms.roles.index')->with([
            'message' => $this->msg_created_success,
            'codeType' => 'success'
        ]);
    }

    private function processDataInput(Request $request): array
    {
        $input = $request->all();
        $_permissions = $request->input('permissions', []);

        if (count((array)$_permissions) !== 0) {
            $permissions = $this->cleanPermissions($_permissions);
            $input['permissions'] = json_encode($permissions);
        } else {
            $input['permissions'] = '{}';
        }

        return $input;
    }

    private function cleanPermissions($permissions): array
    {
        if (!$permissions) {
            return [];
        }
        $cleanedPermissions = [];
        foreach ($permissions as $permissionName => $checkedPermission) {
            $cleanedPermissions[$permissionName] = $checkedPermission;
        }
        return $cleanedPermissions;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    /*public function create(Request $request): Response
    {
        $this->authorize('roles.store');

        $page = (int)$request->input('page', 1);
        $permissions = app('config');

        return Inertia::render('Role/Create', [
            'cateName' => 'Thêm mới Role',
            'statusArr' => $this->statues->lists(),
            'page' => $page,
            'permissions' => $permissions['permissions']
        ]);
    }*/

    public function show(int $id, Request $request): Response
    {
        // $this->authorize('users.show');

        $page = (int)$request->input('page', 1);
        // $roleId = (int)$request->input('roleId', 1);
        // $per_page = (int)$request->input('per_page', 10);
        // $sort_req = $request->input('sort', 'id:desc');
        // $tab = $request->input('tab', 'profile');


        $users = [
            [
                'id' => 1,
                'code' => 'ID5486',
                'name' => 'Mikaela Collins',
                'email' => '<EMAIL>',
                'joined_date' => '05 May 2021, 10:10 pm',
                'avatar' => 'M',
                'avatar_bg' => 'bg-light-warning',
                'avatar_text_color' => 'text-warning'
            ],
            [
                'id' => 2,
                'code' => 'ID3344',
                'name' => 'Francis Mitcham',
                'email' => '<EMAIL>',
                'joined_date' => '20 Dec 2021, 10:30 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-8.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 3,
                'code' => 'ID1125',
                'name' => 'Olivia Wild',
                'email' => '<EMAIL>',
                'joined_date' => '21 Feb 2021, 10:30 am',
                'avatar' => 'O',
                'avatar_bg' => 'bg-light-danger',
                'avatar_text_color' => 'text-danger'
            ],
            [
                'id' => 4,
                'code' => 'ID5225',
                'name' => 'Neil Owen',
                'email' => '<EMAIL>',
                'joined_date' => '19 Aug 2021, 5:30 pm',
                'avatar' => 'N',
                'avatar_bg' => 'bg-light-primary',
                'avatar_text_color' => 'text-primary'
            ],
            [
                'id' => 5,
                'code' => 'ID9557',
                'name' => 'Dan Wilson',
                'email' => '<EMAIL>',
                'joined_date' => '22 Sep 2021, 11:30 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-6.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 6,
                'code' => 'ID6438',
                'name' => 'Emma Bold',
                'email' => '<EMAIL>',
                'joined_date' => '25 Oct 2021, 6:43 am',
                'avatar' => 'E',
                'avatar_bg' => 'bg-light-danger',
                'avatar_text_color' => 'text-danger'
            ],
            [
                'id' => 7,
                'code' => 'ID5532',
                'name' => 'Ana Crown',
                'email' => '<EMAIL>',
                'joined_date' => '24 Jun 2021, 9:23 pm',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-7.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 8,
                'code' => 'ID5907',
                'name' => 'Robert Doe',
                'email' => '<EMAIL>',
                'joined_date' => '22 Sep 2021, 2:40 pm',
                'avatar' => 'A',
                'avatar_bg' => 'bg-light-info',
                'avatar_text_color' => 'text-info'
            ],
            [
                'id' => 9,
                'code' => 'ID7881',
                'name' => 'John Miller',
                'email' => '<EMAIL>',
                'joined_date' => '20 Dec 2021, 11:05 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-17.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ]
        ];


        return Inertia::render('Role/Show', [
            'cateName' => 'Chi tiết role: ',
            'item' => null, // new UserFullResource($user),
            'statusArr' => $this->statues->lists(),
            'page' => $page,
            // 'roleId' => $roleId,
            // 'tab' => $tab,
            'userId' => $id,
            'users' => $users,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Role $role
     * @param Request $request
     * @return Response
     */
    /*public function edit(int $id, Request $request): Response
    {
        // $this->authorize('roles.update');

        $page = (int)$request->input('page', 1);
        $permissions = app('config');

        return Inertia::render('Role/Edit', [
            'cateName' => 'Cập nhật Role: ' . $role->name ?? '',
            'item' => $role,
            'statusArr' => $this->statues->lists(),
            'page' => $page,
            'permissions' => $permissions['permissions']
        ]);
    }*/

    /**
     * Update the specified resource in storage.
     *
     * @param RoleRequest $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(RoleRequest $request, int $id): RedirectResponse
    {
        // $this->authorize('roles.update');

        $input = $this->processDataInput($request);
        // $statesAction = $request->input('statesAction', 'create');

        $role = $this->roleRepository->find($id);

        if (!$role) {
            return to_route('cms.roles.index')->with(['message' => $this->msg_item_not_found]);
        }

        $role->update($input);

        /*if ($statesAction === 'create') {
            return to_route('cms.roles.index')->with('message', $this->msg_updated_success);
        }*/

        return to_route('cms.roles.index')->with('message', $this->msg_updated_success);

        //return to_route('cms.roles.index')->with('message', 'Role updated successfully.');
    }

    /**
     * Delete the specified categories.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroys(Request $request): RedirectResponse
    {
        // $this->authorize('roles.destroy');

        // Retrieve the IDs of the categories to be deleted
        $ids = $request->input('ids');
        try {
            // Check if the IDs are an array and delete the categories
            if (is_array($ids)) {
                Role::destroy($ids);
            }

            // Redirect to the category index page with a success message
            return to_route('cms.roles.index')->with(['message' => $this->msg_delete_success]);
        } catch (Exception $e) {
            // Report any exceptions that occur during the deletion process
            // report($e);
            return to_route('cms.roles.index')->with(['message' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        // $this->authorize('roles.destroy');

        // $user->delete();
        // $role = Role::query()->find($id);
        $role = $this->roleRepository->find($id);

        if (is_null($role)) {
            // return $this->sendError('Role is not found!');
            return to_route('cms.roles.index')->with(['message' => $this->msg_item_not_found]);
        }

        $role->delete();

        return to_route('cms.roles.index')->with(['message' => $this->msg_delete_success]);
    }

    public function duplicate(int $id): RedirectResponse
    {
        // $this->authorize('roles.store');

        $originalItem = Role::find($id);

        if (!$originalItem) {
            return redirect()->route('cms.roles.index')->with('message', $this->msg_item_not_found);
        }

        // Sao chép mục chính
        $duplicateItem = $originalItem->replicate();

        // Tùy chỉnh thuộc tính nếu cần
        $duplicateItem->name = $originalItem->name . ' (Copy)';

        // Lưu bản sao vào cơ sở dữ liệu
        $duplicateItem->save();

        // Sao chép các mối quan hệ
        /*foreach ($originalItem->images as $img) {
            $duplicateItem->images()->attach($img);
        }*/

        return redirect()->route('cms.roles.index')->with('message', $this->msg_duplicate_success);
    }

}
