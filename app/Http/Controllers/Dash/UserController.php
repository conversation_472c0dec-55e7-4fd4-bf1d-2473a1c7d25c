<?php

namespace App\Http\Controllers\Dash;

// use App\DTO\Authen\UserInfoDTO;
use App\DTO\Payment\UserBalanceDTO;
use App\DTO\UserDTO;
use App\Entities\Status;
use App\Http\Resources\PaginationResource;
use App\Http\Resources\Player\PlayerResource;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use App\Repositories\User\UserRepository;
use App\Services\AuthGrpcClientService;
use App\Services\Dash\RoleService;
use App\Services\Dash\UserService;
use App\Services\Games\PlayerService;
use App\Services\PaymentGrpcClientService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends BaseController
{
    private Status $statues;
    private UserService $userService;
    private UserRepository $userRepository;
    // private RoleService $roleService;
    // private OrderService $orderService;
    protected $authGrpc;
    protected $paymentGrpc;

    protected $token = 'token';
    protected PlayerService $playerService;

    public function __construct(
        Status                   $status,
        UserService              $userService,
        UserRepository           $userRepository,
        RoleService              $roleService,
        AuthGrpcClientService    $authGrpcClientService,
        PaymentGrpcClientService $paymentGrpcClientService,
        PlayerService            $playerService
    )
    {
        parent::__construct();
        $this->statues = $status;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        $this->roleService = $roleService;
        $this->authGrpc = $authGrpcClientService;
        $this->paymentGrpc = $paymentGrpcClientService;
        $this->playerService = $playerService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function index(Request $request): Response
    {
        // $this->authorize('users.index');

        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', $this->limit);
        $s = $request->input('s', null);
        $sortReq = $request->input('sort', 'id:desc');
        // $roleId = (int)$request->input('roleId', 1);
        $status = (int)$request->input('status', -1);

        $res = $this->playerService->searchByParams([
            // 'role_id' => $roleId,
            'page' => $page,
            's' => $s,
            'status' => $status,
            'per_page' => $perPage,
            'sort' => 'id:desc'
        ])->withQueryString();
        // $res = $this->userRepository->all();
        $items = PlayerResource::collection($res);
        $paginate = new PaginationResource($items);
        // dd($items);

        $queryParams = [
            's' => $s,
            // 'categoryId' => $cateId,
            'sort' => $sortReq,
            'page' => $page,
            // 'roleId' => $roleId,
            'status' => $status,
            // 'type' => $type
        ];

        # get list roles
        // $roles = $this->roleService->getRoles('id:asc');
        $roles = [];

        $users2 = [
            [
                'id' => 1,
                'name' => 'John Miller',
                'email' => '<EMAIL>',
                'role' => 'Trial',
                'last_login' => '3 weeks ago',
                'joined' => '15 Apr 2021, 9:23 pm',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-17.jpg',
                'two_step' => false,
            ],
            [
                'id' => 2,
                'name' => 'Lucy Kunic',
                'email' => '<EMAIL>',
                'role' => 'Administrator',
                'last_login' => 'Yesterday',
                'joined' => '22 Sep 2021, 10:10 pm',
                'avatar' => null,
                'two_step' => false,
            ],
            [
                'id' => 3,
                'name' => 'Melody Macy',
                'email' => '<EMAIL>',
                'role' => 'Analyst',
                'last_login' => '20 mins ago',
                'joined' => '10 Nov 2021, 8:43 pm',
                'avatar' => null,
                'two_step' => true,
            ],
            [
                'id' => 4,
                'name' => 'Max Smith',
                'email' => '<EMAIL>',
                'role' => 'Developer',
                'last_login' => '3 days ago',
                'joined' => '25 Oct 2021, 9:23 pm',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-26.jpg',
                'two_step' => false,
            ],
            [
                'id' => 5,
                'name' => 'Sean Bean',
                'email' => '<EMAIL>',
                'role' => 'Support',
                'last_login' => '5 hours ago',
                'joined' => '10 Nov 2021, 11:05 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-4.jpg',
                'two_step' => true,
            ],
            [
                'id' => 6,
                'name' => 'Brian Cox',
                'email' => '<EMAIL>',
                'role' => 'Developer',
                'last_login' => '2 days ago',
                'joined' => '20 Dec 2021, 5:20 pm',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-15.jpg',
                'two_step' => true,
            ],
            [
                'id' => 7,
                'name' => 'Mikaela Collins',
                'email' => '<EMAIL>',
                'role' => 'Administrator',
                'last_login' => '5 days ago',
                'joined' => '19 Aug 2021, 6:05 pm',
                'avatar' => null,
                'two_step' => false,
            ],
            [
                'id' => 8,
                'name' => 'Francis Mitcham',
                'email' => '<EMAIL>',
                'role' => 'Trial',
                'last_login' => '3 weeks ago',
                'joined' => '15 Apr 2021, 10:30 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-8.jpg',
                'two_step' => false,
            ],
        ];
        // $firstResult = ($page - 1) * $perPage;
        // $response = $this->authGrpc->searchUserByKeywordMethod('', $page, $perPage);
        // $users = $response->getUsers();
        // $users = $response->getUsers()->toArray();
        // $users = iterator_to_array($response->getUsers());
        /*$users = collect($response->getUsers())->map(function ($user) {
            return [
                'id' => $user->getId(),
                'userId' => $user->getUserId(),
                'username' => $user->getUsername(),
                'email' => $user->getEmail(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'fullName' => $user->getFullName(),
                'gender' => $user->getGender(),
                'loginType' => $user->getLoginType(),
                'createdTimestamp' => $user->getCreatedTimestamp(),
                'createdAt' => $user->getCreatedAt(),
                'lastLogin' => $user->getLastLogin(),
            ];
        })->toArray();*/
        // $usersInfo = UserRPCResource::collection(collect($response->getUsers()));

//        $totalCount = $response->getTotalCount();
//        $totalPages = $response->getTotalPages();

        $conditions = [
            'app_id' => 2,
        ];

        if ($s) {
            $conditions['name'] = $s;
        }

        /*$resPaymentUsers = $this->paymentGrpc->searchAccount(
            'token',
            $conditions,
            $page,
            $perPage, 'id:desc'
        );*/

        /*$usersPayment = UserPaymentRPCResource::collection(collect($resPaymentUsers->getItems()));
        $pagination = $resPaymentUsers->getPagination();*/


        return Inertia::render('Users/Index', [
            'cateName' => 'Danh sách người chơi',
            'items' => $items, // $usersPayment,
            'paginate' => $paginate,
            'statusArr' => $this->statues->lists(),
            // 'roles' => $roles,
            // 'roleId' => $roleId,
            'page' => $page,
            'queryParams' => $queryParams,
            // 'totalCount' => $totalCount,
            // 'totalPages' => $totalPages,
            // 'keyword' => $s,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     * @throws \Exception
     */
    public function store(Request $request): RedirectResponse
    {
        // $this->authorize('users.create');

        $page = (int)$request->input('page', 1);
        // $statesAction = $request->input('statesAction', 'create');

        // Validate request data
        $this->validate($request, [
            'username' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
        ]);

        $username = $request->input('username');
        // convert username to lowercase
        $username = strtolower($username);

        try {
            // Step 1: Create user in authentication service
            $userData = [
                'username' => $username, // $request->input('username'),
                'email' => $request->input('email'),
                'password' => $request->input('password'),
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
            ];

            $authUserResponse = $this->authGrpc->createUser($userData);

            if (!$authUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $authUserResponse->getMessage() ?: 'Failed to create user in authentication service'
                ])->withInput();
            }

            $userId = $authUserResponse->getUserId();
            $userInfo = $authUserResponse->getData();

            // Step 2: Create account in payment service
            $pUserCreateRes = $this->paymentGrpc->createAccount([
                'token' => $this->token,
                'app_id' => 2,
                'uid' => $userId,
                'name' => $username, // $request->input('username'),
                'full_name' => $request->input('first_name') . ' ' . $request->input('last_name'),
            ]);

            // Step 3: Create player in game service
            $this->playerService->createPlayer([
                'id' => $userId,
                'nick_name' => $username, // $request->input('username'),
                'uid' => $userInfo->getUserId(), // $userId,
                'avatar' => 1,
                'win' => 0,
                'win_rate' => 0,
                'lose' => 0,
                'total' => 0,
                'rank' => 0,
                'exp' => 0,
                'level' => 0,
                'vip_point' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            return to_route('cms.users.index', 'page=' . $page)->with('message', 'Thêm mới user thành công!');

        } catch (\Exception $e) {
            // Log the error
            \Log::error('User creation failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Error creating user: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $this->authorize('users.create');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        # get list roles
        $roles = $this->roleService->getRoles();

        return Inertia::render('Users/Create', [
            'cateName' => 'Thêm mới User',
            'statusArr' => $this->statues->lists(),
            'roles' => $roles,
            'page' => $page,
            'roleId' => $roleId
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param User $user
     * @param Request $request
     * @return Response
     */
    public function edit(User $user, Request $request): Response
    {
        $this->authorize('users.update');

        $page = (int)$request->input('page', 1);
        /*$ref = $request->input('ref', null);*/
        $roleId = (int)$request->input('roleId', 1);

        # get list roles
        $roles = $this->roleService->getRoles();

        $user->load('roles');
        // $userRole = $user->roles ?? [];
        // $userItem = $this->userService->getItemById($user->id);
        $item = new UserResource($user);

        return Inertia::render('Users/Edit', [
            'cateName' => 'Cập nhật User: ' . $user->name ?? '',
            'item' => $item, // new UserResource($user),
            'statusArr' => $this->statues->lists(),
            'roles' => $roles,
            'page' => $page,
            /*'ref' => $ref,*/
            'roleId' => $roleId
        ]);
    }

    /**
     * @param int $id - id of user in table users authen
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function show(int $id, Request $request): Response
    {
        // $this->authorize('users.show');
        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);
        // dd($userInfoRes);

        // Get balance
        // $userPaymentInfoRes = $this->paymentGrpc->getAccountById($this->token, 2, $id);
        $userPaymentInfoRes = $this->paymentGrpc->getAccountByUserId($this->token, 2, $id);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        // Get list user account
        $userAccountList = $this->paymentGrpc->getUserAccountList($this->token, $userInfoRes->getId() ?? 0);
        $accountsOfUser = UserBalanceDTO::convertToDTOs($userAccountList);
        // dd($accountsOfUser);

        // Get logs giao dịch của user
        // ====================================================================================================

        // Get logs ván đánh của user

        return Inertia::render('Users/Show', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            'routeUpdatePharmacy' => 'cms.pharmacies.update',
            'routeIndex' => 'cms.users.index',
            'page' => $page,
            'roleId' => $roleId,
            'tab' => $tab,
            'userId' => $id,
            'accountList' => $accountsOfUser
        ]);
    }

    /**
     * @throws \Exception
     */
    public function showByUserId(int $userId, Request $request): Response
    {
        // $this->authorize('users.show');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserByUserId($userId);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes->getData());
        // dd($userInfo);

        // Get balance
        $userPaymentInfoRes = $this->paymentGrpc->getAccountByUserId('token', 2, $userId);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        return Inertia::render('Users/Show', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            // 'routeUpdatePharmacy' => 'cms.pharmacies.update',
            'routeIndex' => 'cms.users.index',
            'page' => $page,
            'roleId' => $roleId,
            'tab' => $tab,
            'userId' => $userId,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        // $this->authorize('users.destroy');

        try {
            // Step 1: Delete user in authentication service
            $deleteUserResponse = $this->authGrpc->deleteUser($id);

            if (!$deleteUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $deleteUserResponse->getMessage() ?: 'Failed to delete user in authentication service'
                ]);
            }

            // Step 2: Delete account in payment service
            $this->paymentGrpc->deleteAccount($this->token, 2, $id);

            // Step 3: Delete player in game service
            $this->playerService->deletePlayer($id);

            return redirect()->back()->with('message', 'Xóa user thành công!');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('User deletion failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Error deleting user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove multiple resources from storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyMultiple(Request $request): \Illuminate\Http\JsonResponse
    {
        // $this->authorize('users.destroy');

        $ids = $request->input('ids', []);
        $successCount = 0;
        $errorMessages = [];

        foreach ($ids as $id) {
            try {
                // Step 1: Delete user in authentication service
                $deleteUserResponse = $this->authGrpc->deleteUser($id);

                if (!$deleteUserResponse->getSuccess()) {
                    $errorMessages[] = "Failed to delete user ID {$id}: " .
                        ($deleteUserResponse->getMessage() ?: 'Unknown error in authentication service');
                    continue;
                }

                // Step 2: Delete account in payment service
                $this->paymentGrpc->deleteAccount($this->token, 2, $id);

                // Step 3: Delete player in game service
                $this->playerService->deletePlayer($id);

                $successCount++;
            } catch (\Exception $e) {
                // Log the error
                \Log::error("User deletion failed for ID {$id}: " . $e->getMessage());
                $errorMessages[] = "Error deleting user ID {$id}: " . $e->getMessage();
            }
        }

        if (count($errorMessages) > 0) {
            return response()->json([
                'message' => "Deleted {$successCount} users, but encountered errors with " . count($errorMessages) . " users",
                'errors' => $errorMessages
            ], 422);
        }

        return response()->json([
            'message' => "Successfully deleted {$successCount} users"
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        // $this->authorize('users.update');

        // Validate request data
        $this->validate($request, [
            // 'username' => 'required|string|max:255',
            // 'email' => 'required|email|max:255',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'password' => 'nullable|string|min:6',
        ]);

        try {
            // Step 1: Update user in authentication service
            $userData = [
                'id' => $id,
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                // Add other fields if needed
            ];

            // Only include password if it was provided
            if ($request->filled('password')) {
                $userData['password'] = $request->input('password');
            }

            $authUserResponse = $this->authGrpc->updateUser($id, $userData);

            if (!$authUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $authUserResponse->getMessage() ?: 'Failed to update user in authentication service'
                ])->withInput();
            }

            // Step 2: Update account in payment service if needed
            /*$this->paymentGrpc->updateAccount($this->token, [
                'app_id' => 2,
                'uid' => $id,
                'name' => $request->input('username'),
                'full_name' => $request->input('first_name') . ' ' . $request->input('last_name'),
            ]);*/

            // Step 3: Update player in game service
            /*$this->playerService->updatePlayer([
                'id' => $id,
                'nick_name' => $request->input('username'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);*/

            return redirect()->back()->with('message', 'Cập nhật người dùng thành công!');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('User update failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Error updating user: ' . $e->getMessage()
            ])->withInput();
        }
    }

}
