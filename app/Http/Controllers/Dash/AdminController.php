<?php

namespace App\Http\Controllers\Dash;

use App\DTO\Payment\UserBalanceDTO;
use App\DTO\UserDTO;
use App\Entities\Status;
use App\Enums\UserType;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use App\Repositories\User\UserRepository;
use App\Services\AuthGrpcClientService;
use App\Services\Dash\RoleService;
use App\Services\Dash\UserService;
use App\Services\PaymentGrpcClientService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;

class AdminController extends BaseController
{
    private Status $statues;
    private UserService $userService;
    private UserRepository $userRepository;
    protected $authGrpc;
    protected $paymentGrpc;

    protected $token = 'token';

    public function __construct(
        Status         $status,
        UserService    $userService,
        UserRepository $userRepository,
        RoleService    $roleService,
        AuthGrpcClientService $authGrpcClientService,
        PaymentGrpcClientService $paymentGrpcClientService
    )
    {
        parent::__construct();
        $this->statues = $status;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        $this->roleService = $roleService;
        $this->authGrpc = $authGrpcClientService;
        $this->paymentGrpc = $paymentGrpcClientService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function index(Request $request): Response
    {
        // $this->authorize('users.index');

        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', $this->limit);
        $s = $request->input('s', null);
        $sortReq = $request->input('sort', 'id:desc');
        $roleId = (int)$request->input('roleId', 1);
        $status = (int)$request->input('status', -1);

        $paramsQuery = [
            'type' => UserType::ADMIN,
            'sort' => $sortReq, // 'id:desc',
            'per_page' => $perPage,
            'page' => $page,// 1
        ];

        // $res = $this->userRepository->all();
        $res = $this->userRepository->filterItems($paramsQuery);

        $items = UserResource::collection($res);

        $querySearch = [
            's' => $s,
            // 'categoryId' => $cateId,
            'sort' => $sortReq,
            'page' => $page,
            'roleId' => $roleId,
            'status' => $status,
            // 'type' => $type
        ];

        # get list roles
        // $roles = $this->roleService->getRoles('id:asc');
        // $roles = [];

//        $response = $this->authGrpc->searchUserByKeywordMethod('', 0, 10);
//        $usersInfo = UserRPCResource::collection(collect($response->getUsers()));
//        $totalCount = $response->getTotalCount();
//        $totalPages = $response->getTotalPages();
//
//        $resPaymentUsers = $this->paymentGrpc->searchAccount('token', ['app_id' => 2], 1, 10, 'id:asc');
//
//        $usersPayment = UserPaymentRPCResource::collection(collect($resPaymentUsers->getItems()));
//        $pagination = $resPaymentUsers->getPagination();
//        $totalUserPayment = $pagination->getTotal();

        // $items = User::paginate(10);

        // $token = \Auth::user()->tokens->first()->token;
        // dd($request->user()->currentAccessToken());

        return Inertia::render('Admin/Index', [
            'cateName' => 'Danh sách Admin',
            'items' => $items,
            // 'statusArr' => $this->statues->lists(),
            // 'roles' => $roles,
            // 'roleId' => $roleId,
            'page' => $page,
            'querySearch' => $querySearch,
            // 'totalCount' => $totalCount,
            // 'totalPages' => $totalPages,
            // 'type' => $type,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreUserRequest $request
     * @return RedirectResponse
     */
    public function store(StoreUserRequest $request): RedirectResponse
    {
        // $this->authorize('users.create');

        // $page = (int)$request->input('page', 1);
        // $statesAction = $request->input('statesAction', 'create');
        // $roleId = $request->input('roleId', 1);

        $validated = $request->validated();

        // $password = $request->input('password');
        // unset($validated['password']);
        # $validated['password'] = bcrypt($request->password);
        $validated['password'] = Hash::make($validated['password']);
        $validated['type'] = UserType::ADMIN;

        User::create($validated);

        // attach roles
        /*$roleIds = $request->input('roleIds', []);
        if (count($roleIds) > 0) {
            $user->roles()->attach($roleIds);
        }*/
        /*$roleIdInput = (int)$request->input('roleId', 0);
        if ($roleIdInput > 0) {
            $user->roles()->attach([$roleIdInput]);
        }*/

        /*if ($statesAction === 'create') {
            return to_route('cms.admin.index', 'page=' . $page . '&roleId=' . $roleId)->with('message', 'Thêm mới user thành công !');
        }*/

        return to_route('cms.admin.index')->with([
            'message' => 'Thêm mới user thành công !',
            'codeType' => 'success'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    /*public function create(Request $request): Response
    {
        // $this->authorize('users.create');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        # get list roles
        $roles = $this->roleService->getRoles();

        return Inertia::render('Users/Create', [
            'cateName' => 'Thêm mới User',
            'statusArr' => $this->statues->lists(),
            'roles' => $roles,
            'page' => $page,
            'roleId' => $roleId
        ]);
    }*/

    /**
     * Show the form for editing the specified resource.
     *
     * @param User $user
     * @param Request $request
     * @return Response
     */
    /*public function edit(User $user, Request $request): Response
    {
        // $this->authorize('users.update');

        $page = (int)$request->input('page', 1);
        // $ref = $request->input('ref', null);
        $roleId = (int)$request->input('roleId', 1);

        # get list roles
        $roles = $this->roleService->getRoles();

        $user->load('roles');
        // $userRole = $user->roles ?? [];
        // $userItem = $this->userService->getItemById($user->id);
        $item = new UserResource($user);

        return Inertia::render('Admin/Edit', [
            'cateName' => 'Cập nhật User: ' . $user->name ?? '',
            'item' => $item, // new UserResource($user),
            'statusArr' => $this->statues->lists(),
            'roles' => $roles,
            'page' => $page,
            // 'ref' => $ref,
            'roleId' => $roleId
        ]);
    }*/

    /**
     * @throws \Exception
     */
    public function show(int $id, Request $request): Response
    {
        // $this->authorize('users.show');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);
        // dd($userInfo);

        // Get balance
        $userPaymentInfoRes = $this->paymentGrpc->getAccountById($this->token, 2, $id);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        // Get list user account
        $userAccountList = $this->paymentGrpc->getUserAccountList($this->token, $userInfo->userId);
        $accountsOfUser = UserBalanceDTO::convertToDTOs($userAccountList);
        // dd($accountsOfUser);

        // Get logs giao dịch của user
        // ====================================================================================================

        // Get logs ván đánh của user

        return Inertia::render('Admin/Show', [
            'cateName' => 'Xem chi tiết: '. $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            'routeIndex' => 'cms.admin.index',
            'page' => $page,
            'roleId' => $roleId,
            'tab' => $tab,
            'userId' => $id,
            'accountList' => $accountsOfUser
        ]);
    }

    /**
     * @throws \Exception
     */
    public function showByUserId(int $userId, Request $request): Response
    {
        // $this->authorize('users.show');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserByUserId($userId);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes->getData());
        // dd($userInfo);

        // Get balance
        $userPaymentInfoRes = $this->paymentGrpc->getAccountByUserId('token', 2, $userId);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        return Inertia::render('Admin/Show', [
            'cateName' => 'Xem chi tiết: '. $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            // 'routeUpdatePharmacy' => 'cms.pharmacies.update',
            'routeIndex' => 'cms.users.index',
            'page' => $page,
            'roleId' => $roleId,
            'tab' => $tab,
            'userId' => $userId,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param User $user
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        // $this->authorize('users.destroy');

        $user = $this->userRepository->find($id);

        if (!$user) {
            return redirect()->back()->with('message', 'User not found!');
        }

        $user->delete();

        // return to_route('cms.users.index')->with('message', 'User deleted successfully.');
        return redirect()->back()->with('message', 'Xóa user thành công !');
    }

    public function destroys(Request $request): RedirectResponse
    {
        // $this->authorize('articles.destroy');

        $ids = $request->input('ids');
        try {
            if (is_array($ids)) {
                // dd($ids);
                // Article::whereIn('id', $idsExplode)->delete();
                User::destroy($ids);
            }
            // return to_route('cms.articles.index')->with('message', 'Articles deleted successfully.');
            return redirect()->back()->with(['message' => 'Xóa user thành công !']);
        } catch (Exception $e) {
            report($e);
            // return to_route('cms.articles.index')->with('message', $e->getMessage());
            return redirect()->back()->with(['message' => $e->getMessage()]);
        }
    }

    /*public function exportDataInExcel(Request $request): BinaryFileResponse
    {
        if ($request->type == 'xlsx') {
            $fileExt = 'xlsx';
            $exportFormat = Excel::XLSX;
        } elseif ($request->type == 'csv') {
            $fileExt = 'csv';
            $exportFormat = Excel::CSV;
        } else {
            $fileExt = 'xlsx';
            $exportFormat = Excel::XLSX;
        }

        $filename = "Users-" . date('d-m-Y-H-i') . "." . $fileExt;
        return Excel::download(new UserExport(), $filename, $exportFormat);
    }*/

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateUserRequest $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(UpdateUserRequest $request, int $id): RedirectResponse
    {
        // $this->authorize('users.update');

        // $page = (int)$request->input('page', 1);
        // $statesAction = $request->input('statesAction', 'create');
        // $ref = $request->input('ref', null);
        // $roleId = $request->input('roleId', 1);

        $validated = $request->validated();

        if ($request->input('password', null)) {
            /*$request->validate([
                'password' => 'required|confirmed|min:1'
            ]);*/
            # $validated['password'] = bcrypt($request->password);
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        // unset($validated['isChangeAvatar']);

        unset($validated['created_at']);
        unset($validated['updated_at']);
        unset($validated['deleted_at']);
        unset($validated['email_verified_at']);

        $user = $this->userRepository->find($id);

        $user->update($validated);

        // print_r($validated);

        // update roles
        /*$roleIds = $request->input('roleIds', []);
        if (count($roleIds) > 0) {
            $user->roles()->sync($roleIds);
        }*/
        /*$roleIds = (int)$request->input('roleId', 0);
        if ($roleIds > 0) {
            $user->roles()->sync([$roleIds]);
        }*/

        // return redirect()->back()->with('message', 'User updated successfully.');

        // xử lý trường hợp call từ màn hình của ROLE
        /*if ($ref === 'role_members') {
            return to_route('cms.roles.members', ['id' => $roleId, 'page' => $page])->with('message', 'Cập nhật user thành công !');
        }*/

        /*if ($statesAction === 'add') {
            return to_route('cms.admin.index', 'page=' . $page . '&roleId=' . $roleId)->with('message', 'Cập nhật user thành công !');
        }*/

        return to_route('cms.admin.index')->with(['message' => 'Cập nhật user thành công !', 'codeType' => 'success']);

        // return to_route('cms.users.index')->with('message', 'User updated successfully.');
    }

    public function updateStatus(Request $request, int $id): RedirectResponse
    {
        $user = $this->userRepository->find($id);

        if (!$user) {
            return redirect()->back()->with(['message' => 'User not found!', 'codeType' => 'error']);
        }

        $user->status = $request->status;
        $user->save();

        return to_route('cms.admin.index')->with(['message' => 'Cập nhật trạng thái user thành công !', 'codeType' => 'success']);
    }

}
