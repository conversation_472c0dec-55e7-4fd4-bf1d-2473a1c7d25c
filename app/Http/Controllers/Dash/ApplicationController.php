<?php

namespace App\Http\Controllers\Dash;

use App\DTO\AppDTO;
use App\Entities\Status;
use App\Services\PaymentGrpcClientService;
use App\Services\TopPoker\PaymentService\AppInfoResponse;
use Exception;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ApplicationController extends BaseController
{
    protected $paymentGrpc;
    protected $token = 'your_token_here';

    public function __construct(
        Status         $status,
        PaymentGrpcClientService $paymentGrpcClientService
    )
    {
        parent::__construct();
        $this->statues = $status;
        $this->paymentGrpc = $paymentGrpcClientService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(): Response
    {

        $items = $this->paymentGrpc->getApps('token');

        // $appsRes = AppRPCResource::collection(collect($items->getApps()));
        /*$apps = collect($items->getApps())->map(function ($item) {
            return [
                'id' => $item->getId(),
                'appName' => $item->getAppName(),
                'status' => $item->getStatus(),
                'isStatus' => Status::ACTIVE === $item->getStatus(),
                'notes' => $item->getNotes(),
                'transfer' => $item->getTransfer(),
                'transferCost' => $item->getTransferCost(),
                'costType' => $item->getCostType(),
                'systemAcc' => $item->getSystemAcc(),
                'unit' => $item->getUnit(),
                'pid' => $item->getPid(),
                'ext' => $item->getExt(),
            ];
        })->toArray();*/

        $apps = AppDTO::convertToAppDTOs($items);

        // dd($apps);

        $articles = [
            [
                'id' => 1,
                'app_name' => 'TopCoin',
                'note' => 'tiền nạp',
                'transfer' => 0,
                'transfer_cost' => 0,
                'cost_type' => 0,
                'system_acc' => 1,
                'unit' => 1,
                'parent_app' => 0,
                'external_app' => 0,
                'status' => 1
            ],
            [
                'id' => 2,
                'app_name' => 'Pokee',
                'note' => 'chip tiền game',
                'transfer' => 1,
                'transfer_cost' => 0,
                'cost_type' => 0,
                'system_acc' => 1,
                'unit' => 1,
                'parent_app' => 0,
                'external_app' => 0,
                'status' => 1
            ]
        ];

        return Inertia::render('Apps/Index', [
            'cateName' => 'Ứng dụng',
            'items' => $apps
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // $token = 'your_token_here';
        $data = new AppInfoResponse();
        $data->setAppName($request->input('appName'));
        $data->setNotes($request->input('notes'));
        $data->setTransfer($request->input('transfer', 0));
        $data->setTransferCost($request->input('transferCost', 0));
        $data->setCostType($request->input('costType', 0));
        $data->setSystemAcc($request->input('systemAcc', '-1'));
        $data->setUnit($request->input('unit'));
        $data->setPid($request->input('pid', 0));
        $data->setExt($request->input('ext', 0));
        $data->setStatus($request->input('status'));

        try {
            $response = $this->paymentGrpc->createAppInfo($this->token, $data);
            // return response()->json(['success' => true, 'data' => $response], 201);
            return to_route('cms.apps.index')->with('message', 'Thêm ứng dụng thành công');
        } catch (Exception $e) {
            // return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
            return to_route('cms.apps.index')->with('message', 'Có lỗi xảy ra');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // $token = 'your_token_here';

        try {
            // Call the getAppInfo method of the PaymentGrpcClientService
            $response = $this->paymentGrpc->getAppInfo($this->token, $id);

            // Convert the response to an array or DTO as needed
            $appDetail = AppDTO::convertToSingleApp($response);

            return response()->json(['success' => true, 'data' => $appDetail], 200);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'appName' => 'required|string|max:255',
            'notes' => 'nullable|string',
            'transfer' => 'required|integer',
            'transferCost' => 'required|numeric',
            'costType' => 'required|integer',
            'systemAcc' => 'required|string|max:255',
            'unit' => 'required|string|max:255',
            'pid' => 'nullable|integer',
            'ext' => 'nullable|integer',
            'status' => 'required|integer',
        ]);

        // Create a new AppInfoResponse object and set its properties
        $data = new AppInfoResponse();
        $data->setId($id);
        $data->setAppName($validatedData['appName']);
        $data->setNotes($validatedData['notes']);
        $data->setTransfer($validatedData['transfer']);
        $data->setTransferCost($validatedData['transferCost']);
        $data->setCostType($validatedData['costType']);
        $data->setSystemAcc($validatedData['systemAcc']);
        $data->setUnit($validatedData['unit']);
        $data->setPid($validatedData['pid'] ?? 0);
        $data->setExt($validatedData['ext'] ?? 0);
        $data->setStatus($validatedData['status']);

        try {
            // Call the updateAppInfo method of the PaymentGrpcClientService
            $response = $this->paymentGrpc->updateAppInfo($this->token, $id, $data);
            return to_route('cms.apps.index')->with('message', 'Cập nhật ứng dụng thành công');
        } catch (Exception $e) {
            return to_route('cms.apps.index')->with('message', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
