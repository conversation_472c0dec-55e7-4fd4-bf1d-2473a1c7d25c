<?php

namespace App\Http\Controllers\Dash;

use App\Enums\BadgeCategory;
use App\Enums\BadgeConditionsType;
use App\Enums\BadgeRewardType;
use App\Enums\BadgeType;
use App\Http\Requests\Badge\BadgeRequest;
use App\Http\Resources\Badge\BadgeResource;
use App\Http\Resources\PaginationResource;
use App\Models\Badge;
use App\Models\BadgeReward;
use App\Repositories\Badge\BadgeRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class BadgeController extends BaseController
{
    private BadgeRepository $badgeRepo;

    private string $routeIndex = 'cms.badges.index';
    private string $routeCreate = 'cms.badges.create';
    private string $routeStore = 'cms.badges.store';
    private string $routeEdit = 'cms.badges.edit';
    private string $routeUpdate = 'cms.badges.update';
    private string $routeDestroy = 'cms.badges.destroy';
    private string $routeDestroys = 'cms.badges.destroys';
    private string $routeShow = 'cms.badges.show';
    private string $routeDuplicate = 'cms.badges.duplicate';

    public function __construct(BadgeRepository $badgeRepository)
    {
        parent::__construct();
        $this->badgeRepo = $badgeRepository;
    }

    public function index(Request $request): Response
    {
        $perPage = (int)$request->input('per_page', $this->limit);
        $page = (int)$request->input('page', 1);
        $type = $request->input('type', null);

        $titleSite = 'Thành tích';

        $items = $this->badgeRepo->filterItems([
            'per_page' => $perPage,
            'page' => $page,
            'type' => $type,
        ]);

        $items = BadgeResource::collection($items);
        $paginate = new PaginationResource($items);

        return Inertia::render('Badge/Index', [
            'cateName' => $titleSite,
            'items' => $items,
            'paginate' => $paginate,
            'perPage' => $perPage,
            'page' => $page,
            'type' => $type,
            'routeIndex' => $this->routeIndex,
            'routeCreate' => $this->routeCreate,
            'routeStore' => $this->routeStore,
            'routeEdit' => $this->routeEdit,
            'routeUpdate' => $this->routeUpdate,
            'routeDestroy' => $this->routeDestroy,
            'routeDestroys' => $this->routeDestroys,
            'routeShow' => $this->routeShow,
            'routeDuplicate' => $this->routeDuplicate,
            'badgeTypes' => BadgeType::getKeyValues(),
            'badgeConditions' => BadgeConditionsType::getKeyValues(),
            'badgeCategories' => BadgeCategory::getKeyValues(),
            'badgeRewardTypes' => BadgeRewardType::getKeyValues()
        ]);
    }

    public function show(int $id): JsonResponse
    {
        $item = $this->badgeRepo->getItem($id);
        $item->load('badgeRewards');
        $a = new BadgeResource($item);
        return response()->json(['success' => true, 'data' => $a]);
    }

    public function store(BadgeRequest $request): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $input = $request->validated();
            unset($input['rewards']);
            $input = array_filter($input, function ($value) {
                return $value !== null;
            });

            $input['code'] = strtoupper(str_replace(' ', '_', $input['name']));

            \Log::info("store badges: ", [$input]);

            $badge = $this->badgeRepo->create($input);

            // Handle badge rewards
            $rewards = $request['rewards'] ?? null;
            if ($rewards && is_array($rewards)) {
                foreach ($rewards as $rewardData) {
                    BadgeReward::create([
                        'badge_id' => $badge->id,
                        'reward_type' => $rewardData['reward_type'],
                        'reward_value' => $rewardData['reward_value'],
                        'is_permanent' => $rewardData['is_permanent'] ?? false,
                    ]);
                }
            }

            DB::commit();

            return redirect()->back()->with(['message' => $this->msg_created_success, 'codeType' => 'success']);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Badge creation failed: ' . $e->getMessage());
            return redirect()->back()->with(['message' => 'Có lỗi xảy ra. Vui lòng thử lại', 'codeType' => 'error']);
        }
    }

    public function update(BadgeRequest $request, int $id): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $validated = $request->validated();
            unset($validated['rewards']);
            $item = $this->badgeRepo->find($id);
            $item->update($validated);

            // Handle badge rewards update
            $rewards = $request['rewards'] ?? null;
            if ($rewards !== null) {
                // Delete existing rewards
                BadgeReward::where('badge_id', $id)->delete();

                // Create new rewards
                if (is_array($rewards)) {
                    foreach ($rewards as $rewardData) {
                        BadgeReward::create([
                            'badge_id' => $id,
                            'reward_type' => $rewardData['reward_type'],
                            'reward_value' => $rewardData['reward_value'],
                            'is_permanent' => $rewardData['is_permanent'] ?? false,
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->back()->with(['message' => $this->msg_updated_success, 'codeType' => 'success']);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Badge update failed: ' . $e->getMessage());
            return redirect()->back()->with(['message' => 'Có lỗi xảy ra. Vui lòng thử lại', 'codeType' => 'error']);
        }
    }

    public function duplicate(int $id): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $item = Badge::with('badgeRewards')->find($id);

            // Create a new badge with copied data
            $newItem = $item->replicate();
            $newItem->code = $newItem->code . '_copy';
            $newItem->name = $newItem->name . ' (Bản sao)';
            $newItem->save();

            // Duplicate badge rewards
            foreach ($item->badgeRewards as $reward) {
                $newReward = $reward->replicate();
                $newReward->badge_id = $newItem->id;
                $newReward->save();
            }

            DB::commit();

            /*return redirect()->route($this->routeIndex)
                ->with([
                    'message' => $this->msg_duplicate_success,
                    'codeType' => 'success'
                ]);*/
            return redirect()->back()->with([
                'message' => $this->msg_created_success,
                'codeType' => 'success'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::info($e->getMessage());
            return redirect()->route($this->routeIndex)
                ->with([
                    'message' => 'Có lỗi xảy ra. Vui lòng thử lại',
                    'codeType' => 'error'
                ]);
        }
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroys(Request $request): RedirectResponse
    {
        $ids = $request->input('ids');
        try {
            DB::beginTransaction();

            // Check if the IDs are an array and delete the badges and their rewards
            if (is_array($ids)) {
                // Delete badge rewards first (due to foreign key constraint)
                BadgeReward::whereIn('badge_id', $ids)->delete();
                // Delete badges
                Badge::destroy($ids);
            }

            DB::commit();

            // Redirect to the badge index page with a success message
            return redirect()->back()->with(['message' => $this->msg_delete_success, 'codeType' => 'success']);
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('message', 'Item deleted failed and ' . $e->getMessage());
        }
    }

    /**
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $item = $this->badgeRepo->getItem($id);

            // Delete badge rewards first (due to foreign key constraint)
            BadgeReward::where('badge_id', $id)->delete();

            // Delete badge
            $item->delete();

            DB::commit();

            return redirect()->back()->with(['message' => $this->msg_delete_success, 'codeType' => 'success']);
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with(['message' => $this->msg_delete_failed, 'codeType' => 'error']);
        }
    }
}
