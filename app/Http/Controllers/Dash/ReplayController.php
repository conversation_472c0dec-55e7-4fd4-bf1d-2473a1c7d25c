<?php

namespace App\Http\Controllers\Dash;

use App\Http\Controllers\Controller;
use App\Http\Resources\LogsGame\LogsGameResource;
use App\Http\Resources\PaginationResource;
use App\Services\Games\LogsGameService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ReplayController extends BaseController
{
    private LogsGameService $logsGameService;
    public function __construct(LogsGameService $logsGameService)
    {
        parent::__construct();
        $this->logsGameService = $logsGameService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $page = (int)$request->input('page', 1);
        $limit = $request->input('per_page', 10);
        $keyword = $request->input('s', null);

        $itemsRes = $this->logsGameService->searchLogsGame($limit, $keyword);
        $items = LogsGameResource::collection($itemsRes);
        $paginate = new PaginationResource($itemsRes);
        // dd($paginate);
        return Inertia::render('Replay/Index', [
            'cateName' => 'Logs ván đánh',
            'items2' => $items,
            'keyword' => $keyword,
            'paginate' => $paginate,
            'limit' => (int) $limit,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
