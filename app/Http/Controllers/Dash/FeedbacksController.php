<?php

namespace App\Http\Controllers\Dash;

use App\Enums\FeedbackType;
use App\Http\Resources\Feedback\FeedbackResource;
use App\Http\Resources\PaginationResource;
use App\Models\Feedback;
use App\Models\Player;
use App\Repositories\Feedback\FeedbackRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FeedbacksController extends BaseController
{
    private FeedbackRepository $feedbackRepo;

    private string $routeIndex = 'cms.feedbacks.index';
    private string $routeDestroy = 'cms.feedbacks.destroy';
    private string $routeDestroys = 'cms.feedbacks.destroys';

    public function __construct(FeedbackRepository $feedbackRepository)
    {
        parent::__construct();
        $this->feedbackRepo = $feedbackRepository;
    }

    public function index(Request $request): Response
    {
        $per_page = (int)$request->input('per_page', $this->limit);
        $page = (int)$request->input('page', 1);
        $type = $request->input('type', null);
        $keyword = $request->input('keyword', null);
        $player_id = (int)$request->input('player_id', 0);
        $player_search = $request->input('player_search', null);

        $title_site = 'Phản hồi';

        $items = $this->feedbackRepo->filterItems([
            'per_page' => $per_page,
            'page' => $page,
            'type' => $type,
            'keyword' => $keyword,
            'player_id' => $player_id,
            'player_search' => $player_search,
        ]);

        $items = FeedbackResource::collection($items);
        $paginate = new PaginationResource($items);

        return Inertia::render('Feedback/Index', [
            'cateName' => $title_site,
            'items' => $items,
            'paginate' => $paginate,
            'page' => $page,
            'type' => $type,
            'keyword' => $keyword,
            'player_id' => $player_id,
            'player_search' => $player_search,
            'routeIndex' => $this->routeIndex,
            'routeDestroy' => $this->routeDestroy,
            'routeDestroys' => $this->routeDestroys,
            'feedbackTypes' => FeedbackType::getKeyValues()
        ]);
    }

    public function searchPlayers(Request $request): JsonResponse
    {
        // \Log::info('searchPlayers', [$request->all()]);
        $query = $request->input('query', '');

        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }

        $players = Player::where(function ($q) use ($query) {
            $q->where('display_name', 'like', '%' . $query . '%')
                ->orWhere('nick_name', 'like', '%' . $query . '%');
        })
            ->select('id', 'display_name', 'nick_name')
            ->limit(10)
            ->get()
            ->map(function ($player) {
                return [
                    'id' => $player->id,
                    'name' => $player->display_name ?: $player->nick_name,
                    'display_name' => $player->display_name,
                    'nick_name' => $player->nick_name,
                ];
            });

        return response()->json($players);
    }

    public function show(int $id): JsonResponse
    {
        // \Log::info('show', [$id]);
        $item = Feedback::with(['attachments', 'player'])->find($id);

        if (!$item) {
            return response()->json(['success' => false, 'message' => 'Feedback not found'], 404);
        }

        $feedbackData = new FeedbackResource($item);
        return response()->json(['success' => true, 'data' => $feedbackData]);
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroys(Request $request): RedirectResponse
    {
        $ids = $request->input('ids');
        try {
            // Check if the IDs are an array and delete the feedbacks
            if (is_array($ids)) {
                Feedback::destroy($ids);
            }

            // Redirect to the feedback index page with a success message
            return redirect()->back()->with('message', 'Xóa thành công !');
        } catch (Exception $e) {
            // Report any exceptions that occur during the deletion process
            return redirect()->back()->with('message', 'Item deleted failed and ' . $e->getMessage());
        }
    }

    /**
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $item = $this->feedbackRepo->getItem($id);
            $item->delete();

            return redirect()->back()->with('message', 'Xóa thành công !');
        } catch (Exception $e) {
            return redirect()->back()->with('message', 'Có lỗi xảy ra khi xóa !');
        }
    }
}
