<?php

namespace App\Http\Controllers\Dash;

use App\Entities\Status;
use App\Enums\UserType;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Resources\PaginationResource;
use App\Http\Resources\User\UserResource;
use App\Models\User;
use App\Repositories\User\UserRepository;
use App\Services\Dash\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;

class PartnerController extends BaseController
{
    private Status $statues;
    private UserService $userService;
    private UserRepository $userRepository;
    public function __construct(
        Status         $status,
        UserService    $userService,
        UserRepository $userRepository,
        // RoleService    $roleService,
        // AuthGrpcClientService $authGrpcClientService,
        // PaymentGrpcClientService $paymentGrpcClientService
    )
    {
        parent::__construct();
        $this->statues = $status;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        // $this->roleService = $roleService;
        // $this->authGrpc = $authGrpcClientService;
        // $this->paymentGrpc = $paymentGrpcClientService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $users = [
            [
                'id' => 1,
                'code' => 'ID5486',
                'name' => 'Mikaela Collins',
                'email' => '<EMAIL>',
                'joined_date' => '05 May 2021, 10:10 pm',
                'avatar' => 'M',
                'avatar_bg' => 'bg-light-warning',
                'avatar_text_color' => 'text-warning'
            ],
            [
                'id' => 2,
                'code' => 'ID3344',
                'name' => 'Francis Mitcham',
                'email' => '<EMAIL>',
                'joined_date' => '20 Dec 2021, 10:30 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-8.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 3,
                'code' => 'ID1125',
                'name' => 'Olivia Wild',
                'email' => '<EMAIL>',
                'joined_date' => '21 Feb 2021, 10:30 am',
                'avatar' => 'O',
                'avatar_bg' => 'bg-light-danger',
                'avatar_text_color' => 'text-danger'
            ],
            [
                'id' => 4,
                'code' => 'ID5225',
                'name' => 'Neil Owen',
                'email' => '<EMAIL>',
                'joined_date' => '19 Aug 2021, 5:30 pm',
                'avatar' => 'N',
                'avatar_bg' => 'bg-light-primary',
                'avatar_text_color' => 'text-primary'
            ],
            [
                'id' => 5,
                'code' => 'ID9557',
                'name' => 'Dan Wilson',
                'email' => '<EMAIL>',
                'joined_date' => '22 Sep 2021, 11:30 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-6.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 6,
                'code' => 'ID6438',
                'name' => 'Emma Bold',
                'email' => '<EMAIL>',
                'joined_date' => '25 Oct 2021, 6:43 am',
                'avatar' => 'E',
                'avatar_bg' => 'bg-light-danger',
                'avatar_text_color' => 'text-danger'
            ],
            [
                'id' => 7,
                'code' => 'ID5532',
                'name' => 'Ana Crown',
                'email' => '<EMAIL>',
                'joined_date' => '24 Jun 2021, 9:23 pm',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-7.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ],
            [
                'id' => 8,
                'code' => 'ID5907',
                'name' => 'Robert Doe',
                'email' => '<EMAIL>',
                'joined_date' => '22 Sep 2021, 2:40 pm',
                'avatar' => 'A',
                'avatar_bg' => 'bg-light-info',
                'avatar_text_color' => 'text-info'
            ],
            [
                'id' => 9,
                'code' => 'ID7881',
                'name' => 'John Miller',
                'email' => '<EMAIL>',
                'joined_date' => '20 Dec 2021, 11:05 am',
                'avatar' => 'templates/dashboard/assets/media/avatars/150-17.jpg',
                'avatar_bg' => '',
                'avatar_text_color' => ''
            ]
        ];

        /*$users = User::where('type', UserType::PARTNER)
            ->orderBy('id', 'desc')
            ->paginate(10); // Adjust the pagination limit as needed*/

        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $keyword = $request->input('s', '');
        $sort = $request->input('sort', 'id:desc');

        $paramsQuery = [
            'type' => UserType::PARTNER,
            'sort' => $sort, // 'id:desc',
            'per_page' => $perPage,
            'page' => $page,// 1
        ];

        if ($keyword) {
            $paramsQuery['keyword'] = $keyword;
        }

        $partners = $this->userRepository->filterItems($paramsQuery);

        $items = UserResource::collection($partners);

        $pagination = new PaginationResource($partners);

        // $perPage = $partners->perPage();
        $currentPage = $partners->currentPage();

        return Inertia::render('Partners/Index', [
            'cateName' => 'Đại lý',
            // 'items' => $users
            'items' => $items, // ->items(),
            'pagination' => $pagination,
            'perPage' => $perPage,
            'currentPage' => $currentPage,
            'keyword' => $keyword,
            /*'pagination' => [
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'from' => $users->firstItem(),
                'to' => $users->lastItem(),
            ],*/
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request): RedirectResponse
    {
        // $this->authorize('users.create');

        // $page = (int)$request->input('page', 1);
        // $statesAction = $request->input('statesAction', 'create');
        // $roleId = $request->input('roleId', 1);

        $validated = $request->validated();

        // $password = $request->input('password');
        // unset($validated['password']);
        # $validated['password'] = bcrypt($request->password);
        $validated['password'] = Hash::make($validated['password']);
        $validated['type'] = UserType::PARTNER;
        User::create($validated);

        // attach roles
        /*$roleIds = $request->input('roleIds', []);
        if (count($roleIds) > 0) {
            $user->roles()->attach($roleIds);
        }*/
        /*$roleIdInput = (int)$request->input('roleId', 0);
        if ($roleIdInput > 0) {
            $user->roles()->attach([$roleIdInput]);
        }*/

        /*if ($statesAction === 'create') {
            return to_route('cms.admin.index', 'page=' . $page . '&roleId=' . $roleId)->with('message', 'Thêm mới user thành công !');
        }*/

        return to_route('cms.partners.index')->with([
            'message' => 'Thêm mới user thành công !',
            'codeType' => 'success'
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, int $id): RedirectResponse
    {
        // $this->authorize('users.update');

        // $page = (int)$request->input('page', 1);
        // $statesAction = $request->input('statesAction', 'create');
        // $ref = $request->input('ref', null);
        // $roleId = $request->input('roleId', 1);

        $validated = $request->validated();

        if ($request->input('password', null)) {
            /*$request->validate([
                'password' => 'required|confirmed|min:1'
            ]);*/
            # $validated['password'] = bcrypt($request->password);
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        // unset($validated['isChangeAvatar']);

        unset($validated['created_at']);
        unset($validated['updated_at']);
        unset($validated['deleted_at']);
        unset($validated['email_verified_at']);

        $user = $this->userRepository->find($id);

        $user->update($validated);

        // print_r($validated);

        // update roles
        /*$roleIds = $request->input('roleIds', []);
        if (count($roleIds) > 0) {
            $user->roles()->sync($roleIds);
        }*/
        /*$roleIds = (int)$request->input('roleId', 0);
        if ($roleIds > 0) {
            $user->roles()->sync([$roleIds]);
        }*/

        // return redirect()->back()->with('message', 'User updated successfully.');

        // xử lý trường hợp call từ màn hình của ROLE
        /*if ($ref === 'role_members') {
            return to_route('cms.roles.members', ['id' => $roleId, 'page' => $page])->with('message', 'Cập nhật user thành công !');
        }*/

        /*if ($statesAction === 'add') {
            return to_route('cms.admin.index', 'page=' . $page . '&roleId=' . $roleId)->with('message', 'Cập nhật user thành công !');
        }*/

        return to_route('cms.partners.index')->with(['message' => 'Cập nhật đại lý thành công !', 'codeType' => 'success']);

        // return to_route('cms.users.index')->with('message', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        // $this->authorize('users.destroy');

        $user = $this->userRepository->find($id);

        if (!$user) {
            return redirect()->back()->with('message', 'Đại lý không tồn tại !');
        }

        $user->delete();

        // return to_route('cms.users.index')->with('message', 'User deleted successfully.');
        return redirect()->back()->with('message', 'Xóa Đại lý thành công !');
    }

    public function updateStatus(Request $request, int $id): RedirectResponse
    {
        $user = $this->userRepository->find($id);

        if (!$user) {
            return redirect()->back()->with([
                'message' => 'Đại lý không tồn tại !',
                'codeType' => 'error'
            ]);
        }

        $user->status = $request->status;
        $user->save();

        return to_route('cms.partners.index')->with([
            'message' => 'Cập nhật trạng thái đại lý thành công !',
            'codeType' => 'success'
        ]);
    }
}
