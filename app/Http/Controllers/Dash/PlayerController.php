<?php

namespace App\Http\Controllers\Dash;

use App\DTO\Payment\UserBalanceDTO;
use App\DTO\UserDTO;
use App\Entities\Status;
use App\Enums\GenderType;
use App\Enums\PlayerLoginType;
use App\Http\Resources\LogsGame\LogsGameResource;
use App\Http\Resources\PaginationResource;
use App\Http\Resources\Player\PlayerResource;
use App\Http\Resources\User\UserResource;
use App\Models\Player;
use App\Models\User;
use App\Repositories\User\UserRepository;
use App\Services\AuthGrpcClientService;
use App\Services\Dash\RoleService;
use App\Services\Dash\UserService;
use App\Services\Games\LogsGameService;
use App\Services\Games\PlayerService;
use App\Services\PaymentGrpcClientService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PlayerController extends BaseController
{
    private Status $statues;
    private UserService $userService;
    private UserRepository $userRepository;
    // private RoleService $roleService;
    // private OrderService $orderService;
    protected $authGrpc;
    protected $paymentGrpc;

    protected $token = 'token';
    protected PlayerService $playerService;
    protected LogsGameService $logsGameService;

    public function __construct(
        Status                   $status,
        UserService              $userService,
        UserRepository           $userRepository,
        RoleService              $roleService,
        AuthGrpcClientService    $authGrpcClientService,
        PaymentGrpcClientService $paymentGrpcClientService,
        PlayerService            $playerService,
        LogsGameService          $logsGameService
    ) {
        parent::__construct();
        $this->statues = $status;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        // $this->roleService = $roleService;
        $this->authGrpc = $authGrpcClientService;
        $this->paymentGrpc = $paymentGrpcClientService;
        $this->playerService = $playerService;
        $this->logsGameService = $logsGameService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function index(Request $request): Response
    {
        // $this->authorize('users.index');

        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', $this->limit);
        $s = $request->input('s', null);
        $sortReq = $request->input('sort', 'id:desc');
        // $roleId = (int)$request->input('roleId', 1);
        $status = (int)$request->input('status', -1);

        $res = $this->playerService->searchByParams([
            // 'role_id' => $roleId,
            'page' => $page,
            's' => $s,
            'status' => $status,
            'per_page' => $perPage,
            'sort' => 'id:desc'
        ])->withQueryString();
        // $res = $this->userRepository->all();
        $items = PlayerResource::collection($res);
        $paginate = new PaginationResource($items);
        // dd($items);

        $queryParams = [
            's' => $s,
            // 'categoryId' => $cateId,
            'sort' => $sortReq,
            'page' => $page,
            // 'roleId' => $roleId,
            'status' => $status,
            // 'type' => $type
        ];

        // $firstResult = ($page - 1) * $perPage;
        // $response = $this->authGrpc->searchUserByKeywordMethod('', $page, $perPage);
        // $users = $response->getUsers();
        // $users = $response->getUsers()->toArray();
        // $users = iterator_to_array($response->getUsers());
        /*$users = collect($response->getUsers())->map(function ($user) {
            return [
                'id' => $user->getId(),
                'userId' => $user->getUserId(),
                'username' => $user->getUsername(),
                'email' => $user->getEmail(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'fullName' => $user->getFullName(),
                'gender' => $user->getGender(),
                'loginType' => $user->getLoginType(),
                'createdTimestamp' => $user->getCreatedTimestamp(),
                'createdAt' => $user->getCreatedAt(),
                'lastLogin' => $user->getLastLogin(),
            ];
        })->toArray();*/
        // $usersInfo = UserRPCResource::collection(collect($response->getUsers()));

        //        $totalCount = $response->getTotalCount();
        //        $totalPages = $response->getTotalPages();

        $conditions = [
            'app_id' => 2,
        ];

        if ($s) {
            $conditions['name'] = $s;
        }

        /*$resPaymentUsers = $this->paymentGrpc->searchAccount(
            'token',
            $conditions,
            $page,
            $perPage, 'id:desc'
        );*/

        /*$usersPayment = UserPaymentRPCResource::collection(collect($resPaymentUsers->getItems()));
        $pagination = $resPaymentUsers->getPagination();*/


        return Inertia::render('Player/Index', [
            'cateName' => 'Danh sách người chơi',
            'items' => $items, // $usersPayment,
            'paginate' => $paginate,
            'statusArr' => $this->statues->lists(),
            // 'roles' => $roles,
            // 'roleId' => $roleId,
            'page' => $page,
            'queryParams' => $queryParams,
            // 'totalCount' => $totalCount,
            // 'totalPages' => $totalPages,
            // 'keyword' => $s,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     * @throws \Exception
     */
    public function store(Request $request): RedirectResponse
    {
        // $this->authorize('users.create');

        $page = (int)$request->input('page', 1);

        // Validate request data
        $this->validate($request, [
            'username' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
        ]);

        $username = $request->input('username');
        // convert username to lowercase
        $username = strtolower($username);

        try {
            // Step 1: Create user in authentication service
            $userData = [
                'username' => $username,
                'email' => $request->input('email'),
                'password' => $request->input('password'),
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
            ];

            $authUserResponse = $this->authGrpc->createUser($userData);

            if (!$authUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $authUserResponse->getMessage() ?: 'Failed to create user in authentication service'
                ])->withInput();
            }

            $userId = $authUserResponse->getUserId();
            $userInfo = $authUserResponse->getData();

            // Step 2: Create account in payment service
            $pUserCreateRes = $this->paymentGrpc->createAccount([
                'token' => $this->token,
                'app_id' => 2,
                'uid' => $userId,
                'name' => $username,
                'full_name' => $request->input('first_name') . ' ' . $request->input('last_name'),
            ]);

            // Step 3: Create player in game service
            $playerData = [
                'id' => $userId,
                'nick_name' => $username,
                'uid' => $userInfo->getUserId(),
                'avatar' => 1,
                'win' => 0,
                'win_rate' => 0,
                'lose' => 0,
                'total' => 0,
                'rank' => 0,
                'exp' => 0,
                'level' => 0,
                'vip_point' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $this->playerService->createPlayer($playerData);

            return to_route('cms.players.index', 'page=' . $page)->with('message', 'Tạo mới người chơi thành công!');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Player creation failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Lỗi khi tạo người chơi: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        // $this->authorize('users.create');

        $page = (int)$request->input('page', 1);

        return Inertia::render('Player/Create', [
            'cateName' => 'Tạo mới người chơi',
            'statusArr' => $this->statues->lists(),
            'page' => $page,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param User $user
     * @param Request $request
     * @return Response
     */
    public function edit(User $user, Request $request): Response
    {
        $this->authorize('users.update');

        $page = (int)$request->input('page', 1);
        /*$ref = $request->input('ref', null);*/
        $roleId = (int)$request->input('roleId', 1);

        # get list roles
        $roles = $this->roleService->getRoles();

        $user->load('roles');
        // $userRole = $user->roles ?? [];
        // $userItem = $this->userService->getItemById($user->id);
        $item = new UserResource($user);

        return Inertia::render('Player/Edit', [
            'cateName' => 'Cập nhật User: ' . $user->name ?? '',
            'item' => $item, // new UserResource($user),
            'statusArr' => $this->statues->lists(),
            'roles' => $roles,
            'page' => $page,
            /*'ref' => $ref,*/
            'roleId' => $roleId
        ]);
    }

    /**
     * @param int $id - id of user in table users authen
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function show(int $id, Request $request): Response
    {
        // $this->authorize('users.show');
        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        $playerData = $this->playerService->getItem($id);
        // $playerItem = new PlayerResource($playerData);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);
        // dd($userInfoRes);

        // Get balance
        // $userPaymentInfoRes = $this->paymentGrpc->getAccountById($this->token, 2, $id);
        $userPaymentInfoRes = $this->paymentGrpc->getAccountByUserId($this->token, 2, $id);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        // Get list user account
        $userAccountList = $this->paymentGrpc->getUserAccountList($this->token, $userInfoRes->getId() ?? 0);
        $accountsOfUser = UserBalanceDTO::convertToDTOs($userAccountList);
        // dd($accountsOfUser);

        // Get logs giao dịch của user
        // ====================================================================================================

        // Get logs ván đánh của user

        return Inertia::render('Player/ShowOverview', [
            'tab' => 'overview',
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'playerInfo' => $playerData,
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            'routeUpdatePharmacy' => 'cms.pharmacies.update',
            'routeIndex' => 'cms.users.index',
            'page' => $page,
            'roleId' => $roleId,
            // 'tab' => $tab,
            'userId' => $id,
            'accountList' => $accountsOfUser,
            'playerLoginTypes' => PlayerLoginType::getKeyValues(),
            'genderTypes' => GenderType::getKeyValues(),
        ]);
    }

    /**
     * Danh sách bạn bè
     * @param string $username
     * @param Request $request
     */
    public function showFriends(int $id, Request $request): Response
    {
        $playerData = $this->playerService->getItem($id);
        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);

        return Inertia::render('Player/ShowFriends', [
            'tab' => 'friends',
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'userInfo' => $userInfo,
            'playerInfo' => $playerData,
        ]);
    }

    /**
     * Danh sách logs ván đánh
     * @param int $id
     * @param Request $request
     * @return Response
     * @throws \Exception
     */
    public function showGamePlayHistories(int $id, Request $request): Response
    {
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);
        $sort = $request->input('sort', 'created_at:desc');
        $keyword = $request->input('s', null);

        $playerData = $this->playerService->getItem($id);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);


        $paramsSearch = [
            'page' => $page,
            'per_page' => $perPage,
            'sort' => $sort,
            'keyword' => $keyword,
        ];

        $data = $this->logsGameService->searchLogsGameByPlayerId($playerData->id, $paramsSearch);
        // for loop data and get logs_game
        /*$abc = [];
        foreach ($data as $d) {
            $logGame = $d->log_game;
            $a = new LogsGameResource($logGame);
            echo "<pre>";
            print_r($a);
            echo "</pre>";
            $abc[] = $a; // $a;
        }*/
        $items = LogsGameResource::collection($data);

        $paginate = new PaginationResource($data);

        return Inertia::render('Player/ShowHistories', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'tab' => 'histories',
            'userInfo' => $userInfo,
            'playerInfo' => $playerData,
            'items' => $items,
            'paginate' => $paginate,
            'querySearch' => $paramsSearch,
            'playerId' => $id
        ]);
    }

    /**
     * Danh sách logs giao dịch
     */
    public function showTransactions(int $id, Request $request): Response
    {
        $playerData = $this->playerService->getItem($id);
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);

        return Inertia::render('Player/ShowTransactions', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'tab' => 'transactions',
            'userInfo' => $userInfo,
            'playerInfo' => $playerData,
            'playerId' => $id,
            'page' => $page,
            'perPage' => $perPage,
        ]);
    }

    /**
     * Danh sách logs giải đấu
     * @param Request $request
     */
    public function showTournaments(int $id, Request $request): Response
    {
        $playerData = $this->playerService->getItem($id);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);

        return Inertia::render('Player/ShowTournaments', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'tab' => 'tournaments',
            'userInfo' => $userInfo,
            'playerInfo' => $playerData,
        ]);
    }

    public function showNotifications(int $id, Request $request): Response
    {
        $playerData = $this->playerService->getItem($id);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);

        return Inertia::render('Player/ShowNotifications', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'tab' => 'notifications',
            'playerInfo' => $playerData,
            'userInfo' => $userInfo,
        ]);
    }

    public function showActivities(int $id, Request $request): Response
    {
        $playerData = $this->playerService->getItem($id);

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserById($id);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes);

        return Inertia::render('Player/ShowActivities', [
            'cateName' => 'Logs Hoạt Động',
            'tab' => 'activities',
            'userInfo' => $userInfo,
            'playerInfo' => $playerData,
        ]);
    }


    /**
     * @throws \Exception
     */
    public function showByUserId(int $userId, Request $request): Response
    {
        // $this->authorize('users.show');

        $page = (int)$request->input('page', 1);
        $roleId = (int)$request->input('roleId', 1);
        $per_page = (int)$request->input('per_page', 10);
        $sort_req = $request->input('sort', 'id:desc');
        $tab = $request->input('tab', 'profile');

        // Get user info from GRPC authen
        $userInfoRes = $this->authGrpc->getUserByUserId($userId);
        // Convert the response to an array or DTO as needed
        $userInfo = UserDTO::convertToSingleItem($userInfoRes->getData());
        // dd($userInfo);

        // Get balance
        $userPaymentInfoRes = $this->paymentGrpc->getAccountByUserId('token', 2, $userId);
        $userBalance = UserBalanceDTO::convertToSingleItem($userPaymentInfoRes);
        // dd($userBalance);

        return Inertia::render('Users/Show', [
            'cateName' => 'Xem chi tiết: ' . $userInfo->fullName,
            'userInfo' => $userInfo, // new UserFullResource($user),
            'userBalance' => $userBalance,
            'statusArr' => $this->statues->lists(),
            // 'routeUpdatePharmacy' => 'cms.pharmacies.update',
            'routeIndex' => 'cms.users.index',
            'page' => $page,
            'roleId' => $roleId,
            'tab' => $tab,
            'userId' => $userId,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        // $this->authorize('users.destroy');

        try {
            // Step 1: Delete user in authentication service
            $deleteUserResponse = $this->authGrpc->deleteUser($id);

            if (!$deleteUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $deleteUserResponse->getMessage() ?: 'Failed to delete user in authentication service'
                ]);
            }

            // Step 2: Delete account in payment service
            $this->paymentGrpc->deleteAccount($this->token, 2, $id);

            // Step 3: Delete player in game service
            $this->playerService->deletePlayer($id);

            return redirect()->back()->with('message', 'Xóa user thành công!');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('User deletion failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Error deleting user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove multiple resources from storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyMultiple(Request $request): \Illuminate\Http\JsonResponse
    {
        // $this->authorize('users.destroy');

        $ids = $request->input('ids', []);
        $successCount = 0;
        $errorMessages = [];

        foreach ($ids as $id) {
            try {
                // Step 1: Delete user in authentication service
                $deleteUserResponse = $this->authGrpc->deleteUser($id);

                if (!$deleteUserResponse->getSuccess()) {
                    $errorMessages[] = "Failed to delete user ID {$id}: " .
                        ($deleteUserResponse->getMessage() ?: 'Unknown error in authentication service');
                    continue;
                }

                // Step 2: Delete account in payment service
                $this->paymentGrpc->deleteAccount($this->token, 2, $id);

                // Step 3: Delete player in game service
                $this->playerService->deletePlayer($id);

                $successCount++;
            } catch (\Exception $e) {
                // Log the error
                \Log::error("User deletion failed for ID {$id}: " . $e->getMessage());
                $errorMessages[] = "Error deleting user ID {$id}: " . $e->getMessage();
            }
        }

        if (count($errorMessages) > 0) {
            return response()->json([
                'message' => "Deleted {$successCount} users, but encountered errors with " . count($errorMessages) . " users",
                'errors' => $errorMessages
            ], 422);
        }

        return response()->json([
            'message' => "Successfully deleted {$successCount} users"
        ]);
    }

    /**
     * Update player information
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function updatePlayer(Request $request, int $id): RedirectResponse
    {
        // Validate request data
        $this->validate($request, [
            'nick_name' => 'required|string|max:50',
            'display_name' => 'nullable|string|max:100',
            'level' => 'required|integer|min:0|max:100',
            'exp' => 'required|integer|min:0',
            'vip_point' => 'required|integer|min:0',
            // 'avatar' => 'required|integer|min:1|max:10',
            'type' => 'required|string',
            'gender' => 'required|in:' . implode(',', array_keys(GenderType::getKeyValues())),
        ]);

        try {
            // Step 1: Call GRPC authentication service to update username
            $updateUsernameResponse = $this->authGrpc->updateUsername($id, [
                'username' => $request->input('nick_name'),
            ]);

            if (!$updateUsernameResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => 'Không thể cập nhật username'
                ])->withInput();
            }

            // Step 2: Call GRPC authentication service to update gender, first_name, last_name
            $nameComponents = extractNameComponents($request->input('display_name'));

            $updateUserResponse = $this->authGrpc->updateUser($id, [
                /*'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),*/
                'first_name' => $request->input('first_name', $nameComponents['first_name']),
                'last_name' => $request->input('last_name', $nameComponents['last_name']),
                'gender' => $request->input('gender'),
            ]);
            if (!$updateUserResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => 'Không thể cập nhật thông tin cá nhân'
                ])->withInput();
            }

            // Step 3: Update player in game service database
            $playerData = [
                'id' => $id,
                'nick_name' => $request->input('nick_name'),
                'display_name' => $request->input('display_name'),
                'level' => $request->input('level'),
                'exp' => $request->input('exp'),
                'vip_point' => $request->input('vip_point'),
                // 'avatar' => $request->input('avatar'),
                'type' => $request->input('type'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $result = $this->playerService->updatePlayer($playerData);

            if (!$result) {
                return redirect()->back()->withErrors([
                    'message' => 'Không thể cập nhật thông tin người chơi'
                ])->withInput();
            }

            return redirect()->back()->with([
                'message' => 'Cập nhật thông tin người chơi thành công!',
                'codeType' => 'success'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Player update failed: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Lỗi khi cập nhật thông tin người chơi: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        // Use the updatePlayer method for player updates
        return $this->updatePlayer($request, $id);
    }

    /**
     * Update player password
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function updatePassword(Request $request, int $id): RedirectResponse
    {
        // Validate request data
        $this->validate($request, [
            // 'current_password' => 'required|string',
            'new_password' => 'required|string|min:6',
            'confirm_password' => 'required|string|same:new_password',
        ]);

        try {
            // Call GRPC authentication service to update password
            $updatePasswordResponse = $this->authGrpc->resetPassword($id, [
                // 'current_password' => $request->input('current_password'),
                'new_password' => $request->input('new_password'),
            ]);

            if (!$updatePasswordResponse->getSuccess()) {
                return redirect()->back()->withErrors([
                    'message' => $updatePasswordResponse->getMessage() ?: 'Không thể cập nhật mật khẩu'
                ])->withInput();
            }

            return redirect()->back()->with([
                'message' => 'Cập nhật mật khẩu thành công!',
                'codeType' => 'success'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Password update failed for user ID ' . $id . ': ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Lỗi khi cập nhật mật khẩu: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Update player email
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function updateEmail(Request $request, int $id): RedirectResponse
    {
        // Validate request data
        $this->validate($request, [
            'current_email' => 'required|email',
            'new_email' => 'required|email|different:current_email',
        ]);

        try {
            // Call GRPC authentication service to update email
            $updateEmailResponse = $this->authGrpc->updateEmail($id, [
                'current_email' => $request->input('current_email'),
                'new_email' => $request->input('new_email'),
            ]);

            if (!$updateEmailResponse->getSuccess()) {
                if ($updateEmailResponse->getMessage() === 'Failed to update email: ERROR: duplicate key value violates unique constraint "idx_users_email" (SQLSTATE 23505)') {
                    return redirect()->back()->withErrors([
                        'message' => 'email đã được sử dụng. vui lòng chọn email khác'
                    ])->withInput();
                }
                return redirect()->back()->withErrors([
                    'message' => $updateEmailResponse->getMessage() ?: 'Không thể cập nhật email'
                ])->withInput();
            }

            return redirect()->back()->with([
                'message' => 'Cập nhật email thành công!',
                'codeType' => 'success'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Email update failed for user ID ' . $id . ': ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'message' => 'Lỗi khi cập nhật email: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Lấy danh sách 5 người chơi mới nhất
     */
    public function getNewPlayers(): \Illuminate\Http\JsonResponse
    {
        $newPlayers = Player::getNewPlayers(5);

        return response()->json([
            'status' => 'success',
            'data' => $newPlayers,
            'message' => 'Lấy danh sách người chơi mới thành công'
        ]);
    }

    /**
     * Update player balance (add/subtract money)
     *
     * @param Request $request
     * @param int $id - player/user ID
     */
    public function updateBalance(Request $request, int $id): RedirectResponse
    {
        // Validate request data
        $this->validate($request, [
            'account_id' => 'required|integer',
            'amount' => 'required|numeric|min:1',
            'type' => 'required|in:add,minus',
            'reason' => 'nullable|string|max:500',
            'uid' => 'required|integer',
            'app_id' => 'required|integer',
        ]);

        try {
            $accountId = $request->input('account_id');
            $amount = $request->input('amount');
            $type = $request->input('type');
            $reason = $request->input('reason', '');
            $uid = (int)$request->input('uid', 0);
            $appId = (int)$request->input('app_id', 0);

            if ($type === 'add') {
                $amount = abs($amount);
            } else {
                $amount = -abs($amount);
            }

            // Get current user account list to validate
            $userAccountList = $this->paymentGrpc->getUserAccountList($this->token, $id);
            $accounts = UserBalanceDTO::convertToDTOs($userAccountList);

            $targetAccount = collect($accounts)->where('id', $accountId)->first();

            if (!$targetAccount) {
                /*return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy tài khoản'
                ], 404);*/
                return redirect()->back()->withErrors([
                    'message' => 'Không tìm thấy tài khoản'
                ])->withInput();
            }
            /*\Log::info('Balance update requested', [
                'admin_id' => auth()->id(),
                'player_id' => $id,
                'account_id' => $accountId,
                'type' => $type,
                'amount' => $amount,
                'reason' => $reason,
                'balance' => $targetAccount['balance'],
                'target_account' => $targetAccount,
            ]);*/
            // For minus operation, check if balance is sufficient
            if ($type === 'minus' && $targetAccount['balance'] < $amount) {
                /*return response()->json([
                    'success' => false,
                    'message' => 'Số dư không đủ để thực hiện giao dịch'
                ], 400);*/
                return redirect()->back()->withErrors([
                    'message' => 'Số dư không đủ để thực hiện giao dịch'
                ])->withInput();
            }

            $a = $this->paymentGrpc->cashIn($this->token, $appId, $uid, $amount, $reason);

            \Log::info('aaa: ', [$a]);

            /*if (!$a->getSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $a->getMessage() ?: 'Không thể cập nhật số dư'
                ], 400);
            }*/

            // Log the transaction for now
            // TODO: Implement actual GRPC call when payment service methods are available
            /*\Log::info('Balance update requested222: ', [
                'admin_id' => auth()->id(),
                'player_id' => $id,
                'account_id' => $accountId,
                'type' => $type,
                'amount' => $amount,
                'current_balance' => $targetAccount,
                'reason' => $reason
            ]);*/

            /*return response()->json([
                'success' => true,
                'message' => $type === 'add'
                    ? 'Yêu cầu cộng tiền đã được ghi nhận!'
                    : 'Yêu cầu trừ tiền đã được ghi nhận!',
                'data' => [
                    'amount' => $amount,
                    'type' => $type,
                    'account_id' => $accountId,
                    // 'current_balance' => $targetAccount['balance'],
                    // 'account_name' => $targetAccount['appName']
                ]
            ]);*/

            return redirect()->back()->with([
                'message' => $type === 'add'
                    ? 'Yêu cầu cộng tiền đã được ghi nhận!'
                    : 'Yêu cầu trừ tiền đã được ghi nhận!',
                'codeType' => 'success'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Balance update failed for player ID ' . $id . ': ' . $e->getMessage());

            /*return response()->json([
                'success' => false,
                'message' => 'Lỗi khi cập nhật số dư: ' . $e->getMessage()
            ], 500);*/
            return redirect()->back()->withErrors([
                'message' => 'Lỗi khi cập nhật số dư: ' . $e->getMessage()
            ])->withInput();
        }
    }
}
