<?php

namespace App\Http\Controllers\Dash;

use App\Entities\Status;
use App\Enums\ShopType;
use App\Http\Requests\Shop\ShopRequest;
use App\Http\Resources\PaginationResource;
use App\Http\Resources\Shop\ShopResource;
use App\Models\Shop;
use App\Repositories\Shop\ShopRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class ShopController extends BaseController
{
    private ShopRepository $shopRepo;
    private Status $statues;

    private string $routeIndex = 'cms.shops.index';
    private string $routeCreate = 'cms.shops.create';
    private string $routeStore = 'cms.shops.store';
    private string $routeEdit = 'cms.shops.edit';
    private string $routeUpdate = 'cms.shops.update';
    private string $routeDestroy = 'cms.shops.destroy';
    private string $routeDestroys = 'cms.shops.destroys';
    private string $routeShow = 'cms.shops.show';
    private string $routeDuplicate = 'cms.shops.duplicate';

    public function __construct(ShopRepository $repository, Status $status)
    {
        parent::__construct();
        $this->shopRepo = $repository;
        $this->statues = $status;
    }

    public function index(Request $request): Response
    {
        $perPage = (int)$request->input('per_page', $this->limit);
        $page = (int)$request->input('page', 1);
        $type = $request->input('type', null);

        $titleSite = 'Cửa hàng';

        $items = $this->shopRepo->filterItems([
            'per_page' => $perPage,
            'page' => $page,
            'type' => $type,
            'sort' => 'id:asc'
        ]);

        $items = ShopResource::collection($items);
        $paginate = new PaginationResource($items);

        return Inertia::render('Shop/Index', [
            'cateName' => $titleSite,
            'items' => $items,
            'paginate' => $paginate,
            'page' => $page,
            'type' => $type,
            'perPage' => $perPage,
            'statusArr' => $this->statues->lists(),
            'routeIndex' => $this->routeIndex,
            'routeCreate' => $this->routeCreate,
            'routeStore' => $this->routeStore,
            'routeEdit' => $this->routeEdit,
            'routeUpdate' => $this->routeUpdate,
            'routeDestroy' => $this->routeDestroy,
            'routeDestroys' => $this->routeDestroys,
            'routeShow' => $this->routeShow,
            'routeDuplicate' => $this->routeDuplicate,
            'shopTypes' => ShopType::getKeyValues()
        ]);
    }

    public function show(int $id): JsonResponse
    {
        $item = $this->shopRepo->getItem($id);
        $shopItem = new ShopResource($item);
        return response()->json(['success' => true, 'data' => $shopItem]);
    }

    public function create(): Response
    {
        return Inertia::render('Shop/Create', [
            'cateName' => 'Thêm mới sản phẩm',
            'shopTypes' => ShopType::getKeyValues(),
            'statusArr' => $this->statues->lists(),
            'routeIndex' => $this->routeIndex,
            'routeStore' => $this->routeStore,
        ]);
    }

    public function edit(int $id): Response
    {
        $item = $this->shopRepo->getItem($id);
        $shopItem = new ShopResource($item);

        return Inertia::render('Shop/Edit', [
            'cateName' => 'Chỉnh sửa sản phẩm',
            'item' => $shopItem,
            'shopTypes' => ShopType::getKeyValues(),
            'statusArr' => $this->statues->lists(),
            'routeIndex' => $this->routeIndex,
            'routeUpdate' => $this->routeUpdate,
        ]);
    }

    public function store(ShopRequest $request): RedirectResponse
    {
        $input = $request->validated();
        $input = array_filter($input, function ($value) {
            return $value !== null;
        });

        $input['created_at'] = now();
        $input['updated_at'] = now();

        \Log::info("store shop item: ", [$input]);

        $this->shopRepo->create($input);

        return redirect()->back()->with(['message' => $this->msg_created_success, 'codeType' => 'success']);
    }

    public function update(ShopRequest $request, int $id): RedirectResponse
    {
        $validated = $request->validated();
        $validated['updated_at'] = now();

        $item = $this->shopRepo->find($id);

        $item->update($validated);

        return redirect()->back()->with(['message' => $this->msg_updated_success, 'codeType' => 'success']);
    }

    public function duplicate(int $id): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $item = Shop::find($id);

            // Create a new shop item with copied data
            $newItem = $item->replicate();

            $newItem->item_name = $newItem->item_name . ' (Bản sao)';
            $newItem->created_at = now();
            $newItem->updated_at = now();
            $newItem->save();

            DB::commit();

            return redirect()->route($this->routeIndex)
                ->with([
                    'message' => $this->msg_duplicate_success,
                    'codeType' => 'success'
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::info($e->getMessage());
            return redirect()->route($this->routeIndex)
                ->with([
                    'message' => 'Có lỗi xảy ra. Vui lòng thử lại',
                    'codeType' => 'error'
                ]);
        }
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroys(Request $request): RedirectResponse
    {
        $ids = $request->input('ids');
        try {
            // Check if the IDs are an array and delete the items
            if (is_array($ids)) {
                Shop::destroy($ids);
            }

            // Redirect to the index page with a success message
            return redirect()->back()->with(['message' => $this->msg_delete_success, 'codeType' => 'success']);
        } catch (Exception $e) {
            // Report any exceptions that occur during the deletion process
            return redirect()->back()->with('message', 'Item deleted failed and ' . $e->getMessage());
        }
    }

    /**
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $item = $this->shopRepo->getItem($id);
            $item->delete();

            return redirect()->back()->with(['message' => $this->msg_delete_success, 'codeType' => 'success']);
        } catch (Exception $e) {
            return redirect()->back()->with(['message' => $this->msg_delete_failed, 'codeType' => 'error']);
        }
    }

    /**
     * Update status of shop item
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function updateStatus(Request $request, int $id): RedirectResponse
    {
        try {
            $status = $request->input('status');
            $item = $this->shopRepo->find($id);

            $item->update([
                'status' => $status,
                'updated_at' => now()
            ]);

            return redirect()->back()->with(['message' => 'Cập nhật trạng thái thành công!', 'codeType' => 'success']);
        } catch (Exception $e) {
            return redirect()->back()->with(['message' => 'Cập nhật trạng thái thất bại!', 'codeType' => 'error']);
        }
    }
}
