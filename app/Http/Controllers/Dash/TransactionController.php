<?php

namespace App\Http\Controllers\Dash;

use App\DTO\Payment\TransactionDTO;
use App\Http\Controllers\Controller;
use App\Services\PaymentGrpcClientService;
use Exception;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class TransactionController extends BaseController
{
    protected $paymentGrpc;
    protected $token = 'your_token_here';

    public function __construct(PaymentGrpcClientService $paymentGrpcClientService)
    {
        parent::__construct();
        $this->paymentGrpc = $paymentGrpcClientService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request): Response
    {
        $page = (int)$request->input('page', 1);
        $per_page = (int)$request->input('per_page', 10);
        $keyword = $request->input('s', null);
        $sort = $request->input('sort', 'id:desc');
        $conditions = null;
        if ($keyword) {
            $conditions = [
                'notes' => $keyword,
                // 'uid' => $keyword,
            ];
        }

        $res = $this->paymentGrpc->searchTransaction($this->token, $conditions, $page, $per_page, $sort);
        // dd($res);
        $items = TransactionDTO::convertToDTOs($res);

        $pagination = $res->getPagination();
        $total = $pagination->getTotal();
        $perPage = $pagination->getPerPage();
        $currentPage = $pagination->getCurrentPage();
        // dd($items);
        return Inertia::render('Transactions/Index', [
            'cateName' => 'Logs Giao Dịch',
            'items' => $items,
            'total' => $total,
            'perPage' => $perPage,
            'currentPage' => $currentPage,
            'keyword' => $keyword,
            'sortData' => $sort,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
