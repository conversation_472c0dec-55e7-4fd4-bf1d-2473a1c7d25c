<?php

namespace App\Http\Controllers\Dash;

use Illuminate\Http\JsonResponse;
use App\Models\Player;

class DashboardController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function dashboardStats(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => [
                'users_online' => rand(1500, 2500),
                'weekly_sales' => rand(80, 95),
                'total_visits' => rand(12000, 18000),
                'revenue' => rand(10000, 20000),
            ],
            'message' => 'Lấy thống kê thành công'
        ]);
    }

    public function dashboardTransactions(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => [
                ['time' => '08:42', 'type' => 'warning', 'content' => 'Người chơi XYZ đã nạp 1,000,000 chips'],
                ['time' => '10:00', 'type' => 'success', 'content' => 'Phiên đấu AEOL đã bắt đầu'],
                ['time' => '14:37', 'type' => 'danger', 'content' => 'Người chơi ABC đã rút 500,000 chips'],
                ['time' => '16:50', 'type' => 'primary', 'content' => 'Cập nhật bảng xếp hạng tuần'],
                ['time' => '21:03', 'type' => 'danger', 'content' => 'Ván chơi mới #XF-2356 đã kết thúc'],
            ],
            'message' => 'Lấy log giao dịch thành công'
        ]);
    }

    public function getTopPlayersByWinRate(): JsonResponse
    {
        $topPlayers = Player::query()
            ->whereNotNull('win_rate')
            ->where('win_rate', '>', 0)
            ->orderBy('win_rate', 'desc')
            ->limit(5)
            ->get([
                'id',
                'nick_name',
                'display_name',
                'win_rate',
                'avatar'
            ]);

        return response()->json([
            'status' => 'success',
            'data' => $topPlayers,
            'message' => 'Lấy danh sách top cao thủ thành công'
        ]);
    }

    public function getTopPlayersByBalance(): JsonResponse
    {
        $topPlayers = Player::query()
            ->whereNotNull('balance')
            ->where('balance', '>', 0)
            ->orderBy('balance', 'desc')
            ->limit(5)
            ->get([
                'id',
                'nick_name',
                'display_name',
                'balance',
                'avatar'
            ]);

        return response()->json([
            'status' => 'success',
            'data' => $topPlayers,
            'message' => 'Lấy danh sách top tài phú thành công'
        ]);
    }

    public function getTopPlayersByLevel(): JsonResponse
    {
        $topPlayers = Player::query()
            ->whereNotNull('level')
            ->where('level', '>', 0)
            ->orderBy('level', 'desc')
            ->limit(5)
            ->get([
                'id',
                'nick_name',
                'display_name',
                'level',
                'avatar'
            ]);

        return response()->json([
            'status' => 'success',
            'data' => $topPlayers,
            'message' => 'Lấy danh sách top level thành công'
        ]);
    }
}
