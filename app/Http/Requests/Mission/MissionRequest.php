<?php

namespace App\Http\Requests\Mission;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class MissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string'],
            'name' => ['required', 'string'],
            'code' => ['string', 'nullable'],
            'description' => ['string'],
            'repeatable' => ['integer'],
            'condition_json' => ['required', 'array'],
            'reward_json' => ['required', 'array'],
            'is_active' => ['integer', 'in:0,1']
        ];
    }
}
