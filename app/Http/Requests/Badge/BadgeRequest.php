<?php

namespace App\Http\Requests\Badge;

use App\Enums\BadgeCategory;
use App\Enums\BadgeRewardType;
use App\Enums\BadgeType;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BadgeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string', Rule::in(BadgeType::getValues())],
            'name' => ['required', 'string', 'max:100'],
            'code' => ['string', 'nullable', 'max:50'],
            'description' => ['required', 'string'],
            'condition_json' => ['required', 'array'],
            'reward' => ['nullable', 'string', 'max:255'],
            'category' => ['required', 'string', Rule::in(BadgeCategory::getValues())],
            'icon_url' => ['nullable', 'string', 'max:255'],

            // Badge rewards validation
            'rewards' => ['nullable', 'array'],
            'rewards.*.reward_type' => ['required_with:rewards', 'string', Rule::in(BadgeRewardType::getValues())],
            'rewards.*.reward_value' => ['required_with:rewards', 'string', 'max:255'],
            'rewards.*.is_permanent' => ['nullable', 'boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'type.required' => 'Loại thành tích là bắt buộc',
            'type.in' => 'Loại thành tích không hợp lệ',
            'name.required' => 'Tên thành tích là bắt buộc',
            'name.max' => 'Tên thành tích không được vượt quá 100 ký tự',
            'description.required' => 'Mô tả là bắt buộc',
            'category.required' => 'Danh mục là bắt buộc',
            'category.in' => 'Danh mục không hợp lệ',
            'condition_json.required' => 'Điều kiện là bắt buộc',
            'condition_json.array' => 'Điều kiện phải là một mảng',
            'rewards.*.reward_type.required_with' => 'Loại phần thưởng là bắt buộc',
            'rewards.*.reward_type.in' => 'Loại phần thưởng không hợp lệ',
            'rewards.*.reward_value.required_with' => 'Giá trị phần thưởng là bắt buộc',
        ];
    }
}
