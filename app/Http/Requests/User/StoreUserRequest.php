<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    final public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:250'],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:250', 'unique:users,email'],
            'phone' => ['nullable', 'string', 'max:250'],
            // 'phone' => ['required', 'string', 'max:250', 'unique:users,phone'],
            // 'roleIds' => ['required', 'array'],
            // 'roleId' => ['required', 'integer', 'gt:0'],
            // 'password' => ['required', 'string', 'min:1', 'confirmed'],
            'password' => ['required', 'string', 'min:1'],
            'status' => ['nullable', 'integer'],
            // 'bio' => ['nullable', 'string'],
            // 'image' => ['nullable', 'image', 'mimes:jpeg,jpg,png', 'max:10048']
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email không được để trống',
            'email.unique' => 'Email đã tồn tai, vui lòng chọn 1 email khác !',
            'phone.required' => 'Số điện thoại không được để trống ',
            'phone.unique' => 'Số điện thoại đã tồn tai, vui này chọn 1 số khác !',
            // 'roleIds.required' => 'Bạn chưa chọn Nhóm',
            'password.required' => 'Mật khẩu không được để trống',
            'password.confirmed' => 'Mật khẩu xác nhận không khớp',
            'password.min' => 'Mật khẩu phải tối thiểu 6 ký tự',
            'name.required' => 'Bạn chưa nhập tên',
        ];
    }
}
