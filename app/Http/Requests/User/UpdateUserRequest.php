<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required'],
            'email' => ['required', 'email'],
            'password' => ['nullable', 'min:1'],
            'status' => ['nullable', 'integer'],
            // 'bio' => ['nullable', 'string'],
            // 'image' => ['nullable', 'image', 'mimes:jpeg,jpg,png', 'max:10048'],
            // 'isChangeAvatar' => ['boolean', 'nullable']
        ];
    }
}
