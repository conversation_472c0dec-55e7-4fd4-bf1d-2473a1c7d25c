<?php

namespace App\Http\Requests\Role;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="RoleRequest",
 *      title="Role Request schema for using references",
 *      @OA\Property(
 *         property="name",
 *         type="string",
 *          example="Administrator"
 *      ),
 *      @OA\Property(
 *          property="description",
 *          type="string",
 *          example="Nhóm admin"
 *      ),
 *      @OA\Property(
 *           property="permissions",
 *           type="array",
 *           collectionFormat="multi",
 *           example={
 *               "roles.index": true,
 *               "roles.create": false
 *           },
 *           @OA\Items(
 *               @OA\Property(
 *                   property="roles.index",
 *                   type="boolean",
 *                   example="true"
 *               )
 *           ),
 *      ),
 *      @OA\Property(
 *          property="status",
 *          type="integer",
 *          example="1"
 *      )
 * )
 */
class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return match ($this->method()) {
            'POST' => [
                'name' => ['required', 'unique:roles,name', 'string'],
                'description' => ['nullable', 'string'],
                // 'permissions' => ['array'],
                'status' => ['numeric', 'in:0,1'],
            ],
            'PUT', 'PATCH' => [
                'name' => ['required', 'string'],
                'description' => ['nullable', 'string'],
                // 'permissions' => ['array'],
                'status' => ['numeric', 'in:0,1'],
            ],
            default => [],
        };
    }
}
