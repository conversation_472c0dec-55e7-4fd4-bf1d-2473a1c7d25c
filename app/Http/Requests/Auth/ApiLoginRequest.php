<?php

namespace App\Http\Requests\Auth;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *      schema="ApiLoginRequest",
 *      title="Api Login Request schema for using references",
 *      @OA\Property(
 *          type="object",
 *          @OA\Property(
 *              property="email",
 *              type="string"
 *          ),
 *          @OA\Property(
 *              property="password",
 *              type="string"
 *          )
 *      ),
 *      example={
 *          "email":"<EMAIL>",
 *          "password":"1234"
 *      }
 * )
 */
class ApiLoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'max:255'],
            'password' => 'required|min:1'
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email không được để trống',
            'email.email' => 'Email không đúng định dạng',
            // 'phone.numeric' => 'Số điện thoại phải là số',
            // 'phone.digits' => 'Số điện thoại phải có 10 chữ số',
            'password.required' => 'Mật khẩu không được để trống',
            'password.min' => 'Mật khẩu phải có ít nhất 1 ký tự',
        ];
    }

    public function failedValidation(Validator $validator): JsonResponse
    {

        $errors = (new ValidationException($validator))->errors();

        $response = [
            'success' => false,
            'message' => 'Có lỗi xảy ra !',
        ];


        if (!empty($errors)) {
            $response['data'] = $errors;
        }

        throw new HttpResponseException(response()->json(
            $response, JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
