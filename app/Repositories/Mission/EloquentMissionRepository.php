<?php


namespace App\Repositories\Mission;

use App\Models\Mission;
use App\Repositories\Base\BaseRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;


class EloquentMissionRepository extends BaseRepository implements MissionRepository
{
    public function __construct(Mission $model)
    {
        parent::__construct($model);
    }

    /*public function getItemsByStatus($status)
    {
        return $this->model::where('status', Status::ACTIVE)->get();
    }*/

    public function filterItems(array $params): LengthAwarePaginator
    {
        $items = $this->model::query();

        if (isset($params['keyword'])) {
            $keywords = $params['keyword'] ?? '';
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('name', 'like', '%' . $keywords . '%');
                $query->orWhere('detail', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['type'])) {
            $items = $items->where('type', $params['type']);
        }

        if (isset($params['is_active'])) {
            $items = $items->where('is_active', $params['is_active']);
        }
        
        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }
        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }
        list($sort_key, $sort_value) = explode(':', $sort);

        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }
}
