<?php


namespace App\Repositories\Faq;

use App\Entities\Status;
use App\Models\Faq;
use App\Repositories\Base\BaseRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;


class EloquentFaqRepository extends BaseRepository implements FaqRepository
{
    public function __construct(Faq $model)
    {
        parent::__construct($model);
    }

    public function getItemsByStatus($status)
    {
        return $this->model::where('status', Status::ACTIVE)->get();
    }

    public function filterItems(array $params): LengthAwarePaginator
    {
        $items = $this->model::query();

        if (isset($params['keyword'])) {
            $keywords = $params['keyword'] ?? '';
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('title', 'like', '%' . $keywords . '%');
                $query->orWhere('detail', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['type']) && $params['type'] > 0) {
            $items = $items->where('type', $params['type']);
        }

        if (isset($params['status']) && $params['status'] != -1) {
            $items = $items->where('status', $params['status']);
        }

        /*if (isset($params['promoted']) && $params['promoted'] != -1) {
            $items = $items->where('promoted', $params['promoted']);
        }*/

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }
        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }
        list($sort_key, $sort_value) = explode(':', $sort);

        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }
}
