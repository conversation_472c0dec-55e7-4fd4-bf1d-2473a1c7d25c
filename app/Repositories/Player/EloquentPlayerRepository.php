<?php


namespace App\Repositories\Player;

use App\Models\Player;
use App\Repositories\Base\BaseRepository;


class EloquentPlayerRepository extends BaseRepository implements PlayerRepository
{
    public function __construct(Player $model)
    {
        parent::__construct($model);
        // $this->minutesCache = 30;
    }

    /**
     * Lấy thông tin của 1 category theo id
     * @param int $id
     */
    public function getItem(int $id)
    {
        return $this->model->find($id);
    }

    public function filterItems(array $params)
    {
        $items = $this->model::query();

        if (isset($params['type'])) {
            $items = $items->where('type', $params['type']);
        }

        // $keywords = $request->input('keyword', '');
        if (isset($params['keyword'])) {
            $keywords = $params['keyword'] ?? '';
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('name', 'like', '%' . $keywords . '%');
                $query->orWhere('email', 'like', '%' . $keywords . '%');
                $query->orWhere('phone', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['roleId']) && (int)$params['roleId'] > 0) {
            $roleId = $params['roleId'] ?? 0;
            $items = $items->whereHas('roles', function ($query) use ($roleId) {
                $query->where('role_id', $roleId);
            });
        }

        if (isset($params['status']) && $params['status'] != -1) {
            $items = $items->where('status', $params['status']);
        }

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }
        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }
        list($sort_key, $sort_value) = explode(':', $sort);
        // return $items->orderBy($sort_key, $sort_value)->paginate($per_page);

        $items = $items->orderBy($sort_key, $sort_value);
        $isPaginate = !(isset($params['paginate']) && $params['paginate'] == 'no');

        if ($isPaginate) {
            return $items->paginate($per_page, ['*'], 'page', $params['page'] ?? 1);
        }
        return $items->get();
    }

}
