<?php


namespace App\Repositories\Shop;

use App\Models\Shop;
use App\Repositories\Base\BaseRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;


class EloquentShopRepository extends BaseRepository implements ShopRepository
{
    public function __construct(Shop $model)
    {
        parent::__construct($model);
    }

    /*public function getItemsByStatus($status)
    {
        return $this->model::where('status', Status::ACTIVE)->get();
    }*/

    public function filterItems(array $params): LengthAwarePaginator
    {
        $items = $this->model::query();

        if (isset($params['keyword'])) {
            $keywords = $params['keyword'] ?? '';
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('item_name', 'like', '%' . $keywords . '%');
                $query->orWhere('description', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['type'])) {
            $items = $items->where('type', $params['type']);
        }

        if (isset($params['category'])) {
            $items = $items->where('category', $params['category']);
        }

        if (isset($params['status']) && $params['status'] != -1) {
            $items = $items->where('status', $params['status']);
        }
        
        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }
        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }
        list($sort_key, $sort_value) = explode(':', $sort);

        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }
}
