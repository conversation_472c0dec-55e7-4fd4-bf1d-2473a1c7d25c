<?php


namespace App\Repositories\Base;


use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;


// abstract class BaseRepository implements RepositoryInterface
class BaseRepository implements RepositoryInterface
{
    /**
     * @var Model
     */
    public Model $model;

    /**
     * The query builder
     *
     * @var Builder
     */
    protected Builder $query;


    /**
     * Alias for the query limit
     *
     * @var int
     */
    protected int $take;


    /**
     * Array of related models to eager load
     *
     * @var array
     */
    protected array $with = array();


    /**
     * Array of one or more where clause parameters
     *
     * @var array
     */
    protected array $wheres = array();


    /**
     * Array of one or more where in clause parameters
     *
     * @var array
     */
    protected array $whereIns = array();


    /**
     * Array of one or more ORDER BY column/value pairs
     *
     * @var array
     */
    protected array $orderBys = array();


    /**
     * Array of scope methods to call on the model
     *
     * @var array
     */
    protected array $scopes = array();

    public function __construct(Model $model)
    {
        // $this->app = new App();
        // $this->makeModel();
        $this->model = $model;
    }

    /**
     * Get all the model records in the database
     *
     * @return Collection
     */
    public function all($columns = ['*'])
    {
        $this->newQuery()->eagerLoad();

        $models = $this->query->get();

        $this->unsetClauses();

        return $models;
    }

    /**
     * Add relationships to the query builder to eager load
     *
     * @return $this
     */
    protected function eagerLoad()
    {
        foreach ($this->with as $relation) {
            $this->query->with($relation);
        }

        return $this;
    }

    /**
     * Set Eloquent relationships to eager load
     *
     * @param $relations
     *
     * @return $this
     */
    public function with($relations)
    {
        if (is_string($relations)) $relations = func_get_args();

        $this->with = $relations;

        return $this;
    }

    /**
     * Create a new instance of the model's query builder
     *
     * @return $this
     */
    protected function newQuery()
    {
        $this->query = $this->model->newQuery();

        return $this;
    }

    /**
     * Get all the specified model records in the database
     *
     * @return Collection
     */
    public function get()
    {
        $this->newQuery()->eagerLoad()->setClauses()->setScopes();

        $models = $this->query->get();

        $this->unsetClauses();

        return $models;
    }

    /*public function all($columns = ['*'])
    {
        return $this->model->all();
    }*/

    /**
     * Set query scopes
     *
     * @return $this
     */
    protected function setScopes()
    {
        foreach ($this->scopes as $method => $args) {
            $this->query->$method(implode(', ', $args));
        }

        return $this;
    }

    /**
     * Set clauses on the query builder
     *
     * @return $this
     */
    protected function setClauses()
    {
        foreach ($this->wheres as $where) {
            $this->query->where($where['column'], $where['operator'], $where['value']);
        }

        foreach ($this->whereIns as $whereIn) {
            $this->query->whereIn($whereIn['column'], $whereIn['values']);
        }

        foreach ($this->orderBys as $orders) {
            $this->query->orderBy($orders['column'], $orders['direction']);
        }

        if (isset($this->take) and !is_null($this->take)) {
            $this->query->take($this->take);
        }

        return $this;
    }

    /**
     * Add a simple where clause to the query
     *
     * @param string $column
     * @param string $value
     * @param string $operator
     *
     * @return $this
     */
    public function where($column, $value, $operator = '=')
    {
        $this->wheres[] = compact('column', 'value', 'operator');

        return $this;
    }

    /**
     * Add a simple where in clause to the query
     *
     * @param string $column
     * @param mixed $values
     *
     * @return $this
     */
    public function whereIn($column, $values)
    {
        $values = is_array($values) ? $values : array($values);

        $this->whereIns[] = compact('column', 'values');

        return $this;
    }

    /**
     * Set an ORDER BY clause
     *
     * @param string $column
     * @param string $direction
     * @return $this
     */
    public function orderBy($column, $direction = 'asc')
    {
        $this->orderBys[] = compact('column', 'direction');

        return $this;
    }

    /**
     * Reset the query clause parameter arrays
     *
     * @return $this
     */
    protected function unsetClauses()
    {
        $this->wheres = array();
        $this->whereIns = array();
        $this->scopes = array();
        $this->take = 0; // null;

        return $this;
    }
    /*public function delete($id)
    {
        return $this->model->destroy($id);
    }*/

    /**
     * Count the number of specified model records in the database
     *
     * @return int
     */
    public function count()
    {
        return $this->get()->count();
    }

    /**
     * Create one or more new model records in the database
     *
     * @param array $data
     *
     * @return Collection
     */
    public function createMultiple(array $data): Collection
    {
        $models = new Collection();

        foreach ($data as $d) {
            $models->push($this->create($d));
        }

        return $models;
    }

    /**
     * Create a new model record in the database
     *
     * @param array $input
     *
     * @return Model
     */
    public function create(array $input): Model
    {
        // return $this->model->create($input);
        $this->unsetClauses();

        return $this->model::create($input);
    }

    /**
     * Delete the specified model record from the database
     *
     * @param $id
     *
     * @return bool|null
     * @throws Exception
     */
    public function deleteById($id)
    {
        $this->unsetClauses();

        return $this->getById($id)->delete();
    }

    /**
     * Delete one or more model records from the database
     *
     * @return mixed
     */
    public function delete()
    {
        $this->newQuery()->setClauses()->setScopes();

        $result = $this->query->delete();

        $this->unsetClauses();

        return $result;
    }

    /**
     * Get the specified model record from the database
     *
     * @param $id
     *
     * @return Model
     */
    public function getById($id): Model
    {
        $this->unsetClauses();

        $this->newQuery()->eagerLoad();

        return $this->query->findOrFail($id);
    }

    /**
     * Delete multiple records
     *
     * @param array $ids
     *
     * @return int
     */
    public function deleteMultipleById(array $ids): int
    {
        return $this->model->destroy($ids);
    }

    /**
     * @param $item
     * @param $column
     * @param array $columns
     * @return Builder|Model|object|null
     */
    public function getByColumn($item, $column, array $columns = ['*'])
    {
        $this->unsetClauses();

        $this->newQuery()->eagerLoad();

        return $this->query->where($column, $item)->first($columns);
    }

    /**
     * Get the first specified model record from the database
     *
     * @return Model
     */
    public function first(): Model
    {
        $this->newQuery()->eagerLoad()->setClauses()->setScopes();

        $model = $this->query->firstOrFail();

        $this->unsetClauses();

        return $model;
    }

    /*public function update($id, array $input)
    {
        $model = $this->model->findOrFail($id);
        $model->fill($input);
        $model->save();

        return $this;
    }*/

    /**
     * Update the specified model record in the database
     *
     * @param       $id
     * @param array $data
     *
     * @return Model
     */
    public function updateById($id, array $data)
    {
        $this->unsetClauses();

        $model = $this->getById($id);

        $model->update($data);

        return $model;
    }

    public function update($model, $data)
    {
        $model->update($data);

        return $model;
    }

    /**
     * @param int $limit
     * @param array $columns
     * @param string $pageName
     * @param null $page
     * @return LengthAwarePaginator
     */
    public function paginate($limit = 25, array $columns = ['*'], $pageName = 'page', $page = null): LengthAwarePaginator
    {
        $this->newQuery()->eagerLoad()->setClauses()->setScopes();

        $models = $this->query->paginate($limit, $columns, $pageName, $page);

        $this->unsetClauses();

        return $models;
    }

    public function allByAttributes(array $attributes, $sort = 'id:desc', $limit = 5)
    {
        $query = $this->buildQueryByAttributes($attributes, $sort, $limit);

        return $query->get();
    }


    /*public function paginate($limit = null, $columns = ['*'])
    {
        //$limit = is_null($limit) ? config('repository.pagination.limit', 10) : $limit;
        $limit = $limit === null ? config('repository.pagination.limit', 10) : $limit;

        return $this->model::paginate($limit, $columns);
    }*/

    private function buildQueryByAttributes(array $attributes, $sort = 'id:desc', $limit = 5)
    {
        $query = $this->model->query();

        foreach ($attributes as $field => $value) {
            $query = $query->where($field, $value);
        }

        if (null !== $sort) {
            list($sort_key, $sort_value) = explode(':', $sort);
            $query->orderBy($sort_key, $sort_value);
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        return $query;
    }

    /**
     * Set the query limit
     *
     * @param int $limit
     *
     * @return $this
     */
    public function limit($limit)
    {
        $this->take = $limit;

        return $this;
    }


    /*public function whereIn($attribute, array $values, $columns = ['*'])
    {
        return $this->model->whereIn($attribute, $values); // ->get($columns);
    }*/

    /*public function where(array $where, $columns = ['*'])
    {
        $this->applyConditions($where);
        return $this->model->get($columns);
    }*/

    public function where2($attribute, $value)
    {
        // $this->applyConditions($where);
        return $this->model::where($attribute, $value);
    }

    public function findBySlug($slug)
    {
        return $this->model::where('slug', $slug)->first();
    }

    public function getItem(int $id)
    {
        return $this->model->find($id);
    }

    public function find($id, $columns = ['*'])
    {
        $this->unsetClauses();

        $this->newQuery()->eagerLoad();

        return $this->query->findOrFail($id, $columns);

        // return $this->model->findOrFail($id, $columns);
    }

    /*public function makeModel()
    {
        $model = $this->app->make($this->model);
        if (!$model instanceof Model) {
            throw new ModelNotFoundException("Class {$this->model()} must be an instance of Illuminate\\Database\\Eloquent\\Model");
        }
        return $this->model = $model;
    }

    abstract public function model();*/

    /**
     * Build Query to catch resources by an array of attributes and params
     * @param array $attributes
     * @param null|string $sort
     * @param string $limit
     * @return Builder
     */
    // private function buildQueryByAttributes(array $attributes, $orderBy = null, $sortOrder = 'asc', $limit = 5)

    protected function applyConditions(array $where)
    {
        foreach ($where as $field => $value) {
            if (is_array($value)) {
                list($field, $condition, $val) = $value;
                $this->model = $this->model->where($field, $condition, $val);
            } else {
                $this->entity = $this->model->where($field, '=', $value);
            }
        }
    }

}
