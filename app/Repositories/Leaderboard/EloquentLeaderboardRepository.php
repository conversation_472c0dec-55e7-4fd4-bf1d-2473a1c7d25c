<?php


namespace App\Repositories\Leaderboard;

use App\Models\LeaderBoard;
use App\Repositories\Base\BaseRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;


class EloquentLeaderboardRepository extends BaseRepository implements LeaderboardRepository
{
    public function __construct(LeaderBoard $model)
    {
        parent::__construct($model);
    }

    /*public function getItemsByStatus($status)
    {
        return $this->model::where('status', Status::ACTIVE)->get();
    }*/

    public function filterItems(array $params): LengthAwarePaginator
    {
        $items = $this->model::query()->with(['player']);

        if (isset($params['keyword'])) {
            $keywords = $params['keyword'] ?? '';
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('title', 'like', '%' . $keywords . '%');
                $query->orWhere('detail', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['type'])) {
            $items = $items->where('leaderboard_type', $params['type']);
        }

        if (isset($params['scope'])) {
            $items = $items->where('scope', $params['scope']);
        }

        if (isset($params['season'])) {
            $items = $items->where('season', $params['season']);
        }

        if (isset($params['player_id']) && $params['player_id'] > 0) {
            $items = $items->where('player_id', $params['player_id']);
        }

        /*if (isset($params['promoted']) && $params['promoted'] != -1) {
            $items = $items->where('promoted', $params['promoted']);
        }*/

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }
        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }
        list($sort_key, $sort_value) = explode(':', $sort);

        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }
}
