<?php

namespace App\Entities;

/**
 * Class Status
 * @package App\Entities
 */
class Status
{
    const INACTIVE = 0;
    const ACTIVE = 1;

    /**
     * @var array
     */
    private array $statuses = [];

    public function __construct()
    {
        $this->statuses = [
            self::INACTIVE => 'Không hiển thị', // 'InActive',
            self::ACTIVE => 'Hiển thị',
        ];
    }

    /**
     * Get the available statuses
     * @return array
     */
    public function lists(): array
    {
        return $this->statuses;
    }

    public function toArrayWithKeyValue(): array
    {
        $statusArr = $this->lists();
        return array_map(function ($key, $value) {
            return ['value' => (string) $key, 'label' => $value];
        }, array_keys($statusArr), $statusArr);
    }

    /**
     * Get the post status
     * @param int $statusId
     * @return string
     */
    public function get(int $statusId): string
    {
        if (isset($this->statuses[$statusId])) {
            return $this->statuses[$statusId];
        }

        return $this->statuses[self::ACTIVE];
    }
}
