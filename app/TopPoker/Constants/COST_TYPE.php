<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.COST_TYPE</code>
 */
class COST_TYPE
{
    /**
     * Generated from protobuf enum <code>COST_BY_PERCENT = 0;</code>
     */
    const COST_BY_PERCENT = 0;
    /**
     * Generated from protobuf enum <code>COST_BY_CONST = 1;</code>
     */
    const COST_BY_CONST = 1;

    private static $valueToName = [
        self::COST_BY_PERCENT => 'COST_BY_PERCENT',
        self::COST_BY_CONST => 'COST_BY_CONST',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

