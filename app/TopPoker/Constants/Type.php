<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.Type</code>
 */
class Type
{
    /**
     * Generated from protobuf enum <code>FANTASY = 0;</code>
     */
    const FANTASY = 0;
    /**
     * Generated from protobuf enum <code>AUTOBIOGRAPHY = 1;</code>
     */
    const AUTOBIOGRAPHY = 1;
    /**
     * Generated from protobuf enum <code>HISTORY = 2;</code>
     */
    const HISTORY = 2;

    private static $valueToName = [
        self::FANTASY => 'FANTASY',
        self::AUTOBIOGRAPHY => 'AUTOBIOGRAPHY',
        self::HISTORY => 'HISTORY',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

