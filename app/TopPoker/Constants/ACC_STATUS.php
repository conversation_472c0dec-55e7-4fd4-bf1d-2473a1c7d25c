<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.ACC_STATUS</code>
 */
class ACC_STATUS
{
    /**
     * Generated from protobuf enum <code>STATUS_ENABLED = 0;</code>
     */
    const STATUS_ENABLED = 0;
    /**
     * Generated from protobuf enum <code>STATUS_LOCKED = 1;</code>
     */
    const STATUS_LOCKED = 1;

    private static $valueToName = [
        self::STATUS_ENABLED => 'STATUS_ENABLED',
        self::STATUS_LOCKED => 'STATUS_LOCKED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

