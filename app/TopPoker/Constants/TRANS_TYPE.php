<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.TRANS_TYPE</code>
 */
class TRANS_TYPE
{
    /**
     * Generated from protobuf enum <code>PAYMENT = 0;</code>
     */
    const PAYMENT = 0;
    /**
     * Generated from protobuf enum <code>TRANSFER = 1;</code>
     */
    const TRANSFER = 1;
    /**
     * Generated from protobuf enum <code>CASHIN = 2;</code>
     */
    const CASHIN = 2;
    /**
     * Generated from protobuf enum <code>EXCHANGE = 3;</code>
     */
    const EXCHANGE = 3;

    private static $valueToName = [
        self::PAYMENT => 'PAYMENT',
        self::TRANSFER => 'TRANSFER',
        self::CASHIN => 'CASHIN',
        self::EXCHANGE => 'EXCHANGE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

