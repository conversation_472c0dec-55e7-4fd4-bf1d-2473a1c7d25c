<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.ErrorCode</code>
 */
class ErrorCode
{
    /**
     * Generated from protobuf enum <code>OK = 0;</code>
     */
    const OK = 0;
    /**
     * Generated from protobuf enum <code>UNAUTHORIZE = 1;</code>
     */
    const UNAUTHORIZE = 1;
    /**
     * Generated from protobuf enum <code>INVALID_TOKEN = 2;</code>
     */
    const INVALID_TOKEN = 2;
    /**
     * Generated from protobuf enum <code>ACCOUNT_NOT_EXISTS = 3;</code>
     */
    const ACCOUNT_NOT_EXISTS = 3;
    /**
     * Generated from protobuf enum <code>ACCOUNT_EXISTS = 4;</code>
     */
    const ACCOUNT_EXISTS = 4;
    /**
     * Generated from protobuf enum <code>ACCOUNT_LOCKED = 5;</code>
     */
    const ACCOUNT_LOCKED = 5;
    /**
     * Generated from protobuf enum <code>NOT_ENOUGH_MONEY = 6;</code>
     */
    const NOT_ENOUGH_MONEY = 6;
    /**
     * Generated from protobuf enum <code>INVALID_PARAM = 7;</code>
     */
    const INVALID_PARAM = 7;
    /**
     * Generated from protobuf enum <code>EXECUTION_FAILED = 8;</code>
     */
    const EXECUTION_FAILED = 8;
    /**
     * Generated from protobuf enum <code>OPERATION_NOT_PERMIT = 9;</code>
     */
    const OPERATION_NOT_PERMIT = 9;
    /**
     * Generated from protobuf enum <code>DATA_NOTFOUND = 10;</code>
     */
    const DATA_NOTFOUND = 10;

    private static $valueToName = [
        self::OK => 'OK',
        self::UNAUTHORIZE => 'UNAUTHORIZE',
        self::INVALID_TOKEN => 'INVALID_TOKEN',
        self::ACCOUNT_NOT_EXISTS => 'ACCOUNT_NOT_EXISTS',
        self::ACCOUNT_EXISTS => 'ACCOUNT_EXISTS',
        self::ACCOUNT_LOCKED => 'ACCOUNT_LOCKED',
        self::NOT_ENOUGH_MONEY => 'NOT_ENOUGH_MONEY',
        self::INVALID_PARAM => 'INVALID_PARAM',
        self::EXECUTION_FAILED => 'EXECUTION_FAILED',
        self::OPERATION_NOT_PERMIT => 'OPERATION_NOT_PERMIT',
        self::DATA_NOTFOUND => 'DATA_NOTFOUND',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

