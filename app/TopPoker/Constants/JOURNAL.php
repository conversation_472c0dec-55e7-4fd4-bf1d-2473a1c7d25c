<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: constants/util.proto

namespace App\TopPoker\Constants;

use UnexpectedValueException;

/**
 * Protobuf type <code>constants.JOURNAL</code>
 */
class JOURNAL
{
    /**
     * Generated from protobuf enum <code>DEBIT = 0;</code>
     */
    const DEBIT = 0;
    /**
     * Generated from protobuf enum <code>CREDIT = 1;</code>
     */
    const CREDIT = 1;

    private static $valueToName = [
        self::DEBIT => 'DEBIT',
        self::CREDIT => 'CREDIT',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

