<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;

class ScanRoutes extends Command
{
    protected $signature = 'scan:routes';
    protected $description = 'Scan all routes and save to permissions table';

    public function handle()
    {
        $routes = Route::getRoutes();
        $permissions = [];

        foreach ($routes as $route) {
            if ($name = $route->getName()) {
                // Exclude routes that belong to the 'swagger' and 'sanctum' groups
                if (!str_contains($name, 'swagger') && !str_contains($name, 'sanctum') && !str_contains($name, 'ignition')
                    && !str_contains($name, 'auth-user') && !str_contains($name, 'not-authenticated')
                    && !str_contains($name, 'verification.'))
                {
                    $methods = implode(',', $route->methods());
                    $permissions[] = [
                        'name' => $name,
                        'value' => $name,
                        'methods' => $methods,
                        'status' => 1,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
            }
        }

        print_r($permissions);
        DB::table('permissions')->insert($permissions);

        $this->info('Routes have been scanned and saved to permissions table.');
    }
}
