<?php

namespace App\DTO;

class UserDTO
{
    public function __construct(
        public int    $id,
        public string $userId,
        public string $username,
        public string $email,
        public string $firstName,
        public string $lastName,
        public string $fullName,
        public string $gender,
        public string $phone,
        public string $avatar,
        public string $loginType,
        public int    $createdTimestamp,
        public string $createdAt,
        public string $lastLogin
    )
    {
    }

    public static function convertToUserDTO($response): array
    {
        $users = [];
        foreach ($response->getUsers() as $user) {
            $users[] = new UserDTO(
                id: $user->getId(),
                userId: $user->getUserId(),
                username: $user->getUsername(),
                email: $user->getEmail(),
                firstName: $user->getFirstName(),
                lastName: $user->getLastName(),
                fullName: $user->getFullName(),
                gender: $user->getGender(),
                phone: $user->getPhoneNumber(),
                avatar: $user->getAvatar(),
                loginType: $user->getLoginType(),
                createdTimestamp: $user->getCreatedTimestamp(),
                createdAt: $user->getCreatedAt(),
                lastLogin: $user->getLastLogin()
            );
        }
        return $users;
    }

    public static function convertToSingleItem($item): object
    {
        return new UserDTO(
            id: $item->getId(),
            userId: $item->getUserId(),
            username: $item->getUsername(),
            email: $item->getEmail(),
            firstName: $item->getFirstName(),
            lastName: $item->getLastName(),
            fullName: $item->getFullName(),
            gender: $item->getGender(),
            phone: $item->getPhoneNumber(),
            avatar: $item->getAvatar(),
            loginType: $item->getLoginType(),
            createdTimestamp: $item->getCreatedTimestamp(),
            createdAt: $item->getCreatedAt(),
            lastLogin: $item->getLastLogin()
        );
    }
}
