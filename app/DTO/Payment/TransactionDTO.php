<?php

namespace App\DTO\Payment;

use App\Services\TopPoker\PaymentService\TRANS_SEARCH_RESULT;

class TransactionDTO
{
    public int $id;
    public int $transType;
    public string $transDate;
    public float $amount;
    public string $description;
    // public int $account_id;
    // public int $uid;
    // public string $type;
    // public float $before_balance;
    // public float $after_balance;
    // public string $status;
    public string $systemNote;
    public int $debitUid;
    public string $debitName;
    public int $debitApp;
    // public string $created_at;
    // public string $updated_at;

    /**
     * Create a DTO instance from a transaction object
     *
     * @param object $transaction
     * @return self
     */
    public static function createFromObject(object $transaction): self
    {
        $dto = new self();
        $dto->id = $transaction->getTid();
        $dto->transType = $transaction->getTransType();
        $dto->transDate = $transaction->getTransDate();
        $dto->amount = $transaction->getAmount();
        $dto->description = $transaction->getDescription();
        $dto->systemNote = $transaction->getSystemNote();
        $dto->debitUid = $transaction->getDebitUid();
        $dto->debitName = $transaction->getDebitName();
        $dto->debitApp = $transaction->getDebitApp();

        // $dto->account_id = $transaction->getAccountId();
        // $dto->uid = $transaction->getUid();
        // $dto->type = $transaction->getType();
        // $dto->before_balance = $transaction->getBeforeBalance();
        // $dto->after_balance = $transaction->getAfterBalance();
        // $dto->status = $transaction->getStatus();
        // $dto->notes = $transaction->getNotes();
        // $dto->created_at = $transaction->getCreatedAt();
        // $dto->updated_at = $transaction->getUpdatedAt();

        return $dto;
    }

    /**
     * Convert a collection of transaction objects to DTOs
     *
     * @param TRANS_SEARCH_RESULT $transSearchResult
     * @return array
     */
    public static function convertToDTOs(TRANS_SEARCH_RESULT $transSearchResult): array
    {
        $dtos = [];
        $transactions = $transSearchResult->getItems();

        foreach ($transactions as $transaction) {
            $dto = self::createFromObject($transaction);
            $dtos[] = (array) $dto;
        }

        return $dtos;
    }
}
