<?php

namespace App\DTO;

use App\Entities\Status;

class AppDTO
{
    public function __construct(
        public int $id,
        public string $appName,
        public string $status,
        public bool $isStatus,
        public string $notes,
        public int $transfer,
        public float $transferCost,
        public string $costType,
        public string $systemAcc,
        public string $unit,
        public int $pid,
        public int $ext,
        public string $value,
        public string $label,
    ) {}

    public static function convertToAppDTOs($response): array
    {
        $apps = [];
        foreach ($response->getApps() as $item) {
            $apps[] = new AppDTO(
                id: $item->getId(),
                appName: $item->getAppName(),
                status: $item->getStatus(),
                isStatus: Status::ACTIVE === $item->getStatus(),
                notes: $item->getNotes(),
                transfer: $item->getTransfer(),
                transferCost: $item->getTransferCost(),
                costType: $item->getCostType(),
                systemAcc: $item->getSystemAcc(),
                unit: $item->getUnit(),
                pid: $item->getPid(),
                ext: $item->getExt(),
                value: $item->getId(),
                label: $item->getAppName(),
            );
        }
        return $apps;
    }

    public static function convertToSingleApp($item): object
    {
        /*$apps = [];
        foreach ($response->getApps() as $item) {
            $apps[] = new AppDTO(
                id: $item->getId(),
                appName: $item->getAppName(),
                status: $item->getStatus(),
                isStatus: Status::ACTIVE === $item->getStatus(),
                notes: $item->getNotes(),
                transfer: $item->getTransfer(),
                transferCost: $item->getTransferCost(),
                costType: $item->getCostType(),
                systemAcc: $item->getSystemAcc(),
                unit: $item->getUnit(),
                pid: $item->getPid(),
                ext: $item->getExt(),
                value: $item->getId(),
                label: $item->getAppName(),
            );
        }
        return $apps;*/

        return new AppDTO(
            id: $item->getId(),
            appName: $item->getAppName(),
            status: $item->getStatus(),
            isStatus: Status::ACTIVE === $item->getStatus(),
            notes: $item->getNotes(),
            transfer: $item->getTransfer(),
            transferCost: $item->getTransferCost(),
            costType: $item->getCostType(),
            systemAcc: $item->getSystemAcc(),
            unit: $item->getUnit(),
            pid: $item->getPid(),
            ext: $item->getExt(),
            value: $item->getId(),
            label: $item->getAppName(),
        );
    }
}
