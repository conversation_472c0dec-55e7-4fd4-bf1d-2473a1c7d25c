<?php

// app/DTO/PlayerData.php

namespace App\DTO;

/**
 * "id",
 * "nick_name",
 * "uid",
 * "avatar",
 * "win",
 * "win_rate",
 * "lose",
 * "total",
 * "rank",
 * "exp",
 * "level",
 * "vip_point",
 * "created_at",
 * "updated_at",
 * "last_login",
 */
class PlayerData
{
    public function __construct(
        public readonly int $id,
        public readonly string $nick_name,
        public readonly string $uid,
        public readonly string $avatar,
        public readonly int $win,
        public readonly float $win_rate,
        public readonly int $lose,
        public readonly int $total,
        public readonly int $rank,
        public readonly int $exp,
        public readonly int $level,
        public readonly int $vipPoint,
        public readonly string $createdAt,
        public readonly string $updatedAt,
        public readonly string $lastLogin
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? 0,
            nick_name: $data['nick_name'] ?? '',
            uid: $data['uid'] ?? '',
            avatar: $data['avatar'] ?? 0,
            win: $data['win'] ?? 0,
            win_rate: $data['win_rate'] ?? 0,
            lose: $data['lose'] ?? 0,
            total: $data['total'] ?? 0,
            rank: $data['rank'] ?? 0,
            exp: $data['exp'] ?? 0,
            level: $data['level'] ?? 0,
            vipPoint: $data['vip_point'] ?? 0,
            createdAt: $data['created_at'] ?? '',
            updatedAt: $data['updated_at'] ?? '',
            lastLogin: $data['last_login'] ?? ''
        );
    }
}
