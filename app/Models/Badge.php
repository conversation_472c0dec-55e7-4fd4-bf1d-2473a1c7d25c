<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Badge extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'badges';
    public $timestamps = false;
    protected $fillable = [
        "type",
        "name",
        "code",
        "description",
        "condition_json",
        "reward",
        "category",
        "icon_url",
        "created_at"
    ];

    protected $casts = [
        'condition_json' => 'json',
    ];

    public function badgeRewards(): HasMany
    {
        return $this->hasMany(BadgeReward::class);
    }
}
