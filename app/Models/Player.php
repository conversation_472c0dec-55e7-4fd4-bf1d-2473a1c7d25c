<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Player extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'players';
    public $timestamps = false;
    protected $fillable = [
        // "id",
        "nick_name",
        "uid",
        "display_name",
        "balance",
        "avatar",
        "win",
        "win_rate",
        "lose",
        "total",
        "rank",
        "exp",
        "level",
        "vip_point",
        "type",
        "created_at",
        "updated_at",
        "last_login",
    ];

    public function friends(): HasMany
    {
        return $this->hasMany(Friend::class, 'f_player_id', 'id');
    }

    public function badges(): HasMany
    {
        return $this->hasMany(PlayerBadges::class, 'player_id', 'id');
    }

    /**
     * <PERSON><PERSON>y danh sách người chơi mới nhất
     */
    public static function getNewPlayers(int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return self::query()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get([
                'id',
                'uid',
                'nick_name',
                'display_name',
                'avatar',
                'balance',
                'level',
                'exp',
                'win_rate',
                'created_at',
                'last_login'
            ]);
    }
}
