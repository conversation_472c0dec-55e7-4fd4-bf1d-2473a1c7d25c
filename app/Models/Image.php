<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Image extends Model
{
    use HasFactory;

    protected $table = 'images';

    protected $fillable = [
        'user_id',
        'upload_id',
        'type',
        'external_user_info',
        'original_filename',
        'mime_type',
        'size',
        'path_original',
    ];

    protected $casts = [
        'size' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function userUpload(): BelongsTo
    {
        return $this->belongsTo(UserUpload::class, 'upload_id');
    }

    public function versions(): HasMany
    {
        return $this->hasMany(ImageVersion::class);
    }

    public function getAbsolutePathAttribute(): string
    {
        return url($this->path_original);
    }

    public function getRelativePathAttribute(): string
    {
        return $this->path_original;
    }
}
