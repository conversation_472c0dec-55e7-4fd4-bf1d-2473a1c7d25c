<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Permission extends Model
{
    use HasFactory;

    public $timestamps = true;
    protected $table = 'permissions';
    protected $fillable = [
        'name',
        'slug',
        'value',
        'methods',
        'status',
        'created_at',
        'updated_at',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($item) {
            $item->slug = Str::slug($item->name);
        });

        static::updating(function ($item) {
            $item->slug = Str::slug($item->name);
        });
    }
}
