<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserUpload extends Model
{
    use HasFactory;

    protected $table = 'user_uploads';

    protected $fillable = [
        'user_id',
        'type',
        'external_user_info',
        'upload_time',
        'total_files',
        'total_size',
        'description',
    ];

    protected $casts = [
        'upload_time' => 'datetime',
        'total_files' => 'integer',
        'total_size' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function images(): HasMany
    {
        return $this->hasMany(Image::class, 'upload_id');
    }
}
