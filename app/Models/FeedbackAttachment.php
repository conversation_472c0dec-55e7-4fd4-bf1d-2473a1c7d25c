<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FeedbackAttachment extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';

    protected $table = 'feedback_attachments';

    protected $fillable = [
        'feedback_id',
        'file_path',
        'created_at'
    ];

    protected $casts = [
        'created_at' => 'datetime',
    ];

    public $timestamps = false;

    public function feedback(): BelongsTo
    {
        return $this->belongsTo(Feedback::class);
    }
}
