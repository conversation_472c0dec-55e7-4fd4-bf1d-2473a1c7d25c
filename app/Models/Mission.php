<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mission extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'missions';
    public $timestamps = false;
    protected $fillable = [
        "code",
        "name",
        "description",
        "type",
        "condition_json",
        "reward_json",
        "repeatable",
        "is_active",
        "start_time",
        "end_time",
        "created_at",
        "updated_at"
    ];

    protected $casts = [
        'condition_json' => 'json',
        'reward_json' => 'json',
        // 'repeatable' => 'boolean',
        // 'is_active' => 'boolean',
    ];
}
