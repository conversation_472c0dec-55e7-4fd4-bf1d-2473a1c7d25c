<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BadgeReward extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'badge_rewards';
    public $timestamps = false;

    protected $fillable = [
        'badge_id',
        'reward_type',
        'reward_value',
        'is_permanent'
    ];

    protected $casts = [
        'is_permanent' => 'boolean',
    ];

    public function badge(): BelongsTo
    {
        return $this->belongsTo(Badge::class);
    }
}
