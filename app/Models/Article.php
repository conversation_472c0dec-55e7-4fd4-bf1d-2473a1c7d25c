<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Article extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $connection = 'mysql_game';

    protected $table = 'articles';
    // public $timestamps = false;
    protected $fillable = [
        'type', 'title', 'slug', 'content', 'thumbnail', 'promoted', 'is_published',
        'created_at', 'updated_at', 'deleted_at', 'published_at', 'uid'
    ];

    protected $hidden = [
        'deleted_at',
    ];
    protected $casts = [
        'promoted' => 'boolean',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    public static function boot(): void
    {
        parent::boot();

        static::creating(function ($item) {
            $item->created_at = now()->timestamp;
            $item->updated_at = now()->timestamp;
            $item->slug = \Str::slug($item->title);
        });

        static::updating(function ($item) {
            $item->updated_at = now()->timestamp;
            $item->slug = \Str::slug($item->title);
        });
    }

}
