<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlayerBadges extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'player_badges';
    public $timestamps = false;
    protected $fillable = [
        "player_id",
        "badge_id",
        "awarded_at",
        "claimed_at",
        "created_at"
    ];
}
