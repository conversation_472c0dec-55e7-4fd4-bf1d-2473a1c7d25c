<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shop extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'shop_items';

    // Enable timestamps since we're managing created_at and updated_at
    public $timestamps = true;

    // Define custom timestamp column names if they exist in the table
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    protected $fillable = [
        "type",
        "category",
        "item_name",
        "item_value",
        "item_id",
        "price",
        "currency",
        "description",
        "item_limit",
        "available_time",
        "icon",
        "gift_status",
        "status",
        "is_sale",
        "note",
        "order_index",
        "created_at",
        "updated_at"
    ];

    protected $casts = [
        'item_value' => 'float',
        'price' => 'float',
        'item_id' => 'integer',
        'item_limit' => 'integer',
        'available_time' => 'integer',
        // 'gift_status' => 'boolean',
        // 'status' => 'boolean',
        // 'is_sale' => 'boolean',
        'order_index' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Scopes for filtering
    public function scopeByType($query, $type)
    {
        if ($type) {
            return $query->where('type', $type);
        }
        return $query;
    }

    public function scopeByStatus($query, $status)
    {
        if ($status !== null) {
            return $query->where('status', $status);
        }
        return $query;
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeOrderByIndex($query)
    {
        return $query->orderBy('order_index', 'asc')->orderBy('id', 'asc');
    }
}
