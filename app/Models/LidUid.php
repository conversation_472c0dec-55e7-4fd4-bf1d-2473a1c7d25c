<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LidUid extends Model
{
    use HasFactory;

    protected $connection = 'mysql_game';
    protected $table = 'lid_uids';
    public $timestamps = false;
    protected $fillable = [
        "player_id",
        "lid",
        "type",
        "created_at"
    ];

    public function player()
    {
        return $this->belongsTo(Player::class);
    }

    public function log_game()
    {
        // return $this->hasMany(LogsGame::class, 'id', 'lid');
        return $this->belongsTo(LogsGame::class, 'lid', 'id');
    }
}
