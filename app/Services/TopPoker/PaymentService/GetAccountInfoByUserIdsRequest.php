<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>GetAccountInfoByUserIdsRequest</code>
 */
class GetAccountInfoByUserIdsRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     */
    protected $app_id = 0;
    /**
     * Generated from protobuf field <code>repeated int32 uids = 3;</code>
     */
    private $uids;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type int $app_id
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $uids
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     * @return int
     */
    public function getAppId()
    {
        return $this->app_id;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setAppId($var)
    {
        GPBUtil::checkInt32($var);
        $this->app_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated int32 uids = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUids()
    {
        return $this->uids;
    }

    /**
     * Generated from protobuf field <code>repeated int32 uids = 3;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUids($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT32);
        $this->uids = $arr;

        return $this;
    }

}

