<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>GetExchangeRateRequest</code>
 */
class GetExchangeRateRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>int32 app1 = 2;</code>
     */
    protected $app1 = 0;
    /**
     * Generated from protobuf field <code>int32 app2 = 3;</code>
     */
    protected $app2 = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type int $app1
     *     @type int $app2
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app1 = 2;</code>
     * @return int
     */
    public function getApp1()
    {
        return $this->app1;
    }

    /**
     * Generated from protobuf field <code>int32 app1 = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setApp1($var)
    {
        GPBUtil::checkInt32($var);
        $this->app1 = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app2 = 3;</code>
     * @return int
     */
    public function getApp2()
    {
        return $this->app2;
    }

    /**
     * Generated from protobuf field <code>int32 app2 = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setApp2($var)
    {
        GPBUtil::checkInt32($var);
        $this->app2 = $var;

        return $this;
    }

}

