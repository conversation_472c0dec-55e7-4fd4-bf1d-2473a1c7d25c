<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>TransferRequest</code>
 */
class TransferRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     */
    protected $app_id = 0;
    /**
     * Generated from protobuf field <code>int32 from_uid = 3;</code>
     */
    protected $from_uid = 0;
    /**
     * Generated from protobuf field <code>int32 to_uid = 4;</code>
     */
    protected $to_uid = 0;
    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     */
    protected $to_name = '';
    /**
     * Generated from protobuf field <code>double amount = 6;</code>
     */
    protected $amount = 0.0;
    /**
     * Generated from protobuf field <code>string description = 7;</code>
     */
    protected $description = '';
    /**
     * Generated from protobuf field <code>double cost = 8;</code>
     */
    protected $cost = 0.0;
    /**
     * Generated from protobuf field <code>string to_full_name = 9;</code>
     */
    protected $to_full_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type int $app_id
     *     @type int $from_uid
     *     @type int $to_uid
     *     @type string $to_name
     *     @type float $amount
     *     @type string $description
     *     @type float $cost
     *     @type string $to_full_name
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     * @return int
     */
    public function getAppId()
    {
        return $this->app_id;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setAppId($var)
    {
        GPBUtil::checkInt32($var);
        $this->app_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 from_uid = 3;</code>
     * @return int
     */
    public function getFromUid()
    {
        return $this->from_uid;
    }

    /**
     * Generated from protobuf field <code>int32 from_uid = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setFromUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->from_uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 to_uid = 4;</code>
     * @return int
     */
    public function getToUid()
    {
        return $this->to_uid;
    }

    /**
     * Generated from protobuf field <code>int32 to_uid = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setToUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->to_uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     * @return string
     */
    public function getToName()
    {
        return $this->to_name;
    }

    /**
     * Generated from protobuf field <code>string to_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setToName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double amount = 6;</code>
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Generated from protobuf field <code>double amount = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string description = 7;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Generated from protobuf field <code>string description = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->description = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double cost = 8;</code>
     * @return float
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * Generated from protobuf field <code>double cost = 8;</code>
     * @param float $var
     * @return $this
     */
    public function setCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->cost = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string to_full_name = 9;</code>
     * @return string
     */
    public function getToFullName()
    {
        return $this->to_full_name;
    }

    /**
     * Generated from protobuf field <code>string to_full_name = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setToFullName($var)
    {
        GPBUtil::checkString($var, True);
        $this->to_full_name = $var;

        return $this;
    }

}

