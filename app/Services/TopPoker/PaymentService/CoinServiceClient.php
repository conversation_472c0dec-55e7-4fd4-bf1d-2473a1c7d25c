<?php
// GENERATED CODE -- DO NOT EDIT!

namespace App\Services\TopPoker\PaymentService;

/**
 */
class CoinServiceClient extends \Grpc\BaseStub {

    /**
     * @param string $hostname hostname
     * @param array $opts channel options
     * @param \Grpc\Channel $channel (optional) re-use channel object
     */
    public function __construct($hostname, $opts, $channel = null) {
        parent::__construct($hostname, $opts, $channel);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetAccountStatusRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAccountInfo(\App\Services\TopPoker\PaymentService\GetAccountStatusRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAccountInfo',
        $argument,
        ['\App\Services\TopPoker\PaymentService\ACC_INFO', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetAccountByIdRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAccountById(\App\Services\TopPoker\PaymentService\GetAccountByIdRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAccountById',
        $argument,
        ['\App\Services\TopPoker\PaymentService\ACC_INFO', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetAccountInfoByUserIdsRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAccountInfoByUserIds(\App\Services\TopPoker\PaymentService\GetAccountInfoByUserIdsRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAccountInfoByUserIds',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountInfoByUserIdsResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetAccountByIdsRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAccountByIds(\App\Services\TopPoker\PaymentService\GetAccountByIdsRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAccountByIds',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountInfoByUserIdsResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetBalanceRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetBalance(\App\Services\TopPoker\PaymentService\GetBalanceRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetBalance',
        $argument,
        ['\App\Services\TopPoker\PaymentService\GetBalanceResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\CreateAccountRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function CreateAccount(\App\Services\TopPoker\PaymentService\CreateAccountRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/CreateAccount',
        $argument,
        ['\App\Services\TopPoker\PaymentService\CreateAccountResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\CreateAccountRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetOrCreateAccount(\App\Services\TopPoker\PaymentService\CreateAccountRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetOrCreateAccount',
        $argument,
        ['\App\Services\TopPoker\PaymentService\GetOrCreateAccountResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\CashInRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function CashIn(\App\Services\TopPoker\PaymentService\CashInRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/CashIn',
        $argument,
        ['\App\Services\TopPoker\PaymentService\CashInResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\TransferRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function Transfer(\App\Services\TopPoker\PaymentService\TransferRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/Transfer',
        $argument,
        ['\App\Services\TopPoker\PaymentService\TransferResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\ExchangeRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function Exchange(\App\Services\TopPoker\PaymentService\ExchangeRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/Exchange',
        $argument,
        ['\App\Services\TopPoker\PaymentService\ExchangeResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\SearchUserTransactionRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function SearchUserTransaction(\App\Services\TopPoker\PaymentService\SearchUserTransactionRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/SearchUserTransaction',
        $argument,
        ['\App\Services\TopPoker\PaymentService\TRANS_DETAIL_RESULT', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetExchangeRateRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetExchangeRate(\App\Services\TopPoker\PaymentService\GetExchangeRateRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetExchangeRate',
        $argument,
        ['\App\Services\TopPoker\PaymentService\EX_RATE', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\AccountListRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetUserAccountList(\App\Services\TopPoker\PaymentService\AccountListRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetUserAccountList',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountListResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\AccountListRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetTransferableUserAccountList(\App\Services\TopPoker\PaymentService\AccountListRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetTransferableUserAccountList',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountListResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\GetListAppRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetListApp(\App\Services\TopPoker\PaymentService\GetListAppRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetListApp',
        $argument,
        ['\App\Services\TopPoker\PaymentService\GetListAppResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\SetAppStatusRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function SetAppStatus(\App\Services\TopPoker\PaymentService\SetAppStatusRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/SetAppStatus',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AppStatusResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\AppInfoRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAppInfo(\App\Services\TopPoker\PaymentService\AppInfoRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAppInfo',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AppInfoResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\UpdateAppInfoRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function UpdateAppInfo(\App\Services\TopPoker\PaymentService\UpdateAppInfoRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/UpdateAppInfo',
        $argument,
        ['\App\Services\TopPoker\PaymentService\UpdateAppInfoResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\CreateAppInfoRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function CreateAppInfo(\App\Services\TopPoker\PaymentService\CreateAppInfoRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/CreateAppInfo',
        $argument,
        ['\App\Services\TopPoker\PaymentService\UpdateAppInfoResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\PaymentRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function Payment(\App\Services\TopPoker\PaymentService\PaymentRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/Payment',
        $argument,
        ['\App\Services\TopPoker\PaymentService\PaymentResponse', 'decode'],
        $metadata, $options);
    }

    /**
     *  rpc Payment(PaymentRequest) returns (PaymentResponse) {
     *    option (google.api.http) = {
     *      post: "/v1/payment"
     *      body: "*"
     *    };
     *  }
     * @param \App\Services\TopPoker\PaymentService\GetAccountStatusRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function GetAccountStatus(\App\Services\TopPoker\PaymentService\GetAccountStatusRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/GetAccountStatus',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountStatusResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\SetAccountStatusRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function SetAccountStatus(\App\Services\TopPoker\PaymentService\SetAccountStatusRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/SetAccountStatus',
        $argument,
        ['\App\Services\TopPoker\PaymentService\AccountStatusResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\SearchTransactionRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function SearchTransaction(\App\Services\TopPoker\PaymentService\SearchTransactionRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/SearchTransaction',
        $argument,
        ['\App\Services\TopPoker\PaymentService\TRANS_SEARCH_RESULT', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\SearchAccountRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function SearchAccount(\App\Services\TopPoker\PaymentService\SearchAccountRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/SearchAccount',
        $argument,
        ['\App\Services\TopPoker\PaymentService\SearchAccountResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\DeductRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function Deduct(\App\Services\TopPoker\PaymentService\DeductRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/Deduct',
        $argument,
        ['\App\Services\TopPoker\PaymentService\DeductResponse', 'decode'],
        $metadata, $options);
    }

    /**
     * @param \App\Services\TopPoker\PaymentService\RefundRequest $argument input argument
     * @param array $metadata metadata
     * @param array $options call options
     * @return \Grpc\UnaryCall
     */
    public function Refund(\App\Services\TopPoker\PaymentService\RefundRequest $argument,
      $metadata = [], $options = []) {
        return $this->_simpleRequest('/CoinService/Refund',
        $argument,
        ['\App\Services\TopPoker\PaymentService\RefundResponse', 'decode'],
        $metadata, $options);
    }

}
