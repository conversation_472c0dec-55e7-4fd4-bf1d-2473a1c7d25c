<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>ACC_INFO</code>
 */
class ACC_INFO extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     * Generated from protobuf field <code>int32 uid = 2;</code>
     */
    protected $uid = 0;
    /**
     * Generated from protobuf field <code>string name = 3;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>int32 app_id = 4;</code>
     */
    protected $app_id = 0;
    /**
     * Generated from protobuf field <code>string app_name = 5;</code>
     */
    protected $app_name = '';
    /**
     * Generated from protobuf field <code>double balance = 6;</code>
     */
    protected $balance = 0.0;
    /**
     * double promotion = 7;
     *
     * Generated from protobuf field <code>string created_at = 8;</code>
     */
    protected $created_at = '';
    /**
     * Generated from protobuf field <code>string updated_at = 9;</code>
     */
    protected $updated_at = '';
    /**
     * Generated from protobuf field <code>int32 status = 10;</code>
     */
    protected $status = 0;
    /**
     * Generated from protobuf field <code>string full_name = 11;</code>
     */
    protected $full_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *     @type int $uid
     *     @type string $name
     *     @type int $app_id
     *     @type string $app_name
     *     @type float $balance
     *     @type string $created_at
     *           double promotion = 7;
     *     @type string $updated_at
     *     @type int $status
     *     @type string $full_name
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 uid = 2;</code>
     * @return int
     */
    public function getUid()
    {
        return $this->uid;
    }

    /**
     * Generated from protobuf field <code>int32 uid = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 4;</code>
     * @return int
     */
    public function getAppId()
    {
        return $this->app_id;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAppId($var)
    {
        GPBUtil::checkInt32($var);
        $this->app_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_name = 5;</code>
     * @return string
     */
    public function getAppName()
    {
        return $this->app_name;
    }

    /**
     * Generated from protobuf field <code>string app_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setAppName($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double balance = 6;</code>
     * @return float
     */
    public function getBalance()
    {
        return $this->balance;
    }

    /**
     * Generated from protobuf field <code>double balance = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setBalance($var)
    {
        GPBUtil::checkDouble($var);
        $this->balance = $var;

        return $this;
    }

    /**
     * double promotion = 7;
     *
     * Generated from protobuf field <code>string created_at = 8;</code>
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * double promotion = 7;
     *
     * Generated from protobuf field <code>string created_at = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setCreatedAt($var)
    {
        GPBUtil::checkString($var, True);
        $this->created_at = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string updated_at = 9;</code>
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * Generated from protobuf field <code>string updated_at = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setUpdatedAt($var)
    {
        GPBUtil::checkString($var, True);
        $this->updated_at = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 status = 10;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Generated from protobuf field <code>int32 status = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string full_name = 11;</code>
     * @return string
     */
    public function getFullName()
    {
        return $this->full_name;
    }

    /**
     * Generated from protobuf field <code>string full_name = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setFullName($var)
    {
        GPBUtil::checkString($var, True);
        $this->full_name = $var;

        return $this;
    }

}

