<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>TRANS<PERSON><PERSON>ON</code>
 */
class TRANS<PERSON><PERSON><PERSON> extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 tid = 1;</code>
     */
    protected $tid = 0;
    /**
     * Generated from protobuf field <code>int32 trans_type = 2;</code>
     */
    protected $trans_type = 0;
    /**
     * Generated from protobuf field <code>string trans_date = 3;</code>
     */
    protected $trans_date = '';
    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     */
    protected $amount = 0.0;
    /**
     * Generated from protobuf field <code>double promotion = 5;</code>
     */
    protected $promotion = 0.0;
    /**
     * Generated from protobuf field <code>string description = 6;</code>
     */
    protected $description = '';
    /**
     * Generated from protobuf field <code>string system_note = 7;</code>
     */
    protected $system_note = '';
    /**
     * Generated from protobuf field <code>int32 credit_uid = 8;</code>
     */
    protected $credit_uid = 0;
    /**
     * Generated from protobuf field <code>string credit_name = 9;</code>
     */
    protected $credit_name = '';
    /**
     * Generated from protobuf field <code>int32 debit_uid = 10;</code>
     */
    protected $debit_uid = 0;
    /**
     * Generated from protobuf field <code>string debit_name = 11;</code>
     */
    protected $debit_name = '';
    /**
     * Generated from protobuf field <code>int32 credit_app = 12;</code>
     */
    protected $credit_app = 0;
    /**
     * Generated from protobuf field <code>int32 debit_app = 13;</code>
     */
    protected $debit_app = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $tid
     *     @type int $trans_type
     *     @type string $trans_date
     *     @type float $amount
     *     @type float $promotion
     *     @type string $description
     *     @type string $system_note
     *     @type int $credit_uid
     *     @type string $credit_name
     *     @type int $debit_uid
     *     @type string $debit_name
     *     @type int $credit_app
     *     @type int $debit_app
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 tid = 1;</code>
     * @return int
     */
    public function getTid()
    {
        return $this->tid;
    }

    /**
     * Generated from protobuf field <code>int32 tid = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setTid($var)
    {
        GPBUtil::checkInt32($var);
        $this->tid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 trans_type = 2;</code>
     * @return int
     */
    public function getTransType()
    {
        return $this->trans_type;
    }

    /**
     * Generated from protobuf field <code>int32 trans_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setTransType($var)
    {
        GPBUtil::checkInt32($var);
        $this->trans_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string trans_date = 3;</code>
     * @return string
     */
    public function getTransDate()
    {
        return $this->trans_date;
    }

    /**
     * Generated from protobuf field <code>string trans_date = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTransDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->trans_date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Generated from protobuf field <code>double amount = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setAmount($var)
    {
        GPBUtil::checkDouble($var);
        $this->amount = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double promotion = 5;</code>
     * @return float
     */
    public function getPromotion()
    {
        return $this->promotion;
    }

    /**
     * Generated from protobuf field <code>double promotion = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setPromotion($var)
    {
        GPBUtil::checkDouble($var);
        $this->promotion = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string description = 6;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Generated from protobuf field <code>string description = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->description = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string system_note = 7;</code>
     * @return string
     */
    public function getSystemNote()
    {
        return $this->system_note;
    }

    /**
     * Generated from protobuf field <code>string system_note = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setSystemNote($var)
    {
        GPBUtil::checkString($var, True);
        $this->system_note = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 credit_uid = 8;</code>
     * @return int
     */
    public function getCreditUid()
    {
        return $this->credit_uid;
    }

    /**
     * Generated from protobuf field <code>int32 credit_uid = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setCreditUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->credit_uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string credit_name = 9;</code>
     * @return string
     */
    public function getCreditName()
    {
        return $this->credit_name;
    }

    /**
     * Generated from protobuf field <code>string credit_name = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setCreditName($var)
    {
        GPBUtil::checkString($var, True);
        $this->credit_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 debit_uid = 10;</code>
     * @return int
     */
    public function getDebitUid()
    {
        return $this->debit_uid;
    }

    /**
     * Generated from protobuf field <code>int32 debit_uid = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setDebitUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->debit_uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string debit_name = 11;</code>
     * @return string
     */
    public function getDebitName()
    {
        return $this->debit_name;
    }

    /**
     * Generated from protobuf field <code>string debit_name = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setDebitName($var)
    {
        GPBUtil::checkString($var, True);
        $this->debit_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 credit_app = 12;</code>
     * @return int
     */
    public function getCreditApp()
    {
        return $this->credit_app;
    }

    /**
     * Generated from protobuf field <code>int32 credit_app = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setCreditApp($var)
    {
        GPBUtil::checkInt32($var);
        $this->credit_app = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 debit_app = 13;</code>
     * @return int
     */
    public function getDebitApp()
    {
        return $this->debit_app;
    }

    /**
     * Generated from protobuf field <code>int32 debit_app = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setDebitApp($var)
    {
        GPBUtil::checkInt32($var);
        $this->debit_app = $var;

        return $this;
    }

}

