<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>SearchAccountResponse</code>
 */
class SearchAccountResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .ACC_INFO items = 1;</code>
     */
    private $items;
    /**
     * Generated from protobuf field <code>.Pagination pagination = 2;</code>
     */
    protected $pagination = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\App\Services\TopPoker\PaymentService\ACC_INFO>|\Google\Protobuf\Internal\RepeatedField $items
     *     @type \App\Services\TopPoker\PaymentService\Pagination $pagination
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .ACC_INFO items = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * Generated from protobuf field <code>repeated .ACC_INFO items = 1;</code>
     * @param array<\App\Services\TopPoker\PaymentService\ACC_INFO>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setItems($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \App\Services\TopPoker\PaymentService\ACC_INFO::class);
        $this->items = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.Pagination pagination = 2;</code>
     * @return \App\Services\TopPoker\PaymentService\Pagination|null
     */
    public function getPagination()
    {
        return $this->pagination;
    }

    public function hasPagination()
    {
        return isset($this->pagination);
    }

    public function clearPagination()
    {
        unset($this->pagination);
    }

    /**
     * Generated from protobuf field <code>.Pagination pagination = 2;</code>
     * @param \App\Services\TopPoker\PaymentService\Pagination $var
     * @return $this
     */
    public function setPagination($var)
    {
        GPBUtil::checkMessage($var, \App\Services\TopPoker\PaymentService\Pagination::class);
        $this->pagination = $var;

        return $this;
    }

}

