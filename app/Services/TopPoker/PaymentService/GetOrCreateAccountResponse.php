<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>GetOrCreateAccountResponse</code>
 */
class GetOrCreateAccountResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     */
    protected $success = false;
    /**
     * Generated from protobuf field <code>int32 type_action = 2;</code>
     */
    protected $type_action = 0;
    /**
     * Generated from protobuf field <code>string error_key = 3;</code>
     */
    protected $error_key = '';
    /**
     * Generated from protobuf field <code>.ACC_INFO data = 4;</code>
     */
    protected $data = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $success
     *     @type int $type_action
     *     @type string $error_key
     *     @type \App\Services\TopPoker\PaymentService\ACC_INFO $data
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @return bool
     */
    public function getSuccess()
    {
        return $this->success;
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setSuccess($var)
    {
        GPBUtil::checkBool($var);
        $this->success = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 type_action = 2;</code>
     * @return int
     */
    public function getTypeAction()
    {
        return $this->type_action;
    }

    /**
     * Generated from protobuf field <code>int32 type_action = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setTypeAction($var)
    {
        GPBUtil::checkInt32($var);
        $this->type_action = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string error_key = 3;</code>
     * @return string
     */
    public function getErrorKey()
    {
        return $this->error_key;
    }

    /**
     * Generated from protobuf field <code>string error_key = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setErrorKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->error_key = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.ACC_INFO data = 4;</code>
     * @return \App\Services\TopPoker\PaymentService\ACC_INFO|null
     */
    public function getData()
    {
        return $this->data;
    }

    public function hasData()
    {
        return isset($this->data);
    }

    public function clearData()
    {
        unset($this->data);
    }

    /**
     * Generated from protobuf field <code>.ACC_INFO data = 4;</code>
     * @param \App\Services\TopPoker\PaymentService\ACC_INFO $var
     * @return $this
     */
    public function setData($var)
    {
        GPBUtil::checkMessage($var, \App\Services\TopPoker\PaymentService\ACC_INFO::class);
        $this->data = $var;

        return $this;
    }

}

