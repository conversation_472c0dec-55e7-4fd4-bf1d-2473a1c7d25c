<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>CreateAccountResponse</code>
 */
class CreateAccountResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     */
    protected $success = false;
    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     */
    protected $error_key = '';
    /**
     * Generated from protobuf field <code>int32 app_id = 3;</code>
     */
    protected $app_id = 0;
    /**
     * Generated from protobuf field <code>int32 uid = 4;</code>
     */
    protected $uid = 0;
    /**
     * Generated from protobuf field <code>double balance = 5;</code>
     */
    protected $balance = 0.0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $success
     *     @type string $error_key
     *     @type int $app_id
     *     @type int $uid
     *     @type float $balance
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @return bool
     */
    public function getSuccess()
    {
        return $this->success;
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setSuccess($var)
    {
        GPBUtil::checkBool($var);
        $this->success = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @return string
     */
    public function getErrorKey()
    {
        return $this->error_key;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setErrorKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->error_key = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 3;</code>
     * @return int
     */
    public function getAppId()
    {
        return $this->app_id;
    }

    /**
     * Generated from protobuf field <code>int32 app_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setAppId($var)
    {
        GPBUtil::checkInt32($var);
        $this->app_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 uid = 4;</code>
     * @return int
     */
    public function getUid()
    {
        return $this->uid;
    }

    /**
     * Generated from protobuf field <code>int32 uid = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setUid($var)
    {
        GPBUtil::checkInt32($var);
        $this->uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double balance = 5;</code>
     * @return float
     */
    public function getBalance()
    {
        return $this->balance;
    }

    /**
     * Generated from protobuf field <code>double balance = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setBalance($var)
    {
        GPBUtil::checkDouble($var);
        $this->balance = $var;

        return $this;
    }

}

