<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PaymentResponse</code>
 */
class PaymentResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>map<int32, double> payments = 1;</code>
     */
    private $payments;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array|\Google\Protobuf\Internal\MapField $payments
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>map<int32, double> payments = 1;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getPayments()
    {
        return $this->payments;
    }

    /**
     * Generated from protobuf field <code>map<int32, double> payments = 1;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setPayments($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::INT32, \Google\Protobuf\Internal\GPBType::DOUBLE);
        $this->payments = $arr;

        return $this;
    }

}

