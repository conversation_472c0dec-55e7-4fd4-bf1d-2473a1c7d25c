<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AppInfoResponse</code>
 */
class AppInfoResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     * Generated from protobuf field <code>string app_name = 2;</code>
     */
    protected $app_name = '';
    /**
     * Generated from protobuf field <code>int32 status = 3;</code>
     */
    protected $status = 0;
    /**
     * Generated from protobuf field <code>string notes = 4;</code>
     */
    protected $notes = '';
    /**
     * Generated from protobuf field <code>int32 transfer = 5;</code>
     */
    protected $transfer = 0;
    /**
     * Generated from protobuf field <code>double transfer_cost = 6;</code>
     */
    protected $transfer_cost = 0.0;
    /**
     * Generated from protobuf field <code>int32 cost_type = 7;</code>
     */
    protected $cost_type = 0;
    /**
     * Generated from protobuf field <code>string system_acc = 8;</code>
     */
    protected $system_acc = '';
    /**
     * Generated from protobuf field <code>string unit = 9;</code>
     */
    protected $unit = '';
    /**
     * Generated from protobuf field <code>int32 pid = 10;</code>
     */
    protected $pid = 0;
    /**
     * Generated from protobuf field <code>int32 ext = 11;</code>
     */
    protected $ext = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *     @type string $app_name
     *     @type int $status
     *     @type string $notes
     *     @type int $transfer
     *     @type float $transfer_cost
     *     @type int $cost_type
     *     @type string $system_acc
     *     @type string $unit
     *     @type int $pid
     *     @type int $ext
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string app_name = 2;</code>
     * @return string
     */
    public function getAppName()
    {
        return $this->app_name;
    }

    /**
     * Generated from protobuf field <code>string app_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAppName($var)
    {
        GPBUtil::checkString($var, True);
        $this->app_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 status = 3;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Generated from protobuf field <code>int32 status = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkInt32($var);
        $this->status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string notes = 4;</code>
     * @return string
     */
    public function getNotes()
    {
        return $this->notes;
    }

    /**
     * Generated from protobuf field <code>string notes = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setNotes($var)
    {
        GPBUtil::checkString($var, True);
        $this->notes = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 transfer = 5;</code>
     * @return int
     */
    public function getTransfer()
    {
        return $this->transfer;
    }

    /**
     * Generated from protobuf field <code>int32 transfer = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setTransfer($var)
    {
        GPBUtil::checkInt32($var);
        $this->transfer = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>double transfer_cost = 6;</code>
     * @return float
     */
    public function getTransferCost()
    {
        return $this->transfer_cost;
    }

    /**
     * Generated from protobuf field <code>double transfer_cost = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setTransferCost($var)
    {
        GPBUtil::checkDouble($var);
        $this->transfer_cost = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 cost_type = 7;</code>
     * @return int
     */
    public function getCostType()
    {
        return $this->cost_type;
    }

    /**
     * Generated from protobuf field <code>int32 cost_type = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setCostType($var)
    {
        GPBUtil::checkInt32($var);
        $this->cost_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string system_acc = 8;</code>
     * @return string
     */
    public function getSystemAcc()
    {
        return $this->system_acc;
    }

    /**
     * Generated from protobuf field <code>string system_acc = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setSystemAcc($var)
    {
        GPBUtil::checkString($var, True);
        $this->system_acc = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string unit = 9;</code>
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * Generated from protobuf field <code>string unit = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setUnit($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 pid = 10;</code>
     * @return int
     */
    public function getPid()
    {
        return $this->pid;
    }

    /**
     * Generated from protobuf field <code>int32 pid = 10;</code>
     * @param int $var
     * @return $this
     */
    public function setPid($var)
    {
        GPBUtil::checkInt32($var);
        $this->pid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 ext = 11;</code>
     * @return int
     */
    public function getExt()
    {
        return $this->ext;
    }

    /**
     * Generated from protobuf field <code>int32 ext = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setExt($var)
    {
        GPBUtil::checkInt32($var);
        $this->ext = $var;

        return $this;
    }

}

