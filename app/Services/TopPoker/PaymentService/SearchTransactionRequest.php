<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: coin-service.proto

namespace App\Services\TopPoker\PaymentService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>SearchTransactionRequest</code>
 */
class SearchTransactionRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>.SEARCH_COND cond = 2;</code>
     */
    protected $cond = null;
    /**
     * Generated from protobuf field <code>int32 page = 3;</code>
     */
    protected $page = 0;
    /**
     * Generated from protobuf field <code>int32 limit = 4;</code>
     */
    protected $limit = 0;
    /**
     * Generated from protobuf field <code>string sort = 5;</code>
     */
    protected $sort = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type \App\Services\TopPoker\PaymentService\SEARCH_COND $cond
     *     @type int $page
     *     @type int $limit
     *     @type string $sort
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\CoinService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.SEARCH_COND cond = 2;</code>
     * @return \App\Services\TopPoker\PaymentService\SEARCH_COND|null
     */
    public function getCond()
    {
        return $this->cond;
    }

    public function hasCond()
    {
        return isset($this->cond);
    }

    public function clearCond()
    {
        unset($this->cond);
    }

    /**
     * Generated from protobuf field <code>.SEARCH_COND cond = 2;</code>
     * @param \App\Services\TopPoker\PaymentService\SEARCH_COND $var
     * @return $this
     */
    public function setCond($var)
    {
        GPBUtil::checkMessage($var, \App\Services\TopPoker\PaymentService\SEARCH_COND::class);
        $this->cond = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 page = 3;</code>
     * @return int
     */
    public function getPage()
    {
        return $this->page;
    }

    /**
     * Generated from protobuf field <code>int32 page = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setPage($var)
    {
        GPBUtil::checkInt32($var);
        $this->page = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 limit = 4;</code>
     * @return int
     */
    public function getLimit()
    {
        return $this->limit;
    }

    /**
     * Generated from protobuf field <code>int32 limit = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setLimit($var)
    {
        GPBUtil::checkInt32($var);
        $this->limit = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sort = 5;</code>
     * @return string
     */
    public function getSort()
    {
        return $this->sort;
    }

    /**
     * Generated from protobuf field <code>string sort = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSort($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort = $var;

        return $this;
    }

}

