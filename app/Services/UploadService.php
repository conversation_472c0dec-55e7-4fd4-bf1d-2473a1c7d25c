<?php

namespace App\Services;

use App\Models\Image;
use App\Models\ImageVersion;
use App\Models\UserUpload;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class UploadService
{
    private ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    private function getImageSizes(): array
    {
        return config('upload.image_sizes', [
            // 'avatar' => ['width' => 90, 'height' => 116],
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 800, 'height' => 600],
            'large' => ['width' => 1200, 'height' => 900],
        ]);
    }

    private function getAllowedMimeTypes(): array
    {
        return config('upload.allowed_mime_types', [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp',
            'image/svg+xml'
        ]);
    }

    private function getBaseUrl(): string
    {
        return rtrim(config('upload.base_url', config('app.url')), '/');
    }

    public function uploadSingle(UploadedFile $file, int $userId, ?string $description = null, array $context = []): array
    {
        return $this->uploadMultiple([$file], $userId, $description, $context);
    }

    public function uploadMultiple(array $files, int $userId, ?string $description = null, array $context = []): array
    {
        $totalSize = 0;
        $uploadedImages = [];

        // Determine upload type and external user info from context
        $uploadType = $context['type'] ?? 'admin';
        $externalUserInfo = $context['external_user_info'] ?? null;
        \Log::info('externalUserInfo: ', [$externalUserInfo, $userId]);

        // Create upload record
        $userUpload = UserUpload::create([
            'user_id' => $userId,
            'type' => $uploadType,
            'external_user_info' => json_encode($externalUserInfo),
            'upload_time' => now(),
            'total_files' => count($files),
            'total_size' => 0, // Will update after processing
            'description' => $description,
        ]);

        foreach ($files as $file) {
            if (!$this->isValidFile($file)) {
                continue;
            }

            $imageData = $this->processFile($file, $userId, $userUpload->id, $uploadType, $externalUserInfo);
            if ($imageData) {
                $uploadedImages[] = $imageData;
                $totalSize += $file->getSize();
            }
        }

        // Update total size
        $userUpload->update(['total_size' => $totalSize]);

        return [
            'upload_id' => $userUpload->id,
            'total_files' => count($uploadedImages),
            'total_size' => $totalSize,
            'images' => $uploadedImages,
        ];
    }

    protected function processFile(UploadedFile $file, int $userId, int $uploadId, string $type = 'admin', ?array $externalUserInfo = null): ?array
    {
        try {
            // Generate MD5 hash for unique filename
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $hashedName = md5($originalName . time() . $userId) . '.' . $extension;

            // Store original file
            $originalPath = 'uploads/' . date('Y/m/d') . '/' . $hashedName;
            $file->storeAs('public', $originalPath);

            // Create image record
            $image = Image::create([
                'user_id' => $userId,
                'upload_id' => $uploadId,
                'type' => $type,
                'external_user_info' => json_encode($externalUserInfo),
                'original_filename' => $originalName,
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'path_original' => 'storage/' . $originalPath,
            ]);

            // Generate resized versions if it's an image
            $versions = [];
            if ($this->isImage($file)) {
                $versions = $this->generateImageVersions($file, $image->id, $originalPath);
            }

            return [
                'id' => $image->id,
                'original_filename' => $originalName,
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'absolute_path' => $this->getBaseUrl() . '/storage/' . $originalPath,
                'relative_path' => $originalPath,
                'versions' => $versions,
            ];
        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage());
            return null;
        }
    }

    protected function generateImageVersions(UploadedFile $file, int $imageId, string $originalPath): array
    {
        $versions = [];
        $basePath = dirname($originalPath);
        $fileName = pathinfo($originalPath, PATHINFO_FILENAME);
        $extension = pathinfo($originalPath, PATHINFO_EXTENSION);

        foreach ($this->getImageSizes() as $versionType => $dimensions) {
            \Log::info('versionType: ', [$versionType, $dimensions]);
            try {
                $img = $this->imageManager->read($file->getPathname());
                $img->scaleDown($dimensions['width'], $dimensions['height']);

                $versionFileName = $fileName . '_' . $versionType . '.' . $extension;
                $versionPath = $basePath . '/' . $versionFileName;
                $fullPath = storage_path('app/public/' . $versionPath);

                // Ensure directory exists
                $directory = dirname($fullPath);
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                $img->save($fullPath);

                // Create version record
                $version = ImageVersion::create([
                    'image_id' => $imageId,
                    'version_type' => $versionType,
                    'width' => $img->width(),
                    'height' => $img->height(),
                    'size' => filesize($fullPath),
                    'path' => 'storage/' . $versionPath,
                ]);

                $versions[] = [
                    'version_type' => $versionType,
                    'width' => $img->width(),
                    'height' => $img->height(),
                    'size' => filesize($fullPath),
                    'absolute_path' => $this->getBaseUrl() . '/storage/' . $versionPath,
                    'relative_path' => $versionPath,
                ];
            } catch (\Exception $e) {
                \Log::error('Image resize error: ' . $e->getMessage());
            }
        }

        return $versions;
    }

    public function deleteFile(int $imageId, int $userId): bool
    {
        $image = Image::where('id', $imageId)
            ->where('user_id', $userId)
            ->with('versions')
            ->first();

        if (!$image) {
            return false;
        }

        try {
            // Delete original file
            if ($image->path_original && Storage::disk('public')->exists(str_replace('storage/', '', $image->path_original))) {
                Storage::disk('public')->delete(str_replace('storage/', '', $image->path_original));
            }

            // Delete version files
            foreach ($image->versions as $version) {
                if ($version->path && Storage::disk('public')->exists(str_replace('storage/', '', $version->path))) {
                    Storage::disk('public')->delete(str_replace('storage/', '', $version->path));
                }
            }

            // Delete database records
            $image->versions()->delete();
            $image->delete();

            return true;
        } catch (\Exception $e) {
            \Log::error('File deletion error: ' . $e->getMessage());
            return false;
        }
    }

    public function deleteAllUserFiles(int $userId): bool
    {
        try {
            $images = Image::where('user_id', $userId)->with('versions')->get();

            foreach ($images as $image) {
                $this->deleteFile($image->id, $userId);
            }

            // Delete upload records
            UserUpload::where('user_id', $userId)->delete();

            return true;
        } catch (\Exception $e) {
            \Log::error('User files deletion error: ' . $e->getMessage());
            return false;
        }
    }

    protected function isValidFile(UploadedFile $file): bool
    {
        return $file->isValid() && in_array($file->getMimeType(), $this->getAllowedMimeTypes());
    }

    protected function isImage(UploadedFile $file): bool
    {
        return str_starts_with($file->getMimeType(), 'image/');
    }
}
