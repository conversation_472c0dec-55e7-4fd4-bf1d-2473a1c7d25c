<?php


namespace App\Services\Games;

use App\Repositories\Faq\FaqRepository;
use Illuminate\Support\Facades\Cache;

class FaqService
{
    private $repository;
    private $cache_ttl;
    private $key_tag = 'faqs';
    private $key_question_detail = 'question_detail_';
    private $key_question_by_status = 'question_by_status_';

    public function __construct(FaqRepository $faqRepository)
    {
        $this->repository = $faqRepository;
        $this->cache_ttl = env('CACHE_TTL', 3600);
    }

    public function create($data)
    {
        Cache::tags([$this->key_tag])->flush();
        return $this->repository->create($data);
    }

    public function update($id, $dataPost)
    {
        $model = $this->repository->find($id);
        $row = $this->repository->update($model, $dataPost);
        // Cache::forget($this->key_question_detail . $id);
        Cache::tags([$this->key_tag])->flush();
        return $row;
    }

    /**
     * @param $ids
     */
    public function destroys($ids): void
    {
        $this->repository->whereIn('id', explode(',', $ids))->delete();
    }

    /**
     * @param $id
     * @return bool
     */
    public function delete($id): bool
    {
        $row = $this->repository->find($id);
        if ($row->delete()) {
            Cache::tags([$this->key_tag])->flush();
            return true;
        }
        return false;
    }

    /**
     * @param $status
     * @return array|mixed
     */
    public function getItemsByStatus($status)
    {
        $key_question_by_status = $this->key_question_by_status . $status;

        return Cache::tags([$this->key_tag])->remember($key_question_by_status, $this->cache_ttl, function () use ($status) {
            return $this->repository->getItemsByStatus($status);
        });
    }
}
