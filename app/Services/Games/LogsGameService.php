<?php

namespace App\Services\Games;

use App\Models\LidUid;
use App\Models\LogsGame;
use App\Repositories\Player\PlayerRepository;
use Illuminate\Support\Facades\DB;

class LogsGameService
{
    private PlayerRepository $playerRepository;

    public function __construct(PlayerRepository $playerRepository)
    {
        $this->playerRepository = $playerRepository;
    }

    /*public function searchLogsGame(int $limit, ?string $keyword = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $data = DB::connection('mysql_game')->table('logs_games')
                ->orderBy('created_at', 'desc')->paginate($limit);

        return $data;
    }*/
    /*public function searchLogsGame22(int $limit, ?string $keyword = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = DB::connection('mysql_game')->table('logs_games');

        if ($keyword) {
            $query->whereRaw('JSON_EXTRACT(data, "$.tid") LIKE ?', ['%' . $keyword . '%']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($limit);
    }*/

    public function searchLogsGame(int $limit, ?string $keyword = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = DB::connection('mysql_game')->table('logs_games');

        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                // Search by tid in JSON data
                $q->whereRaw('JSON_EXTRACT(data, "$.tid") LIKE ?', ['%' . $keyword . '%'])
                    // Search by id in JSON data
                    ->orWhereRaw('JSON_EXTRACT(data, "$.id") LIKE ?', ['%' . $keyword . '%'])
                    // Search by playerName in players array
                    ->orWhereRaw('JSON_SEARCH(JSON_EXTRACT(data, "$.players[*].playerName"), "one", ?) IS NOT NULL', ['%' . $keyword . '%']);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($limit);
    }

    public function searchLogsGameByPlayerId(int $playerId, array $params)
    {
        /*$items = LidUid::query()->with(['log_game']);
        $items = $items->where('player_id', $playerId);*/

        $lids = LidUid::query()->where('player_id', $playerId)->pluck('lid');

        $items = LogsGame::query()->whereIn('id', $lids);

        if (isset($params['keyword'])) {
            $keyword = $params['keyword'];
            /*$items = $items->where(function ($query) use ($keywords) {
                $query->where('nick_name', 'like', '%' . $keywords . '%');
                $query->orWhere('display_name', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });*/
            $items->where(function ($q) use ($keyword) {
                // Search by tid in JSON data
                $q->whereRaw('JSON_EXTRACT(data, "$.tid") LIKE ?', ['%' . $keyword . '%'])
                    // Search by id in JSON data
                    ->orWhereRaw('JSON_EXTRACT(data, "$.id") LIKE ?', ['%' . $keyword . '%'])
                    // Search by playerName in players array
                    ->orWhereRaw('JSON_SEARCH(JSON_EXTRACT(data, "$.players[*].playerName"), "one", ?) IS NOT NULL', ['%' . $keyword . '%']);
            });
        }

        /*if (isset($params['status']) && $params['status'] !== -1) {
            $status = $params['status'];
            $items = $items->where('status', $status);
        }*/

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }

        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }

        list($sort_key, $sort_value) = explode(':', $sort);
        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }

}
