<?php

namespace App\Services\Games;

use App\Models\Player;
use App\Repositories\Player\PlayerRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class PlayerService
{
    private PlayerRepository $playerRepo;

    public function __construct(PlayerRepository $playerRepository)
    {
        $this->playerRepo = $playerRepository;
    }

    public function getPlayerByUid(int $uid)
    {
        return $this->playerRepo->getByColumn($uid, 'uid')->first();
    }

    public function getItem(int $id)
    {
        return $this->playerRepo->getById($id);
    }

    public function createPlayer(array $data): int
    {
        try {
            return DB::connection('mysql_game')->table('players')->insertGetId($data);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Player creation failed: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Update player data in the game database
     *
     * @param array $data Player data with 'id' key required
     * @return bool True if successful, false otherwise
     */
    public function updatePlayer(array $data): bool
    {
        // Ensure the ID exists in the data
        if (!isset($data['id']) || empty($data['id'])) {
            \Log::error('Player update failed: Missing player ID');
            return false;
        }

        $playerId = $data['id'];

        try {
            // Remove id from data as it's used in the where clause
            unset($data['id']);

            // Add updated_at timestamp if not provided
            if (!isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }

            // Update player record
            $affected = DB::connection('mysql_game')
                ->table('players')
                ->where('id', $playerId)
                ->update($data);

            return $affected > 0;
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Player update failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a player from the game database
     *
     * @param int $playerId The ID of the player to delete
     * @return bool True if successful, false otherwise
     */
    public function deletePlayer(int $playerId): bool
    {
        try {
            $deleted = DB::connection('mysql_game')
                ->table('players')
                ->where('id', $playerId)
                ->delete();

            return $deleted > 0;
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Player deletion failed: ' . $e->getMessage());
            return false;
        }
    }

    public function searchByParams(array $params): LengthAwarePaginator
    {
        $items = Player::query(); // ->with(['roles']);

        /*if (isset($params['role_id']) && (int)$params['role_id'] > 0) {
            $role_id = $params['role_id'];
            $items = $items->whereHas('roles', function ($query) use ($role_id) {
                $query->where('role_id', $role_id);
            });
        }*/

        if (isset($params['s'])) {
            $keywords = $params['s'];
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('nick_name', 'like', '%' . $keywords . '%');
                $query->orWhere('display_name', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        /*if (isset($params['status']) && $params['status'] !== -1) {
            $status = $params['status'];
            $items = $items->where('status', $status);
        }*/

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }

        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }

        list($sort_key, $sort_value) = explode(':', $sort);
        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }
}
