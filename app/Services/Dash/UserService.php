<?php

namespace App\Services\Dash;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class UserService
{
    /*private RoleRepository $roleRepo;
    private UserRepository $userRepo;*/

    public function __construct(
        /*RoleRepository $roleRepository,
        UserRepository $userRepository*/
    )
    {
        /*$this->roleRepo = $roleRepository;
        $this->userRepo = $userRepository;*/
    }

    public function getItemById(int $id): Model
    {
        return User::with('roles')->find($id);
    }

    public function searchItems(Request $request, string $sort = 'id:desc', int $per_page = 10): LengthAwarePaginator
    {
        $items = User::query()->with(['roles', 'orders', 'orders2']);

        $keywords = $request->input('s', '');
        if ($keywords) {
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('name', 'like', '%' . $keywords . '%');
                // $query->orWhere('description', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        $status = $request->input('status', -1);
        if ($status != -1) {
            $items = $items->where('status', $status);
        }

        $roleId = $request->input('roleId', 0);

        if (isset($roleId) && (int)$roleId > 0) {
            $items = $items->whereHas('roles', function ($query) use ($roleId) {
                $query->where('role_id', $roleId);
            });
        }

        list($sort_key, $sort_value) = explode(':', $sort);
        if ($sort_key === 'tensanpham') {
            $sort_key = 'name';
        }
        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }

    public function searchByParams(array $params): LengthAwarePaginator
    {
        $items = User::query(); // ->with(['roles']);

        if (isset($params['role_id']) && (int)$params['role_id'] > 0) {
            $role_id = $params['role_id'];
            $items = $items->whereHas('roles', function ($query) use ($role_id) {
                $query->where('role_id', $role_id);
            });
        }

        if (isset($params['s'])) {
            $keywords = $params['s'];
            $items = $items->where(function ($query) use ($keywords) {
                $query->where('name', 'like', '%' . $keywords . '%');
                // $query->orWhere('description', 'like', '%' . $keywords . '%');
                $query->orWhere('id', 'like', '%' . $keywords . '%');
            });
        }

        if (isset($params['status']) && $params['status'] !== -1) {
            $status = $params['status'];
            $items = $items->where('status', $status);
        }

        $per_page = 10;
        if (isset($params['per_page'])) {
            $per_page = $params['per_page'];
        }

        $sort = 'id:desc';
        if (isset($params['sort'])) {
            $sort = $params['sort'];
        }

        list($sort_key, $sort_value) = explode(':', $sort);
        if ($sort_key === 'tensanpham') {
            $sort_key = 'name';
        }
        return $items->orderBy($sort_key, $sort_value)->paginate($per_page);
    }


    /**
     * Delete an item by its ID.
     *
     * @param int $id The ID of the item to be deleted.
     * @return array
     */
    public function deleteItemById(int $id): array
    {
        $item = User::query()->find($id);
        $status = true;
        $msg = '';
        $code = 200;

        if (is_null($item)) {
            $status = false;
            $msg = 'Item is not found!';
            $code = 404;
        }

        if ($item) {
            $item->delete();
        }

        return [
            'status' => $status,
            'msg' => $msg,
            'code' => $code,
            'item' => $item
        ];
    }

}
