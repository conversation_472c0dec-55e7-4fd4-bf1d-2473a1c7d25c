<?php

namespace App\Services\Dash;

use App\Entities\Status;
use App\Models\Role;
use Illuminate\Database\Eloquent\Model;

class RoleService
{
    public function __construct()
    {
        // todo
    }

    public function getItemById(int $id): Model
    {
        return Role::with('users')->find($id);
    }

    public function searchItems(int $page, string $sort = 'id:desc')
    {
        list($sort_key, $sort_value) = explode(':', $sort);
        if ($sort_key === 'rolename') {
            $sort_key = 'name';
        }
        return Role::with('users')->orderBy($sort_key, $sort_value)->paginate($page);
    }

    public function getRoles(string $sort = 'id:desc')
    {
        list($sort_key, $sort_value) = explode(':', $sort);
        return Role::where('status', Status::ACTIVE)->orderBy($sort_key, $sort_value)->get();
    }

}
