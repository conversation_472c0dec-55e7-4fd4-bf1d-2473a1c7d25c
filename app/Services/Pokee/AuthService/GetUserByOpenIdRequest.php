<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.GetUserByOpenIdRequest</code>
 */
class GetUserByOpenIdRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string open_id = 1;</code>
     */
    protected $open_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $open_id
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string open_id = 1;</code>
     * @return string
     */
    public function getOpenId()
    {
        return $this->open_id;
    }

    /**
     * Generated from protobuf field <code>string open_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setOpenId($var)
    {
        GPBUtil::checkString($var, True);
        $this->open_id = $var;

        return $this;
    }

}

