<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.DeviceLoginRequest</code>
 */
class DeviceLoginRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * ID của thiết bị
     *
     * Generated from protobuf field <code>string device_id = 1;</code>
     */
    protected $device_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $device_id
     *           ID của thiết bị
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * ID của thiết bị
     *
     * Generated from protobuf field <code>string device_id = 1;</code>
     * @return string
     */
    public function getDeviceId()
    {
        return $this->device_id;
    }

    /**
     * ID của thiết bị
     *
     * Generated from protobuf field <code>string device_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setDeviceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->device_id = $var;

        return $this;
    }

}

