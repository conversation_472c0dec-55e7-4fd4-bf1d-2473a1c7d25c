<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.LoginResponse</code>
 */
class LoginResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     */
    protected $success = false;
    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     */
    protected $error_key = '';
    /**
     * Generated from protobuf field <code>string token = 3;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>string refresh_token = 4;</code>
     */
    protected $refresh_token = '';
    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     */
    protected $data = null;
    /**
     * Generated from protobuf field <code>int32 expires_at = 6;</code>
     */
    protected $expires_at = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $success
     *     @type string $error_key
     *     @type string $token
     *     @type string $refresh_token
     *     @type \App\Services\Pokee\AuthService\UserInfo $data
     *     @type int $expires_at
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @return bool
     */
    public function getSuccess()
    {
        return $this->success;
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setSuccess($var)
    {
        GPBUtil::checkBool($var);
        $this->success = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @return string
     */
    public function getErrorKey()
    {
        return $this->error_key;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setErrorKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->error_key = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string token = 3;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string refresh_token = 4;</code>
     * @return string
     */
    public function getRefreshToken()
    {
        return $this->refresh_token;
    }

    /**
     * Generated from protobuf field <code>string refresh_token = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRefreshToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->refresh_token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     * @return \App\Services\Pokee\AuthService\UserInfo|null
     */
    public function getData()
    {
        return $this->data;
    }

    public function hasData()
    {
        return isset($this->data);
    }

    public function clearData()
    {
        unset($this->data);
    }

    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     * @param \App\Services\Pokee\AuthService\UserInfo $var
     * @return $this
     */
    public function setData($var)
    {
        GPBUtil::checkMessage($var, \App\Services\Pokee\AuthService\UserInfo::class);
        $this->data = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 expires_at = 6;</code>
     * @return int
     */
    public function getExpiresAt()
    {
        return $this->expires_at;
    }

    /**
     * Generated from protobuf field <code>int32 expires_at = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setExpiresAt($var)
    {
        GPBUtil::checkInt32($var);
        $this->expires_at = $var;

        return $this;
    }

}

