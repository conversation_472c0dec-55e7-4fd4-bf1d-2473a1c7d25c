<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.CreateUserRequest</code>
 */
class CreateUserRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string username = 1;</code>
     */
    protected $username = '';
    /**
     * Generated from protobuf field <code>string email = 2;</code>
     */
    protected $email = '';
    /**
     * Generated from protobuf field <code>string password = 3;</code>
     */
    protected $password = '';
    /**
     * Generated from protobuf field <code>string first_name = 4;</code>
     */
    protected $first_name = '';
    /**
     * Generated from protobuf field <code>string last_name = 5;</code>
     */
    protected $last_name = '';
    /**
     * Generated from protobuf field <code>string phone = 6;</code>
     */
    protected $phone = '';
    /**
     * Generated from protobuf field <code>string avatar = 7;</code>
     */
    protected $avatar = '';
    /**
     * Generated from protobuf field <code>string gender = 8;</code>
     */
    protected $gender = '';
    /**
     * Generated from protobuf field <code>string open_id = 9;</code>
     */
    protected $open_id = '';
    /**
     * Generated from protobuf field <code>string login_type = 10;</code>
     */
    protected $login_type = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $username
     *     @type string $email
     *     @type string $password
     *     @type string $first_name
     *     @type string $last_name
     *     @type string $phone
     *     @type string $avatar
     *     @type string $gender
     *     @type string $open_id
     *     @type string $login_type
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string username = 1;</code>
     * @return string
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * Generated from protobuf field <code>string username = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setUsername($var)
    {
        GPBUtil::checkString($var, True);
        $this->username = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEmail($var)
    {
        GPBUtil::checkString($var, True);
        $this->email = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string password = 3;</code>
     * @return string
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * Generated from protobuf field <code>string password = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPassword($var)
    {
        GPBUtil::checkString($var, True);
        $this->password = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string first_name = 4;</code>
     * @return string
     */
    public function getFirstName()
    {
        return $this->first_name;
    }

    /**
     * Generated from protobuf field <code>string first_name = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFirstName($var)
    {
        GPBUtil::checkString($var, True);
        $this->first_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string last_name = 5;</code>
     * @return string
     */
    public function getLastName()
    {
        return $this->last_name;
    }

    /**
     * Generated from protobuf field <code>string last_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setLastName($var)
    {
        GPBUtil::checkString($var, True);
        $this->last_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string phone = 6;</code>
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * Generated from protobuf field <code>string phone = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setPhone($var)
    {
        GPBUtil::checkString($var, True);
        $this->phone = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string avatar = 7;</code>
     * @return string
     */
    public function getAvatar()
    {
        return $this->avatar;
    }

    /**
     * Generated from protobuf field <code>string avatar = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setAvatar($var)
    {
        GPBUtil::checkString($var, True);
        $this->avatar = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string gender = 8;</code>
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Generated from protobuf field <code>string gender = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setGender($var)
    {
        GPBUtil::checkString($var, True);
        $this->gender = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string open_id = 9;</code>
     * @return string
     */
    public function getOpenId()
    {
        return $this->open_id;
    }

    /**
     * Generated from protobuf field <code>string open_id = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setOpenId($var)
    {
        GPBUtil::checkString($var, True);
        $this->open_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string login_type = 10;</code>
     * @return string
     */
    public function getLoginType()
    {
        return $this->login_type;
    }

    /**
     * Generated from protobuf field <code>string login_type = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setLoginType($var)
    {
        GPBUtil::checkString($var, True);
        $this->login_type = $var;

        return $this;
    }

}

