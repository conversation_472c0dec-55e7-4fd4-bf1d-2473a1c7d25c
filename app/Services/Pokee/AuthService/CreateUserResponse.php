<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.CreateUserResponse</code>
 */
class CreateUserResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     */
    protected $success = false;
    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     */
    protected $error_key = '';
    /**
     * Generated from protobuf field <code>string message = 3;</code>
     */
    protected $message = '';
    /**
     * Generated from protobuf field <code>int32 user_id = 4;</code>
     */
    protected $user_id = 0;
    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     */
    protected $data = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $success
     *     @type string $error_key
     *     @type string $message
     *     @type int $user_id
     *     @type \App\Services\Pokee\AuthService\UserInfo $data
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @return bool
     */
    public function getSuccess()
    {
        return $this->success;
    }

    /**
     * Generated from protobuf field <code>bool success = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setSuccess($var)
    {
        GPBUtil::checkBool($var);
        $this->success = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @return string
     */
    public function getErrorKey()
    {
        return $this->error_key;
    }

    /**
     * Generated from protobuf field <code>string error_key = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setErrorKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->error_key = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string message = 3;</code>
     * @return string
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Generated from protobuf field <code>string message = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setMessage($var)
    {
        GPBUtil::checkString($var, True);
        $this->message = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 user_id = 4;</code>
     * @return int
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * Generated from protobuf field <code>int32 user_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setUserId($var)
    {
        GPBUtil::checkInt32($var);
        $this->user_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     * @return \App\Services\Pokee\AuthService\UserInfo|null
     */
    public function getData()
    {
        return $this->data;
    }

    public function hasData()
    {
        return isset($this->data);
    }

    public function clearData()
    {
        unset($this->data);
    }

    /**
     * Generated from protobuf field <code>.userservice.UserInfo data = 5;</code>
     * @param \App\Services\Pokee\AuthService\UserInfo $var
     * @return $this
     */
    public function setData($var)
    {
        GPBUtil::checkMessage($var, \App\Services\Pokee\AuthService\UserInfo::class);
        $this->data = $var;

        return $this;
    }

}

