<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: pkg/pb/user_service.proto

namespace App\Services\Pokee\AuthService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>userservice.UpdateUserRequest</code>
 */
class UpdateUserRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    protected $id = 0;
    /**
     * Generated from protobuf field <code>string first_name = 2;</code>
     */
    protected $first_name = '';
    /**
     * Generated from protobuf field <code>string last_name = 3;</code>
     */
    protected $last_name = '';
    /**
     * bool enabled = 4;
     *
     * Generated from protobuf field <code>string phone = 4;</code>
     */
    protected $phone = '';
    /**
     * Generated from protobuf field <code>string avatar = 5;</code>
     */
    protected $avatar = '';
    /**
     * Generated from protobuf field <code>string gender = 6;</code>
     */
    protected $gender = '';
    /**
     * Generated from protobuf field <code>string date_of_birth = 7;</code>
     */
    protected $date_of_birth = '';
    /**
     * Generated from protobuf field <code>string last_login = 8;</code>
     */
    protected $last_login = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *     @type string $first_name
     *     @type string $last_name
     *     @type string $phone
     *           bool enabled = 4;
     *     @type string $avatar
     *     @type string $gender
     *     @type string $date_of_birth
     *     @type string $last_login
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Pkg\Pb\UserService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string first_name = 2;</code>
     * @return string
     */
    public function getFirstName()
    {
        return $this->first_name;
    }

    /**
     * Generated from protobuf field <code>string first_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFirstName($var)
    {
        GPBUtil::checkString($var, True);
        $this->first_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string last_name = 3;</code>
     * @return string
     */
    public function getLastName()
    {
        return $this->last_name;
    }

    /**
     * Generated from protobuf field <code>string last_name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setLastName($var)
    {
        GPBUtil::checkString($var, True);
        $this->last_name = $var;

        return $this;
    }

    /**
     * bool enabled = 4;
     *
     * Generated from protobuf field <code>string phone = 4;</code>
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * bool enabled = 4;
     *
     * Generated from protobuf field <code>string phone = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setPhone($var)
    {
        GPBUtil::checkString($var, True);
        $this->phone = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string avatar = 5;</code>
     * @return string
     */
    public function getAvatar()
    {
        return $this->avatar;
    }

    /**
     * Generated from protobuf field <code>string avatar = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setAvatar($var)
    {
        GPBUtil::checkString($var, True);
        $this->avatar = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string gender = 6;</code>
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Generated from protobuf field <code>string gender = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setGender($var)
    {
        GPBUtil::checkString($var, True);
        $this->gender = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string date_of_birth = 7;</code>
     * @return string
     */
    public function getDateOfBirth()
    {
        return $this->date_of_birth;
    }

    /**
     * Generated from protobuf field <code>string date_of_birth = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDateOfBirth($var)
    {
        GPBUtil::checkString($var, True);
        $this->date_of_birth = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string last_login = 8;</code>
     * @return string
     */
    public function getLastLogin()
    {
        return $this->last_login;
    }

    /**
     * Generated from protobuf field <code>string last_login = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setLastLogin($var)
    {
        GPBUtil::checkString($var, True);
        $this->last_login = $var;

        return $this;
    }

}

