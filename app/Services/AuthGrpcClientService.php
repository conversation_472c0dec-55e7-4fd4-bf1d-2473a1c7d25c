<?php

namespace App\Services;

use App\Services\Pokee\AuthService\ChangePasswordRequest;
use App\Services\Pokee\AuthService\ChangePasswordResponse;
use App\Services\Pokee\AuthService\GetUserByIDRequest;
use App\Services\Pokee\AuthService\GetUserInfoRequest;
use App\Services\Pokee\AuthService\GetUserInfoResponse;
use App\Services\Pokee\AuthService\ListUsersRequest;
use App\Services\Pokee\AuthService\ResetPasswordRequest;
use App\Services\Pokee\AuthService\UpdateEmailRequest;
use App\Services\Pokee\AuthService\UpdateEmailResponse;
use App\Services\Pokee\AuthService\UpdateUsernameRequest;
use App\Services\Pokee\AuthService\UpdateUsernameResponse;
use App\Services\Pokee\AuthService\UserInfo;
use App\Services\Pokee\AuthService\UserServiceClient;
use Grpc\ChannelCredentials;

class AuthGrpcClientService
{
    private $client;

    public function __construct()
    {
        $hostname = config('services.grpc_auth.hostname');
        $this->client = new UserServiceClient($hostname, [
            'credentials' => ChannelCredentials::createInsecure(),
        ]);
    }

    public function getUserById(int $id): UserInfo
    {
        $request = new GetUserByIDRequest();
        $request->setId($id);

        list($response, $status) = $this->client->GetUserByID($request)->wait();

        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }


    public function getUserByUserId(int $userId): GetUserInfoResponse
    {
        $request = new GetUserInfoRequest();
        $request->setUserId($userId);

        list($response, $status) = $this->client->GetUserInfo($request)->wait();

        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }


    public function searchUserByKeywordMethod($keyword, $firstResult = 0, $maxResult = 10)
    {
        $request = new ListUsersRequest();
        $request->setSearch($keyword);
        $request->setPage($firstResult);
        $request->setPageSize($maxResult);

        list($response, $status) = $this->client->SearchUsers($request)->wait();

        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    /**
     * Create a new user in the authentication service
     *
     * @param array $userData The user data
     * @return \App\Services\Pokee\AuthService\CreateUserResponse
     * @throws \Exception
     */
    public function createUser(array $userData): \App\Services\Pokee\AuthService\CreateUserResponse
    {
        $request = new \App\Services\Pokee\AuthService\CreateUserRequest();

        // Set required fields from userData
        $request->setUsername($userData['username'] ?? '');
        $request->setEmail($userData['email'] ?? '');
        $request->setPassword($userData['password'] ?? '');
        $request->setFirstName($userData['first_name'] ?? '');
        $request->setLastName($userData['last_name'] ?? '');

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->CreateUser($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    /**
     * Update an existing user in the authentication service
     *
     * @param int $id The user ID to update
     * @param array $userData The user data to update
     * @return \App\Services\Pokee\AuthService\GetUserInfoResponse
     * @throws \Exception
     */
    public function updateUser(int $id, array $userData): \App\Services\Pokee\AuthService\GetUserInfoResponse
    {
        $request = new \App\Services\Pokee\AuthService\UpdateUserRequest();

        // Set the user ID
        $request->setId($id);

        // Set the fields to update if they exist in the userData array
        if (isset($userData['first_name'])) {
            $request->setFirstName($userData['first_name']);
        }

        if (isset($userData['last_name'])) {
            $request->setLastName($userData['last_name']);
        }

        if (isset($userData['phone'])) {
            $request->setPhone($userData['phone']);
        }

        if (isset($userData['avatar'])) {
            $request->setAvatar($userData['avatar']);
        }

        if (isset($userData['gender'])) {
            $request->setGender($userData['gender']);
        }

        if (isset($userData['date_of_birth'])) {
            $request->setDateOfBirth($userData['date_of_birth']);
        }

        if (isset($userData['last_login'])) {
            $request->setLastLogin($userData['last_login']);
        }

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->UpdateUser($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    /**
     * Delete a user from the authentication service
     *
     * @param int $id The user ID to delete
     * @return \App\Services\Pokee\AuthService\DeleteUserResponse
     * @throws \Exception
     */
    public function deleteUser(int $id): \App\Services\Pokee\AuthService\DeleteUserResponse
    {
        $request = new \App\Services\Pokee\AuthService\DeleteUserRequest();

        // Set the user ID to delete
        $request->setId($id);

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->DeleteUser($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    /**
     * Change password for a user in the authentication service
     *
     * @param int $id The user ID
     * @param array $passwordData Array containing old_password and new_password
     * @return ChangePasswordResponse
     * @throws \Exception
     */
    public function changePassword(int $id, array $passwordData): ChangePasswordResponse
    {
        $request = new ChangePasswordRequest();

        // Set the user ID
        $request->setId($id);

        // Set the old and new passwords
        $request->setOldPassword($passwordData['current_password'] ?? '');
        $request->setNewPassword($passwordData['new_password'] ?? '');

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->ChangePassword($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    public function resetPassword(int $id, array $passwordData): ChangePasswordResponse
    {
        $request = new ResetPasswordRequest();

        // Set the user ID
        $request->setId($id);

        // Set the new password
        $request->setNewPassword($passwordData['new_password'] ?? '');

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->ResetPassword($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    public function updateEmail(int $id, array $emailData): UpdateEmailResponse
    {
        $request = new UpdateEmailRequest();

        // Set the user ID
        $request->setId($id);

        // Set the new email
        $request->setEmail($emailData['new_email'] ?? '');

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->UpdateEmail($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }

    public function updateUsername(int $id, array $data): UpdateUsernameResponse
    {
        $request = new UpdateUsernameRequest();

        // Set the user ID
        $request->setId($id);

        // Set the username
        $request->setUsername($data['username'] ?? '');

        // Call the gRPC method and wait for the response
        list($response, $status) = $this->client->UpdateUsername($request)->wait();

        // Check if the call was successful
        if ($status->code !== \Grpc\STATUS_OK) {
            throw new \Exception($status->details, $status->code);
        }

        return $response;
    }
}
