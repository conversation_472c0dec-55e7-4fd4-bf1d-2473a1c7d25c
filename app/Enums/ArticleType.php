<?php

namespace App\Enums;

enum ArticleType: string
{
    case NEWS = 'NEWS';
    case EVENT = 'EVENT';

    public function label(): string
    {
        return match($this) {
            self::NEWS => 'Tin tức',
            self::EVENT => 'Sự kiện',
        };
    }

    public static function toArrayWithKeyValue(): array
    {
        return [
            self::NEWS->value => self::NEWS->label(),
            self::EVENT->value => self::EVENT->label(),
        ];
    }
}
