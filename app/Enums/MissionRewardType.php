<?php

namespace App\Enums;

enum MissionRewardType: string
{
    case COINS = 'coins';
    case EXP = 'exp';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::COINS => 'Thưởng Chip',
            self::EXP => 'Thưởng kinh nghiệm',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
