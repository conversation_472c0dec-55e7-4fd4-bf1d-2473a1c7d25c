<?php

namespace App\Enums;

enum MissionType: string
{
    case WIN_HANDS = 'WIN_HANDS';
    case PLAY_TABLES = 'PLAY_TABLES';
    case DAILY_LOGIN = 'DAILY_LOGIN';
    case MAKE_FRIEND = 'MAKE_FRIEND';
    case TOTAL_BET = 'TOTAL_BET';
    case WIN_STREAK = 'WIN_STREAK';
    case LEVEL_UP = 'LEVEL_UP';
    case BUY_ITEM = 'BUY_ITEM';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::WIN_HANDS => 'Ván thắng',
            self::PLAY_TABLES => 'Số bàn chơi',
            self::DAILY_LOGIN => 'Login hàng ngày',
            self::MAKE_FRIEND => 'Kết bạn',
            self::TOTAL_BET => 'Tổng cược',
            self::WIN_STREAK => 'Ván thắng liên tiếp',
            self::LEVEL_UP => 'Lên level',
            self::BUY_ITEM => 'Mua vật phẩm',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
