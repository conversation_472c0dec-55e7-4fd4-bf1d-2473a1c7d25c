<?php

namespace App\Enums;

enum BadgeRewardType: string
{
    case VIP = 'VIP';
    case CHIPS = 'CHIPS';
    case AVATAR = 'AVATAR';
    case TITLE = 'TITLE';
    case DECORATION = 'DECORATION';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::VIP => 'VIP',
            self::CHIPS => 'Chips',
            self::AVATAR => 'Avatar',
            self::TITLE => 'Danh hiệu',
            self::DECORATION => 'Trang trí',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
