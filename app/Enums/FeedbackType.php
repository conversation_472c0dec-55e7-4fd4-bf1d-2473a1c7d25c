<?php

namespace App\Enums;

enum FeedbackType: string
{
    case DEPOSIT_ISSUES = 'DEPOSIT_ISSUES';
    case ACCOUNT_ISSUES = 'ACCOUNT_ISSUES';
    case GAME_BUGS = 'GAME_BUGS';
    case UI_ISSUES = 'UI_ISSUES';
    case OTHER = 'OTHER';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::DEPOSIT_ISSUES => 'Vấn đề nạp tiền',
            self::ACCOUNT_ISSUES => 'Vấn đề tài khoản',
            self::GAME_BUGS => 'Lỗi game',
            self::UI_ISSUES => 'Vấn đề giao diện',
            self::OTHER => 'Khác'
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
