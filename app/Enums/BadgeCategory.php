<?php

namespace App\Enums;

enum BadgeCategory: string
{
    case LEVEL = 'LEVEL';
    case CHIPS = 'CHIPS';
    case FIRST_TIME = 'FIRST_TIME';
    case SPECIAL = 'SPECIAL';
    case WIN_COUNT = 'WIN_COUNT';
    case LOSE_COUNT = 'LOSE_COUNT';
    case PLAY_COUNT = 'PLAY_COUNT';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::LEVEL => 'Cấp độ',
            self::CHIPS => 'Chips',
            self::FIRST_TIME => 'Lần đầu',
            self::SPECIAL => 'Đặc biệt',
            self::WIN_COUNT => 'Số lần thắng',
            self::LOSE_COUNT => 'Số lần thua',
            self::PLAY_COUNT => 'Số lần chơi',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
