<?php

namespace App\Enums;

enum UserType: string
{
    case ADMIN = 'ADMIN';
    case PARTNER = 'PARTNER';
    case SUPPORT = 'SUPPORT';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::ADMIN => 'Administrator',
            self::PARTNER => 'Đại lý',
            self::SUPPORT => 'Vận Hành',
        };
    }
}
