<?php

namespace App\Enums;

enum LeaderBoardType: string
{
    case CHIPS = 'CHIPS';
    case XP = 'XP';
    case WINNINGS = 'WINNINGS';
    case TOURNAMENT = 'TOURNAMENT';
    case LEVEL = 'LEVEL';
    case BADGES = 'BADGES';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::CHIPS => 'CHIPS',
            self::XP => 'XP',
            self::WINNINGS => 'WINNINGS',
            self::TOURNAMENT => 'TOURNAMENT',
            self::LEVEL => 'LEVEL',
            self::BADGES => 'BADGES',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
