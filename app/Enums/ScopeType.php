<?php

namespace App\Enums;

enum ScopeType: string
{
    case GLOBAL = 'GLOBAL';
    case FRIENDS = 'FRIENDS';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::GLOBAL => 'Thế giới',
            self::FRIENDS => 'Bạn bè',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
