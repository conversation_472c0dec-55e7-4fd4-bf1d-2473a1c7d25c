<?php

namespace App\Enums;

enum ShopType: string
{
    case CHIP = 'CHIP';
    case DIAMOND = 'DIAMOND';
    case PROPS = 'PROPS';
    case CARD = 'CARD';
    case DECOR = 'DECOR';
    case COIN = 'COIN';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::CHIP => 'Chip',
            self::DIAMOND => '<PERSON>ơ<PERSON>',
            self::PROPS => 'Đạo cụ',
            self::CARD => 'Card',
            self::DECOR => 'Trang trí',
            self::COIN => 'Coin',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
