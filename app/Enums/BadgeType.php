<?php

namespace App\Enums;

enum BadgeType: string
{
    case POKE_CAREER = 'POKE_CAREER';
    case ACHIEVEMENTS = 'ACHIEVEMENTS';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::POKE_CAREER => 'Nghề Poker',
            self::ACHIEVEMENTS => 'Chiến tích',
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
