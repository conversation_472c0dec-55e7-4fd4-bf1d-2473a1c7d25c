<?php

namespace App\Enums;

enum PlayerLoginType: string
{
    case EMAIL = 'EMAIL';
    case DEVICE = 'DEVICE';
    case FACEBOOK = 'FACEBOOK';
    case GOOGLE = 'GOOGLE';

    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::EMAIL => 'Email',
            self::DEVICE => 'Device',
            self::FACEBOOK => 'Facebook',
            self::GOOGLE => 'Google',
        };
    }
}
