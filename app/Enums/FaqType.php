<?php

namespace App\Enums;

enum FaqType: string
{
    case LOGIN_REWARDS = 'LOGIN_REWARDS';
    case ACCOUNT_ISSUES = 'ACCOUNT_ISSUES';
    case DEPOSIT_ISSUES = 'DEPOSIT_ISSUES';
    case HOT_ISSUES = 'HOT_ISSUES';
    
    public static function getKeyValues(): array
    {
        $keyValues = [];
        foreach (self::cases() as $case) {
            $keyValues[$case->value] = $case->customName();
        }
        return $keyValues;
    }

    public function customName(): string
    {
        return match ($this) {
            self::LOGIN_REWARDS => 'Câu hỏi về thưởng đăng nhập',
            self::ACCOUNT_ISSUES => 'Câu hỏi về tài khoản',
            self::DEPOSIT_ISSUES => 'Câu hỏi về nạp tiền',
            self::HOT_ISSUES => 'Câu hỏi thường gặp'
        };
    }

    /**
     * Get an array of the enum values.
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
