<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Upload Base URL
    |--------------------------------------------------------------------------
    |
    | This URL will be used as the base for all absolute file paths.
    | You can change this to your CDN URL or any other domain.
    |
    */
    'base_url' => env('UPLOAD_BASE_URL', env('APP_URL', 'http://localhost:8000')),

    /*
    |--------------------------------------------------------------------------
    | Image Sizes
    |--------------------------------------------------------------------------
    |
    | Define the different image sizes that will be generated
    | when images are uploaded.
    |
    */
    'image_sizes' => [
        // 'avatar' => ['width' => 90, 'height' => 116],
        'thumbnail' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 800, 'height' => 600],
        'large' => ['width' => 1200, 'height' => 900],
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed MIME Types
    |--------------------------------------------------------------------------
    |
    | Define which file types are allowed for upload.
    |
    */
    'allowed_mime_types' => [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml'
    ],
];
