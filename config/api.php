<?php

return [
    /*
    |--------------------------------------------------------------------------
    | External API Keys
    |--------------------------------------------------------------------------
    |
    | Define API keys and secrets for external systems that need to access
    | the upload endpoints without Sanctum authentication.
    |
    */
    'external_keys' => [
        env('GAME_SERVER_API_KEY') => [
            'name' => 'Game Server',
            'secret' => env('GAME_SERVER_API_SECRET'),
            'virtual_user_id' => 999999, // Virtual user ID for game server uploads
            'email' => '<EMAIL>',
            'description' => 'Game server system for uploading player images',
            'permissions' => ['upload'],
            'rate_limit' => 1000, // requests per hour
        ],

        // Add more external systems as needed
        // env('ANOTHER_SYSTEM_API_KEY') => [
        //     'name' => 'Another System',
        //     'secret' => env('ANOTHER_SYSTEM_API_SECRET'),
        //     'virtual_user_id' => 999998,
        //     'email' => '<EMAIL>',
        //     'description' => 'Another external system',
        //     'permissions' => ['upload'],
        //     'rate_limit' => 500,
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for external API access
    |
    */
    'rate_limiting' => [
        'enabled' => env('API_RATE_LIMITING_ENABLED', true),
        'default_limit' => 100, // requests per hour
        'burst_limit' => 20, // requests per minute
    ],

    /*
    |--------------------------------------------------------------------------
    | Upload Limits for External APIs
    |--------------------------------------------------------------------------
    |
    | Configure specific upload limits for external systems
    |
    */
    'external_upload_limits' => [
        'max_file_size' => 10240, // KB (10MB)
        'max_files_per_request' => 5, // Lower than regular users for external systems
        'allowed_mime_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'max_daily_uploads' => 1000, // files per day per system
    ],
];
